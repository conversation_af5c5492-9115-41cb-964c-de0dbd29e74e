'use client'

import Pagination1 from '../common/Pagination1'
import Link from 'next/link'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function Blog1() {
  const { t } = useTranslation()

  const [filteredBlogs, setFilteredBlogs] = useState([])
  const getBlogs = async () => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_APP_API_URL}/blogs?page=1&limit=10&order=desc&type=news`
      )
      const response = await result.json()
      setFilteredBlogs(response)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getBlogs()
  }, [])
  return (
    <>
      <section className='blog-page-title mb-4 mb-xl-5'>
        <div className='title-bg'>
          <Image
            loading='lazy'
            src='/assets/images/blog_title_bg.jpg'
            width='1780'
            height='420'
            alt='image'
          />
        </div>
        <div className='container'>
          <h2 className='page-title'>Bài viết</h2>
        </div>
      </section>
      <section className='blog-page container'>
        <h2 className='d-none'>Bài viết</h2>
        <div className='blog-grid row row-cols-1 row-cols-md-2'>
          {filteredBlogs.map((elm, i) => (
            <div key={i} className='blog-grid__item'>
              <div className='blog-grid__item-image'>
                <Image
                  loading='lazy'
                  className='h-auto'
                  src={elm.images[0]?.url}
                  width='690'
                  height='500'
                  alt='image'
                />
              </div>
              <div className='blog-grid__item-detail'>
                <div className='blog-grid__item-meta'>
                  {/* <span className='blog-grid__item-meta__author'>By {elm.author}</span> */}
                  <span className='blog-grid__item-meta__date'>{elm.createdAt}</span>
                </div>
                <div className='blog-grid__item-title'>
                  <Link href={`/blog_single/${elm.id}`}>{elm.title}</Link>
                </div>
                <div className='blog-grid__item-content'>
                  <p>{elm.content}</p>
                  <Link href={`/blog_single/${elm.id}`} className='readmore-link'>
                    Continue Reading
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
        <p className='mb-5 text-center fw-medium'>SHOWING 36 of 497 items</p>
        <Pagination1 />

        <div className='text-center'>
          <a className='btn-link btn-link_lg text-uppercase fw-medium' href='#'>
            {t('showMore', 'Xem tiếp')}
          </a>
        </div>
      </section>
    </>
  )
}
