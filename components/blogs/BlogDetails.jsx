import React from 'react'
import ReviewForm from './ReviewForm'
import Reviews from './Reviews'
import Image from 'next/image'

export default function BlogDetails({ blog }) {
  return (
    <section className='blog-page blog-single container'>
      <div className='mw-930'>
        <h2 className='page-title'>{blog.title}</h2>
        <div className='blog-single__item-meta'>
          <span className='blog-single__item-meta__author'>{/* By Admin */}</span>
          <span className='blog-single__item-meta__date'>{blog.date}</span>
          {/* <span className='blog-single__item-meta__category'>Trends</span> */}
        </div>
      </div>
      <div className='blog-single__item-content'>
        {blog.images && blog.images.length ? (
          <p>
            <Image
              loading='lazy'
              className='w-100 h-auto d-block'
              src={blog.images[0].url}
              width='1410'
              height='550'
              alt='image'
            />
          </p>
        ) : (
          ''
        )}
        <div className='mw-930' dangerouslySetInnerHTML={{ __html: blog.description }}></div>
      </div>
    </section>
  )
}
