'use client'

import { closeModalUserlogin } from '@/utils/aside'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/context/AuthContext'

export default function CustomerLogin() {
  const { t } = useTranslation()
  const { loginWithGoogle, isLoading, user, isAuthenticated } = useAuth()
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  useEffect(() => {
    const pageOverlay = document.getElementById('pageOverlay')

    pageOverlay.addEventListener('click', closeModalUserlogin)

    return () => {
      pageOverlay.removeEventListener('click', closeModalUserlogin)
    }
  }, [])

  const handleGoogleLogin = async () => {
    try {
      setIsGoogleLoading(true)
      await loginWithGoogle()
      // Close modal after successful login
      closeModalUserlogin()
    } catch (error) {
      console.error('Google login failed:', error)
      // You can add toast notification here
      alert('<PERSON><PERSON><PERSON> nhập Google thất bại. Vui lòng thử lại.')
    } finally {
      setIsGoogleLoading(false)
    }
  }

  return (
    <div id='userAside' className='aside aside_right overflow-hidden customer-forms '>
      <div className='customer-forms__wrapper d-flex position-relative'>
        <div className='customer__login'>
          <div className='aside-header d-flex align-items-center'>
            <h3 className='text-uppercase fs-6 mb-0'>{t('auth.login', 'Đăng nhập')}</h3>
            <button
              onClick={() => closeModalUserlogin()}
              className='btn-close-lg js-close-aside ms-auto'
            />
          </div>
          <form onSubmit={e => e.preventDefault()} className='aside-content'>
            <div className='mb-3 text-center'>
              <span className='text-secondary'>
                <span style={{ color: 'gray' }}>
                  {t('auth.loginGoogle', 'Đăng nhập với Gmail')}
                </span>
              </span>
            </div>
            <div className='mb-3 text-center'>
              <button
                type='button'
                onClick={handleGoogleLogin}
                disabled={isGoogleLoading}
                className='btn btn-outline-secondary w-100 d-flex align-items-center justify-content-center gap-2'
                style={{
                  borderColor: '#dadce0',
                  color: '#3c4043',
                  backgroundColor: '#fff',
                  padding: '12px 16px',
                  fontSize: '14px',
                  fontWeight: '500',
                  borderRadius: '4px',
                  transition: 'all 0.2s ease',
                  opacity: isGoogleLoading ? 0.7 : 1,
                  cursor: isGoogleLoading ? 'not-allowed' : 'pointer'
                }}
                onMouseEnter={e => {
                  if (!isGoogleLoading) {
                    e.target.style.backgroundColor = '#f8f9fa'
                    e.target.style.borderColor = '#dadce0'
                    e.target.style.boxShadow =
                      '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)'
                  }
                }}
                onMouseLeave={e => {
                  if (!isGoogleLoading) {
                    e.target.style.backgroundColor = '#fff'
                    e.target.style.borderColor = '#dadce0'
                    e.target.style.boxShadow = 'none'
                  }
                }}
              >
                {isGoogleLoading ? (
                  <div
                    style={{
                      width: '18px',
                      height: '18px',
                      border: '2px solid #f3f3f3',
                      borderTop: '2px solid #3c4043',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}
                  />
                ) : (
                  <svg width='18' height='18' viewBox='0 0 24 24'>
                    <use href='#icon_google' />
                  </svg>
                )}
                <span>
                  {isGoogleLoading
                    ? t('auth.loggingIn', 'Đang đăng nhập...')
                    : t('auth.loginGoogle', 'Đăng nhập với Gmail')}
                </span>
              </button>
            </div>
            <div className='mb-3 text-center'>
              <button
                type='button'
                onClick={() => {
                  // TODO: Implement Apple Sign In authentication
                  console.log('Apple login clicked')
                }}
                className='btn btn-dark w-100 d-flex align-items-center justify-content-center gap-2'
                style={{
                  backgroundColor: '#000',
                  borderColor: '#000',
                  color: '#fff',
                  padding: '12px 16px',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={e => {
                  e.target.style.backgroundColor = '#333'
                  e.target.style.borderColor = '#333'
                }}
                onMouseLeave={e => {
                  e.target.style.backgroundColor = '#000'
                  e.target.style.borderColor = '#000'
                }}
              >
                <svg
                  width='21'
                  height='21'
                  viewBox='0 0 20 20'
                  fill='white'
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingBottom: '1px'
                  }}
                >
                  <use href='#icon_apple' />
                </svg>
                <span>{t('auth.loginApple', 'Đăng nhập với Apple')}</span>
              </button>
            </div>
            <div className='mb-3 text-center'>
              <span className='text-secondary'>
                <span style={{ color: 'gray' }}>
                  {t('auth.loginWithEmail', 'Hoặc đăng nhập với email và số điện thoại')}
                </span>
              </span>
            </div>
            <div className='form-floating mb-3'>
              <input
                name='email'
                type='email'
                className='form-control form-control_gray'
                placeholder='<EMAIL>'
              />
              <label>{t('auth.email', 'Email')} *</label>
            </div>
            <div className='pb-3' />
            <div className='form-label-fixed mb-3'>
              <label className='form-label'>{t('auth.password', 'Password')} *</label>
              <input
                name='password'
                className='form-control form-control_gray'
                type='password'
                placeholder='********'
              />
            </div>
            <div className='d-flex align-items-center mb-3 pb-2'>
              <div className='form-check mb-0'>
                <input
                  name='remember'
                  className='form-check-input form-check-input_fill'
                  type='checkbox'
                  defaultValue
                />
                <label className='form-check-label text-secondary'>
                  {t('auth.remember', 'Ghi nhớ mật khẩu')}
                </label>
              </div>
              <Link href='/reset_password' className='btn-text ms-auto'>
                {t('auth.lostPassword', 'Quên mật khẩu?')}
              </Link>
            </div>
            <button className='btn btn-primary w-100 text-uppercase' type='submit'>
              {t('auth.login', 'Đăng nhập')}
            </button>
            <div className='customer-option mt-4 text-center'>
              <span className='text-secondary'>{t('auth.notFound', 'Bạn chưa có tài khoản?')}</span>{' '}
              <Link href='/login_register#register-tab' className='btn-text js-show-register'>
                {t('auth.register', 'Đăng ký')}
              </Link>
            </div>
          </form>
        </div>
        <div className='customer__register'>
          <div className='aside-header d-flex align-items-center'>
            <h3 className='text-uppercase fs-6 mb-0'>{t('auth.createAccount', 'Tạo tài khoản')}</h3>
            <button className='btn-close-lg js-close-aside btn-close-aside ms-auto' />
          </div>
          <form onSubmit={e => e.preventDefault()} className='aside-content'>
            <div className='form-floating mb-4'>
              <input
                name='username'
                type='text'
                className='form-control form-control_gray'
                placeholder='Username'
              />
              <label>Username</label>
            </div>
            <div className='pb-1' />
            <div className='form-floating mb-4'>
              <input
                name='email'
                type='email'
                className='form-control form-control_gray'
                placeholder='<EMAIL>'
              />
              <label>Email address *</label>
            </div>
            <div className='pb-1' />
            <div className='form-label-fixed mb-4'>
              <label className='form-label'>Password *</label>
              <input
                name='password'
                className='form-control form-control_gray'
                type='password'
                placeholder='*******'
              />
            </div>
            <p className='text-secondary mb-4'>
              Your personal data will be used to support your experience throughout this website, to
              manage access to your account, and for other purposes described in our privacy policy.
            </p>
            <button className='btn btn-primary w-100 text-uppercase' type='submit'>
              {t('auth.register', 'Đăng ký')}
            </button>
            <div className='customer-option mt-4 text-center'>
              <span className='text-secondary'>Already have account?</span>
              <a href='#' className='btn-text js-show-login'>
                {t('auth.login', 'Đăng nhập')}
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
