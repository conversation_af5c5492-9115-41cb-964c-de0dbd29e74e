'use client'

const countries = ['Australia', 'Canada', 'United Kingdom', 'United States', 'Turkey']
import { useContextElement } from '@/context/Context'
import { useState } from 'react'
// import Link from 'next/link'
import { useFormatCurrency } from '@/hooks/format-currency.hooks'
import { useTranslation } from 'react-i18next'

export default function Checkout() {
  const { t } = useTranslation()

  const { formatCurrency } = useFormatCurrency()

  const { cartProducts, totalPrice } = useContextElement()
  const [selectedRegion, setSelectedRegion] = useState('')
  const [idDDActive, setIdDDActive] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const [order, setOrder] = useState({})

  const handleFormChange = (field, value) => {
    const fieldSetted = { ...order, [field]: value }

    setOrder(fieldSetted)
  }

  const submitOrder = async () => {
    try {
      const orderItems = cartProducts.map(product => ({
        productId: product.childSelected.id,
        productName: product.childSelected.productName,
        quantity: product.quantity,
        currentPrice: product.childSelected.sellingPrice,
        discount: 0
      }))
      const orderData = {
        customerName: order.lastName + order.firstName,
        customerPhone: order.customerPhone,
        customerAddress: order.customerAddress,
        customerCityId: '79',
        customerDictrictId: '765',
        customerWardId: '26953',
        receiverName: 'Nguyễn Văn B',
        receiverPhone: '0905123123',
        receiverAddress: 'Đường 19 Lã Xuân Oai, Phường Trường Thạnh, Quận 9, TP Hồ Chí Minh',
        receiverCityId: '79',
        receiverDictrictId: '769',
        receiverWardId: '26854',
        totalAmount: totalPrice,
        totalDiscount: 0,
        shipAmount: 20000,
        shipType: 'cod',
        status: 'new',
        orderItems: orderItems
      }
      console.log('orderData', orderData)
      await fetch('api/orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
        headers: {
          'Content-Type': 'application/json'
        }
      })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <form onSubmit={e => e.preventDefault()}>
      <div className='checkout-form'>
        <div className='billing-info__wrapper'>
          <h4>{t('orderInformation')}</h4>
          <div className='row'>
            <div className='col-md-6'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_first_name'
                  placeholder='Tên'
                  onChange={e => handleFormChange('firstName', e.target.value)}
                />
                <label htmlFor='checkout_first_name'>{t('firstName')}</label>
              </div>
            </div>
            <div className='col-md-6'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_last_name'
                  placeholder='Họ'
                  onChange={e => handleFormChange('lastName', e.target.value)}
                />
                <label htmlFor='checkout_last_name'>{t('lastName')}</label>
              </div>
            </div>
            {/* <div className='col-md-12'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_company_name'
                  placeholder='Company Name (optional)'
                />
                <label htmlFor='checkout_company_name'>Company Name (optional)</label>
              </div>
            </div> */}
            <div className='col-md-12'>
              <div className='search-field my-3'>
                <div
                  className={`form-label-fixed hover-container ${idDDActive ? 'js-content_visible' : ''
                    }`}
                >
                  <label htmlFor='search-dropdown' className='form-label'>
                    {t('shopCheckout.country')}
                  </label>
                  <div className='js-hover__open'>
                    <input
                      type='text'
                      className='form-control form-control-lg search-field__actor search-field__arrow-down'
                      id='search-dropdown'
                      name='search-keyword'
                      value={selectedRegion}
                      readOnly
                      placeholder={t('shopCheckout.chooseALocation')}
                      onClick={() => setIdDDActive(pre => !pre)}
                    />
                  </div>
                  <div className='filters-container js-hidden-content mt-2'>
                    <div className='search-field__input-wrapper'>
                      <input
                        type='text'
                        className='search-field__input form-control form-control-sm bg-lighter border-lighter'
                        placeholder='Search'
                        onChange={e => {
                          setSearchQuery(e.target.value)
                        }}
                      />
                    </div>
                    <ul className='search-suggestion list-unstyled'>
                      {countries
                        .filter(elm => elm.toLowerCase().includes(searchQuery.toLowerCase()))
                        .map((elm, i) => (
                          <li
                            onClick={() => {
                              setSelectedRegion(elm)
                              setIdDDActive(false)
                            }}
                            key={i}
                            className='search-suggestion__item js-search-select'
                          >
                            {elm}
                          </li>
                        ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className='col-md-12'>
              <div className='form-floating mt-3 mb-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_street_address'
                  placeholder='Street Address *'
                  onChange={e => handleFormChange('customerAddress', e.target.value)}
                />
                <label htmlFor='checkout_company_name'>{t('address')}</label>
              </div>
              {/* <div className='form-floating mt-3 mb-3'>
                <input type='text' className='form-control' id='checkout_street_address_2' />
              </div> */}
            </div>
            <div className='col-md-12'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_city'
                  placeholder='Town / City *'
                />
                <label htmlFor='checkout_city'>{t('shopCheckout.town')}*</label>
              </div>
            </div>
            {/* <div className='col-md-12'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_zipcode'
                  placeholder='Postcode / ZIP *'
                />
                <label htmlFor='checkout_zipcode'>Postcode / ZIP *</label>
              </div>
            </div> */}
            <div className='col-md-12'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_province'
                  placeholder='Province *'
                />
                <label htmlFor='checkout_province'>{t('shopCheckout.province')}*</label>
              </div>
            </div>
            <div className='col-md-12'>
              <div className='form-floating my-3'>
                <input
                  type='text'
                  className='form-control'
                  id='checkout_phone'
                  placeholder='Phone *'
                  onChange={e => handleFormChange('customerPhone', e.target.value)}
                />
                <label htmlFor='checkout_phone'>{t('shopCheckout.phoneNumber')}*</label>
              </div>
            </div>
            <div className='col-md-12'>
              <div className='form-floating my-3'>
                <input
                  type='email'
                  className='form-control'
                  id='checkout_email'
                  placeholder='Email *'
                  onChange={e => handleFormChange('customerEmail', e.target.value)}
                />
                <label htmlFor='checkout_email'>{t('email')}*</label>
              </div>
            </div>
            <div className='col-md-12'>
              {/* <div className='form-check mt-3'>
                <input
                  className='form-check-input form-check-input_fill'
                  type='checkbox'
                  defaultValue=''
                  id='create_account'
                />
                <label className='form-check-label' htmlFor='create_account'>
                  CREATE AN ACCOUNT?
                </label>
              </div> */}
              {/* <div className='form-check mb-3'>
                <input
                  className='form-check-input form-check-input_fill'
                  type='checkbox'
                  defaultValue=''
                  id='ship_different_address'
                />
                <label className='form-check-label' htmlFor='ship_different_address'>
                  SHIP TO A DIFFERENT ADDRESS?
                </label>
              </div> */}
            </div>
          </div>
          <div className='col-md-12'>
            <div className='mt-3'>
              <textarea
                className='form-control form-control_gray'
                placeholder={`${t('note')} (${t('optional')})`}
                cols='30'
                rows='8'
              ></textarea>
            </div>
          </div>
        </div>
        <div className='checkout__totals-wrapper'>
          <div className='sticky-content'>
            <div className='checkout__totals'>
              <h3>{t('shopCheckout.yourOrder')}</h3>
              <table className='checkout-cart-items'>
                <thead>
                  <tr>
                    <th>{t('product')}</th>
                    <th>{t('shopCheckout.total')}</th>
                  </tr>
                </thead>
                <tbody>
                  {cartProducts.map((elm, i) => (
                    <tr key={i}>
                      <td>
                        {elm.productName} x {elm.quantity}
                      </td>
                      <td>{formatCurrency(elm.sellingPrice * elm.quantity)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <table className='checkout-totals'>
                <tbody>
                  <tr>
                    <th>{t('shopCheckout.provisional')}</th>
                    <td>{formatCurrency(totalPrice)}</td>
                  </tr>
                  <tr>
                    <th>{t('shopCheckout.shippingFee')}</th>
                    <td>{t('shopCheckout.freeShipping')}</td>
                  </tr>
                  {/* <tr>
                    <th>VAT</th>
                    <td>${totalPrice && 19}</td>
                  </tr> */}
                  <tr>
                    <th>{t('shopCheckout.total')}</th>
                    <td>{totalPrice && formatCurrency(totalPrice)}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className='checkout__payment-methods'>
              {/* <div className='form-check'>
                <input
                  className='form-check-input form-check-input_fill'
                  type='radio'
                  name='checkout_payment_method'
                  id='checkout_payment_method_1'
                  defaultChecked
                />
                <label className='form-check-label' htmlFor='checkout_payment_method_1'>
                  Direct bank transfer
                  <span className='option-detail d-block'>
                    Make your payment directly into our bank account. Please use your Order ID as
                    the payment reference.Your order will not be shipped until the funds have
                    cleared in our account.
                  </span>
                </label>
              </div> */}
              {/* <div className='form-check'>
                <input
                  className='form-check-input form-check-input_fill'
                  type='radio'
                  name='checkout_payment_method'
                  id='checkout_payment_method_2'
                />
                <label className='form-check-label' htmlFor='checkout_payment_method_2'>
                  Check payments
                  <span className='option-detail d-block'>
                    Phasellus sed volutpat orci. Fusce eget lore mauris vehicula elementum gravida
                    nec dui. Aenean aliquam varius ipsum, non ultricies tellus sodales eu. Donec
                    dignissim viverra nunc, ut aliquet magna posuere eget.
                  </span>
                </label>
              </div> */}
              <div className='form-check'>
                <input
                  className='form-check-input form-check-input_fill'
                  type='radio'
                  name='checkout_payment_method'
                  id='checkout_payment_method_3'
                />
                <label className='form-check-label' htmlFor='checkout_payment_method_3'>
                  {t('shopCheckout.cash')}
                  <span className='option-detail d-block'>
                    {t('shopCheckout.cashDesc')}
                  </span>
                </label>
              </div>
              {/* <div className='form-check'>
                <input
                  className='form-check-input form-check-input_fill'
                  type='radio'
                  name='checkout_payment_method'
                  id='checkout_payment_method_4'
                />
                <label className='form-check-label' htmlFor='checkout_payment_method_4'>
                  Paypal
                  <span className='option-detail d-block'>
                    Phasellus sed volutpat orci. Fusce eget lore mauris vehicula elementum gravida
                    nec dui. Aenean aliquam varius ipsum, non ultricies tellus sodales eu. Donec
                    dignissim viverra nunc, ut aliquet magna posuere eget.
                  </span>
                </label>
              </div> */}
              {/* <div className='policy-text'>
                Your personal data will be used to process your order, support your experience
                throughout this website, and for other purposes described in our
                <Link href='/terms' target='_blank'>
                  privacy policy
                </Link>
                .
              </div> */}
            </div>
            <button className='btn btn-primary btn-checkout' onClick={submitOrder}>
              {t('order')}
            </button>
          </div>
        </div>
      </div>
    </form>
  )
}
