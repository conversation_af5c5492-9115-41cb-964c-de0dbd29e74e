'use client'

import { useAuth } from '@/context/AuthContext'
import { useTranslation } from 'react-i18next'
import Image from 'next/image'

export default function UserProfile() {
  const { user, logout, isAuthenticated } = useAuth()
  const { t } = useTranslation()

  if (!isAuthenticated || !user) {
    return null
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <div className="user-profile d-flex align-items-center gap-3">
      {user.image && (
        <Image
          src={user.image}
          alt={user.name || 'User'}
          width={32}
          height={32}
          className="rounded-circle"
        />
      )}
      <div className="user-info">
        <div className="user-name fw-bold">{user.name}</div>
        <div className="user-email text-muted small">{user.email}</div>
      </div>
      <button
        onClick={handleLogout}
        className="btn btn-outline-secondary btn-sm"
      >
        {t('auth.logout', 'Đăng xuất')}
      </button>
    </div>
  )
}
