'use client'
import React, { useEffect, useState } from 'react'
import ProductSlider1 from '../singleProduct/sliders/ProductSlider1'
import BreadCumb from '../singleProduct/BreadCumb'
import Star from '../common/Star'
import Colors from '../singleProduct/Colors'
import Size from '../singleProduct/Size'
import Description from '../singleProduct/Description'
import AdditionalInfo from '../singleProduct/AdditionalInfo'
import Reviews from '../singleProduct/Reviews'
import ShareComponent from '../common/ShareComponent'
import { useContextElement } from '@/context/Context'
import { useFormatCurrency } from '@/hooks/format-currency.hooks'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import SizeGuideCustom from '../modals/SizeGuideCustom'

export default function VariantProduct({ product }) {
  const router = useRouter()
  const [selected, setSelected] = useState({})
  const { t } = useTranslation()
  const { formatCurrency } = useFormatCurrency()

  const { cartProducts, setCartProducts } = useContextElement()
  const [quantity, setQuantity] = useState(1)
  const [selectedProduct, setSelectedProduct] = useState(null)

  const isIncludeCard = () => {
    const item = cartProducts.filter(elm => elm.childSelected.id == product.childSelected?.id)[0]
    return item
  }
  const setQuantityCartItem = (id, quantity) => {
    if (isIncludeCard()) {
      if (quantity >= 1) {
        const item = cartProducts.filter(elm => elm.childSelected.id == id)[0]
        const items = [...cartProducts]
        const itemIndex = items.indexOf(item)
        item.quantity = quantity
        items[itemIndex] = item
        setCartProducts(items)
      }
    } else {
      setQuantity(quantity - 1 ? quantity : 1)
    }
  }
  const addToCart = () => {
    if (!isIncludeCard()) {
      const item = product
      item.quantity = quantity
      setCartProducts(pre => [...pre, item])
    }
  }

  const handleSelected = (attributeId, propertyId) => {
    const newSeletected = {
      ...selected,
      [attributeId]: propertyId
    }
    setSelected(prev => ({
      ...prev,
      [attributeId]: propertyId
    }))

    const found = product.childs.find(child => {
      // child.attributes[attributeId].properties[propertyId]
      const length = Object.keys(newSeletected).length
      let count = 0
      Object.keys(newSeletected).map(key => {
        child.attributes[key]?.properties[newSeletected[key]] ? count++ : 0
      })

      if (count === length) {
        return child
      }
      return null
    })

    if (found) {
      router.push(`/san-pham/${found.slug}`)
    }
  }

  const handleSizeChosen = product => {
    setSelectedProduct(product)
    const modalElement = document.getElementById('sizeGuideCustom')
    if (modalElement) {
      const bootstrap = require('bootstrap')
      const modal = new bootstrap.Modal(modalElement)
      modal.show()
    }
  }

  useEffect(() => {
    if (product.childSelected) {
      const attributes = product.childSelected.attributes
      if (attributes) {
        Object.values(attributes).map(element => {
          setSelected(prev => ({
            ...prev,
            [element.id]: Object.keys(element.properties)[0]
          }))
        })
      }
    }
  }, [product.childSelected])

  return (
    <section className='product-single container'>
      <div className='row'>
        <div className='col-lg-7'>
          <ProductSlider1
            images={
              product.childSelected?.images.length
                ? [...product.childSelected?.images].concat(product.images ? product.images : [])
                : product.images
            }
          />
        </div>
        <div className='col-lg-5'>
          <div className='d-flex justify-content-between mb-4 pb-md-2'>
            <div className='breadcrumb mb-0 d-none d-md-block flex-grow-1'>
              <BreadCumb />
            </div>
            {/* <!-- /.breadcrumb --> */}

            {/* <!-- /.shop-acs --> */}
          </div>
          <h1 className='product-single__name'>{product.productName}</h1>
          <div className='product-single__rating'>
            <div className='reviews-group d-flex'>
              <Star stars={5} />
            </div>
            <span className='reviews-note text-lowercase text-secondary ms-1'>8k+ reviews</span>
          </div>
          <div className='product-single__price'>
            {product.listedPrice ? (
              <>
                <span className='money price price-old'>{formatCurrency(product.listedPrice)}</span>
                <span className='current-price'>
                  {formatCurrency(product.childSelected?.sellingPrice)}
                </span>
              </>
            ) : (
              <span className='current-price'>
                {formatCurrency(product.childSelected?.sellingPrice)}
              </span>
            )}
          </div>
          <div className='product-single__short-desc'>
            {/* <p>
                            Phasellus sed volutpat orci. Fusce eget lore mauris vehicula elementum
                            gravida nec dui. Aenean aliquam varius ipsum, non ultricies tellus
                            sodales eu. Donec dignissim viverra nunc, ut aliquet magna posuere eget.
                        </p> */}
          </div>
          <form onSubmit={e => e.preventDefault()}>
            <div className='product-single__swatches'>
              {(product.attributesProperties || []).map((item, index) => (
                <>
                  {!item.hasColor && (
                    <div key={item.id} className='product-swatch text-swatches'>
                      <label>{item.attributeName}</label>
                      <div className='swatch-list'>
                        <Size
                          properties={item.properties}
                          attributeId={item.id}
                          childSelected={product.childSelected}
                          handleSelected={handleSelected}
                        />
                      </div>
                      {index == 0 ? (
                        <>
                          <a
                            href='javascript:void(0)'
                            className='sizeguide-link'
                            onClick={() => handleSizeChosen(product)}
                          >
                            {t('sizeGuide', 'Chọn size')}
                          </a>
                          {<SizeGuideCustom product={selectedProduct} />}
                        </>
                      ) : (
                        ''
                      )}
                    </div>
                  )}
                  {item.hasColor && (
                    <div key={item.id} className='product-swatch color-swatches'>
                      <label>Color</label>
                      <div className='swatch-list'>
                        <Colors
                          properties={item.properties}
                          attributeId={item.id}
                          childSelected={product.childSelected}
                          handleSelected={handleSelected}
                        />
                      </div>
                      {index == 0 ? (
                        <>
                          <a
                            href='javascript:void(0)'
                            className='sizeguide-link'
                            onClick={() => handleSizeChosen(product)}
                          >
                            {t('sizeGuide', 'Chọn size')}
                          </a>
                          {<SizeGuideCustom product={selectedProduct} />}
                        </>
                      ) : (
                        ''
                      )}
                    </div>
                  )}
                </>
              ))}
            </div>
            <div className='product-single__addtocart'>
              <div className='qty-control position-relative'>
                <input
                  type='number'
                  name='quantity'
                  value={isIncludeCard() ? isIncludeCard().quantity : quantity}
                  min='1'
                  onChange={e => setQuantityCartItem(product.childSelected.id, e.target.value)}
                  className='qty-control__number text-center'
                />
                <div
                  onClick={() =>
                    setQuantityCartItem(
                      product.childSelected.id,
                      isIncludeCard()?.quantity - 1 || quantity - 1
                    )
                  }
                  className='qty-control__reduce'
                >
                  -
                </div>
                <div
                  onClick={() =>
                    setQuantityCartItem(
                      product.childSelected.id,
                      isIncludeCard()?.quantity + 1 || quantity + 1
                    )
                  }
                  className='qty-control__increase'
                >
                  +
                </div>
              </div>
              {/* <!-- .qty-control --> */}
              <button
                type='submit'
                className='btn btn-primary btn-addtocart js-open-aside'
                onClick={() => addToCart()}
              >
                {isIncludeCard()
                  ? t('alreadyAdded', 'Đã thêm vào giỏ')
                  : t('addToCart', 'Thêm vào giỏ')}
              </button>
            </div>
          </form>
          <div className='product-single__addtolinks'>
            <a href='#' className='menu-link menu-link_us-s add-to-wishlist'>
              <svg
                width='16'
                height='16'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <use href='#icon_heart' />
              </svg>
              <span>{t('addToWishlist', 'Thêm vào ưu thích')}</span>
            </a>
            <ShareComponent title={product.title} />
          </div>
          <div className='product-single__meta-info'>
            <div className='meta-item'>
              <label>SKU: </label>
              <span>{product?.childSelected?.productCode || 'N/A'}</span>
            </div>
            <div className='meta-item'>
              <label>{t('categories', 'Danh mục')}:</label>
              <span>Casual & Urban Wear, Jackets, Men</span>
            </div>
            <div className='meta-item'>
              <label>{t('tags', 'Thẻ')}:</label>
              <span>biker, black, bomber, leather</span>
            </div>
          </div>
        </div>
      </div>
      <div className='product-single__details-tab'>
        <ul className='nav nav-tabs' id='myTab1' role='tablist'>
          <li className='nav-item' role='presentation'>
            <a
              className='nav-link nav-link_underscore active'
              id='tab-description-tab'
              data-bs-toggle='tab'
              href='#tab-description'
              role='tab'
              aria-controls='tab-description'
              aria-selected='true'
            >
              {t('description', 'Mô tả')}
            </a>
          </li>
          {/* <li className='nav-item' role='presentation'>
            <a
              className='nav-link nav-link_underscore'
              id='tab-additional-info-tab'
              data-bs-toggle='tab'
              href='#tab-additional-info'
              role='tab'
              aria-controls='tab-additional-info'
              aria-selected='false'
            >
              {t('additionalInformation', 'Thông tin thêm')}
            </a>
          </li> */}
          {/* <li className='nav-item' role='presentation'>
            <a
              className='nav-link nav-link_underscore'
              id='tab-reviews-tab'
              data-bs-toggle='tab'
              href='#tab-reviews'
              role='tab'
              aria-controls='tab-reviews'
              aria-selected='false'
            >
              {t('reviews', 'Đánh giá')} (2)
            </a>
          </li> */}
        </ul>
        <div className='tab-content'>
          <div
            className='tab-pane fade show active'
            id='tab-description'
            role='tabpanel'
            aria-labelledby='tab-description-tab'
          >
            <Description description={product.childSelected?.description || product.description} />
          </div>
          <div
            className='tab-pane fade'
            id='tab-additional-info'
            role='tabpanel'
            aria-labelledby='tab-additional-info-tab'
          >
            <AdditionalInfo />
          </div>
          <div
            className='tab-pane fade'
            id='tab-reviews'
            role='tabpanel'
            aria-labelledby='tab-reviews-tab'
          >
            <Reviews />
          </div>
        </div>
      </div>
    </section>
  )
}
