'use client'

import React from 'react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function Products1() {
  const { t } = useTranslation()
  const [banner, setBanner] = useState({
    heading: '',
    subHeading: '',
    bannerImages: {}
  })

  const getBanner = async () => {
    try {
      const result = await fetch(`api/banners/home?type=sectionUnderBanner`)
      const response = await result.json()
      const keyByBannerPosition = response.bannerImages.reduce((result, item) => {
        result[item.bannerPosition] = item
        return result
      }, {})
      setBanner({ ...response, bannerImages: keyByBannerPosition })
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getBanner()
  }, [])
  return (
    <section
      className='collections-grid collections-grid_masonry'
      id='section-collections-grid_masonry'
    >
      <div className='container h-md-100'>
        <div className='row h-md-100'>
          <div className='col-lg-6 h-md-100'>
            <div className='collection-grid__item position-relative h-md-100'>
              <div
                className='background-img'
                style={{
                  backgroundImage: `url(${banner.bannerImages['banner.left']?.image})`
                }}
              ></div>
              <div className='content_abs content_bottom content_left content_bottom-md content_left-md'>
                <p className='text-uppercase mb-1'>{banner.bannerImages['banner.left']?.heading}</p>
                <h3 className='text-uppercase'>
                  <strong>{banner.bannerImages['banner.left']?.subHeading}</strong>{' '}
                  {banner.bannerImages['banner.left']?.subHeading2}
                </h3>
                <Link
                  href={banner.bannerImages['banner.left']?.directLink || ''}
                  className='btn-link default-underline text-uppercase fw-medium'
                >
                  {t('seeMore', 'Xem Thêm')}
                </Link>
              </div>
              {/* <!-- /.content_abs content_bottom content_left content_bottom-md content_left-md --> */}
            </div>
          </div>
          {/* <!-- /.col-md-6 --> */}

          <div className='col-lg-6 d-flex flex-column'>
            <div className='collection-grid__item position-relative flex-grow-1 mb-lg-4'>
              <div
                className='background-img'
                style={{
                  backgroundImage: `url(${banner.bannerImages['banner.right.top']?.image})`
                }}
              ></div>
              <div className='content_abs content_bottom content_left content_bottom-md content_left-md'>
                <p className='text-uppercase mb-1'>
                  {banner.bannerImages['banner.right.top']?.heading}
                </p>
                <h3 className='text-uppercase'>
                  <strong>{banner.bannerImages['banner.right.top']?.subHeading}</strong>{' '}
                  {banner.bannerImages['banner.right.top']?.subHeading2}
                </h3>
                <Link
                  href='/shop-1'
                  className='btn-link default-underline text-uppercase fw-medium'
                >
                  {t('seeMore', 'Xem Thêm')}
                </Link>
              </div>
              {/* <!-- /.content_abs content_bottom content_left content_bottom-md content_left-md --> */}
            </div>
            <div className='position-relative flex-grow-1 mt-lg-1'>
              <div className='row h-md-100'>
                <div className='col-md-6 h-md-100'>
                  <div className='collection-grid__item h-md-100 position-relative'>
                    <div
                      className='background-img'
                      style={{
                        backgroundImage: `url(${banner.bannerImages['banner.right.bottom.left']?.image})`
                      }}
                    ></div>
                    <div className='content_abs content_bottom content_left content_bottom-md content_left-md'>
                      <p className='text-uppercase mb-1'>
                        {banner.bannerImages['banner.right.bottom.left']?.heading}
                      </p>
                      <h3 className='text-uppercase'>
                        <strong>
                          {banner.bannerImages['banner.right.bottom.left']?.subHeading}
                        </strong>{' '}
                        {banner.bannerImages['banner.right.bottom.left']?.subHeading2}
                      </h3>
                      <Link
                        href='/shop-1'
                        className='btn-link default-underline text-uppercase fw-medium'
                      >
                        {t('seeMore', 'Xem Thêm')}
                      </Link>
                    </div>
                    {/* <!-- /.content_abs content_bottom content_left content_bottom-md content_left-md --> */}
                  </div>
                  {/* <!-- /.collection-grid__item --> */}
                </div>

                <div className='col-md-6 h-md-100'>
                  <div className='collection-grid__item h-md-100 position-relative'>
                    <div
                      className='background-img'
                      style={{
                        backgroundImage: `url(${banner.bannerImages['banner.right.bottom.right']?.image})`
                      }}
                    ></div>
                    <div className='content_abs content_bottom content_left content_bottom-md content_left-md'>
                      <p className='text-uppercase mb-1'>
                        {banner.bannerImages['banner.right.bottom.right']?.heading}
                      </p>
                      <h3 className='text-uppercase'>
                        <strong>
                          {banner.bannerImages['banner.right.bottom.right']?.subHeading}
                        </strong>{' '}
                        {banner.bannerImages['banner.right.bottom.right']?.subHeading2}
                      </h3>
                      <Link
                        href='/shop-1'
                        className='btn-link default-underline text-uppercase fw-medium'
                      >
                        {t('seeMore', 'Xem Thêm')}
                      </Link>
                    </div>
                    {/* <!-- /.content_abs content_bottom content_left content_bottom-md content_left-md --> */}
                  </div>
                  {/* <!-- /.collection-grid__item --> */}
                </div>
              </div>
            </div>
          </div>
          {/* <!-- /.col-md-6 --> */}
        </div>
        {/* <!-- /.row --> */}
      </div>
      {/* <!-- /.container --> */}
    </section>
  )
}
