'use client'

import React from 'react'
import Image from 'next/image'
import { useEffect, useState } from 'react'

export default function Instagram() {
  const [socialMedias, setSocialMedias] = useState([])

  const getSocialMedia = async () => {
    try {
      const result = await fetch(`api/social-medias`)
      const response = await result.json()
      setSocialMedias(response)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getSocialMedia()
  }, [])

  return (
    <section className='instagram container'>
      <h2 className='section-title text-uppercase text-center mb-4 pb-xl-2 mb-xl-4'>@INSTAGRAM</h2>

      <div className='row row-cols-3 row-cols-md-4 row-cols-xl-6'>
        {(socialMedias || []).map((elm, i) => (
          <div key={i} className='instagram__tile'>
            <a
              href={elm.permaLink}
              target='_blank'
              className='position-relative overflow-hidden d-block effect overlay-plus'
            >
              <div className='image-wrapper'>
                {elm.mediaType === 'VIDEO' ? (
                  <Image
                    loading='lazy'
                    className='instagram__img'
                    src={elm.thumbnailUrl}
                    width='230'
                    height='230'
                    alt='Instagram image 1'
                  />
                ) : (
                  <Image
                    loading='lazy'
                    className='instagram__img'
                    src={elm.mediaUrl}
                    width='230'
                    height='230'
                    alt='Instagram image 2'
                  />
                )}
                <div className='instagram__overlay'>
                  <svg
                    className='icon_instagram'
                    viewBox='0 0 9 9'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <use href='#icon_instagram' />
                  </svg>
                </div>
              </div>
            </a>
          </div>
        ))}
      </div>
    </section>
  )
}
