'use client'

import React from 'react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function Products4() {
  const { t } = useTranslation()
  const [banner, setBanner] = useState({
    heading: '',
    subHeading: '',
    bannerImages: []
  })

  const getBanner = async () => {
    try {
      const result = await fetch(`api/banners/home?type=sectionMiddle`)
      const response = await result.json()
      const keyByBannerPosition = response.bannerImages.reduce((result, item) => {
        result[item.bannerPosition] = item
        return result
      }, {})
      setBanner({ ...response, bannerImages: keyByBannerPosition })
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getBanner()
  }, [])

  return (
    <section className='grid-banner container mb-3'>
      <div className='row'>
        <div className='col-md-6'>
          <div className='grid-banner__item grid-banner__item_rect position-relative mb-3'>
            <div
              className='background-img'
              style={{ backgroundImage: `url(${banner.bannerImages['banner.left']?.image})` }}
            ></div>
            <div className='content_abs content_bottom content_left content_bottom-lg content_left-lg'>
              <h6 className='text-uppercase text-white fw-medium mb-3'>{banner.heading}</h6>
              <h3 className='text-white mb-3'>{banner.subHeading}</h3>
              <Link href='/shop-1' className='btn-link default-underline text-uppercase fw-medium'>
                {t('seeMore', 'Xem Thêm')}
              </Link>
            </div>
            {/* <!-- /.content_abs content_bottom content_left content_bottom-md content_left-md --> */}
          </div>
        </div>
        {/* <!-- /.col-md-6 --> */}

        <div className='col-md-6'>
          <div className='grid-banner__item grid-banner__item_rect position-relative mb-3'>
            <div
              className='background-img'
              style={{ backgroundImage: `url(${banner.bannerImages['banner.right']?.image})` }}
            ></div>
            <div className='content_abs content_bottom content_left content_bottom-lg content_left-lg'>
              <h6 className='text-uppercase fw-medium mb-3'>{banner.heading}</h6>
              <h3 className='mb-3'>{banner.subHeading}</h3>
              <Link href='/shop-1' className='btn-link default-underline text-uppercase fw-medium'>
                {t('seeMore', 'Xem Thêm')}
              </Link>
            </div>
            {/* <!-- /.content_abs content_bottom content_left content_bottom-md content_left-md --> */}
          </div>
        </div>
        {/* <!-- /.col-md-6 --> */}
      </div>
      {/* <!-- /.row --> */}
    </section>
  )
}
