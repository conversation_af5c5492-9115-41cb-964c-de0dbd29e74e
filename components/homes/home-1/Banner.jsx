'use client'

import { Swiper, SwiperSlide } from 'swiper/react'
import { EffectFade, Pagination } from 'swiper/modules'
import Image from 'next/image'
import { socialLink } from '@/data/footer'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function Banner() {
  const { t } = useTranslation()

  const swiperOptions = {
    autoplay: {
      delay: 5000
    },
    slidesPerView: 1,
    modules: [Pagination, EffectFade],
    effect: 'fade',
    loop: true,
    pagination: {
      el: '.slideshow-pagination',
      type: 'bullets',
      clickable: true
    }
  }

  const [banner, setBanner] = useState({
    heading: '',
    subHeading: '',
    bannerImages: [
      {
        image: '/assets/images/slideshow-pattern.png'
      }
    ]
  })

  const getBanner = async () => {
    try {
      const result = await fetch(`api/banners/home?type=sectionBanner`)
      const response = await result.json()
      setBanner(response)
    } catch (error) {
      console.log(error)
    }
  }
  const setSocialMedia = setting => {
    if (setting.facebook) {
      socialLink['facebook'].href = setting.facebook
    }
    if (setting.instagram) {
      socialLink['instagram'].href = setting.instagram
    }
    if (setting.youtube) {
      socialLink['youtube'].href = setting.youtube
    }
    if (setting.tiktok) {
      socialLink['tiktok'].href = setting.tiktok
    }
  }
  const fetchSetting = async () => {
    try {
      const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/settings`)
      const setting = await result.json()
      setSocialMedia(setting)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getBanner()
    fetchSetting()
  }, [])

  return (
    <Swiper
      spaceBetween={50}
      slidesPerView={1}
      {...swiperOptions} // Default for mobile devices
      breakpoints={{
        640: {
          slidesPerView: 1 // For small screens and above
        },
        768: {
          slidesPerView: 1 // For tablets and above
        },
        1024: {
          slidesPerView: 1 // For larger screens
        }
      }}
    >
      {banner.bannerImages.map((elm, i) => (
        <SwiperSlide
          key={i}
          className='swiper-slide full-width_border border-1'
          style={{ borderColor: '#f5e6e0' }}
        >
          <div>
            <Image
              src={elm.image}
              width={1920}
              height={855}
              quality={100}
              priority={i === 0}
              alt='Banner'
              className='slideshow-bg__img '
            />
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
  )
}
