'use client'
import Link from 'next/link'

import { Swiper, SwiperSlide } from 'swiper/react'
import { EffectFade, Pagination } from 'swiper/modules'
import Image from 'next/image'
import { socialLink } from '@/data/footer'
import classNames from 'classnames'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
export default function Hero() {
  const { t } = useTranslation()
  const swiperOptions = {
    autoplay: {
      delay: 5000
    },
    slidesPerView: 1,
    modules: [Pagination, EffectFade],
    effect: 'fade',
    loop: true,
    pagination: {
      el: '.slideshow-pagination',
      type: 'bullets',
      clickable: true
    }
  }

  const [banner, setBanner] = useState({
    heading: '',
    subHeading: '',
    bannerImages: [
      {
        image: '/assets/images/slideshow-pattern.png'
      }
    ]
  })

  const getBanner = async () => {
    try {
      const result = await fetch(`api/banners/home?type=sectionBanner`)
      const response = await result.json()
      setBanner(response)
    } catch (error) {
      console.log(error)
    }
  }
  const setSocialMedia = setting => {
    if (setting.facebook) {
      socialLink['facebook'].href = setting.facebook
    }
    if (setting.instagram) {
      socialLink['instagram'].href = setting.instagram
    }
    if (setting.youtube) {
      socialLink['youtube'].href = setting.youtube
    }
    if (setting.tiktok) {
      socialLink['tiktok'].href = setting.tiktok
    }
  }
  const fetchSetting = async () => {
    try {
      const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/settings`)
      const setting = await result.json()
      setSocialMedia(setting)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getBanner()
    fetchSetting()
  }, [])

  return (
    <Swiper
      style={{ maxWidth: '100%', overflow: 'hidden' }}
      className={classNames(
        'swiper-container',
        'js-swiper-slider',
        'slideshow',
        'full-width_padding',
        'swiper-container-fade',
        'swiper-container-initialized',
        'swiper-container-horizontal',
        'swiper-container-pointer-events'
      )}
      {...swiperOptions}
    >
      {banner.bannerImages.map((elm, i) => (
        <SwiperSlide
          key={i}
          className='swiper-slide full-width_border border-1'
          style={{ borderColor: '#f5e6e0' }}
        >
          <div className='ratio ratio-16x9'>
            <Image
              // loading='lazy'
              src={elm.image}
              fill
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw'
              priority={i === 0}
              alt='Pattern'
              className='slideshow-bg__img object-fit-cover'
            />
          </div>
        </SwiperSlide>
      ))}
      <div className='container'>
        <div className='slideshow-pagination d-flex align-items-center position-absolute bottom-0'></div>
        {/* <!-- /.products-pagination --> */}
      </div>
      {/* <!-- /.container --> */}
      <div className='slideshow-social-follow d-none d-xxl-block position-absolute top-50 start-0 translate-middle-y text-center'>
        <ul className='social-links list-unstyled mb-0 text-secondary'>
          {Object.values(socialLink).map((link, index) => (
            <li key={index}>
              <a href={link.href} className='footer__social-link d-block'>
                <svg
                  className={link.className}
                  width={link.width}
                  height={link.height}
                  viewBox={link.viewBox}
                  xmlns='http://www.w3.org/2000/svg'
                >
                  {typeof link.icon === 'string' ? <use href={link.icon} /> : link.icon}
                </svg>
              </a>
            </li>
          ))}
        </ul>
        {/* <!-- /.social-links list-unstyled mb-0 text-secondary --> */}
        <span className='slideshow-social-follow__title d-block mt-5 text-uppercase fw-medium text-secondary'>
          {t('followUs', 'Theo dõi')}
        </span>
      </div>
      {/* <!-- /.slideshow-social-follow --> */}
      <a
        href='#section-collections-grid_masonry'
        className='slideshow-scroll d-none d-xxl-block position-absolute end-0 bottom-0 text_dash text-uppercase fw-medium'
      >
        {t('scroll', 'Scroll')}
      </a>
    </Swiper>
  )
}
