'use client'

import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function About() {
  const { t } = useTranslation()
  const [blog, setBlog] = useState([])

  const getBlog = async type => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_APP_API_URL}/blogs?page=1&limit=10&order=desc&type=${type}`
      )
      const response = await result.json()
      setBlog(response[0])
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getBlog('aboutMe')
  }, [])

  return (
    <section className='about-us container'>
      <div className='mw-930 text-center'>
        <h2 className='page-title'>{t('aboutUs')}</h2>
      </div>
      <div
        className='about-us__content pb-5 mb-5'
        dangerouslySetInnerHTML={{ __html: blog.description }}
      ></div>
    </section>
  )
}
