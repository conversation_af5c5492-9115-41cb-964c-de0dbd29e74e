"use client";
import React from "react";
import { useTranslation } from 'react-i18next'


export default function EditAccount() {
  const { t } = useTranslation()

  return (
    <div className="col-lg-9">
      <div className="page-content my-account__edit">
        <div className="my-account__edit-form">
          <form
            onSubmit={(e) => e.preventDefault()}
            className="needs-validation"
          >
            <div className="row">
              <div className="col-md-6">
                <div className="form-floating my-3">
                  <input
                    type="text"
                    className="form-control"
                    id="account_first_name"
                    placeholder="First Name"
                    required
                  />
                  <label htmlFor="account_first_name">{t('firstName')}</label>
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-floating my-3">
                  <input
                    type="text"
                    className="form-control"
                    id="account_last_name"
                    placeholder="Last Name"
                    required
                  />
                  <label htmlFor="account_last_name">{t('lastName')}</label>
                </div>
              </div>
              <div className="col-md-12">
                <div className="form-floating my-3">
                  <input
                    type="text"
                    className="form-control"
                    id="account_display_name"
                    placeholder="Display Name"
                    required
                  />
                  <label htmlFor="account_display_name">{t('displayName')}</label>
                </div>
              </div>
              <div className="col-md-12">
                <div className="form-floating my-3">
                  <input
                    type="email"
                    className="form-control"
                    id="account_email"
                    placeholder="Email Address"
                    required
                  />
                  <label htmlFor="account_email">{t('emailAddress')}</label>
                </div>
              </div>
              <div className="col-md-12">
                <div className="my-3">
                  <h5 className="text-uppercase mb-0">{t('passwordChange')}</h5>
                </div>
              </div>
              <div className="col-md-12">
                <div className="form-floating my-3">
                  <input
                    type="password"
                    className="form-control"
                    id="account_current_password"
                    placeholder="Current password"
                    required
                  />
                  <label htmlFor="account_current_password">
                    {t('currentPassword')}
                  </label>
                </div>
              </div>
              <div className="col-md-12">
                <div className="form-floating my-3">
                  <input
                    type="password"
                    className="form-control"
                    id="account_new_password"
                    placeholder="New password"
                    required
                  />
                  <label htmlFor="account_new_password">{t('newPassword')}</label>
                </div>
              </div>
              <div className="col-md-12">
                <div className="form-floating my-3">
                  <input
                    type="password"
                    className="form-control"
                    data-cf-pwd="#account_new_password"
                    id="account_confirm_password"
                    placeholder="Confirm new password"
                    required
                  />
                  <label htmlFor="account_confirm_password">
                    {t('confirmNewPassword')}
                  </label>
                  <div className="invalid-feedback">
                    {t('passwordDidNotMatch')}

                  </div>
                </div>
              </div>
              <div className="col-md-12">
                <div className="my-3">
                  <button className="btn btn-primary">
                    {t('saveChanges')}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
