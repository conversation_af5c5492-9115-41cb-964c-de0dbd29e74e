'use client'

import React, { useEffect } from 'react'
import tippy from 'tippy.js'
const switches = [
  {
    id: 'swatch-11',
    label: 'Black',
    color: '#222',
    defaultChecked: false
  },
  {
    id: 'swatch-12',
    label: 'Red',
    color: '#c93a3e',
    defaultChecked: true
  },
  {
    id: 'swatch-13',
    label: 'Grey',
    color: '#e4e4e4',
    defaultChecked: false
  }
]
export default function Colors({ properties, attributeId, childSelected, handleSelected }) {
  useEffect(() => {
    tippy('[data-tippy-content]')
  }, [])

  const handleClick = (attributeId, propertyId) => {
    handleSelected(attributeId, propertyId)
  }
  // const colorsArray = colors?.length ? colors : [...switches]
  return (
    <>
      {(properties || []).map(swatch => (
        <React.Fragment key={swatch.id}>
          <input
            type='radio'
            name='color'
            id={swatch.id}
            defaultChecked={
              childSelected.attributes
                ? childSelected.attributes[attributeId].properties[swatch.id]
                  ? true
                  : false
                : false
            }
          />
          <label
            className='swatch swatch-color js-swatch'
            htmlFor={swatch.id}
            aria-label={swatch.propertyName}
            data-bs-toggle='tooltip'
            data-bs-placement='top'
            data-tippy-content={swatch.propertyName}
            style={{ color: swatch.color }}
            onClick={() => handleClick(attributeId, swatch.id)}
          ></label>
        </React.Fragment>
      ))}
    </>
  )
}
