'use client'

import { useContextElement } from '@/context/Context'
import { products51 } from '@/data/products/fashion'
import Link from 'next/link'
import { Navigation, Pagination } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useFormatCurrency } from '@/hooks/format-currency.hooks'

export default function RelatedSlider() {
  const { t } = useTranslation()
  const { formatCurrency } = useFormatCurrency()

  const { toggleWishlist, isAddedtoWishlist } = useContextElement()
  const { setQuickViewItem } = useContextElement()
  const { addProductToCart, isAddedToCartProducts } = useContextElement()
  const swiperOptions = {
    autoplay: false,
    slidesPerView: 4,
    slidesPerGroup: 4,
    effect: 'none',
    loop: true,
    modules: [Pagination, Navigation],
    pagination: {
      el: '#related_products .products-pagination',
      type: 'bullets',
      clickable: true
    },
    navigation: {
      nextEl: '.ssn11',
      prevEl: '.ssp11'
    },
    breakpoints: {
      320: {
        slidesPerView: 2,
        slidesPerGroup: 2,
        spaceBetween: 14
      },
      768: {
        slidesPerView: 3,
        slidesPerGroup: 3,
        spaceBetween: 24
      },
      992: {
        slidesPerView: 4,
        slidesPerGroup: 4,
        spaceBetween: 30
      }
    }
  }

  const [products, setProducts] = useState([])

  const fetchProductRelative = async () => {
    try {
      const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/products/relative`)
      const products = await result.json()
      setProducts(products)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    fetchProductRelative()
  }, [])

  return (
    <section className='products-carousel container'>
      <h2 className='h3 text-uppercase mb-4 pb-xl-2 mb-xl-4'>
        <strong>Sản phẩm</strong> liên quan
      </h2>

      <div id='related_products' className='position-relative'>
        <Swiper {...swiperOptions} className='swiper-container js-swiper-slider' data-settings=''>
          {products.map((elm, i) => (
            <SwiperSlide key={i} className='swiper-slide product-card'>
              <div className='pc__img-wrapper'>
                <Link href={`/san-pham/${elm.childSelected.slug}`}>
                  <Image
                    loading='lazy'
                    src={elm.thumbNailOne}
                    width='330'
                    height='400'
                    alt='Cropped Faux leather Jacket'
                    className='pc__img'
                  />
                  <Image
                    loading='lazy'
                    src={elm.thumbNailOne}
                    width='330'
                    height='400'
                    alt='Cropped Faux leather Jacket'
                    className='pc__img pc__img-second'
                  />
                </Link>
                <button
                  className='pc__atc btn anim_appear-bottom btn position-absolute border-0 text-uppercase fw-medium js-add-cart js-open-aside'
                  onClick={() => addProductToCart(elm.childSelected.id)}
                  title={
                    isAddedToCartProducts(elm.childSelected.id)
                      ? t('alreadyAdded', 'Đã thêm vào giỏ')
                      : t('addToCart', 'Thêm vào giỏ')
                  }
                >
                  {isAddedToCartProducts(elm.childSelected.id)
                    ? t('alreadyAdded', 'Đã thêm vào giỏ')
                    : t('addToCart', 'Thêm vào giỏ')}
                </button>
              </div>

              <div className='pc__info position-relative'>
                <p className='pc__category'>{elm.categoryName}</p>
                <h6 className='pc__title'>
                  <Link href={`/san-pham/${elm.childSelected.slug || elm.childSelected.id}`}>
                    {elm.productName}
                  </Link>
                </h6>
                <div className='product-card__price d-flex'>
                  {elm.listedPrice ? (
                    <>
                      <span className='money price price-old'>
                        {formatCurrency(elm.listedPrice)}
                      </span>
                      <span className='money price price-sale'>
                        {formatCurrency(elm.sellingPrice)}
                      </span>
                    </>
                  ) : (
                    <span className='money price'>{formatCurrency(elm.sellingPrice)}</span>
                  )}
                </div>

                <button
                  className={`pc__btn-wl position-absolute top-0 end-0 bg-transparent border-0 js-add-wishlist ${
                    isAddedtoWishlist(elm.childSelected.id) ? 'active' : ''
                  }`}
                  title='Add To Wishlist'
                  onClick={() => toggleWishlist(elm.childSelected.id)}
                >
                  <svg
                    width='16'
                    height='16'
                    viewBox='0 0 20 20'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <use href='#icon_heart' />
                  </svg>
                </button>
              </div>
            </SwiperSlide>
          ))}

          {/* <!-- /.swiper-wrapper --> */}
        </Swiper>
        {/* <!-- /.swiper-container js-swiper-slider --> */}

        <div className='cursor-pointer products-carousel__prev ssp11 position-absolute top-50 d-flex align-items-center justify-content-center'>
          <svg width='25' height='25' viewBox='0 0 25 25' xmlns='http://www.w3.org/2000/svg'>
            <use href='#icon_prev_md' />
          </svg>
        </div>
        {/* <!-- /.products-carousel__prev --> */}
        <div className='cursor-pointer products-carousel__next ssn11 position-absolute top-50 d-flex align-items-center justify-content-center'>
          <svg width='25' height='25' viewBox='0 0 25 25' xmlns='http://www.w3.org/2000/svg'>
            <use href='#icon_next_md' />
          </svg>
        </div>
        {/* <!-- /.products-carousel__next --> */}

        <div className='products-pagination mt-4 mb-5 d-flex align-items-center justify-content-center'></div>
        {/* <!-- /.products-pagination --> */}
      </div>
      {/* <!-- /.position-relative --> */}
    </section>
  )
}
