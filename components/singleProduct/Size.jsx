'use client'

import { useEffect } from 'react'
import tippy from 'tippy.js'

export default function Size({ properties, attributeId, childSelected, handleSelected }) {
  useEffect(() => {
    tippy('[data-tippy-content]')
  }, [])
  const handleClick = (attributeId, propertyId) => {
    handleSelected(attributeId, propertyId)
  }
  return (
    <>
      {(properties || []).map(item => (
        <>
          <input
            type='radio'
            name={attributeId}
            id={item.id}
            defaultChecked={
              childSelected.attributes
                ? childSelected.attributes[attributeId].properties[item.id]
                  ? true
                  : false
                : false
            }
            onClick={() => handleClick(attributeId, item.id)}
          />
          <label
            className='swatch js-swatch'
            htmlFor={item.id}
            aria-label={item.propertyName}
            data-bs-toggle='tooltip'
            data-bs-placement='top'
            data-tippy-content={item.propertyName}
          >
            {item.propertyName}
          </label>
        </>
      ))}
    </>
  )
}
