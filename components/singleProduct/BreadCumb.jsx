import React from "react";
import { useTranslation } from "react-i18next";

export default function BreadCumb() {
    const { t } = useTranslation();
    return (
        <>
            <a href="/" className="menu-link menu-link_us-s text-uppercase fw-medium">
                {t("home.page", "Trang chủ")}
            </a>
            <span className="breadcrumb-separator menu-link fw-medium ps-1 pe-1">/</span>
            <a href="" className="menu-link menu-link_us-s text-uppercase fw-medium">
                {t("shop.page", "Sản phẩm")}
            </a>
        </>
    );
}
