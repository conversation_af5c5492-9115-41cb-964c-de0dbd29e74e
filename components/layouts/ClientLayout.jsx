'use client'
import { useEffect } from 'react'
import Context from '@/context/Context'
import MobileHeader from '@/components/headers/MobileHeader'
import MobileFooter1 from '@/components/footers/MobileFooter1'
import Svgs from '@/components/common/Svgs'
import LoginFormPopup from '@/components/common/LoginFormPopup'
import QuickView from '@/components/modals/QuickView'
import NewsLetter from '@/components/modals/NewsLetter'
import CookieContainer from '@/components/common/CookieContainer'
import SizeGuide from '@/components/modals/SizeGuide'
import Delivery from '@/components/modals/Delivery'
import CartDrawer from '@/components/shopCartandCheckout/CartDrawer'
import SiteMap from '@/components/modals/SiteMap'
import ShopFilter from '../asides/ShopFilter'
import ProductDescription from '@/components/asides/ProductDescription'
import ProductAdditionalInformation from '@/components/asides/ProductAdditionalInformation'
import ProductReviews from '@/components/asides/ProductReviews'
import ScrollTop from '@/components/common/ScrollTop'
import CustomerLogin from '@/components/asides/CustomerLogin'

export default function ClientLayout({ children }) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('bootstrap/dist/js/bootstrap.esm')
    }
  }, [])

  return (
    <Context>
      <Svgs />
      <MobileHeader />
      {children}
      <MobileFooter1 />
      {/* Modals and asides */}
      <LoginFormPopup />
      <QuickView />
      <NewsLetter />
      <CookieContainer />
      <SizeGuide />
      <Delivery />
      <CartDrawer />
      <SiteMap />
      <CustomerLogin />
      <ShopFilter />
      <ProductDescription />
      <ProductAdditionalInformation />
      <ProductReviews />
      <div className='page-overlay' id='pageOverlay'></div>
      <ScrollTop />
    </Context>
  )
}
