'use client'
import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import {
  currencyOptions,
  footerLinks1,
  footerLinks2,
  footerLinks3,
  languageOptions,
  socialLink
} from '@/data/footer'
import { useTranslation } from 'react-i18next'
import { useRouter, usePathname } from 'next/navigation'
import i18nConfig from '@/i18n.config'

export default function Footer1() {
  const { i18n, t } = useTranslation()
  const router = useRouter()
  const currentPathname = usePathname()
  const currentLocale = i18n.language
  const [logo, setLogo] = useState('/assets/images/logo.png')

  const [companySetting, setCompanySetting] = useState({})
  const setSocialMedia = setting => {
    if (setting.facebook) {
      socialLink['facebook'].href = setting.facebook
    }
    if (setting.instagram) {
      socialLink['instagram'].href = setting.instagram
    }
    if (setting.youtube) {
      socialLink['youtube'].href = setting.youtube
    }
    if (setting.tiktok) {
      socialLink['tiktok'].href = setting.tiktok
    }
  }
  const fetchSetting = async () => {
    try {
      const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/settings`)
      const setting = await result.json()
      setCompanySetting(setting)
      setSocialMedia(setting)

      const images = setting.images.reduce((result, current) => {
        result[current.type] = current
        return result
      }, {})
      setLogo(images['icon'].image)
    } catch (error) {
      console.log(error)
    }
  }

  const handleChange = e => {
    const newLocale = e.target.value

    // set cookie for next-i18n-router
    const days = 30
    const date = new Date()
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
    const expires = date.toUTCString()
    document.cookie = `NEXT_LOCALE=${newLocale};expires=${expires};path=/`

    // redirect to the new locale path
    if (currentLocale === i18nConfig.defaultLocale && !i18nConfig.prefixDefault) {
      router.push('/' + newLocale + currentPathname)
    } else {
      router.push(currentPathname.replace(`/${currentLocale}`, `/${newLocale}`))
    }

    router.refresh()
  }

  const [blogs, setBlogs] = useState([])

  const getBlog = async type => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_APP_API_URL}/blogs?page=1&limit=10&order=desc&type=${type}`
      )
      const response = await result.json()
      setBlogs(response)
    } catch (error) {
      console.log(error)
    }
  }

  // Fetch all items on component mount
  useEffect(() => {
    fetchSetting()
    getBlog('policies')
  }, [])

  return (
    <footer className='footer footer_type_1'>
      <div className='footer-middle container'>
        <div className='row row-cols-lg-5 row-cols-2'>
          <div className='footer-column footer-store-info col-12 mb-4 mb-lg-0'>
            <div className='logo'>
              <Link href='/'>
                <Image
                  src={logo}
                  width={112}
                  height={28}
                  alt='Wstore'
                  className='logo__image d-block'
                />
              </Link>
            </div>
            {/* <!-- /.logo --> */}
            <p className='footer-address'>{companySetting.companyName}</p>

            <p className='m-0'>
              <strong className='fw-medium'>{t('company.address', 'Địa chỉ')}: </strong>
              {companySetting.companyAddress}
            </p>
            <p className='m-0'>
              <strong className='fw-medium'>{t('company.taxNo', 'Mã số doanh nghiệp')}:</strong>
              {companySetting.companyTaxNo}
            </p>
            <p className='m-0'>
              <strong className='fw-medium'>{t('company.taxNoDate', 'Ngày cấp')}:</strong>
              {companySetting.companyTaxNoDate}
            </p>
            <p className='m-0'>
              <strong className='fw-medium'>{t('company.phone', 'SĐT')}:</strong>
              {companySetting.companyPhone}
            </p>
            <p className='m-0'>
              <strong className='fw-medium'>{t('company.email', 'Email')}:</strong>
              {companySetting.companyEmail}
            </p>
            <p className='m-0'>
              <strong className='fw-medium'>{t('company.website', 'Website')}:</strong>
              <a href={companySetting.companyWebsite} target='_blank'>
                {companySetting.companyWebsite}
              </a>
            </p>
          </div>
          {/* <!-- /.footer-column --> */}
          <div className='footer-column footer-menu mb-4 mb-lg-0'>
            <h5 className='sub-menu__title text-uppercase'>{t('policy.page', 'Chính sách')}</h5>
            <ul className='sub-menu__list list-unstyled'>
              {blogs.map((elm, i) => (
                <li key={i} className='sub-menu__item'>
                  <Link
                    href={elm.id ? `../blog_single/${elm.slug}` : '#'}
                    className='menu-link menu-link_us-s'
                  >
                    {t(`${elm.id}`, `${elm.title}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          {/* <!-- /.footer-column --> */}
          <div className='footer-column footer-menu mb-4 mb-lg-0'>
            <h5 className='sub-menu__title text-uppercase'>{t('pages', 'Trang')}</h5>
            <ul className='sub-menu__list list-unstyled'>
              {footerLinks1.map((elm, i) => (
                <li key={i} className='sub-menu__item'>
                  <Link href={elm.href} className='menu-link menu-link_us-s'>
                    {t(elm.key, elm.text)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* <!-- /.footer-column --> */}
          <div className='footer-column footer-menu mb-4 mb-lg-0'>
            <h5 className='sub-menu__title text-uppercase'>{t('blog.page', 'Bài viết')}</h5>
            <ul className='sub-menu__list list-unstyled'>
              {footerLinks3.map((elm, i) => (
                <li key={i} className='sub-menu__item'>
                  <Link href={elm.href} className='menu-link menu-link_us-s'>
                    {t(elm.key, elm.text)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* <!-- /.footer-column --> */}
          <div className='footer-column footer-newsletter col-12 mb-4 mb-lg-0'>
            <h5 className='sub-menu__title text-uppercase'>W.STORE Social Media</h5>
            <ul className='social-links list-unstyled d-flex flex-wrap mb-0'>
              {Object.values(socialLink).map((link, index) => (
                <li key={index}>
                  <a href={link.href} className='footer__social-link d-block'>
                    <svg
                      className={link.className}
                      width={link.width}
                      height={link.height}
                      viewBox={link.viewBox}
                      xmlns='http://www.w3.org/2000/svg'
                    >
                      {typeof link.icon === 'string' ? <use href={link.icon} /> : link.icon}
                    </svg>
                  </a>
                </li>
              ))}
            </ul>
            <div className='mt-4 pt-3'>
              <strong className='fw-medium'>
                {t('acceptablePayments', 'Chấp nhận thanh toán')}
              </strong>
              <p className='mt-2'>
                <Image
                  loading='lazy'
                  width={40}
                  height={30}
                  src='/assets/images/payments/mastercard.svg'
                  alt='Acceptable payment gateways'
                  className='mw-100'
                />
                <Image
                  loading='lazy'
                  width={80}
                  height={40}
                  src='/assets/images/payments/paypal.svg'
                  alt='Acceptable payment gateways'
                  className='mw-100'
                />
                <Image
                  loading='lazy'
                  width={40}
                  height={30}
                  src='/assets/images/payments/visa.svg'
                  alt='Acceptable payment gateways'
                  className='mw-100'
                />
              </p>
            </div>
          </div>
          {/* <!-- /.footer-column --> */}
        </div>
        {/* <!-- /.row-cols-5 --> */}
      </div>
      {/* <!-- /.footer-middle container --> */}

      <div className='footer-bottom container'>
        <div className='d-block d-md-flex align-items-center'>
          <span className='footer-copyright me-auto'>©{new Date().getFullYear()} Wstore</span>
          <div className='footer-settings d-block d-md-flex align-items-center'>
            <div className='d-flex align-items-center'>
              <label htmlFor='footerSettingsLanguage' className='me-2 text-secondary'>
                {t('language', 'Ngôn ngữ')}
              </label>
              <select
                id='footerSettingsLanguage'
                className='form-select form-select-sm bg-transparent'
                aria-label='Default select example'
                name='store-language'
                onChange={handleChange}
                defaultValue={currentLocale}
              >
                {languageOptions.map((option, index) => (
                  <option key={index} className='footer-select__option' value={option.value}>
                    {option.text}
                  </option>
                ))}
              </select>
            </div>

            <div className='d-flex align-items-center'>
              <label htmlFor='footerSettingsCurrency' className='ms-md-3 me-2 text-secondary'>
                {t('currency', 'Tiền tệ')}
              </label>
              <select
                id='footerSettingsCurrency'
                className='form-select form-select-sm bg-transparent'
                aria-label='Default select example'
                name='store-language'
                defaultValue={currentLocale}
              >
                {(currencyOptions || []).map((option, index) => (
                  <option key={index} className='footer-select__option' value={option.value}>
                    {option.text}
                  </option>
                ))}
              </select>
            </div>
          </div>
          {/* <!-- /.footer-settings --> */}
        </div>
        {/* <!-- /.d-flex --> */}
      </div>
      {/* <!-- /.footer-bottom container --> */}
    </footer>
  )
}
