import { useContextElement } from '@/context/Context'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function MobileFooter1() {
  const [showFooter, setShowFooter] = useState(false)
  const { wishList } = useContextElement()
  useEffect(() => {
    setShowFooter(true)
  }, [])

  const { t } = useTranslation()

  return (
    <footer
      className={`footer-mobile container w-100 px-5 d-md-none bg-body ${
        showFooter ? 'position-fixed footer-mobile_initialized' : ''
      }`}
    >
      <div className='row text-center'>
        <div className='col-4'>
          <Link href='/' className='footer-mobile__link d-flex flex-column align-items-center'>
            <svg
              className='d-block'
              width='18'
              height='18'
              viewBox='0 0 18 18'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <use href='#icon_home' />
            </svg>
            <span> {t('home.page', 'Trang chủ')}</span>
          </Link>
        </div>
        {/* <!-- /.col-3 --> */}

        <div className='col-4'>
          <Link
            href='/shop-1'
            className='footer-mobile__link d-flex flex-column align-items-center'
          >
            <svg
              className='d-block'
              width='18'
              height='18'
              viewBox='0 0 18 18'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <use href='#icon_hanger' />
            </svg>
            <span> {t('shop.page', 'Sản phẩm')}</span>
          </Link>
        </div>
        {/* <!-- /.col-3 --> */}

        <div className='col-4'>
          <Link
            href='/account_wishlist'
            className='footer-mobile__link d-flex flex-column align-items-center'
          >
            <div className='position-relative'>
              <svg
                className='d-block'
                width='18'
                height='18'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <use href='#icon_heart' />
              </svg>
              <span className='wishlist-amount d-block position-absolute js-wishlist-count'>
                {wishList.length}
              </span>
            </div>
            <span>Wishlist</span>
          </Link>
        </div>
        {/* <!-- /.col-3 --> */}
      </div>
      {/* <!-- /.row --> */}
    </footer>
  )
}
