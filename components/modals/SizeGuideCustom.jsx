import Image from 'next/image'
import React from 'react'
import { useTranslation } from 'react-i18next'

export default function SizeGuideCustom({ product }) {
  const { t } = useTranslation()
  return (
    <div className='modal fade' id='sizeGuideCustom' tabIndex='-1' aria-hidden='true'>
      <div className='modal-dialog size-guide'>
        <div className='modal-content'>
          <div className='modal-header'>
            <h5 className='modal-title'>{t('sizeGuide')}</h5>
            <button
              type='button'
              className='btn-close'
              data-bs-dismiss='modal'
              aria-label='Close'
            ></button>
          </div>
          <div className='modal-body'>
            {product && (
              <Image
                width={800}
                height={511}
                layout='responsive'
                loading='lazy'
                src={`${
                  product.childSelected.imageSize ||
                  product.imageSize ||
                  '/assets/images/size-guide.jpg'
                }`}
                alt='image'
              />
            )}
          </div>
        </div>
      </div>
      {/* <!-- /.modal-dialog --> */}
    </div>
  )
}
