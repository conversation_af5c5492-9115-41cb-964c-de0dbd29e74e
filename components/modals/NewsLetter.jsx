'use client'

import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

export default function NewsLetter() {
  const modalElement = useRef()
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const bootstrap = require('bootstrap') // dynamically import bootstrap
    const myModal = new bootstrap.Modal(document.getElementById('newsletterPopup'), {
      keyboard: false
    })
    modalElement.current.addEventListener('hidden.bs.modal', () => {
      myModal.hide()
    })
    if (isLoaded) {
      myModal.show()
    }
  }, [isLoaded])

  const [banner, setBanner] = useState({
    bannerImages: []
  })

  const getBanner = async () => {
    try {
      setIsLoaded(false)
      const result = await fetch(`/api/banners/home?type=sectionPopup`)
      const response = await result.json()
      setBanner(response)
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoaded(true)
    }
  }

  useEffect(() => {
    getBanner()
  }, [])

  return (
    <div
      className='modal fade'
      id='newsletterPopup'
      ref={modalElement}
      tabIndex='-1'
      data-bs-backdrop={'true'}
      aria-hidden='true'
    >
      <div className='modal-dialog newsletter-popup modal-dialog-centered'>
        <div className='modal-content'>
          <div className='row p-0 m-0'>
            <div className='col-md-12 p-0 d-sm-none d-md-block'>
              <div className='newsletter-popup__bg position-relative'>
                <button
                  type='button'
                  className='btn-close'
                  data-bs-dismiss='modal'
                  aria-label='Close'
                ></button>
                <Image
                  width={450}
                  height={350}
                  loading='lazy'
                  src={
                    Object.values(banner.bannerImages)[0]?.image ||
                    '/assets/images/newsletter-popup.jpg'
                  }
                  className='h-100 w-100'
                  alt='image'
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
