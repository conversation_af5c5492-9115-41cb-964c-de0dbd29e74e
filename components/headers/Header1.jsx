'use client'

import Link from 'next/link'
import { useEffect, useState } from 'react'
import Nav from './components/Nav'
import { openCart } from '@/utils/openCart'
import CartLength from './components/CartLength'
import Image from 'next/image'
import User from './components/User'
import SearchPopup from './components/SearchPopup'
import { openModalUserlogin } from '@/utils/aside'

export default function Header1() {
  const [scrollDirection, setScrollDirection] = useState('down')
  // /assets/images/logo.png default logo
  const [logo, setLogo] = useState('')

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      if (currentScrollY > 250) {
        if (currentScrollY > lastScrollY.current) {
          // Scrolling down
          setScrollDirection('down')
        } else {
          // Scrolling up
          setScrollDirection('up')
        }
      } else {
        // Below 250px
        setScrollDirection('down')
      }

      lastScrollY.current = currentScrollY
    }

    const lastScrollY = { current: window.scrollY }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll)

    // Cleanup: remove event listener when component unmounts
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const fetchSetting = async () => {
    try {
      const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/settings`)
      const setting = await result.json()
      const images = setting.images.reduce((result, current) => {
        result[current.type] = current
        return result
      }, {})
      setting.images = images
      setLogo(images['icon'].image)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    fetchSetting()
  }, [])

  return (
    <header
      id='header'
      className={`header header_sticky ${
        scrollDirection == 'up' ? 'header_sticky-active' : 'position-absolute'
      } `}
    >
      <div className='container'>
        <div className='header-desk header-desk_type_1'>
          <div className='logo'>
            <Link href='/'>
              {logo && (
                <Image
                  src={logo}
                  width={112}
                  height={28}
                  alt='Wstore'
                  className='logo__image d-block'
                />
              )}
            </Link>
          </div>
          {/* <!-- /.logo --> */}

          <nav className='navigation'>
            <ul className='navigation__list list-unstyled d-flex'>
              <Nav />
            </ul>
            {/* <!-- /.navigation__list --> */}
          </nav>
          {/* <!-- /.navigation --> */}

          <div className='header-tools d-flex align-items-center'>
            <SearchPopup />

            {/* <!-- /.header-tools__item hover-container --> */}

            <div className='header-tools__item hover-container'>
              <a
                className='header-tools__item js-open-aside'
                href='javascript:;'
                onClick={openModalUserlogin}
              >
                <User />
              </a>
            </div>

            <Link className='header-tools__item' href='/account_wishlist'>
              <svg
                width='20'
                height='20'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <use href='#icon_heart' />
              </svg>
            </Link>

            <a
              onClick={() => openCart()}
              className='header-tools__item header-tools__cart js-open-aside'
            >
              <svg
                className='d-block'
                width='20'
                height='20'
                viewBox='0 0 20 20'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <use href='#icon_cart' />
              </svg>
              <span className='cart-amount d-block position-absolute js-cart-items-count'>
                <CartLength />
              </span>
            </a>

            {/* <a
              className='header-tools__item'
              href='#'
              data-bs-toggle='modal'
              data-bs-target='#siteMap'
            >
              <svg
                className='nav-icon'
                width='25'
                height='18'
                viewBox='0 0 25 18'
                xmlns='http://www.w3.org/2000/svg'
              >
                <use href='#icon_nav' />
              </svg>
            </a> */}
          </div>
          {/* <!-- /.header__tools --> */}
        </div>
        {/* <!-- /.header-desk header-desk_type_1 --> */}
      </div>
      {/* <!-- /.container --> */}
    </header>
  )
}
