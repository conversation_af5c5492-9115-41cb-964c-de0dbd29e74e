'use client'
import { currencyOptions, languageOptions } from '@/data/footer'

import { socialLinks } from '@/data/socials'

import React, { useEffect, useState } from 'react'
import CartLength from './components/CartLength'
import { openCart } from '@/utils/openCart'
import MobileNav from './components/MobileNav'
import Image from 'next/image'

export default function MobileHeader() {
  const [scrollDirection, setScrollDirection] = useState('down')
  const [logo, setLogo] = useState('')

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      if (currentScrollY > 250) {
        if (currentScrollY > lastScrollY.current) {
          // Scrolling down
          setScrollDirection('down')
        } else {
          // Scrolling up
          setScrollDirection('up')
        }
      } else {
        // Below 250px
        setScrollDirection('down')
      }

      lastScrollY.current = currentScrollY
    }

    const lastScrollY = { current: window.scrollY }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll)

    // Cleanup: remove event listener when component unmounts
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const fetchSetting = async () => {
    try {
      const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/settings`)
      const setting = await result.json()
      const images = setting.images.reduce((result, current) => {
        result[current.type] = current
        return result
      }, {})
      setting.images = images
      setLogo(images['icon'].image)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    fetchSetting()
  }, [])

  return (
    <div
      className={`header-mobile header_sticky ${
        scrollDirection == 'up' ? 'header_sticky-active' : 'position-absolute'
      } `}
    >
      <div className='container d-flex align-items-center h-100'>
        <a className='mobile-nav-activator d-block position-relative' href='#'>
          <svg
            className='nav-icon'
            width='25'
            height='18'
            viewBox='0 0 25 18'
            xmlns='http://www.w3.org/2000/svg'
          >
            <use href='#icon_nav' />
          </svg>
          <span className='btn-close-lg position-absolute top-0 start-0 w-100'></span>
        </a>

        <div className='logo'>
          <a href='/'>
            {logo && (
              <Image
                src={logo}
                width={112}
                height={28}
                alt='Wstore'
                className='logo__image d-block'
              />
            )}
          </a>
        </div>
        {/* <!-- /.logo --> */}

        <a
          onClick={() => openCart()}
          className='header-tools__item header-tools__cart js-open-aside'
        >
          <svg
            className='d-block'
            width='20'
            height='20'
            viewBox='0 0 20 20'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <use href='#icon_cart' />
          </svg>
          <span className='cart-amount d-block position-absolute js-cart-items-count'>
            <CartLength />
          </span>
        </a>
      </div>
      {/* <!-- /.container --> */}

      <nav className='header-mobile__navigation navigation d-flex flex-column w-100 position-absolute top-100 bg-body overflow-auto'>
        <div className='container'>
          <form
            onSubmit={e => e.preventDefault()}
            className='search-field position-relative mt-4 mb-3'
          >
            <div className='position-relative'>
              <input
                className='search-field__input w-100 border rounded-1'
                type='text'
                name='search-keyword'
                placeholder='Search products'
              />
              <button className='btn-icon search-popup__submit pb-0 me-2' type='submit'>
                <svg
                  className='d-block'
                  width='20'
                  height='20'
                  viewBox='0 0 20 20'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <use href='#icon_search' />
                </svg>
              </button>
              <button
                className='btn-icon btn-close-lg search-popup__reset pb-0 me-2'
                type='reset'
              ></button>
            </div>

            <div className='position-absolute start-0 top-100 m-0 w-100'>
              <div className='search-result'></div>
            </div>
          </form>
          {/* <!-- /.header-search --> */}
        </div>
        {/* <!-- /.container --> */}

        <div className='container'>
          <div className='overflow-hidden'>
            <ul className='navigation__list list-unstyled position-relative'>
              <MobileNav />
            </ul>
            {/* <!-- /.navigation__list --> */}
          </div>
          {/* <!-- /.overflow-hidden --> */}
        </div>
        {/* <!-- /.container --> */}

        <div className='border-top mt-auto pb-2'>
          {/* <div className='customer-links container mt-4 mb-2 pb-1'>
            <svg
              className='d-inline-block align-middle'
              width='20'
              height='20'
              viewBox='0 0 20 20'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <use href='#icon_user' />
            </svg>
            <span className='d-inline-block ms-2 text-uppercase align-middle fw-medium'>
              My Account
            </span>
          </div> */}

          <div className='container d-flex align-items-center'>
            <label className='me-2 text-secondary'>Language</label>
            <select
              className='form-select form-select-sm bg-transparent border-0'
              aria-label='Default select example'
              name='store-language'
            >
              {languageOptions.map((option, index) => (
                <option key={index} className='footer-select__option' value={option.value}>
                  {option.text}
                </option>
              ))}
            </select>
          </div>

          <div className='container d-flex align-items-center'>
            <label className='me-2 text-secondary'>Currency</label>
            <select
              className='form-select form-select-sm bg-transparent border-0'
              aria-label='Default select example'
              name='store-language'
              defaultValue={'fghgjhgj'}
            >
              {currencyOptions.map((option, index) => (
                <option key={index} className='footer-select__option' value={option.value}>
                  {option.text}
                </option>
              ))}
            </select>
          </div>

          <ul className='container social-links list-unstyled d-flex flex-wrap mb-0'>
            {socialLinks.map((link, index) => (
              <li key={index}>
                <a href={link.href} className='footer__social-link d-block color-white'>
                  <svg
                    className={link.className}
                    width={link.width}
                    height={link.height}
                    viewBox={link.viewBox}
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <use href={link.icon} />
                  </svg>
                </a>
              </li>
            ))}
          </ul>
        </div>
      </nav>
      {/* <!-- /.navigation --> */}
    </div>
  )
}
