'use client'
import { additionalShopPageitems, blogmenuItems, shopDetails, productCategories } from '@/data/menu'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'

export default function Nav() {
  const pathname = usePathname()
  const isMenuActive = menu => {
    return menu.split('/')[1] == pathname.split('/')[1]
  }
  const isActiveParentMenu = menus => {
    return menus.some(menu => menu.href.split('/')[1] == pathname.split('/')[1])
  }
  useEffect(() => {
    function setBoxMenuPosition(menu) {
      const scrollBarWidth = 17 // You might need to calculate or define this value
      const limitR = window.innerWidth - menu.offsetWidth - scrollBarWidth
      const limitL = 0
      const menuPaddingLeft = parseInt(
        window.getComputedStyle(menu, null).getPropertyValue('padding-left')
      )
      const parentPaddingLeft = parseInt(
        window.getComputedStyle(menu.previousElementSibling, null).getPropertyValue('padding-left')
      )
      const centerPos = menu.previousElementSibling.offsetLeft - menuPaddingLeft + parentPaddingLeft

      let menuPos = centerPos
      if (centerPos < limitL) {
        menuPos = limitL
      } else if (centerPos > limitR) {
        menuPos = limitR
      }

      menu.style.left = `${menuPos}px`
    }
    document.querySelectorAll('.box-menu').forEach(el => {
      setBoxMenuPosition(el)
    })
  }, [])

  const { t } = useTranslation()

  const [categoryGroups, setCategoryGroups] = useState([])

  const getCategories = async () => {
    try {
      // const SIZE = 1
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_APP_URL}/api/categories?type=products&isParent=true`
      )
      const response = await result.json()

      setCategoryGroups(response)
    } catch (error) {
      console.log(error)
    }
  }

  const [blogs, setBlogs] = useState([])

  const getBlog = async type => {
    try {
      const result = await fetch(
        `${process.env.NEXT_PUBLIC_APP_API_URL}/blogs?page=1&limit=10&order=desc&type=${type}`
      )
      const response = await result.json()
      setBlogs(response)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getCategories()
    getBlog('policies')
  }, [])
  return (
    <>
      <li className='navigation__item'>
        <Link href='/' className={`navigation__link ${pathname == '/' ? 'menu-active' : ''}`}>
          {t('home.page', 'Trang chủ')}
        </Link>
      </li>
      <li className='navigation__item'>
        <a
          href='#'
          className={`navigation__link
           ${isActiveParentMenu(productCategories) ? 'menu-active' : ''}
           ${isActiveParentMenu(shopDetails) ? 'menu-active' : ''}
           ${isActiveParentMenu(additionalShopPageitems) ? 'menu-active' : ''}
          `}
        >
          {t('shop.page', 'Sản phẩm')}
        </a>
        <ul className='default-menu list-unstyled'>
          {categoryGroups.map((elm, i) => (
            <li key={i} className='sub-menu__item'>
              <Link
                href={elm.id ? `../shop-1/${elm.id}` : '#'}
                className={`menu-link menu-link_us-s ${isMenuActive(elm.id) ? 'menu-active' : ''}`}
              >
                {t(`${elm.id}`, `${elm.categoryName}`)}
              </Link>
            </li>
          ))}
        </ul>
      </li>
      <li className='navigation__item'>
        <a
          href='#'
          className={`navigation__link ${isActiveParentMenu(blogmenuItems) ? 'menu-active' : ''}`}
        >
          {t('blog.page', 'Đánh giá')}
        </a>
        <ul className='default-menu list-unstyled'>
          {blogmenuItems.map((elm, i) => (
            <li key={i} className='sub-menu__item'>
              <Link
                href={elm.href}
                className={`menu-link menu-link_us-s ${
                  isMenuActive(elm.href) ? 'menu-active' : ''
                }`}
              >
                {t(`${elm.key}`, `${elm.title}`)}
              </Link>
            </li>
          ))}
        </ul>
        {/* <!-- /.box-menu --> */}
      </li>

      {/* <li className="navigation__item">
                <a
                    href="#"
                    className={`navigation__link ${
                        isActiveParentMenu(othersMenuItems) ? "menu-active" : ""
                    }`}
                >
                    Pages
                </a>
                <ul className="default-menu list-unstyled">
                    {othersMenuItems.map((elm, i) => (
                        <li key={i} className="sub-menu__item">
                            <Link
                                href={elm.href}
                                className={`menu-link menu-link_us-s ${
                                    isMenuActive(elm.href) ? "menu-active" : ""
                                }`}
                            >
                                {elm.title}
                            </Link>
                        </li>
                    ))}
                </ul>
                <!-- /.box-menu -->
            </li> */}
      <li className='navigation__item'>
        <Link
          href='/about'
          className={`navigation__link ${pathname == '/about' ? 'menu-active' : ''}`}
        >
          {t('about.page', 'Về chúng tôi')}
        </Link>
      </li>
      <li className='navigation__item'>
        {/* <Link
          href='/contact'
          className={`navigation__link ${pathname == '/contact' ? 'menu-active' : ''}`}
        >
          {t('policy.page', 'Chính sách')}
        </Link> */}

        <a href='#' className={`navigation__link`}>
          {t('policy.page', 'Chính sách')}
        </a>
        <ul className='default-menu list-unstyled'>
          {blogs.map((elm, i) => (
            <li key={i} className='sub-menu__item'>
              <Link
                href={elm.id ? `../blog_single/${elm.slug}` : '#'}
                className={`menu-link menu-link_us-s ${isMenuActive(elm.id) ? 'menu-active' : ''}`}
              >
                {t(`${elm.id}`, `${elm.title}`)}
              </Link>
            </li>
          ))}
        </ul>
      </li>
      {/* <li className="navigation__item">
                <Link
                    href="/contact"
                    className={`navigation__link ${pathname == "/contact" ? "menu-active" : ""}`}
                >
                    {t("contact", "Liên hệ")}
                </Link>
            </li> */}
    </>
  )
}
