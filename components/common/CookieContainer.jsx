'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'

export default function CookieContainer() {
  const { t } = useTranslation()
  const [show, setShow] = useState(false)

  useEffect(() => {
    // Check if user has already accepted cookies
    const cookieConsent = localStorage.getItem('cookieConsent')
    if (!cookieConsent) {
      setShow(true)
    }
  }, [])

  const handleAccept = () => {
    // Save consent to localStorage
    localStorage.setItem('cookieConsent', 'true')

    // Set cookie for server-side awareness
    const expiryDate = new Date()
    expiryDate.setMonth(expiryDate.getMonth() + 6) // <PERSON>ie expires in 6 months
    document.cookie = `cookieConsent=true; expires=${expiryDate.toUTCString()}; path=/`

    // Hide the cookie banner
    setShow(false)
  }

  return (
    <>
      {show && (
        <div className='cookieConsentContainer' style={{ opacity: 1, display: 'block' }}>
          <div className='cookieDesc'>
            <p>{t('cookie.message')}</p>
          </div>
          <div className='cookieButton'>
            <a onClick={handleAccept}>{t('accept')}</a>
          </div>
        </div>
      )}
    </>
  )
}
