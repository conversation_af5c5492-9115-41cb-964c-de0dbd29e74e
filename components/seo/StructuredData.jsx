/**
 * Component for adding JSON-LD structured data to pages
 * Helps with SEO and rich snippets in search results
 */

export function BlogStructuredData({ blog, locale = 'vi' }) {
  if (!blog) return null

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: blog.title,
    description:
      blog.shortDescription || blog.description?.replace(/<[^>]*>/g, '').substring(0, 160),
    image: blog.image ? [blog.image] : [],
    author: {
      '@type': 'Organization',
      name: blog.author || 'Shapewear by W.Store',
      url: process.env.NEXT_PUBLIC_APP_URL
    },
    publisher: {
      '@type': 'Organization',
      name: 'Shapewear by <PERSON><PERSON><PERSON>',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_APP_URL}/assets/images/logo.png`
      }
    },
    datePublished: blog.publishedAt || blog.createdAt,
    dateModified: blog.updatedAt || blog.publishedAt || blog.createdAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${process.env.NEXT_PUBLIC_APP_URL}/${locale}/blog/${blog.slug || blog.id}`
    },
    inLanguage: locale === 'vi' ? 'vi-VN' : 'en-US'
  }

  if (blog.category) {
    structuredData.articleSection = blog.category
  }

  if (blog.tags && blog.tags.length > 0) {
    structuredData.keywords = blog.tags.join(', ')
  }

  return (
    <script
      type='application/ld+json'
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

export function ProductStructuredData({ product, locale = 'vi' }) {
  if (!product) return null

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.productName,
    description:
      product.shortDescription || product.description?.replace(/<[^>]*>/g, '').substring(0, 160),
    image: product.images?.map(img => img.url || img) || [],
    brand: {
      '@type': 'Brand',
      name: 'W.Store'
    },
    manufacturer: {
      '@type': 'Organization',
      name: 'Shapewear by W.Store'
    },
    offers: {
      '@type': 'Offer',
      price: product.sellingPrice || product.price,
      priceCurrency: locale === 'vi' ? 'VND' : 'USD',
      availability: product.inStock
        ? 'https://schema.org/InStock'
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'Shapewear by W.Store'
      }
    },
    aggregateRating: product.rating
      ? {
          '@type': 'AggregateRating',
          ratingValue: product.rating,
          reviewCount: product.reviewCount || 1,
          bestRating: 5,
          worstRating: 1
        }
      : undefined
  }

  if (product.categories && product.categories.length > 0) {
    structuredData.category = product.categories[0]
  }

  if (product.sku) {
    structuredData.sku = product.sku
  }

  if (product.gtin || product.barcode) {
    structuredData.gtin = product.gtin || product.barcode
  }

  return (
    <script
      type='application/ld+json'
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

export function OrganizationStructuredData({ locale = 'vi' }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Shapewear by W.Store',
    alternateName: 'W.Store',
    url: process.env.NEXT_PUBLIC_APP_URL,
    logo: `${process.env.NEXT_PUBLIC_APP_URL}/assets/images/logo.png`,
    description:
      locale === 'vi'
        ? 'Thương hiệu shapewear hàng đầu Việt Nam, chuyên cung cấp đồ lót định hình cao cấp cho phái nữ.'
        : 'Leading shapewear brand in Vietnam, specializing in premium shapewear for women.',
    sameAs: [
      'https://www.facebook.com/shapewearbywstore',
      'https://www.instagram.com/shapewearbywstore'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+84-xxx-xxx-xxx',
      contactType: 'customer service',
      availableLanguage: ['Vietnamese', 'English']
    }
  }

  return (
    <script
      type='application/ld+json'
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

export function WebsiteStructuredData({ locale = 'vi' }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Shapewear by W.Store',
    url: process.env.NEXT_PUBLIC_APP_URL,
    description:
      locale === 'vi'
        ? 'Website chính thức của Shapewear by W.Store - Thương hiệu đồ lót định hình hàng đầu Việt Nam'
        : 'Official website of Shapewear by W.Store - Leading shapewear brand in Vietnam',
    inLanguage: locale === 'vi' ? 'vi-VN' : 'en-US',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${process.env.NEXT_PUBLIC_APP_URL}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    }
  }

  return (
    <script
      type='application/ld+json'
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
