export const slides1 = [
  {
    id: 1,

    characterImg: "/assets/images/slideshow-character1.png",
    characterAlt: "Woman Fashion 1",
    characterText: "Summer",
    text1: "New Trend",
    text2: "Summer Sale Stylish",
    text3: "Womens",
  },
  {
    id: 2,

    characterImg: "/assets/images/slideshow-character2.png",
    characterAlt: "Woman Fashion 2",
    characterText: "",
    text1: "Summer 2020",
    text3: "Hello New Season",
    text4: "Limited Time Offer - Up to 60% off & Free Shipping",
  },
];
export const slides2 = [
  {
    id: 1,
    imageSrc: "/assets/images/home/<USER>/slider1.jpg",
    imageAlt: "Pattern",
    imageStyle: { objectPosition: "80% center" },
    season: "Summer 2023",
    title: "Hello New Season",
    discount: "Limited time offer - up to 60% off & free shipping",
  },
  {
    id: 2,
    imageSrc: "/assets/images/home/<USER>/slider2.jpg",
    imageAlt: "Pattern",
    imageStyle: { objectPosition: "70% center" },
    season: "Summer 2023",
    title: "Hello New Season",
    discount: "Limited time offer - up to 60% off & free shipping",
  },
  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/slider3.jpg",
    imageAlt: "Pattern",
    imageStyle: { objectPosition: "70% center" },
    season: "Summer 2023",
    title: "Hello New Season",
    discount: "Limited time offer - up to 60% off & free shipping",
  },
];
export const slides3 = [
  {
    id: 1,
    imageUrl: "/assets/images/home/<USER>/slideshow-character1.png",
    imageWidth: 542,
    imageHeight: 733,
    altText: "Woman Fashion 1",
    characterMarkup: "Dresses",
  },
  {
    id: 2,
    imageUrl: "/assets/images/slideshow-character1.png",
    imageWidth: 400,
    imageHeight: 733,
    altText: "Woman Fashion 1",
    characterMarkup: "Summer",
  },
  {
    id: 3,
    imageUrl: "/assets/images/slideshow-character2.png",
    imageWidth: 400,
    imageHeight: 690,
    altText: "Woman Fashion 2",
  },
];
export const slides4 = [
  {
    id: 1,
    bgImg: "/assets/images/home/<USER>/slider1.png",
    markImg: "/assets/images/home/<USER>/slider_mark.png",
    title: "The Classics",
    description: "An exclusive selection of this season's trends.",
    characterImgWidth: 493,
    characterImgHeight: 693,
  },
  {
    id: 2,
    bgImg: "/assets/images/home/<USER>/slider2.png",
    markImg: "/assets/images/home/<USER>/slider_mark.png",
    title: "The Classics",
    description: "An exclusive selection of this season's trends.",
    characterImgWidth: 490,
    characterImgHeight: 690,
  },
  {
    id: 3,
    bgImg: "/assets/images/home/<USER>/slider3.png",
    markImg: "/assets/images/home/<USER>/slider_mark.png",
    title: "The Classics",
    description: "An exclusive selection of this season's trends.",
    characterImgWidth: 675,
    characterImgHeight: 733,
  },
];
export const slides5 = [
  {
    id: 1,
    slideshowImg: "/assets/images/home/<USER>/slider1.png",
    bgImg: "/assets/images/home/<USER>/slider_mark.png",
    title: "The Classics",
    description: "An exclusive selection of this season's trends.",
    imgWidth: "493",
    imgHeight: "693",
    bgImgWidth: "690",
    bgImgHeight: "690",
  },
  {
    id: 2,
    slideshowImg: "/assets/images/home/<USER>/slider2.png",
    bgImg: "/assets/images/home/<USER>/slider_mark.png",
    title: "The Classics",
    description: "An exclusive selection of this season's trends.",
    imgWidth: "490",
    imgHeight: "690",
    bgImgWidth: "560",
    bgImgHeight: "560",
  },
  {
    id: 3,
    slideshowImg: "/assets/images/home/<USER>/slider3.png",
    bgImg: "/assets/images/home/<USER>/slider_mark.png",
    title: "The Classics",
    description: "An exclusive selection of this season's trends.",
    imgWidth: "675",
    imgHeight: "733",
    bgImgWidth: "690",
    bgImgHeight: "690",
  },
];

export const slides6 = [
  {
    id: 1,
    bgColor: "#f5e6e0",
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Better Things In a Better Way",
  },
  {
    id: 2,
    bgColor: "#f5e6e0",
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Better Things In a Better Way",
  },
  {
    id: 3,
    bgColor: "#f5e6e0",
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Better Things In a Better Way",
  },
  {
    id: 4,
    bgColor: "#f5e6e0",
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Better Things In a Better Way",
  },
];
export const slidesData = [
  {
    id: 1,
    src: "/assets/images/home/<USER>/slider1.jpg",
    title: "Our All-Time Favourites",
    subtitle: "Blouses & Tops",
  },
  {
    id: 2,
    src: "/assets/images/home/<USER>/slider1.jpg",
    title: "Our All-Time Favourites",
    subtitle: "Blouses & Tops",
  },
  {
    id: 3,
    src: "/assets/images/home/<USER>/slider1.jpg",
    title: "Our All-Time Favourites",
    subtitle: "Blouses & Tops",
  },
];
export const slidesData2 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "New In",
    width: 475,
    height: 800,
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/slider2.jpg",
    title: "Coats",
    width: 475,
    height: 800,
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/slider3.jpg",
    title: "Shoes",
    subMenuLinks: [
      { text: "Trainers", link: "/shop-1" },
      { text: "Sandals", link: "/shop-1" },
      { text: "Heel shoes", link: "/shop-1" },
      { text: "Flat shoes", link: "/shop-1" },
      { text: "Special prices", link: "/shop-1" },
    ],
    width: 475,
    height: 800,
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/slider4.jpg",
    title: "Accessories",
    width: 475,
    height: 800,
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "New In",
    width: 475,
    height: 800,
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/slider2.jpg",
    title: "Coats",
    width: 475,
    height: 800,
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/slider3.jpg",
    title: "Shoes",
    subMenuLinks: [
      { text: "Trainers", link: "/shop-1" },
      { text: "Sandals", link: "/shop-1" },
      { text: "Heel shoes", link: "/shop-1" },
      { text: "Flat shoes", link: "/shop-1" },
      { text: "Special prices", link: "/shop-1" },
    ],
    width: 475,
    height: 800,
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/slider4.jpg",
    title: "Accessories",
    width: 475,
    height: 800,
  },
];
export const slidesData3 = [
  {
    id: 1,
    imageSrc: "/assets/images/home/<USER>/slider1.jpg",
    season: "Summer 2023",
    objectPosition: "50% center",
    title: "New Arrival Men’s",
    subtitle: "Collection",
  },
  {
    id: 2,
    imageSrc: "/assets/images/home/<USER>/slider2.jpg",
    season: "Summer 2023",
    objectPosition: "70% center",
    title: "New Arrival Women’s",
    subtitle: "Collection",
  },
  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/slider3.jpg",
    season: "Summer 2023",
    objectPosition: "50% center",
    title: "New Arrival Kid’s",
    subtitle: "Collection",
  },
];
export const slidesData4 = [
  {
    id: 1,
    imageSrc: "/assets/images/home/<USER>/slider1.jpg",
    imageAlt: "TRENDING 2023",
    subtitle: "Interior Designs",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  {
    id: 2,
    imageSrc: "/assets/images/home/<USER>/slider1.jpg",
    imageAlt: "TRENDING 2023",
    subtitle: "Interior Designs",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/slider1.jpg",
    imageAlt: "TRENDING 2023",
    subtitle: "Interior Designs",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  {
    id: 4,
    imageSrc: "/assets/images/home/<USER>/slider1.jpg",
    imageAlt: "TRENDING 2023",
    subtitle: "Interior Designs",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Odio pulvinar in ipsum amet.",
  },
];
export const slidesData5 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/slideshow-character1.png",
    imgAlt: "Woman Fashion 1",
    title: "Modern Jogger",
    price: "399,50 TL",
    type: "New Arrivals",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/slideshow-character1.png",
    imgAlt: "Woman Fashion 1",
    title: "Modern Jogger",
    price: "399,50 TL",
    type: "New Arrivals",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/slideshow-character1.png",
    imgAlt: "Woman Fashion 1",
    title: "Modern Jogger",
    price: "399,50 TL",
    type: "New Arrivals",
  },
];
export const slideData6 = [
  {
    id: 1,
    src: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Woman Fashion 1",
    category: "TOP SMARTPHONES",
    price: 279,
    feature1: "Heavy on features.",
    feature2: "Light on price.",
  },
  {
    id: 2,
    src: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Woman Fashion 1",
    category: "TOP SMARTPHONES",
    price: 279,
    feature1: "Heavy on features.",
    feature2: "Light on price.",
  },
  {
    id: 3,
    src: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Woman Fashion 1",
    category: "TOP SMARTPHONES",
    price: 279,
    feature1: "Heavy on features.",
    feature2: "Light on price.",
  },
  {
    id: 4,
    src: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Woman Fashion 1",
    category: "TOP SMARTPHONES",
    price: 279,
    feature1: "Heavy on features.",
    feature2: "Light on price.",
  },
];

export const slidesData7 = [
  {
    id: 1,
    backgroundColor: "#f5e6e0",
    imageUrl: "/assets/images/home/<USER>/slideshow-pattern.png",
    imgUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Pattern",
    title: "Shop Our Freshest",
    subtitle1: "Fresh Hand-Picked Vegetables",
    subtitle2: "Everyday",
  },
  {
    id: 1,
    backgroundColor: "#f5e6e0",
    imageUrl: "/assets/images/home/<USER>/slideshow-pattern.png",
    imgUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Pattern",
    title: "Shop Our Freshest",
    subtitle1: "Fresh Hand-Picked Vegetables",
    subtitle2: "Everyday",
  },
  {
    id: 1,
    backgroundColor: "#f5e6e0",
    imageUrl: "/assets/images/home/<USER>/slideshow-pattern.png",
    imgUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    alt: "Pattern",
    title: "Shop Our Freshest",
    subtitle1: "Fresh Hand-Picked Vegetables",
    subtitle2: "Everyday",
  },
  // Add more slide data objects as needed
];

export const slideData8 = [
  {
    id: 1,
    backgroundColor: "#f5e6e0",
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    imgAlt: "Pattern",
    title1: "EYEWEAR",
    title2: "UP TO $60 OFF POLARISED",
    title3: "SUNGLASSES",
  },
  {
    id: 2,
    backgroundColor: "#f5e6e0",
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    imgAlt: "Pattern",
    title1: "EYEWEAR",
    title2: "UP TO $60 OFF POLARISED",
    title3: "SUNGLASSES",
  },
];

export const slideData9 = [
  {
    id: 1,
    bgImg: "/assets/images/home/<USER>/slider1_bg.jpg",
    characterImg: "/assets/images/home/<USER>/slider1_character.png",
    title: "Search Lab N95 Face Mask",
    subtitle: "Just a few seconds to measure your body temperature.",
    btnLink: "/shop-1",
  },
  {
    id: 2,
    bgImg: "/assets/images/home/<USER>/slider1_bg.jpg",
    characterImg: "/assets/images/home/<USER>/slider2_character.png",
    title: "Search Lab N95 Face Mask",
    subtitle: "Just a few seconds to measure your body temperature.",
    btnText: "SHOP NOW",
  },
];

export const slideData10 = [
  {
    id: 1,
    bgImage: "/assets/images/home/<USER>/slider1_bg.jpg",
    title: "Natural Glow",
    description:
      "Beaux products protect, moisturize, and lubricate your skin. It smartly nourish your skin. with lotions, day creams, night creams, tinted moisturizers, and more.",
  },
  {
    id: 2,
    bgImage: "/assets/images/home/<USER>/slider2_bg.jpg",
    title: "Natural Glow",
    description:
      "Beaux products protect, moisturize, and lubricate your skin. It smartly nourish your skin. with lotions, day creams, night creams, tinted moisturizers, and more.",
  },
];

export const slideData11 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/slider1_bg.jpg",
    title: "NEW BIKE FOR 2024!",
    subtitle: "New Arrivals",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/slider2_bg.jpg",
    title: "MOUNTAIN BIKE 2024!",
    subtitle: "New Arrivals",
  },
];

export const slideData12 = [
  {
    id: 1,
    imageUrl: "/assets/images/home/<USER>/slider_bg_1.jpg",
    imageUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    text: "More than 30 varieties of wine",
    title: "Wine enjoying passion",
  },
  {
    id: 2,
    imageUrl: "/assets/images/home/<USER>/slider_bg_1.jpg",
    imageUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    text: "More than 30 varieties of wine",
    title: "Wine enjoying passion",
  },
  {
    id: 3,
    imageUrl: "/assets/images/home/<USER>/slider_bg_1.jpg",
    imageUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    text: "More than 30 varieties of wine",
    title: "Wine enjoying passion",
  },
  {
    id: 4,
    imageUrl: "/assets/images/home/<USER>/slider_bg_1.jpg",
    imageUrl2: "/assets/images/home/<USER>/slideshow-character1.png",
    text: "More than 30 varieties of wine",
    title: "Wine enjoying passion",
  },
];

export const slideData13 = [
  {
    id: 1,
    imageUrl: "/assets/images/home/<USER>/banner-1.jpg",
    title: "Special Offer",
    subTitle: "Save 20%",
    description: "First Service",
  },
  {
    id: 2,
    imageUrl: "/assets/images/home/<USER>/banner-1.jpg",
    title: "Special Offer",
    subTitle: "Save 20%",
    description: "First Service",
  },
  {
    id: 3,
    imageUrl: "/assets/images/home/<USER>/banner-1.jpg",
    title: "Special Offer",
    subTitle: "Save 20%",
    description: "First Service",
  },
  {
    id: 4,
    imageUrl: "/assets/images/home/<USER>/banner-1.jpg",
    title: "Special Offer",
    subTitle: "Save 20%",
    description: "First Service",
  },
];

export const heroSlides14 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    title: "Classic Collection",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    title: "Gold Collection",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    title: "Sport Collection",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    title: "Heritage Collection",
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    title: "Classic Collection",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    title: "Gold Collection",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    title: "Sport Collection",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    title: "Heritage Collection",
  },
];

export const slideshows = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Newest Arrivals",
    subtitle: "LATEST TRENDS",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Newest Arrivals",
    subtitle: "LATEST TRENDS",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Newest Arrivals",
    subtitle: "LATEST TRENDS",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    title: "Newest Arrivals",
    subtitle: "LATEST TRENDS",
    description:
      "Lorem ipsum dolor sit amet, consectetur elit. Odio pulvinar in ipsum amet.",
  },
  // Add more slides as needed
];

export const slidesData15 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    imgAlt: "Pattern",
    title: "Classic Play Kitchen",
    subTitle: "New Toys",
    price: "Starting at $59.99",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    imgAlt: "Pattern 2",
    title: "Educational Toys",
    subTitle: "Explore & Learn",
    price: "Starting at $39.99",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/slider1.jpg",
    imgAlt: "Pattern 3",
    title: "Outdoor Adventure",
    subTitle: "Gear Up for Fun",
    price: "Starting at $79.99",
  },
  // Add more slide data objects as needed
];

export const slidesData16 = [
  {
    id: 1,
    imageSrc: "/assets/images/home/<USER>/slider-1.jpg",
    category: "GARDEN PRODUCTS",
    title: "WELCOME THE SUMMER IN YOUR GARDEN",
  },
  {
    id: 2,
    imageSrc: "/assets/images/home/<USER>/slider-2.jpg",
    category: "New Arrivals",
    title: "Up To 40% Off",
  },
  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/slider-1.jpg",
    category: "GARDEN PRODUCTS",
    title: "WELCOME THE SUMMER IN YOUR GARDEN",
  },
  {
    id: 4,
    imageSrc: "/assets/images/home/<USER>/slider-2.jpg",
    category: "New Arrivals",
    title: "Up To 40% Off",
  },
  // Add more slide objects as needed
];
