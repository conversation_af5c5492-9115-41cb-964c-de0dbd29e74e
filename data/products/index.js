import { products43, products44, products45, products46 } from "./baby";
import { products35, products36, products37 } from "./bikes";
import { products40 } from "./car";
import { products33, products34 } from "./cosmetics";
import { products21, products22, products23, products24 } from "./electronics";
import {
  products1,
  products10,
  products11,
  products12,
  products13,
  products14,
  products15,
  products16,
  products17,
  products18,
  products19,
  products2,
  products20,
  products3,
  products4,
  products5,
  products51,
  products52,
  products53,
  products54,
  products55,
  products56,
  products57,
  products58,
  products59,
  products6,
  products7,
  products8,
  products9,
} from "./fashion";
import { products28 } from "./glasses";
import { products25, products26, products27 } from "./grocery";
import { products42 } from "./jewelry";
import { products29, products30, products31, products32 } from "./medical";
import { products47, products48, products49 } from "./tools";
import { products41 } from "./watches";

export const allProducts = [
  ...products1,
  ...products2,
  ...products3,

  ...products4,
  ...products5,
  ...products6,
  ...products7,
  ...products8,
  ...products9,
  ...products10,
  ...products11,
  ...products12,
  ...products13,
  ...products14,
  ...products15,
  ...products16,
  ...products17,
  ...products18,
  ...products19,
  ...products20,
  ...products21,
  ...products22,
  ...products23,
  ...products24,
  ...products25,
  ...products26,
  ...products27,
  ...products28,
  ...products29,
  ...products30,
  ...products31,
  ...products32,
  ...products33,
  ...products34,
  ...products35,
  ...products36,
  ...products37,

  ...products40,
  ...products41,
  ...products42,
  ...products43,
  ...products44,
  ...products45,
  ...products46,
  ...products47,
  ...products48,
  ...products49,

  ...products51,
  ...products52,
  ...products53,
  ...products54,
  ...products55,
  ...products56,
  ...products57,
  ...products58,
  ...products59,
];
