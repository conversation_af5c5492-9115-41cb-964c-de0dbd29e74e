export const categories = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category1.jpg",
    name: "Accessories",
    productCount: 20,
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category2.jpg",
    name: "Bags",
    productCount: 20,
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category3.jpg",
    name: "Shoes",
    productCount: 20,
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category4.jpg",
    name: "Outerwear",
    productCount: 20,
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category5.jpg",
    name: "Top",
    productCount: 20,
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category1.jpg",
    name: "Accessories",
    productCount: 20,
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category2.jpg",
    name: "Bags",
    productCount: 20,
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category3.jpg",
    name: "Shoes",
    productCount: 20,
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category4.jpg",
    name: "Outerwear",
    productCount: 20,
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category5.jpg",
    name: "Top",
    productCount: 20,
  },
];
export const categories2 = [
  {
    id: 1,
    imageUrl: "/assets/images/home/<USER>/category_1.png",
    text: "Women Tops",
  },
  {
    id: 2,
    imageUrl: "/assets/images/home/<USER>/category_2.png",
    text: "Women Pants",
  },
  {
    id: 3,
    imageUrl: "/assets/images/home/<USER>/category_3.png",
    text: "Women Clothes",
  },
  {
    id: 4,
    imageUrl: "/assets/images/home/<USER>/category_4.png",
    text: "Men Jeans",
  },
  {
    id: 5,
    imageUrl: "/assets/images/home/<USER>/category_5.png",
    text: "Men Shirts",
  },
  {
    id: 6,
    imageUrl: "/assets/images/home/<USER>/category_6.png",
    text: "Men Shoes",
  },
  {
    id: 7,
    imageUrl: "/assets/images/home/<USER>/category_7.png",
    text: "Women Dresses",
  },
  {
    id: 8,
    imageUrl: "/assets/images/home/<USER>/category_8.png",
    text: "Kids Tops",
  },
  {
    id: 1,
    imageUrl: "/assets/images/home/<USER>/category_1.png",
    text: "Women Tops",
  },
  {
    id: 2,
    imageUrl: "/assets/images/home/<USER>/category_2.png",
    text: "Women Pants",
  },
  {
    id: 3,
    imageUrl: "/assets/images/home/<USER>/category_3.png",
    text: "Women Clothes",
  },
  {
    id: 4,
    imageUrl: "/assets/images/home/<USER>/category_4.png",
    text: "Men Jeans",
  },
  {
    id: 5,
    imageUrl: "/assets/images/home/<USER>/category_5.png",
    text: "Men Shirts",
  },
  {
    id: 6,
    imageUrl: "/assets/images/home/<USER>/category_6.png",
    text: "Men Shoes",
  },
  {
    id: 7,
    imageUrl: "/assets/images/home/<USER>/category_7.png",
    text: "Women Dresses",
  },
  {
    id: 8,
    imageUrl: "/assets/images/home/<USER>/category_8.png",
    text: "Kids Tops",
  },
];
export const categoryBanners = [
  {
    id: 1,
    imageUrl: "/assets/images/home/<USER>/category_9.jpg",
    price: 19,
    category: "Blazers",
  },
  {
    id: 2,
    imageUrl: "/assets/images/home/<USER>/category_10.jpg",
    price: 19,
    category: "Sportswear",
  },
];

export const gridBannerItems = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/grid-banner-1.jpg",
    imgWidth: 690,
    imgHeight: 450,
    category: "Basic Collection",
    title: "New Arrivals",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/grid-banner-2.jpg",
    imgWidth: 690,
    imgHeight: 285,
    category: "Shop Casual",
    title: "Free Shipping",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/grid-banner-3.jpg",
    imgWidth: 690,
    imgHeight: 285,
    category: "Want and Need",
    title: "The Everygirl Wears",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/grid-banner-4.jpg",
    imgWidth: 690,
    imgHeight: 450,
    category: "Sale off This week",
    title: "Running Shoes",
  },
];

export const categories3 = [
  {
    id: 1,
    name: "Handbags",
    imgSrc: "/assets/images/home/<USER>/category_1.png",
  },
  {
    id: 2,
    name: "Clothings",
    imgSrc: "/assets/images/home/<USER>/category_2.png",
  },
  {
    id: 3,
    name: "Jackets",
    imgSrc: "/assets/images/home/<USER>/category_3.png",
  },
  {
    id: 4,
    name: "Watches",
    imgSrc: "/assets/images/home/<USER>/category_4.png",
  },
  {
    id: 5,
    name: "Dresses",
    imgSrc: "/assets/images/home/<USER>/category_5.png",
  },
  { id: 6, name: "Shoes", imgSrc: "/assets/images/home/<USER>/category_6.png" },
  { id: 7, name: "Jeans", imgSrc: "/assets/images/home/<USER>/category_7.png" },
  { id: 8, name: "Shirts", imgSrc: "/assets/images/home/<USER>/category_8.png" },
  {
    id: 1,
    name: "Handbags",
    imgSrc: "/assets/images/home/<USER>/category_1.png",
  },
  {
    id: 2,
    name: "Clothings",
    imgSrc: "/assets/images/home/<USER>/category_2.png",
  },
  {
    id: 3,
    name: "Jackets",
    imgSrc: "/assets/images/home/<USER>/category_3.png",
  },
  {
    id: 4,
    name: "Watches",
    imgSrc: "/assets/images/home/<USER>/category_4.png",
  },
  {
    id: 5,
    name: "Dresses",
    imgSrc: "/assets/images/home/<USER>/category_5.png",
  },
  { id: 6, name: "Shoes", imgSrc: "/assets/images/home/<USER>/category_6.png" },
  { id: 7, name: "Jeans", imgSrc: "/assets/images/home/<USER>/category_7.png" },
  { id: 8, name: "Shirts", imgSrc: "/assets/images/home/<USER>/category_8.png" },
];
export const collectionsData = [
  {
    id: 1,
    imageSrc: "/assets/images/home/<USER>/collection_grid_1.jpg",
    title: "Furniture",
    productCount: "954 Products",
  },
  {
    id: 2,
    imageSrc: "/assets/images/home/<USER>/collection_grid_2.jpg",
    title: "Clocks",
    productCount: "710 Products",
  },
  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/collection_grid_3.jpg",
    title: "Accessories",
    productCount: "954 Products",
  },
  {
    id: 4,
    imageSrc: "/assets/images/home/<USER>/collection_grid_4.jpg",
    title: "Lighting",
    productCount: "184 Products",
  },
  {
    id: 5,
    imageSrc: "/assets/images/home/<USER>/collection_grid_5.jpg",
    title: "Toys",
    productCount: "245 Products",
  },
];
export const categories4 = [
  {
    id: 1,
    imageSrc: "/assets/images/home/<USER>/category_1.png",
    category: "Cellphones & Tablets",
  },
  {
    id: 2,
    imageSrc: "/assets/images/home/<USER>/category_2.png",
    category: "TV / Video",
  },
  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/category_3.png",
    category: "Computers & Accessories",
  },
  {
    id: 4,
    imageSrc: "/assets/images/home/<USER>/category_4.png",
    category: "Video Games",
  },
  {
    id: 5,
    imageSrc: "/assets/images/home/<USER>/category_5.png",
    category: "Headphones",
  },
  {
    id: 6,
    imageSrc: "/assets/images/home/<USER>/category_6.png",
    category: "Camera",
  },
  {
    id: 7,
    imageSrc: "/assets/images/home/<USER>/category_7.png",
    category: "Office Electronics",
  },
  {
    id: 8,
    imageSrc: "/assets/images/home/<USER>/category_8.png",
    category: "Smart Home",
  },

  {
    id: 3,
    imageSrc: "/assets/images/home/<USER>/category_3.png",
    category: "Computers & Accessories",
  },
  {
    id: 4,
    imageSrc: "/assets/images/home/<USER>/category_4.png",
    category: "Video Games",
  },
  {
    id: 5,
    imageSrc: "/assets/images/home/<USER>/category_5.png",
    category: "Headphones",
  },
  {
    id: 6,
    imageSrc: "/assets/images/home/<USER>/category_6.png",
    category: "Camera",
  },
];

export const categories5 = [
  { name: "Fruit", imageSrc: "/assets/images/home/<USER>/category-1.jpg" },
  {
    name: "Bakery",
    imageSrc: "/assets/images/home/<USER>/category-2.jpg",
    productCount: 20,
  },
  { name: "Fish", imageSrc: "/assets/images/home/<USER>/category-3.jpg" },
  { name: "Milk", imageSrc: "/assets/images/home/<USER>/category-4.jpg" },
  { name: "Vegetables", imageSrc: "/assets/images/home/<USER>/category-5.jpg" },
  { name: "Fruit", imageSrc: "/assets/images/home/<USER>/category-1.jpg" },
  {
    name: "Bakery",
    imageSrc: "/assets/images/home/<USER>/category-2.jpg",
    productCount: 20,
  },
  { name: "Fish", imageSrc: "/assets/images/home/<USER>/category-3.jpg" },
  { name: "Milk", imageSrc: "/assets/images/home/<USER>/category-4.jpg" },
  { name: "Vegetables", imageSrc: "/assets/images/home/<USER>/category-5.jpg" },
];

export const categorys6 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    imgAlt: "",
    category: "SQUARE",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    imgAlt: "",
    category: "AVIATOR",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    imgAlt: "",
    category: "RECTANGULAR",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    imgAlt: "",
    category: "ROUND",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    imgAlt: "",
    category: "OVAL",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/category-6.jpg",
    imgAlt: "",
    category: "GEOMETRIC",
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    imgAlt: "",
    category: "SQUARE",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    imgAlt: "",
    category: "AVIATOR",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    imgAlt: "",
    category: "RECTANGULAR",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    imgAlt: "",
    category: "ROUND",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    imgAlt: "",
    category: "OVAL",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/category-6.jpg",
    imgAlt: "",
    category: "GEOMETRIC",
  },
];

export const categories7 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/m1.jpg",
    title: "Hospital Equipment",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/m2.jpg",
    title: "Blood Pressure",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/m3.jpg",
    title: "Accessories",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/m4.jpg",
    title: "Personal",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/m5.jpg",
    title: "Independent Living",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/m6.jpg",
    title: "Pharmacy",
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/m1.jpg",
    title: "Hospital Equipment",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/m2.jpg",
    title: "Blood Pressure",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/m3.jpg",
    title: "Accessories",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/m4.jpg",
    title: "Personal",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/m5.jpg",
    title: "Independent Living",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/m6.jpg",
    title: "Pharmacy",
  },
];

export const categories8 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    altText: "Lipstick",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    altText: "Foundation",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    altText: "Mascara",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    altText: "Skincare",
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    altText: "Lipstick",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    altText: "Foundation",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    altText: "Mascara",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    altText: "Skincare",
  },
];

export const categories9 = [
  {
    id: 1,
    name: "Break System",
    imgSrc: "/assets/images/home/<USER>/category-1.png",
  },
  {
    id: 2,
    name: "Damping",
    imgSrc: "/assets/images/home/<USER>/category-2.png",
  },
  { id: 3, name: "Body", imgSrc: "/assets/images/home/<USER>/category-3.png" },
  {
    id: 4,
    name: "Engine",
    imgSrc: "/assets/images/home/<USER>/category-4.png",
  },
  {
    id: 5,
    name: "Filters",
    imgSrc: "/assets/images/home/<USER>/category-5.png",
  },
  { id: 6, name: "Oils", imgSrc: "/assets/images/home/<USER>/category-1.png" },
  {
    id: 1,
    name: "Break System",
    imgSrc: "/assets/images/home/<USER>/category-1.png",
  },
  {
    id: 2,
    name: "Damping",
    imgSrc: "/assets/images/home/<USER>/category-2.png",
  },
  { id: 3, name: "Body", imgSrc: "/assets/images/home/<USER>/category-3.png" },
  {
    id: 4,
    name: "Engine",
    imgSrc: "/assets/images/home/<USER>/category-4.png",
  },
  {
    id: 5,
    name: "Filters",
    imgSrc: "/assets/images/home/<USER>/category-5.png",
  },
  { id: 6, name: "Oils", imgSrc: "/assets/images/home/<USER>/category-1.png" },
];

export const categories10 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    name: "Bracelets",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    name: "Earrings",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    name: "Rings",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    name: "Charms",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    name: "Necklaces",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/category-6.jpg",
    name: "Brooches",
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    name: "Bracelets",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    name: "Earrings",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    name: "Rings",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    name: "Charms",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    name: "Necklaces",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/category-6.jpg",
    name: "Brooches",
  },
  // Add more categories as needed
];

export const categoriesData11 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    title: "Bikes",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    title: "Car Seats",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    title: "Diapers",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    title: "Skin Care",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    title: "Clothing",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/category-6.jpg",
    title: "Toys",
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    title: "Bikes",
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    title: "Car Seats",
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    title: "Diapers",
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    title: "Skin Care",
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    title: "Clothing",
  },
  {
    id: 6,
    imgSrc: "/assets/images/home/<USER>/category-6.jpg",
    title: "Toys",
  },
];

export const categoryData12 = [
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    title: "Power Tools",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    title: "Hand Tools",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    title: "Lawn & Garden",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    title: "Tool Storage",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    title: "Kitchen Tools",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 1,
    imgSrc: "/assets/images/home/<USER>/category-1.jpg",
    title: "Power Tools",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 2,
    imgSrc: "/assets/images/home/<USER>/category-2.jpg",
    title: "Hand Tools",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 3,
    imgSrc: "/assets/images/home/<USER>/category-3.jpg",
    title: "Lawn & Garden",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 4,
    imgSrc: "/assets/images/home/<USER>/category-4.jpg",
    title: "Tool Storage",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  {
    id: 5,
    imgSrc: "/assets/images/home/<USER>/category-5.jpg",
    title: "Kitchen Tools",
    products: [
      "Drills & Drivers",
      "Saws",
      "Combo Kits",
      "Rotary & Oscillating Tools",
    ],
  },
  // Add more category objects as needed
];

export const shopCategories = [
  {
    id: 1,
    imgSrc: "/assets/images/shop/category-item1.png",
    imgAlt: "Women Tops",
    category: "Women Tops",
  },
  {
    id: 2,
    imgSrc: "/assets/images/shop/category-item2.png",
    imgAlt: "Women Pants",
    category: "Women Pants",
  },
  {
    id: 3,
    imgSrc: "/assets/images/shop/category-item3.png",
    imgAlt: "Women Clothes",
    category: "Women Clothes",
  },
  {
    id: 4,
    imgSrc: "/assets/images/shop/category-item4.png",
    imgAlt: "Men Jeans",
    category: "Men Jeans",
  },
  {
    id: 5,
    imgSrc: "/assets/images/shop/category-item5.png",
    imgAlt: "Men Shirts",
    category: "Men Shirts",
  },
  {
    id: 6,
    imgSrc: "/assets/images/shop/category-item6.png",
    imgAlt: "Men Shoes",
    category: "Men Shoes",
  },
  {
    id: 7,
    imgSrc: "/assets/images/shop/category-item7.png",
    imgAlt: "Women Dresses",
    category: "Women Dresses",
  },
  {
    id: 8,
    imgSrc: "/assets/images/shop/category-item8.png",
    imgAlt: "Kids Tops",
    category: "Kids Tops",
  },
];
