export const footerLinks1 = [
  { key: 'home.page', href: '/', text: 'Trang chủ' },
  { key: 'product', href: '/shop-1', text: '<PERSON><PERSON>n phẩm' },
  { key: 'home.blog', href: '/blog_list1', text: 'Bài viết' }
]
export const footerLinks2 = [
  { key: 'policy.exchangePolicy', href: '#', text: 'Chính sách đổi trả' },
  { key: 'policy.paymentMethods', href: '#', text: 'Phương thức thanh toán' },
  { key: 'policy.deliveryPolicy', href: '#', text: '<PERSON><PERSON>h sách giao hàng' },
  {
    key: 'policy.exchangeWarrrantyPolicy',
    href: '/shop-5',
    text: '<PERSON><PERSON>h sách bảo hành sản phẩm'
  },
  {
    key: 'policy.informationSecurityPolicy',
    href: '/shop-1',
    text: '<PERSON><PERSON><PERSON> sách bảo mật thông tin'
  },
  { key: 'policy.shoppingGuide', href: '/shop-1', text: 'Hướng dẫn mua hàng' }
]
export const footerLinks3 = [
  { key: 'review.page', href: '/about', text: 'Đánh giá' },
  { key: 'tips.page', href: '/account_dashboard', text: 'Mẹo' }
]

export const languageOptions = [
  { value: 'vi', text: 'Việt Nam', selected: true },
  { value: 'en', text: 'English' }
]

export const languageOptions2 = [
  { value: 'english', text: 'English', selected: true },
  { value: 'german', text: 'German' },
  { value: 'french', text: 'French' },
  { value: 'swedish', text: 'Swedish' }
]

export const currencyOptions = [
  { value: 'en', text: 'USD', selected: true },
  { value: 'vi', text: 'VNĐ' }
]

export const socialLinks = [
  {
    href: 'https://facebook.com',
    className: 'svg-icon svg-icon_facebook',
    width: 9,
    height: 15,
    viewBox: '0 0 9 15',
    icon: '#icon_facebook'
  },
  {
    href: 'https://twitter.com',
    className: 'svg-icon svg-icon_twitter',
    width: 14,
    height: 13,
    viewBox: '0 0 14 13',
    icon: '#icon_twitter'
  },
  {
    href: 'https://instagram.com',
    className: 'svg-icon svg-icon_instagram',
    width: 14,
    height: 13,
    viewBox: '0 0 14 13',
    icon: '#icon_instagram'
  },
  {
    href: 'https://youtube.com',
    className: 'svg-icon svg-icon_youtube',
    width: 16,
    height: 11,
    viewBox: '0 0 16 11',
    icon: (
      <path d='M15.0117 1.8584C14.8477 1.20215 14.3281 0.682617 13.6992 0.518555C12.5234 0.19043 7.875 0.19043 7.875 0.19043C7.875 0.19043 3.19922 0.19043 2.02344 0.518555C1.39453 0.682617 0.875 1.20215 0.710938 1.8584C0.382812 3.00684 0.382812 5.46777 0.382812 5.46777C0.382812 5.46777 0.382812 7.90137 0.710938 9.07715C0.875 9.7334 1.39453 10.2256 2.02344 10.3896C3.19922 10.6904 7.875 10.6904 7.875 10.6904C7.875 10.6904 12.5234 10.6904 13.6992 10.3896C14.3281 10.2256 14.8477 9.7334 15.0117 9.07715C15.3398 7.90137 15.3398 5.46777 15.3398 5.46777C15.3398 5.46777 15.3398 3.00684 15.0117 1.8584ZM6.34375 7.68262V3.25293L10.2266 5.46777L6.34375 7.68262Z' />
    )
  },
  {
    href: 'https://pinterest.com',
    className: 'svg-icon svg-icon_pinterest',
    width: 14,
    height: 15,
    viewBox: '0 0 14 15',
    icon: '#icon_pinterest'
  }
]

export const socialLink = {
  facebook: {
    href: 'https://facebook.com',
    className: 'svg-icon svg-icon_facebook',
    width: 9,
    height: 15,
    viewBox: '0 0 9 15',
    icon: '#icon_facebook'
  },
  instagram: {
    href: 'https://instagram.com',
    className: 'svg-icon svg-icon_instagram',
    width: 14,
    height: 13,
    viewBox: '0 0 14 13',
    icon: '#icon_instagram'
  },
  youtube: {
    href: 'https://youtube.com',
    className: 'svg-icon svg-icon_youtube',
    width: 16,
    height: 11,
    viewBox: '0 0 16 10',
    icon: (
      <path d='M15.0117 1.8584C14.8477 1.20215 14.3281 0.682617 13.6992 0.518555C12.5234 0.19043 7.875 0.19043 7.875 0.19043C7.875 0.19043 3.19922 0.19043 2.02344 0.518555C1.39453 0.682617 0.875 1.20215 0.710938 1.8584C0.382812 3.00684 0.382812 5.46777 0.382812 5.46777C0.382812 5.46777 0.382812 7.90137 0.710938 9.07715C0.875 9.7334 1.39453 10.2256 2.02344 10.3896C3.19922 10.6904 7.875 10.6904 7.875 10.6904C7.875 10.6904 12.5234 10.6904 13.6992 10.3896C14.3281 10.2256 14.8477 9.7334 15.0117 9.07715C15.3398 7.90137 15.3398 5.46777 15.3398 5.46777C15.3398 5.46777 15.3398 3.00684 15.0117 1.8584ZM6.34375 7.68262V3.25293L10.2266 5.46777L6.34375 7.68262Z' />
    )
  },
  tiktok: {
    href: 'https://tiktok.com',
    className: 'svg-icon svg-icon_tiktok',
    width: 14,
    height: 15,
    viewBox: '0 0 14 15',
    icon: (
      <path d='M9.426 1.018c1.049.803 2.453 1.324 3.894 1.337v2.075c-1.417.002-2.746-.37-3.894-1.034v6.405c0 2.29-1.755 4.157-3.917 4.157C2.745 13.958 1 12.092 1 9.801c0-2.293 1.745-4.158 3.917-4.158.347 0 .684.048 1.002.136v2.16c-.31-.127-.646-.198-.999-.198-1.144 0-2.073.961-2.073 2.156s.93 2.156 2.073 2.156c1.144 0 2.073-.962 2.073-2.156V0h2.432c.02.337.076.668.159.994z' />
    )
  }
}
