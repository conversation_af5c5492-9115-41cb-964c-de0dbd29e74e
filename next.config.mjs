import { withSentryConfig } from '@sentry/nextjs'
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
        port: '', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'http',
        hostname: 'res.cloudinary.com',
        port: '', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'https',
        hostname: 'www.instagram.com',
        port: '', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'https',
        hostname: 'sapo.dktcdn.net',
        port: '', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'http',
        hostname: '*************',
        port: '4005', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'http',
        hostname: '**************',
        port: '4005', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'https',
        hostname: 'api.shapewearbywstore.com',
        port: '', // leave empty for default port
        pathname: '/**' // This will allow all paths
      },
      {
        protocol: 'https',
        hostname: 'scontent.cdninstagram.com',
        port: '', // leave empty for default port
        pathname: '/**' // This will allow all paths
      }
    ]
  }
}

export default withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: 'chi-trung',
  project: 'shapewearbywstore',

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  //tunnelRoute: "/monitoring", remove because POST /en/monitoring?o=4504487793262592&p=4509379571744768&r=us 404

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true
})
