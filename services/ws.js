const getHeader = () => {
  const headers = new Headers()

  headers.append('Content-Type', 'application/json')

  return headers
}

const getHeaderUpload = () => {
  const headers = new Headers()

  return headers
}

const getBaseUrl = (url = '') => {
  return [process.env.NEXT_PUBLIC_APP_API_URL, url].join('/')
}

const catchError = error => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  console.log(error)

  switch (error.response.status) {
    case 400:
      /* Handle */ break

    case 401: {
      break
    }

    case 404:
      /* Handle */ break
    case 500:
      /* Handle */ break
  }
}

const buildQueryString = params => {
  return Object.keys(params)
    .map(key => {
      const value = params[key]

      if (value == null) return '' // Skip null or undefined values

      // Handle array by joining values with a comma or return string directly
      if (Array.isArray(value)) {
        return value.map(v => `${encodeURIComponent(key)}[]=${encodeURIComponent(v)}`).join('&')
      } else {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
      }
    })
    .filter(Boolean) // Remove empty strings
    .join('&')
}

const handleResponse = async response => {
  if (response.status === 401) {
    return Promise.reject('Unauthorized')
  }

  if (!response.ok) {
    const error = await response.json()

    return Promise.reject(error.message || 'Something went wrong')
  }

  return response.json()
}

export const apiService = {
  get: async (url, params, headers) => {
    try {
      const queryString = params ? buildQueryString(params) : ''

      const response = await fetch(getBaseUrl(`${queryString ? `${url}?${queryString}` : url}`), {
        method: 'GET',
        headers: Object.assign(getHeader(), headers)
      })

      return handleResponse(response)
    } catch (error) {
      catchError(error)

      return error
    }
  },
  post: async (url, body, headers) => {
    try {
      const response = await fetch(getBaseUrl(url), {
        method: 'POST',
        body: JSON.stringify(body),
        headers: Object.assign(getHeader(), headers)
      })

      return handleResponse(response)
    } catch (error) {
      catchError(error)

      return error
    }
  },
  put: async (url, body, headers) => {
    try {
      const response = await fetch(getBaseUrl(url), {
        method: 'PUT',
        body: JSON.stringify(body),
        headers: Object.assign(getHeader(), headers)
      })

      return handleResponse(response)
    } catch (error) {
      catchError(error)

      return error
    }
  },
  delete: async (url, body, headers) => {
    try {
      const response = await fetch(getBaseUrl(url), {
        method: 'DELETE',
        body: JSON.stringify(body),
        headers: Object.assign(getHeader(), headers)
      })

      return handleResponse(response)
    } catch (error) {
      catchError(error)

      return error
    }
  },
  upload: async (url, body, headers) => {
    try {
      const response = await fetch(getBaseUrl(url), {
        method: 'POST',
        headers: Object.assign(getHeaderUpload(), headers),
        body: body
      })

      return handleResponse(response)
    } catch (error) {
      catchError(error)

      return error
    }
  }
}
