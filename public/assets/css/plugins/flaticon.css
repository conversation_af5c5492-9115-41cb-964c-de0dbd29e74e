/*
  Flaticon icon font: Flaticon
  Creation date: 02/11/2020 14:09
*/

/* body {
  font-family: 'Be Vietnam Pro', sans-serif;
} */

body {
  font-family: 'Roboto', sans-serif;
}

/* @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff2") format("woff2"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

.flaticon {
  font-family: Flaticon;
  font-size: 21px;
  font-style: normal;
  line-height: 1;
}

.flaticon-loupe:before { content: "\f100"; }
.flaticon-user:before { content: "\f101"; }
.flaticon-shopping-bag:before { content: "\f102"; }
.flaticon-heart:before { content: "\f103"; }
.flaticon-shipping:before { content: "\f104"; }
.flaticon-headphone:before { content: "\f105"; }
.flaticon-shield:before { content: "\f106"; }
.flaticon-next:before { content: "\f107"; }
.flaticon-back:before { content: "\f108"; }
.flaticon-upload:before { content: "\f109"; }
.flaticon-download:before { content: "\f10a"; }
.flaticon-view:before { content: "\f10b"; }
.flaticon-handbag:before { content: "\f10c"; }
.flaticon-clothes-hanger:before { content: "\f10d"; }
.flaticon-wristwatch:before { content: "\f10e"; }
.flaticon-wedding-dress:before { content: "\f10f"; }
.flaticon-shoes:before { content: "\f110"; }
.flaticon-trousers:before { content: "\f111"; }
.flaticon-tshirt:before { content: "\f112"; }
.flaticon-hoodie:before { content: "\f113"; }
.flaticon-play:before { content: "\f114"; }
.flaticon-gift:before { content: "\f115"; }
.flaticon-filter-results-button:before { content: "\f116"; }
.flaticon-sharing:before { content: "\f117"; }
.flaticon-placeholder:before { content: "\f118"; }
.flaticon-hanger:before { content: "\f119"; }
.flaticon-home:before { content: "\f11a"; }
.flaticon-360-degrees:before { content: "\f11b"; }
.flaticon-check:before { content: "\f11c"; } */
