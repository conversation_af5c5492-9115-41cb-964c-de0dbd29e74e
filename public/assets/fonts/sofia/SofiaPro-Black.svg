<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Mon Dec 14 13:22:29 2020
 By <PERSON><PERSON><PERSON>,,,
Copyright (c) Olivier <PERSON> - Mostardesign Studio, 2012. All rights reserved.
</metadata>
<defs>
<font id="SofiaPro-Black" horiz-adv-x="1187" >
  <font-face 
    font-family="Sofia Pro Black"
    font-weight="900"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 0 0 0 0 0 0 0 0"
    ascent="1536"
    descent="-512"
    x-height="954"
    cap-height="1411"
    bbox="-381 -618 2472 2331"
    underline-thickness="102"
    underline-position="-307"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="1632" 
d="M1528 1456h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h200v-239h-200v-715h-326v715h-524v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h524v35q0 225 121 346t338 121z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1490" 
d="M1044 0v715h-499v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h538h287v-954h-326zM1010 1272q0 77 58 130.5t138 53.5q82 0 138.5 -53t56.5 -131t-56.5 -132.5t-138.5 -54.5q-79 0 -137.5 56t-58.5 131z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1458" 
d="M1010 0v1147h-332q-61 0 -96.5 -40.5t-36.5 -117.5v-35h200v-239h-200v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h657v-1456h-325z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="2340" 
d="M2220 0h-326v715h-499v-715h-326v715h-524v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h524v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h825v-954zM1860 1272
q0 77 58 130.5t138 53.5q82 0 138.5 -53t56.5 -131t-56.5 -132.5t-138.5 -54.5q-79 0 -137.5 56t-58.5 131z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="2308" 
d="M2185 0h-325v1147h-332q-61 0 -96.5 -40.5t-36.5 -117.5v-35h200v-239h-200v-715h-326v715h-524v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h524v35q0 225 121 346t338 121h657v-1456z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="2295" 
d="M1716 -485h-123v309h123q61 0 96.5 40.5t36.5 117.5v733h-454v-715h-326v715h-524v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h524v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5
t-36.5 -117.5v-35h780v-972q0 -225 -121 -346t-338 -121zM1817 1272q0 77 58 130.5t138 53.5q82 0 138.5 -53t56.5 -131t-56.5 -132.5t-138.5 -54.5q-79 0 -137.5 56t-58.5 131z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="1445" 
d="M866 -485h-123v309h123q61 0 96.5 40.5t36.5 117.5v733h-454v-715h-326v715h-178v239h178v35q0 225 121 346t338 121h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h780v-972q0 -225 -121 -346t-338 -121zM967 1272q0 77 58 130.5t138 53.5q82 0 138.5 -53
t56.5 -131t-56.5 -132.5t-138.5 -54.5q-79 0 -137.5 56t-58.5 131z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="487" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="604" 
d="M436 473h-268l-37 938h342zM106 172q0 79 56.5 132.5t136.5 53.5q82 0 139.5 -53.5t57.5 -132.5q0 -81 -57.5 -135.5t-139.5 -54.5q-80 0 -136.5 54.5t-56.5 135.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="847" 
d="M377 1411l-41 -616h-182l-41 616h264zM735 1411l-41 -616h-182l-41 616h264z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1538" 
d="M813 522l111 338h-205l-109 -338h203zM1094 1411h243l-112 -356h243l-55 -195h-244l-108 -338h244l-60 -194h-241l-111 -328h-242l107 328h-205l-109 -328h-241l110 328h-241l55 194h240l106 338h-237l57 195h239l109 356h248l-111 -356h203z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1196" 
d="M520 1425v224h158v-226q182 -25 289.5 -139.5t107.5 -294.5h-342q0 131 -147 131q-69 0 -109.5 -30.5t-40.5 -81.5q0 -75 131 -115l162 -51q391 -120 391 -430q0 -191 -129 -299q-128 -107 -313 -125v-246h-158v244q-205 20 -323.5 136t-118.5 298h346q5 -64 48.5 -100.5
t125.5 -36.5q75 0 124.5 34t49.5 93q0 14 -4 30q-6 15 -43.5 41.5t-93.5 44.5l-166 56q-364 99 -369 415q0 174 117 291q119 116 307 137z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1431" 
d="M952 1343h195l-676 -1343h-192zM618 1096q0 -116 -79 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q79 -79 79 -192zM442 1096q0 39 -28.5 68.5t-69.5 29.5t-68.5 -28.5t-27.5 -69.5t27.5 -69t68.5 -28t69.5 28
t28.5 69zM891 59q-78 81 -78 195t78 195q79 79 194 79q116 0 195 -79q80 -80 80 -195t-80 -195q-80 -77 -195 -77q-114 0 -194 77zM1016 324q-26 -29 -26 -70t26 -70q29 -26 69.5 -26t69.5 26q29 29 29 70t-29 70t-69.5 29t-69.5 -29z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1452" 
d="M547 -20q-204 0 -322 112q-121 115 -121 295q0 131 68 222.5t176 121.5q-89 17 -149 105.5t-60 205.5q0 156 119 263q119 110 309 122q268 18 430 -174l-245 -196q-46 55 -107 55q-65 0 -104 -37t-39 -82q0 -32 18 -78q29 -63 144 -63h184v145h301v-155h176v-240h-176
v-170q0 -76 10 -120t35 -65.5t66.5 -14.5t105.5 40v-241q-180 -69 -304 -35t-159 168q-47 -83 -151 -135q-104 -49 -205 -49zM848 547v35h-195q-79 0 -128.5 -43t-51.5 -101q0 -57 49.5 -99t130.5 -42q17 0 38.5 5t51 21t52.5 42t39 73.5t14 108.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="489" 
d="M377 1411l-41 -616h-182l-41 616h264z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="815" 
d="M70 463q0 407 198 684q200 280 486 280v-237q-92 0 -191 -111q-100 -109 -164 -280q-63 -166 -63 -336q0 -167 63 -330q61 -158 162 -270q99 -105 193 -105v-235q-286 0 -486 270q-198 265 -198 670z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="815" 
d="M745 463q0 -405 -198 -670q-199 -270 -486 -270v235q97 0 191 105q97 103 162 270q65 168 65 330q0 165 -65 336q-65 174 -162 280q-99 111 -191 111v237q284 0 484 -280t200 -684z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="966" 
d="M487 922l-155 -226l-156 119l168 207l-258 74l64 190l247 -98l-16 268h205l-19 -266l252 96l60 -190l-256 -70l170 -213l-160 -113z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1030" 
d="M936 592v-221h-311v-312h-222v312h-309v221h309v309h222v-309h311z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="563" 
d="M162 -252l-168 82q85 118 121 215q39 98 39 279h288q0 -338 -280 -576z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="964" 
d="M846 635v-277h-727v277h727z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="589" 
d="M100 172q0 79 56.5 132.5t136.5 53.5q82 0 139 -53.5t57 -132.5q0 -81 -57 -135.5t-139 -54.5q-80 0 -136.5 54.5t-56.5 135.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="1026" 
d="M297 0h-238l670 1411h238z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1236" 
d="M440 670q0 -172 46 -272.5t137 -100.5q174 0 174 373q0 377 -174 377q-91 0 -137 -101.5t-46 -275.5zM113 670q0 694 510 694q140 0 241.5 -52t155.5 -151q104 -184 104 -491q0 -688 -501 -688q-510 0 -510 688z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="811" 
d="M74 807v280l569 287v-1374h-334v913z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1163" 
d="M606 295h436v-295h-944v184l555 643q62 71 62 123q0 46 -36.5 77.5t-105.5 31.5q-78 0 -117 -45.5t-53 -136.5l-294 45q32 208 160 325t329 117q181 0 313 -117q131 -116 131 -297q0 -182 -139 -336z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1124" 
d="M498 569h-138v252h138q46 0 78.5 18t44.5 48q18 46 18 84q0 33 -37 76q-33 45 -102 45q-102 0 -183 -84l-192 157q64 98 179.5 152t250.5 47q180 -9 295 -121q116 -110 113 -258q0 -165 -177 -260q110 -41 173 -127.5t63 -196.5q0 -191 -152 -307q-149 -110 -366 -110
q-148 0 -263.5 65t-170.5 182l209 185q28 -62 86.5 -105t138.5 -43q93 0 143.5 44t50.5 110q0 64 -52.5 105.5t-147.5 41.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1290" 
d="M1032 580h160v-269h-160v-311h-332v311h-661l579 1032h414v-763zM700 580v448l-239 -448h239z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1236" 
d="M819 489q0 87 -61.5 138t-161.5 51q-192 0 -332 -143l-141 79l106 729h815v-284h-557l-28 -182q107 73 248 73q182 0 309 -127t127 -334q0 -220 -158 -366q-155 -143 -416 -143q-141 0 -270 69q-133 71 -217 187l254 184q110 -131 233 -131q107 0 176 63q74 65 74 137z
" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1224" 
d="M711 862q127 0 264 -90q70 -46 112.5 -134t42.5 -198q0 -202 -135 -331q-136 -127 -379 -127q-193 0 -329 127q-135 129 -162 342q-12 92 -14 181.5t9.5 182t36 173.5t69.5 152t106.5 121.5t150.5 78t197 24.5q151 -6 255.5 -66t170.5 -196l-266 -127q-49 104 -170 104
q-71 0 -122.5 -35t-78.5 -96q-49 -119 -49 -235q14 56 92.5 102.5t198.5 46.5zM467 434q0 -69 47 -115.5t121 -46.5t123 47t49 115q0 63 -48.5 109.5t-123.5 46.5q-70 0 -119 -47t-49 -109z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1124" 
d="M612 1047h-530v296h1016l-537 -1343h-368z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1251" 
d="M432 451q0 -76 55 -126.5t142 -50.5q86 0 140 50.5t54 126.5q0 69 -53.5 120.5t-130.5 51.5h-16q-83 0 -137 -50.5t-54 -121.5zM184 997q0 159 131 263t314 104q180 0 305 -104q125 -101 125 -263q0 -78 -32.5 -147t-86.5 -94q90 -27 154 -129q63 -100 63 -209
q0 -193 -153 -316q-153 -120 -375 -120q-221 0 -377 120q-154 124 -154 316q0 117 56 210t153 128q-55 35 -89 103t-34 138zM492 985q0 -54 38 -88.5t99 -34.5q60 0 96.5 34.5t36.5 88.5q0 46 -36.5 82.5t-88.5 36.5h-12q-56 0 -94.5 -35.5t-38.5 -83.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1224" 
d="M516 483q-127 0 -264 90q-70 46 -113 134.5t-43 197.5q0 204 135 330q135 129 379 129q195 0 328 -127q137 -128 164 -342q12 -92 14 -181.5t-9.5 -182t-36 -173.5t-69.5 -152t-106.5 -121.5t-150.5 -78t-197 -24.5q-151 6 -255.5 66t-170.5 196l266 127
q50 -105 170 -105q71 0 122.5 35t76.5 96q51 117 51 236q-14 -56 -93 -103t-198 -47zM760 911q0 69 -47 115.5t-121 46.5t-123 -47t-49 -115q0 -62 48.5 -108.5t123.5 -46.5q70 0 119 47t49 108z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="632" 
d="M123 172q0 79 56 132.5t136 53.5t136.5 -53.5t56.5 -132.5q0 -81 -56.5 -135.5t-136.5 -54.5t-136 54.5t-56 135.5zM123 786q0 81 56 136t136 55t136.5 -55t56.5 -136q0 -79 -56.5 -132.5t-136.5 -53.5t-136 53.5t-56 132.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="614" 
d="M125 786q0 81 56 136t136 55t136.5 -55t56.5 -136q0 -79 -56.5 -132.5t-136.5 -53.5t-136 53.5t-56 132.5zM188 -252l-168 82q85 118 121 215q39 98 39 279h289q0 -337 -281 -576z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1261" 
d="M1112 360v-237l-1024 393v250l1024 391v-235l-723 -283z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1212" 
d="M1077 338h-942v209h942v-209zM135 965h942v-209h-942v209z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1261" 
d="M1174 766v-250l-1024 -393v237l722 279l-722 283v235z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1015" 
d="M573 473h-292v25q0 78 5.5 121t24.5 94q38 102 183 164l75 32q70 27 70 103q0 53 -36 81.5t-99 28.5q-55 0 -93 -37.5t-38 -93.5h-295q0 211 115.5 326t316.5 115q192 0 313 -109q119 -107 119 -291q0 -144 -68.5 -243t-193.5 -144q-61 -23 -84 -60.5t-23 -111.5z
M238 172q0 79 56 132.5t136 53.5q82 0 139.5 -53.5t57.5 -132.5q0 -81 -57.5 -135.5t-139.5 -54.5q-80 0 -136 54.5t-56 135.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1972" 
d="M1182 307v256q0 97 -68 162q-65 68 -160 68q-91 0 -159 -68q-68 -65 -68 -162q0 -89 68 -157t159 -68q90 0 146 33v-201q-52 -27 -146 -27q-175 0 -301 123q-123 123 -123 297q0 178 123 301q126 123 301 123t297 -119q123 -117 129 -284v-287q0 -58 34 -91.5t81 -33.5
q59 0 102 41.5t64 112.5q39 133 39 264q0 283 -213 475q-210 192 -512 192q-290 0 -490 -206q-198 -204 -198 -498t198 -492q200 -200 490 -200h125v-199h-125q-374 0 -629 258q-256 259 -256 633q0 375 256 637q258 264 629 264q120 3 238.5 -26.5t220.5 -84.5t189 -135.5
t148 -176t95 -209.5t32 -232q0 -207 -88 -393q-46 -97 -129 -155t-186 -58q-142 0 -227.5 81t-85.5 242z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1366" 
d="M1389 0h-363l-119 324h-444l-119 -324h-367l521 1411h372zM819 618l-137 465l-127 -465h264z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1318" 
d="M1167 1032q0 -81 -47 -164q-48 -84 -151 -114q133 -21 202 -121q70 -101 70 -215q0 -175 -113 -297q-115 -121 -352 -121h-618v1411h577q198 0 314 -104q118 -103 118 -275zM502 1112v-246h184q54 0 86.5 31.5t32.5 85.5q0 129 -115 129h-188zM719 586h-217v-287h219
q73 0 115.5 41.5t42.5 103.5q0 59 -42 100.5t-118 41.5z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1447" 
d="M1116 502l281 -197q-99 -150 -258 -236.5t-342 -86.5q-297 0 -508 211t-211 512q0 300 209 512q213 213 510 213q184 0 343.5 -86.5t258.5 -237.5l-285 -199q-50 82 -134.5 130t-182.5 48q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269q107 -110 265 -110
q99 0 183.5 47t135.5 129z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1443" 
d="M717 1106h-215v-801h215q142 0 219 115q80 118 80 282q0 167 -80 285q-76 119 -219 119zM717 0h-559v1411h559q295 0 469 -201q172 -199 172 -508q0 -306 -172 -505q-170 -197 -469 -197z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1189" 
d="M1092 299v-299h-934v1411h915v-299h-573v-270h504v-293h-504v-250h592z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1116" 
d="M504 547v-547h-346v1411h907v-299h-561v-270h444v-295h-444z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1548" 
d="M1356 1180l-254 -215q-120 147 -297 147q-160 0 -270 -119q-111 -120 -111 -288t111 -291q112 -121 270 -121q276 0 323 188h-395v275h768q0 -388 -188 -582q-186 -192 -508 -192q-305 0 -516 211t-211 512t211 512q213 213 516 213q166 0 311.5 -66t239.5 -184z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1435" 
d="M500 854h430v557h348v-1411h-348v549h-430v-549h-342v1411h342v-557z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="657" 
d="M500 0h-342v1411h342v-1411z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="661" 
d="M-199 -432l31 307q73 -25 197 -25q64 0 96.5 44t32.5 120v1397h346v-1397q0 -215 -117 -350q-114 -135 -358 -135q-118 0 -228 39z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1296" 
d="M1208 1411l-530 -696l659 -715h-469l-368 430v-430h-342v1411h342v-403l280 403h428z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1060" 
d="M1012 299v-299h-854v1411h346v-1112h508z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1869" 
d="M539 1411l397 -858l391 858h289l172 -1411h-352l-86 760l-285 -596h-258l-287 596l-88 -760h-352l174 1411h285z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1388" 
d="M889 651v760h342v-1411h-299l-432 770v-770h-342v1411h309z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1265" 
d="M500 471v-471h-342v1411h583q232 0 359 -139q125 -137 125 -332t-125 -332t-359 -137h-241zM725 1112h-225v-346h225q97 -5 137 84.5t0 178.5t-137 83z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1609" 
d="M1237 121l131 -221l-238 -142l-147 244q-79 -20 -178 -20q-305 0 -516 211t-211 512t211 512q213 213 516 213q305 0 516 -211q213 -213 213 -514q0 -171 -79.5 -325.5t-217.5 -258.5zM700 475l238 139l125 -204q125 113 125 295q0 168 -113 284q-113 119 -270 119
q-162 0 -273 -117q-108 -117 -108 -286q0 -133 74 -237t196 -142q34 -15 107 -21z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1314" 
d="M500 471v-471h-342v1411h583q232 0 359 -139q125 -137 125 -332q0 -162 -88 -287q-85 -125 -250 -166l344 -487h-397l-304 471h-30zM725 1112h-225v-346h225q97 -5 137 84.5t0 178.5t-137 83z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1200" 
d="M82 420h346q5 -64 48.5 -100.5t125.5 -36.5q75 0 124.5 34t49.5 93q0 14 -4 30q-6 15 -43.5 41.5t-93.5 44.5l-166 56q-364 99 -369 415q0 190 140 312q139 121 354 121q216 0 350 -119q135 -117 135 -322h-342q0 131 -147 131q-69 0 -109.5 -30.5t-40.5 -81.5
q0 -75 131 -115l162 -51q391 -120 391 -430q0 -146 -84 -250q-153 -180 -438 -180q-240 0 -379 118q-141 120 -141 320z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1181" 
d="M420 0v1112h-365v299h1071v-299h-364v-1112h-342z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1396" 
d="M141 514v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1212" 
d="M756 0h-301l-430 1411h356l225 -860l221 860h361z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1808" 
d="M762 1208h285l157 -600l246 803h373l-480 -1411h-264l-178 686l-172 -686h-266l-475 1411h370l244 -803z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1386" 
d="M694 1020l240 391h409l-446 -661l487 -750h-403l-287 477l-288 -477h-402l485 750l-446 661h414z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1357" 
d="M850 629v-629h-344v635l-551 776h407l314 -512l319 512h406z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1232" 
d="M84 0v252l604 862h-577v297h1026v-252l-613 -860h613v-299h-1053z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="700" 
d="M637 -471h-498v1882h498v-221h-234v-1434h234v-227z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="1026" 
d="M59 1411h238l670 -1411h-238z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="700" 
d="M63 1190v221h498v-1882h-498v227h234v1434h-234z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1560" 
d="M1444 -70v-219h-1327v219h1327z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="1120" 
d="M547 1124l-283 357h303l191 -357h-211z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1245" 
d="M797 852v102h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60
z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1247" 
d="M123 1456h326v-590q101 113 264 113q186 0 325 -139q140 -140 140 -361q0 -220 -140 -360q-137 -137 -325 -137q-92 0 -169 40t-114 115v-137h-307v1456zM786 336q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57
t138.5 57z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1077" 
d="M748 369l272 -152q-155 -235 -422 -235q-222 0 -377 137q-153 138 -153 360q0 224 155 361q157 139 375 139q265 0 420 -236l-266 -159q-58 94 -168 94q-82 0 -140.5 -58.5t-58.5 -140.5t58.5 -140t140.5 -58q103 0 164 88z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1245" 
d="M797 866v590h325v-1456h-307v137q-37 -75 -114 -115t-169 -40q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q164 0 265 -113zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1126" 
d="M1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-19 73 -73 107.5
t-118 34.5q-33 0 -67 -9q-37 -9 -75 -45.5t-48 -87.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="782" 
d="M678 1456h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-35h200v-239h-200v-715h-326v715h-178v239h178v35q0 225 121 346t338 121z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1245" 
d="M457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60zM88 -172l274 111q25 -58 81.5 -92.5t129.5 -34.5q122 0 187 78t65 229q-76 -137 -293 -137q-188 0 -325 137q-139 139 -139 360q0 222 139 361
t325 139q78 0 150.5 -33.5t114.5 -93.5v102h325v-833q0 -295 -142 -450.5t-407 -155.5q-164 0 -295.5 86t-189.5 227z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1208" 
d="M449 502v-502h-326v1456h326v-651q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 74 -47.5 120t-108.5 46q-71 0 -119 -42t-48 -120z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="571" 
d="M123 954h326v-954h-326v954zM88 1272q0 77 58.5 130.5t138.5 53.5q82 0 138 -53t56 -131t-56 -132.5t-138 -54.5q-79 0 -138 56t-59 131z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="571" 
d="M-10 -485h-123v309h123q61 0 96.5 40.5t36.5 117.5v34v938h326v-938v-34q0 -225 -121 -346t-338 -121zM90 1272q0 77 58.5 130.5t138.5 53.5q82 0 138 -53t56 -131t-56 -132.5t-138 -54.5q-79 0 -138 56t-59 131z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1101" 
d="M1110 0h-389l-272 344v-344h-326v1456h326v-733l186 231h399l-352 -424z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="571" 
d="M449 0h-326v1456h326v-1456z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1810" 
d="M1075 502v-502h-327v498q0 82 -45 124t-111 42q-65 0 -104 -40t-39 -122v-502h-326v954h326v-149q21 81 96 129q71 45 147 45q262 0 342 -203q49 98 128 150.5t157 52.5q203 0 292 -115.5t89 -355.5v-508h-326v498q0 82 -44.5 124t-110.5 42q-65 0 -104.5 -40t-39.5 -122
z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1208" 
d="M449 502v-502h-326v954h326v-149q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 82 -45 124t-111 42q-71 0 -119 -41t-48 -121z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 618q-57 -57 -57 -138.5t57 -141.5q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1247" 
d="M449 88v-559h-326v1425h307v-137q37 77 115 119.5t168 42.5q188 0 325 -137q140 -140 140 -361q0 -222 -140 -362q-137 -137 -325 -137q-171 0 -264 106zM647 682q-81 0 -139 -61q-59 -62 -59 -144q-4 -60 33 -108t90 -69.5t113.5 -12t100.5 54.5q47 41 56.5 102.5
t-12.5 114.5t-72 90t-111 33z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1245" 
d="M1122 -471h-325v559q-93 -106 -265 -106q-188 0 -325 137q-139 139 -139 362q0 222 139 361q137 137 325 137q90 0 168 -42.5t115 -119.5v137h307v-1425zM797 477q0 81 -60 144q-58 61 -139 61q-61 4 -111 -33t-72 -90t-13 -114.5t55 -102.5q41 -45 102.5 -54.5t114.5 12
t90 69.5t33 108z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="878" 
d="M449 313v-313h-326v954h309v-235q34 125 116 192.5t185 67.5q83 0 141 -27l-79 -307q-70 25 -148 25q-103 0 -150.5 -80t-47.5 -277z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="985" 
d="M500 340l-121 33q-78 21 -139.5 56.5t-94.5 70.5q-32 36 -45 75q-3 11 -5.5 20.5t-4.5 16.5t-3 15.5t-1.5 12.5t-1 13.5t-0.5 11.5v14v15q0 111 115 197q116 88 274 88q80 0 151 -17.5t132.5 -54.5t98 -103.5t36.5 -154.5h-305q0 86 -97 86q-79 0 -79 -53q0 -46 71 -61
l170 -48q84 -23 143 -62t83 -85q41 -73 41 -141q0 -149 -132 -226q-133 -77 -301 -77q-162 0 -290 90q-125 91 -125 247h305q0 -40 29 -69t81 -29q42 0 71.5 17.5t29.5 46.5q0 20 -16.5 31t-69.5 24z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="774" 
d="M215 1296h322v-342h200v-239h-200v-715h-322v715h-174v239h174v342z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1083" 
d="M1087 954l-407 -954h-279l-405 954h350l197 -542l192 542h352z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1554" 
d="M1049 428l143 526h348l-328 -954h-276l-160 571l-151 -571h-277l-338 954h348l144 -526l145 526h256z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1114" 
d="M379 0h-375l377 516l-318 438h375l123 -180l117 180h373l-316 -438l373 -516h-373l-178 262z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1060" 
d="M535 492l178 462h346l-590 -1425h-342l240 598l-375 827h356z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1048" 
d="M485 659h-376v295h843v-237l-409 -422h409v-295h-870v233z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="847" 
d="M55 383v174q50 0 87.5 34t37.5 91v315q-3 183 119 299q124 115 299 115h190v-221h-163q-90 0 -135.5 -56t-45.5 -151v-278q0 -101 -61 -162q-59 -62 -125 -74q65 -9 125 -72q61 -61 61 -161v-279q0 -95 45.5 -151t135.5 -56h163v-221h-190q-175 0 -299 115
q-122 116 -119 299v315q0 57 -37.5 91t-87.5 34z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="436" 
d="M297 -258h-158v1907h158v-1907z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="847" 
d="M793 557v-174q-50 0 -87.5 -34t-37.5 -91v-315q3 -184 -121 -299q-121 -115 -297 -115h-191v221h164q90 0 135 56t45 151v279q0 98 60 161q63 63 127 72q-65 12 -127 74q-60 63 -60 162v278q0 95 -45 151t-135 56h-164v221h191q176 0 297 -115q124 -115 121 -299v-315
q0 -57 37.5 -91t87.5 -34z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="571" 
d="M457 -469h-342l37 938h268zM90 788q0 81 56.5 136t136.5 55q82 0 139 -55t57 -136q0 -79 -57 -132.5t-139 -53.5q-80 0 -136.5 53.5t-56.5 132.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1105" 
d="M502 969v256h157v-250q224 -21 367 -232l-266 -159q-58 94 -168 94q-82 0 -140.5 -58.5t-58.5 -140.5t58.5 -140t140.5 -58q103 0 164 88l272 -152q-143 -210 -369 -231v-244h-157v250q-185 30 -305 162q-121 133 -121 325q0 191 121 324q119 131 305 166z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1437" 
d="M840 737v-131h-205v-334h342q33 0 45 20t12 64v103h320v-176q0 -118 -86 -201q-82 -82 -197 -82h-993v272h229v334h-160v131h160v242q0 204 121 328q123 123 322 123q489 0 489 -494h-324q0 166 -145 166q-60 0 -97.5 -44.5t-37.5 -111.5v-209h205z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1261" 
d="M473 694q0 -66 46 -111.5t112 -45.5q67 0 113.5 45.5t46.5 111.5q0 68 -46.5 114t-113.5 46q-66 0 -112 -46.5t-46 -113.5zM412 336l-146 -148l-141 142l145 147q-59 95 -59 217q0 121 61 219l-147 148l141 141l148 -147q101 61 217 61q121 0 219 -61l145 147l142 -141
l-148 -150q64 -107 64 -217q0 -122 -62 -217l146 -147l-142 -142l-145 148q-98 -62 -219 -62t-219 62z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1568" 
d="M1102 1411h405l-520 -741h268v-144h-297v-202h297v-142h-297v-182h-348v182h-297v142h297v202h-297v144h271l-523 741h408l313 -512z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="444" 
d="M143 -258v782h158v-782h-158zM143 1649h158v-777h-158v777z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1138" 
d="M655 414v-279q-52 -10 -98 -10q-187 0 -319 123q-129 123 -129 321v418q0 197 129 320q132 123 319 123q157 0 291 -93q131 -91 158 -270l-269 -80q-19 81 -68.5 120.5t-111.5 39.5q-68 0 -116 -46t-48 -114v-418q0 -77 50.5 -118t131.5 -41q40 0 80 4zM483 590v278
q57 11 99 11q188 0 317 -123q131 -122 131 -322v-418q0 -197 -131 -319q-129 -123 -317 -123q-158 0 -291 92q-131 91 -158 271l268 79q19 -81 69 -120t112 -39q68 0 115.5 45.5t47.5 113.5v418q0 77 -50.5 118.5t-131.5 41.5q-40 0 -80 -4z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M246 1313q0 62 45.5 104.5t111.5 42.5q63 0 108.5 -43t45.5 -104q0 -63 -44 -106.5t-110 -43.5t-111.5 43.5t-45.5 106.5zM631 1313q0 61 47 104t113 43q63 0 107 -43t44 -104q0 -63 -44 -106.5t-107 -43.5q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1622" 
d="M1112 502q-50 -76 -131.5 -122t-171.5 -46q-152 0 -252 106q-102 108 -102 256q0 151 102 256q101 107 252 107q192 0 299 -162l-135 -94q-70 94 -164 94q-79 0 -130.5 -57t-51.5 -144q0 -83 51.5 -139.5t130.5 -56.5q96 0 166 94zM303 190q-207 213 -207 512t207 512
q210 213 508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210zM418 1100q-162 -162 -162 -398q0 -235 162 -397q164 -164 393 -164t393 164q162 162 162 397q0 236 -162 398q-163 166 -393 166t-393 -166z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="968" 
d="M821 647h-176v68q-82 -82 -194 -82q-145 0 -246 98q-101 101 -101 260q0 160 101 258q102 99 246 99q110 0 194 -78v63h176v-686zM645 993q0 79 -54.5 128.5t-129.5 49.5q-76 0 -127 -50.5t-51 -127.5q0 -79 51.5 -131.5t126.5 -52.5q80 0 132 50.5t52 133.5zM821 406
h-686v139h686v-139z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1081" 
d="M356 53l-282 424l282 424h244l-283 -424l283 -424h-244zM756 53l-283 424l283 424h243l-282 -424l282 -424h-243z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1269" 
d="M848 502h-717v274h993v-575h-276v301z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1622" 
d="M733 897v-166h109q31 0 52 26t21 60q0 28 -19.5 54t-47.5 26h-115zM1104 356h-207l-149 224h-15v-224h-172v695h291q112 0 176 -66.5t64 -167.5q0 -80 -43 -142t-117 -87zM303 190q-207 213 -207 512t207 512q210 213 508 213t508 -213q209 -212 209 -512t-209 -512
q-207 -210 -508 -210t-508 210zM418 1100q-162 -162 -162 -398q0 -235 162 -397q164 -164 393 -164t393 164q162 162 162 397q0 236 -162 398q-163 166 -393 166t-393 -166z" />
    <glyph glyph-name="overscore" unicode="&#xaf;" horiz-adv-x="1081" 
d="M836 1364v-178h-590v178h590z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="753" 
d="M82 1083q0 121 86 207q85 88 207 88q124 0 209 -88q88 -85 88 -207q0 -123 -88 -211q-86 -86 -209 -86q-121 0 -207 86q-86 89 -86 211zM250 1083q0 -56 35.5 -92.5t89.5 -36.5q55 0 92 37t37 92q0 54 -37 90.5t-92 36.5q-51 0 -88 -37t-37 -90z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1114" 
d="M133 223h848v-225h-848v225zM981 831v-221h-313v-291h-222v291h-313v221h313v287h222v-287h313z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="702" 
d="M59 993v125l340 383q26 38 8 71.5t-63 33.5q-72 0 -84 -82l-201 28q40 258 301 258q111 0 191 -71t80 -178q-3 -120 -84 -201l-174 -180h262v-187h-576z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="690" 
d="M297 1335h-86v152h86q92 0 92 76q0 26 -24 44.5t-66 18.5q-78 0 -123 -51l-113 117q81 118 273 118q111 0 186 -69q79 -70 76 -158q0 -97 -115 -156q144 -52 150 -180q6 -126 -88 -196q-95 -68 -232 -68q-212 0 -280 139l110 111q55 -76 162 -76q59 0 91 26.5t32 69.5
q0 36 -38 59t-93 23z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="1120" 
d="M264 1124l191 357h303l-283 -357h-211z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1224" 
d="M420 -389h-281v1343h326v-497q0 -82 45 -124t111 -42q71 0 119 41t48 121v501h326v-954h-326v150q-21 -83 -108 -130q-80 -45 -160 -45q-36 0 -100 11v-375z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1529" 
d="M512 1411h207v-1505h-227v616q-174 9 -302 140q-122 128 -122 305q0 184 129 313q128 131 315 131zM1163 1190h-82v-1348q0 -111 -80 -194q-80 -86 -200 -86h-314v198h291q40 0 63 26.5t23 68.5v1556h314q117 0 198 -84q82 -82 82 -197v-176h-201v150q0 38 -27.5 62
t-66.5 24z" />
    <glyph glyph-name="middot" unicode="&#xb7;" horiz-adv-x="587" 
d="M100 793q0 79 56.5 132.5t136.5 53.5t137 -53.5t57 -132.5q0 -81 -56.5 -135t-137.5 -54q-82 0 -137.5 54t-55.5 135z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="1093" 
d="M610 -176l-110 223h137l111 -170q22 -42 22 -104q0 -92 -63 -152q-63 -63 -150 -63q-99 0 -163 70t-60 194l143 -8q-9 -85 51 -107q52 -16 78 27q25 35 4 90z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="497" 
d="M41 1458v178l360 187v-830h-217v531z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="942" 
d="M113 993q0 157 102 256q105 99 254 99q151 0 256 -99q104 -98 104 -256q0 -160 -106 -260q-103 -100 -254 -100t-254 100q-102 102 -102 260zM651 993q0 79 -51.5 128.5t-130.5 49.5t-130.5 -49.5t-51.5 -128.5q0 -81 51.5 -132.5t130.5 -51.5t130.5 51.5t51.5 132.5z
M813 406h-686v139h686v-139z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1081" 
d="M725 901l283 -424l-283 -424h-244l283 424l-283 424h244zM326 901l282 -424l-282 -424h-244l283 424l-283 424h244z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1699" 
d="M1317 618l-127 -249h127v249zM1624 369v-179h-92v-190h-215v190h-404l330 617h289v-438h92zM78 1001v179l360 186v-829h-217v530zM1341 1366l-923 -1384h-193l926 1384h190z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1710" 
d="M76 1001v179l360 186v-829h-217v530zM1339 1366l-923 -1384h-193l926 1384h190zM1386 508q25 41 7.5 72.5t-62.5 31.5q-72 0 -84 -82l-200 29q41 258 301 258q109 0 190 -72q80 -71 80 -178q-3 -119 -84 -200l-174 -181h262v-186h-575v125z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1888" 
d="M338 879h-86v151h86q92 0 92 76q0 26 -24 44.5t-66 18.5q-78 0 -123 -51l-113 117q82 119 273 119q110 0 186 -70q79 -70 76 -158q0 -96 -115 -155q144 -52 150 -180q6 -127 -88 -197q-95 -68 -232 -68q-212 0 -280 140l110 110q55 -76 162 -76q59 0 91 27t32 70
q0 37 -37.5 59.5t-93.5 22.5zM1378 369h127v249zM1720 807v-438h92v-179h-92v-190h-215v190h-403l330 617h288zM414 -18l925 1384h191l-924 -1384h-192z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="997" 
d="M434 477h293v-24q0 -76 -6 -119.5t-25 -95.5q-39 -103 -182 -164l-76 -33q-69 -26 -69 -102q0 -53 36 -82t99 -29q55 0 93 37.5t38 93.5h295q0 -211 -115.5 -325.5t-316.5 -114.5q-194 0 -314 108q-118 106 -118 291q0 144 68.5 243t193.5 144q61 23 83.5 60.5
t22.5 111.5zM770 788q0 -79 -56 -132.5t-136 -53.5q-82 0 -139.5 53.5t-57.5 132.5q0 81 57.5 136t139.5 55q80 0 136 -55t56 -136z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1366" 
d="M1389 0h-363l-119 324h-444l-119 -324h-367l521 1411h372zM819 618l-137 465l-127 -465h264zM625 1534l-283 356h303l191 -356h-211z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1366" 
d="M907 324h-444l-119 -324h-367l521 1411h372l519 -1411h-363zM555 618h264l-137 465zM498 1534l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1366" 
d="M1389 0h-363l-119 324h-444l-119 -324h-367l521 1411h372zM819 618l-137 465l-127 -465h264zM518 1550h-219l223 355h320l225 -355h-221l-164 183z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1366" 
d="M1389 0h-363l-119 324h-444l-119 -324h-367l521 1411h372zM819 618l-137 465l-127 -465h264zM594 1632q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41
q-55 43 -76 43z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1366" 
d="M907 324h-444l-119 -324h-367l521 1411h372l519 -1411h-363zM555 618h264l-137 465zM334 1722q0 62 46 105t112 43q63 0 108 -43.5t45 -104.5q0 -63 -43.5 -106t-109.5 -43t-112 43t-46 106zM719 1722q0 61 47 104.5t113 43.5q63 0 107 -43t44 -105q0 -63 -44 -106
t-107 -43q-66 0 -113 43t-47 106z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1366" 
d="M907 324h-444l-119 -324h-367l521 1411h372l519 -1411h-363zM555 618h264l-137 465zM422 1788q0 109 74 186q78 78 184 78q108 0 186 -78q76 -76 76 -186q0 -108 -76 -184t-186 -76q-108 0 -184 76q-74 77 -74 184zM549 1788q0 -57 37.5 -95t93.5 -38q57 0 97 38t40 95
t-40 98t-97 41q-56 0 -93.5 -39.5t-37.5 -99.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1916" 
d="M682 606h197v385zM356 0h-413l891 1411h966v-299h-575v-270h504v-289h-504v-254h592v-299h-936v2l-2 -2v311h-328z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1449" 
d="M752 -176l-84 170q-252 46 -422 246q-168 198 -168 465q0 300 209 512q213 213 510 213q184 0 343.5 -86.5t258.5 -237.5l-285 -199q-50 82 -134.5 130t-182.5 48q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269q107 -110 265 -110q99 0 183.5 47t135.5 129
l281 -197q-98 -145 -250 -231t-328 -92l70 -105q22 -42 22 -104q0 -92 -63 -152q-63 -63 -150 -63q-99 0 -163 70t-60 194l143 -8q-9 -85 52 -107q52 -16 78 27q25 35 4 90z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1189" 
d="M1092 299v-299h-934v1411h915v-299h-573v-270h504v-293h-504v-250h592zM551 1534l-283 356h303l191 -356h-211z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1189" 
d="M158 0v1411h915v-299h-573v-270h504v-293h-504v-250h592v-299h-934zM428 1534l190 356h304l-283 -356h-211z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1189" 
d="M1092 299v-299h-934v1411h915v-299h-573v-270h504v-293h-504v-250h592zM420 1550h-219l223 355h319l226 -355h-221l-164 183z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1189" 
d="M158 0v1411h915v-299h-573v-270h504v-293h-504v-250h592v-299h-934zM236 1722q0 62 45.5 105t111.5 43q63 0 108.5 -43.5t45.5 -104.5q0 -63 -44 -106t-110 -43t-111.5 43t-45.5 106zM621 1722q0 61 46.5 104.5t112.5 43.5q63 0 107.5 -43.5t44.5 -104.5
q0 -63 -44.5 -106t-107.5 -43q-66 0 -112.5 43t-46.5 106z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="657" 
d="M500 0h-342v1411h342v-1411zM291 1534l-283 356h303l191 -356h-211z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="657" 
d="M158 1411h342v-1411h-342v1411zM156 1534l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="657" 
d="M500 0h-342v1411h342v-1411zM164 1550h-219l223 355h319l226 -355h-221l-164 183z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="657" 
d="M158 1411h342v-1411h-342v1411zM-18 1722q0 62 45.5 105t111.5 43q63 0 108.5 -43.5t45.5 -104.5q0 -63 -44 -106t-110 -43t-111.5 43t-45.5 106zM367 1722q0 61 46.5 104.5t112.5 43.5q63 0 107.5 -43.5t44.5 -104.5q0 -63 -44.5 -106t-107.5 -43q-66 0 -112.5 43
t-46.5 106z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1478" 
d="M754 784v-206h-217v-273h215q142 0 219 115q80 118 80 282q0 167 -80 285q-76 119 -219 119h-215v-322h217zM193 0v578h-193v206h193v627h559q295 0 469 -201q172 -199 172 -508q0 -306 -172 -505q-170 -197 -469 -197h-559z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1388" 
d="M604 1632q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 75 -41t36 22t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41q-58 43 -76 43zM889 651v760h342v-1411h-299l-432 770v-770h-342v1411h309z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM657 1534l-282 356h303l190 -356h-211z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM702 1534l191 356h303l-283 -356h-211z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM633 1550h-219l223 355h319l226 -355h-221l-164 183z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM709 1632q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-42 0 -101 41q-58 43 -77 43z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM449 1722q0 62 45.5 105t111.5 43q63 0 108.5 -43.5t45.5 -104.5q0 -63 -44 -106t-110 -43t-111.5 43t-45.5 106zM834 1722q0 61 46.5 104.5t112.5 43.5q63 0 107.5 -43.5t44.5 -104.5q0 -63 -44.5 -106t-107.5 -43q-66 0 -112.5 43t-46.5 106z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="995" 
d="M729 909l156 -155l-234 -236l234 -229l-156 -158l-233 231l-230 -225l-155 158l227 223l-229 232l155 157l232 -231z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1593" 
d="M795 324q156 0 266 110q110 113 110 271q0 119 -67 213l-518 -531q92 -63 209 -63zM422 705q0 -123 67 -224l523 535q-89 71 -217 71q-159 0 -265 -112q-108 -111 -108 -270zM348 139l31 31l-197 -201l-96 97l164 167q-174 204 -174 472q0 300 209 512q213 213 510 213
q263 0 456 -166l162 166l96 -95l-163 -168q170 -206 170 -462q0 -301 -211 -512q-208 -211 -510 -211q-252 0 -447 157z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1396" 
d="M141 514v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385zM633 1534l-283 356h303l191 -356h-211z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1396" 
d="M1255 514q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897zM565 1534l191 356h303l-283 -356h-211z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1396" 
d="M141 514v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385zM532 1550h-219l224 355h319l225 -355h-221l-164 183z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1396" 
d="M1255 514q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897zM350 1722q0 62 46 105t112 43q63 0 108.5 -43.5t45.5 -104.5q0 -63 -44 -106t-110 -43t-112 43
t-46 106zM735 1722q0 61 47 104.5t113 43.5q63 0 107.5 -43.5t44.5 -104.5q0 -63 -44.5 -106t-107.5 -43q-66 0 -113 43t-47 106z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1357" 
d="M850 629v-629h-344v635l-551 776h407l314 -512l319 512h406zM537 1534l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1177" 
d="M664 242h-164v-242h-342v1411h342v-227h164q199 0 331 -129q135 -126 135 -338t-135 -344q-131 -131 -331 -131zM797 711q0 87 -45 138.5t-119 51.5h-133v-377h133q74 0 119 50.5t45 136.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1210" 
d="M516 2l76 289q52 -6 82 -6q68 0 116 51.5t48 118.5q0 15 -3 33.5t-15 45.5t-31 48t-54.5 36t-81.5 15h-75v276h43q48 0 74 30.5t26 82.5q0 42 -34 74t-101 32q-137 0 -137 -196v-932h-326v932q0 495 452 495q201 0 326 -110t125 -250q0 -221 -139 -274q112 -26 184 -124
t72 -223q0 -213 -121 -338.5t-350 -125.5q-92 0 -156 20z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1245" 
d="M797 852v102h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60
zM592 1124l-283 357h303l191 -357h-211z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1245" 
d="M797 954h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5v102zM457 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5
zM465 1124l190 357h303l-282 -357h-211z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1245" 
d="M797 852v102h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60
zM461 1141h-219l223 354h319l226 -354h-222l-163 182z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1245" 
d="M797 852v102h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60
zM537 1223q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-42 0 -101 41q-58 43 -77 43z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1245" 
d="M797 954h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5v102zM457 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5
zM276 1313q0 61 46 104t112 43q63 0 108.5 -43t45.5 -104q0 -63 -44 -106.5t-110 -43.5t-112 43.5t-46 106.5zM662 1313q0 61 46.5 104t112.5 43q63 0 107.5 -43t44.5 -104q0 -63 -44.5 -106.5t-107.5 -43.5q-66 0 -112.5 43.5t-46.5 106.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1245" 
d="M797 954h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5v102zM457 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5
zM365 1378q0 111 73 187q77 77 185 77q109 0 186 -77q76 -76 76 -187q0 -108 -76 -184t-186 -76q-109 0 -185 76q-73 76 -73 184zM492 1378q0 -57 37.5 -95t93.5 -38q57 0 97 38t40 95t-40 98.5t-97 41.5q-56 0 -93.5 -40t-37.5 -100z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="2015" 
d="M1960 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q85 -58 110 -78q-135 -178 -432 -178q-202 0 -342 122v-104h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5
v102h325v-96q139 121 342 121q212 0 353 -135q143 -131 143 -352zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60zM1270 569h381q-19 73 -73 107.5t-118 34.5q-32 0 -67 -9q-37 -9 -75 -45.5
t-48 -87.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1077" 
d="M625 -16l73 -107q23 -41 23 -104q0 -91 -64 -152q-63 -63 -149 -63q-99 0 -163 70t-60 194l143 -8q-9 -85 51 -107q52 -16 78 27q25 35 4 90l-88 172q-178 38 -291 166q-114 129 -114 317q0 224 155 361q157 139 375 139q265 0 420 -236l-285 -172q-47 86 -158 86
q-72 0 -124 -53t-52 -125t52 -125t124 -53q110 0 154 78l291 -162q-147 -221 -395 -233z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1126" 
d="M1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-19 73 -73 107.5
t-118 34.5q-33 0 -67 -9q-37 -9 -75 -45.5t-48 -87.5zM492 1124l-283 357h303l190 -357h-210z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1126" 
d="M455 1124l190 357h303l-282 -357h-211zM1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352
zM381 569h381q-13 41 -37.5 71.5t-52.5 45t-53.5 21t-47.5 4.5q-66 0 -120 -34.5t-70 -107.5z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1126" 
d="M1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-19 73 -73 107.5
t-118 34.5q-33 0 -67 -9q-37 -9 -75 -45.5t-48 -87.5zM406 1141h-220l224 354h319l225 -354h-221l-164 182z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1126" 
d="M221 1313q0 61 46 104t112 43q63 0 108 -43t45 -104q0 -63 -43.5 -106.5t-109.5 -43.5t-112 43.5t-46 106.5zM606 1313q0 61 47 104t113 43q63 0 107.5 -43t44.5 -104q0 -63 -44.5 -106.5t-107.5 -43.5q-66 0 -113 43.5t-47 106.5zM1071 492q0 -26 -6 -105h-684
q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-13 41 -37.5 71.5t-52.5 45t-53.5 21t-47.5 4.5
q-66 0 -120 -34.5t-70 -107.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="567" 
d="M123 0v956h321v-956h-321zM233 1124l-282 357h303l190 -357h-211z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="567" 
d="M444 956v-956h-321v956h321zM125 1124l190 357h303l-282 -357h-211z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="567" 
d="M123 0v956h321v-956h-321zM117 1141h-219l223 354h319l226 -354h-222l-163 182z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="567" 
d="M444 956v-956h-321v956h321zM-66 1313q0 61 46 104t112 43q63 0 108.5 -43t45.5 -104q0 -63 -44 -106.5t-110 -43.5t-112 43.5t-46 106.5zM319 1313q0 61 47 104t113 43q63 0 107.5 -43t44.5 -104q0 -63 -44.5 -106.5t-107.5 -43.5q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1130" 
d="M563 301q75 0 124.5 49t49.5 127q0 74 -50 122t-124 48q-73 0 -122.5 -48t-49.5 -122q0 -78 49.5 -127t122.5 -49zM776 1354l70 -94l-166 -70l287 -416q94 -134 94 -297q0 -218 -146 -358q-143 -137 -352 -137q-207 0 -350 137t-143 358q0 173 97 298.5t257 162.5
q57 11 131 14l-106 146l-175 -68l-55 100l166 64l-152 217h297l86 -127z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1208" 
d="M449 502v-502h-326v954h326v-149q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 82 -45 124t-111 42q-71 0 -119 -41t-48 -121zM516 1223q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 36 22
t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-41 0 -100 41q-58 43 -78 43z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 618q-57 -57 -57 -138.5t57 -141.5q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60zM457 1124l-283 357
h303l191 -357h-211z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 338q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60q-57 -57 -57 -138.5t57 -141.5zM446 1124l191 357
h303l-283 -357h-211z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 618q-57 -57 -57 -138.5t57 -141.5q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60zM412 1141h-219
l223 354h319l226 -354h-222l-164 182z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 618q-57 -57 -57 -138.5t57 -141.5q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60zM487 1223
q-19 0 -35.5 -24t-17.5 -64h-153q6 129 65.5 197.5t140.5 68.5q48 0 103 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h153q-6 -127 -65.5 -194.5t-140.5 -67.5q-42 0 -101 41q-58 43 -78 43z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 338q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60q-57 -57 -57 -138.5t57 -141.5zM229 1313
q0 61 46 104t112 43q63 0 108.5 -43t45.5 -104q0 -63 -44 -106.5t-110 -43.5t-112 43.5t-46 106.5zM614 1313q0 61 47 104t113 43q63 0 107.5 -43t44.5 -104q0 -63 -44.5 -106.5t-107.5 -43.5q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1271" 
d="M512 29q-50 49 -50 121t50 116q50 47 121 47t121 -47q51 -44 51 -116t-51 -121q-50 -47 -121 -47t-121 47zM463 942q0 69 49 116.5t121 47.5t122 -47.5t50 -116.5q0 -73 -49.5 -119.5t-122.5 -46.5t-121.5 46.5t-48.5 119.5zM1169 655v-209h-1067v209h1067z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1153" 
d="M270 76l-112 -115l-84 84l108 111q-114 135 -114 323q0 223 147 361q151 139 358 139q179 0 314 -100l104 106l84 -82l-102 -104q112 -133 112 -320q0 -219 -151 -358t-361 -139q-172 0 -303 94zM375 479q0 -59 26 -98l267 272q-52 25 -95 25q-82 0 -140 -58.5
t-58 -140.5zM573 281q82 0 140.5 58t58.5 140q0 41 -22 90l-263 -270q41 -18 86 -18z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM539 1124l-283 357h303l191 -357h-211z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM473 1124l191 357h303l-283 -357h-211z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM428 1141h-219l223 354h320l225 -354h-221l-164 182z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM270 1313q0 61 46 104t112 43q63 0 108.5 -43t45.5 -104q0 -63 -44 -106.5t-110 -43.5t-112 43.5t-46 106.5z
M655 1313q0 61 47 104t113 43q63 0 107.5 -43t44.5 -104q0 -63 -44.5 -106.5t-107.5 -43.5q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1060" 
d="M1059 954l-590 -1425h-342l240 598l-375 827h356l187 -462l178 462h346zM375 1124l190 357h303l-282 -357h-211z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1247" 
d="M123 1456h326v-590q101 113 264 113q186 0 325 -139q140 -140 140 -361q0 -220 -140 -360q-137 -137 -325 -137q-92 0 -169 40t-114 115v-608h-307v1927zM786 336q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57
t138.5 57z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1060" 
d="M1059 954l-590 -1425h-342l240 598l-375 827h356l187 -462l178 462h346zM182 1313q0 61 46 104t112 43q63 0 108.5 -43t45.5 -104q0 -63 -44 -106.5t-110 -43.5t-112 43.5t-46 106.5zM567 1313q0 61 47 104t113 43q63 0 107.5 -43t44.5 -104q0 -63 -44.5 -106.5
t-107.5 -43.5q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1366" 
d="M1389 0h-363l-119 324h-444l-119 -324h-367l521 1411h372zM819 618l-137 465l-127 -465h264zM977 1774v-179h-590v179h590z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1245" 
d="M797 852v102h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60
zM920 1364v-178h-590v178h590z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1366" 
d="M907 324h-444l-119 -324h-367l521 1411h372l519 -1411h-363zM555 618h264l-137 465zM403 1835h183q0 -55 21.5 -78.5t74.5 -23.5q92 0 92 102h187q0 -121 -77.5 -195.5t-201.5 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1245" 
d="M797 954h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5v102zM457 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5
zM346 1425h182q0 -55 22 -78.5t75 -23.5q92 0 92 102h186q0 -121 -77.5 -195.5t-200.5 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1366" 
d="M819 618l-137 465l-127 -465h264zM870 1411l519 -1411l-91 -176q-27 -77 25 -111q44 -26 84 11q31 31 25 90l143 8q4 -124 -60 -194t-163 -70q-87 0 -150 63q-63 60 -63 152q0 62 22 104l82 123h-217l-119 324h-444l-119 -324h-367l521 1411h372z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1245" 
d="M1122 954v-954l-90 -176q-27 -77 25 -111q44 -26 84 11q30 30 24 90l144 8q4 -124 -60.5 -194t-163.5 -70q-86 0 -149 63q-64 61 -64 152q0 60 23 104l82 123h-180v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5
t114.5 -93.5v102h325zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1447" 
d="M1397 305q-99 -150 -258 -236.5t-342 -86.5q-297 0 -508 211t-211 512q0 300 209 512q213 213 510 213q184 0 343.5 -86.5t258.5 -237.5l-40 -28l-97 -68q-51 -34 -96 -66t-52 -37q-50 82 -134.5 130t-182.5 48q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110q99 0 183.5 47t135.5 129zM668 1581l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1077" 
d="M575 301q110 0 154 78l291 -162q-155 -235 -422 -235q-222 0 -377 137q-153 138 -153 360q0 224 155 361q157 139 375 139q265 0 420 -236l-285 -172q-47 86 -158 86q-72 0 -124 -53t-52 -125t52 -125t124 -53zM463 1124l190 357h303l-282 -357h-211z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1447" 
d="M1116 502l281 -197q-99 -150 -258 -236.5t-342 -86.5q-297 0 -508 211t-211 512q0 300 209 512q213 213 510 213q184 0 343.5 -86.5t258.5 -237.5l-285 -199q-50 82 -134.5 130t-182.5 48q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269q107 -110 265 -110
q99 0 183.5 47t135.5 129zM629 1597h-219l223 355h319l226 -355h-222l-163 183z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1077" 
d="M575 301q110 0 154 78l291 -162q-155 -235 -422 -235q-222 0 -377 137q-153 138 -153 360q0 224 155 361q157 139 375 139q265 0 420 -236l-285 -172q-47 86 -158 86q-72 0 -124 -53t-52 -125t52 -125t124 -53zM416 1141h-219l223 354h319l226 -354h-222l-163 182z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1447" 
d="M1397 305q-99 -150 -258 -236.5t-342 -86.5q-297 0 -508 211t-211 512q0 300 209 512q213 213 510 213q184 0 343.5 -86.5t258.5 -237.5l-40 -28l-97 -68q-51 -34 -96 -66t-52 -37q-50 82 -134.5 130t-182.5 48q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110q99 0 183.5 47t135.5 129zM569 1782q0 69 49 116.5t121 47.5t121 -47.5t49 -116.5q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1077" 
d="M575 301q110 0 154 78l291 -162q-155 -235 -422 -235q-222 0 -377 137q-153 138 -153 360q0 224 155 361q157 139 375 139q265 0 420 -236l-285 -172q-47 86 -158 86q-72 0 -124 -53t-52 -125t52 -125t124 -53zM403 1325q0 69 49 116.5t121 47.5t121 -47.5t49 -116.5
q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1447" 
d="M1397 305q-99 -150 -258 -236.5t-342 -86.5q-297 0 -508 211t-211 512q0 300 209 512q213 213 510 213q184 0 343.5 -86.5t258.5 -237.5l-40 -28l-97 -68q-51 -34 -96 -66t-52 -37q-50 82 -134.5 130t-182.5 48q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110q99 0 183.5 47t135.5 129zM1186 1937l-225 -354h-320l-223 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1077" 
d="M575 301q110 0 154 78l291 -162q-155 -235 -422 -235q-222 0 -377 137q-153 138 -153 360q0 224 155 361q157 139 375 139q265 0 420 -236l-285 -172q-47 86 -158 86q-72 0 -124 -53t-52 -125t52 -125t124 -53zM946 1481l-225 -355h-320l-223 355h219l164 -185l164 185
h221z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1443" 
d="M502 305h215q142 0 219 115q80 118 80 282q0 167 -80 285q-76 119 -219 119h-215v-801zM158 1411h559q295 0 469 -201q172 -199 172 -508q0 -306 -172 -505q-170 -197 -469 -197h-559v1411zM1126 1890l-225 -354h-319l-224 354h220l163 -184l164 184h221z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1245" 
d="M1325 952l-147 84q69 93 98 187q28 95 31 233h262q-34 -328 -244 -504zM797 1456h325v-1456h-307v137q-37 -75 -114 -115t-169 -40q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q164 0 265 -113v590zM457 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5
t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1478" 
d="M754 784v-206h-217v-273h215q142 0 219 115q80 118 80 282q0 167 -80 285q-76 119 -219 119h-215v-322h217zM193 0v578h-193v206h193v627h559q295 0 469 -201q172 -199 172 -508q0 -306 -172 -505q-170 -197 -469 -197h-559z" />
    <glyph glyph-name="dslash" unicode="&#x111;" horiz-adv-x="1245" 
d="M797 866v246h-271v164h271v180h325v-180h135v-164h-135v-1112h-307v137q-37 -75 -114 -115t-169 -40q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q164 0 265 -113zM457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5
t-60 138.5q-57 60 -138.5 60t-141.5 -60z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1189" 
d="M1092 299v-299h-934v1411h915v-299h-573v-270h504v-293h-504v-250h592zM879 1774v-179h-590v179h590z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1126" 
d="M1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-19 73 -73 107.5
t-118 34.5q-33 0 -67 -9q-37 -9 -75 -45.5t-48 -87.5zM864 1364v-178h-590v178h590z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1189" 
d="M158 0v1411h915v-299h-573v-270h504v-293h-504v-250h592v-299h-934zM305 1835h182q0 -55 22 -78.5t75 -23.5q92 0 92 102h186q0 -121 -77 -195.5t-201 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1126" 
d="M291 1425h182q0 -55 21.5 -78.5t74.5 -23.5q93 0 93 102h186q0 -121 -77.5 -195.5t-201.5 -74.5q-129 0 -203.5 77.5t-74.5 192.5zM1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178
q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-13 41 -37.5 71.5t-52.5 45t-53.5 21t-47.5 4.5q-66 0 -120 -34.5t-70 -107.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1189" 
d="M158 0v1411h915v-299h-573v-270h504v-293h-504v-250h592v-299h-934zM414 1735q0 69 49 116t121 47t121 -47t49 -116q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1126" 
d="M399 1325q0 69 49 116.5t121 47.5t121 -47.5t49 -116.5q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5zM1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139
q-147 141 -147 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352zM381 569h381q-13 41 -37.5 71.5t-52.5 45t-53.5 21t-47.5 4.5q-66 0 -120 -34.5t-70 -107.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1189" 
d="M500 299h592v-299l-91 -176q-27 -77 25 -111q44 -26 84 11q31 31 25 90l143 8q4 -124 -60 -194t-163 -70q-87 0 -150 63q-63 60 -63 152q0 62 22 104l82 123h-788v1411h915v-299h-573v-270h504v-293h-504v-250z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1128" 
d="M580 -18q-212 0 -363 139q-149 140 -149 362q0 219 147 357q151 139 360 139q212 0 353 -135q143 -131 143 -352q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-1 -2 -64 -68q-125 -136 -182 -268q-27 -77 24 -111
q44 -26 84 11q31 31 25 90l143 8q4 -124 -60 -194t-163 -70q-86 0 -149 63q-64 61 -64 152q0 60 23 104l73 115q-75 -10 -118 -10zM381 569h381q-19 73 -73 107.5t-118 34.5q-33 0 -67 -9q-37 -9 -75 -45.5t-48 -87.5z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1189" 
d="M158 0v1411h915v-299h-573v-270h504v-293h-504v-250h592v-299h-934zM969 1890l-226 -354h-319l-223 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1126" 
d="M954 1481l-225 -355h-319l-224 355h220l163 -185l164 185h221zM1071 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q92 -63 111 -78q-135 -178 -433 -178q-209 0 -360 139q-147 141 -147 362q0 219 147 357q151 139 360 139
q212 0 353 -135q143 -131 143 -352zM381 569h381q-13 41 -37.5 71.5t-52.5 45t-53.5 21t-47.5 4.5q-66 0 -120 -34.5t-70 -107.5z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1548" 
d="M1356 1180l-254 -215q-120 147 -297 147q-160 0 -270 -119q-111 -120 -111 -288t111 -291q112 -121 270 -121q276 0 323 188h-395v275h768q0 -388 -188 -582q-186 -192 -508 -192q-305 0 -516 211t-211 512t211 512q213 213 516 213q166 0 311.5 -66t239.5 -184z
M616 1550h-219l224 355h319l225 -355h-221l-164 183z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1245" 
d="M457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60zM88 -172l274 111q25 -58 81.5 -92.5t129.5 -34.5q122 0 187 78t65 229q-76 -137 -293 -137q-188 0 -325 137q-139 139 -139 360q0 222 139 361
t325 139q78 0 150.5 -33.5t114.5 -93.5v102h325v-833q0 -295 -142 -450.5t-407 -155.5q-164 0 -295.5 86t-189.5 227zM461 1141h-219l223 354h319l226 -354h-222l-163 182z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1548" 
d="M522 1835h183q0 -55 21.5 -78.5t74.5 -23.5q92 0 92 102h186q0 -121 -77.5 -195.5t-200.5 -74.5q-129 0 -204 77.5t-75 192.5zM1356 1180l-254 -215q-120 147 -297 147q-160 0 -270 -119q-111 -120 -111 -288t111 -291q112 -121 270 -121q276 0 323 188h-395v275h768
q0 -388 -188 -582q-186 -192 -508 -192q-305 0 -516 211t-211 512t211 512q213 213 516 213q166 0 311.5 -66t239.5 -184z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1245" 
d="M457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60zM88 -172l274 111q25 -58 81.5 -92.5t129.5 -34.5q122 0 187 78t65 229q-76 -137 -293 -137q-188 0 -325 137q-139 139 -139 360q0 222 139 361
t325 139q78 0 150.5 -33.5t114.5 -93.5v102h325v-833q0 -295 -142 -450.5t-407 -155.5q-164 0 -295.5 86t-189.5 227zM346 1425h182q0 -55 22 -78.5t75 -23.5q92 0 92 102h186q0 -121 -77.5 -195.5t-200.5 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1548" 
d="M610 1735q0 69 49 116t121 47t121 -47t49 -116q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5zM1356 1180l-254 -215q-120 147 -297 147q-160 0 -270 -119q-111 -120 -111 -288t111 -291q112 -121 270 -121q276 0 323 188h-395v275h768q0 -388 -188 -582
q-186 -192 -508 -192q-305 0 -516 211t-211 512t211 512q213 213 516 213q166 0 311.5 -66t239.5 -184z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1245" 
d="M457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60zM88 -172l274 111q25 -58 81.5 -92.5t129.5 -34.5q122 0 187 78t65 229q-76 -137 -293 -137q-188 0 -325 137q-139 139 -139 360q0 222 139 361
t325 139q78 0 150.5 -33.5t114.5 -93.5v102h325v-833q0 -295 -142 -450.5t-407 -155.5q-164 0 -295.5 86t-189.5 227zM455 1325q0 69 49 116.5t121 47.5t121 -47.5t49 -116.5q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1548" 
d="M1356 1180l-254 -215q-120 147 -297 147q-160 0 -270 -119q-111 -120 -111 -288t111 -291q112 -121 270 -121q276 0 323 188h-395v275h768q0 -388 -188 -582q-186 -192 -508 -192q-305 0 -516 211t-211 512t211 512q213 213 516 213q166 0 311.5 -66t239.5 -184z
M694 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1245" 
d="M457 616q-57 -57 -57 -138.5t57 -141.5q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60zM88 -172l274 111q25 -58 81.5 -92.5t129.5 -34.5q122 0 187 78t65 229q-76 -137 -293 -137q-188 0 -325 137q-139 139 -139 360q0 222 139 361
t325 139q78 0 150.5 -33.5t114.5 -93.5v102h325v-833q0 -295 -142 -450.5t-407 -155.5q-164 0 -295.5 86t-189.5 227zM653 1591l148 -86q-71 -96 -99 -186q-27 -88 -30 -232h-262q34 329 243 504z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1435" 
d="M500 854h430v557h348v-1411h-348v549h-430v-549h-342v1411h342v-557zM553 1550h-219l223 355h320l225 -355h-221l-164 183z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1208" 
d="M449 502v-502h-326v1456h326v-651q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 74 -47.5 120t-108.5 46q-71 0 -119 -42t-48 -120zM123 1554h-219l223 355h319l226 -355h-221l-164 183z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1466" 
d="M946 1044h-430v-190h430v190zM516 0h-344v1044h-149v164h149v203h344v-203h430v203h348v-203h150v-164h-150v-1044h-348v551h-430v-551z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1208" 
d="M449 0h-326v1112h-148v164h148v180h326v-180h258v-164h-258v-307q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 74 -47.5 120t-108.5 46q-71 0 -119 -42t-48 -120v-502z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="657" 
d="M240 1632q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41q-58 43 -75 43zM500 0h-342v1411h342v-1411z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="567" 
d="M123 0v956h321v-956h-321zM193 1223q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-42 0 -101 41q-58 43 -77 43z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="657" 
d="M625 1774v-179h-590v179h590zM500 0h-342v1411h342v-1411z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="567" 
d="M123 0v956h321v-956h-321zM578 1364v-178h-590v178h590z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="657" 
d="M158 1411h342v-1411h-342v1411zM51 1835h182q0 -55 22 -78.5t75 -23.5q92 0 92 102h186q0 -121 -77 -195.5t-201 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="567" 
d="M444 956v-956h-321v956h321zM4 1425h182q0 -55 22 -78.5t75 -23.5q92 0 92 102h186q0 -121 -77.5 -195.5t-200.5 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="657" 
d="M272 -143l82 143h-196v1411h342v-1411l-90 -197q-27 -76 24 -110q45 -26 84 10q31 31 25 90l143 8q4 -124 -60 -194t-163 -70q-86 0 -150 64q-63 60 -63 151q0 63 22 105z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="571" 
d="M88 1272q0 77 58.5 130.5t138.5 53.5q82 0 138 -53t56 -131t-56 -132.5t-138 -54.5q-79 0 -138 56t-59 131zM221 -143l82 143h-180v954h326v-954h-3l-88 -197q-27 -76 25 -110q45 -26 84 10q31 31 25 90l143 8q4 -124 -60 -194t-163 -70q-86 0 -150 64q-63 60 -63 151
q0 63 22 105z" />
    <glyph glyph-name="Idot" unicode="&#x130;" horiz-adv-x="657" 
d="M158 1411h342v-1411h-342v1411zM160 1735q0 69 49 116t121 47t121 -47t49 -116q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="571" 
d="M449 0h-326v954h326v-954z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1318" 
d="M459 -432l30 307q73 -25 197 -25q64 0 96.5 44t32.5 120v1397h346v-1397q0 -215 -117 -350q-114 -135 -358 -135q-117 0 -227 39zM500 0h-342v1411h342v-1411z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1142" 
d="M561 -485h-123v309h123q61 0 96.5 40.5t36.5 117.5v34v938h326v-938v-34q0 -225 -121 -346t-338 -121zM662 1272q0 77 58 130.5t138 53.5q82 0 138.5 -53t56.5 -131t-56.5 -132.5t-138.5 -54.5q-79 0 -137.5 56t-58.5 131zM123 954h326v-954h-326v954zM88 1272
q0 77 58.5 130.5t138.5 53.5q82 0 138 -53t56 -131t-56 -132.5t-138 -54.5q-79 0 -138 56t-59 131z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="661" 
d="M-199 -432l31 307q73 -25 197 -25q64 0 96.5 44t32.5 120v1397h346v-1397q0 -215 -117 -350q-114 -135 -358 -135q-118 0 -228 39zM172 1550h-219l223 355h320l225 -355h-221l-164 183z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="571" 
d="M-10 -485h-123v309h123q61 0 96.5 40.5t36.5 117.5v34v938h326v-938v-34q0 -225 -121 -346t-338 -121zM143 1141h-219l223 354h320l225 -354h-221l-164 182z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1296" 
d="M1208 1411l-530 -696l659 -715h-469l-368 430v-430h-342v1411h342v-403l280 403h428zM627 -618l-148 86q71 96 99 186q27 88 30 231h262q-34 -327 -243 -503z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1101" 
d="M1110 0h-389l-272 344v-344h-326v1456h326v-733l186 231h399l-352 -424zM461 -618l-148 86q71 96 99 186q27 88 30 231h263q-34 -326 -244 -503z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1058" 
d="M643 485l422 -485h-416l-207 299v-299h-319v956h319v-266l181 266h368z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1060" 
d="M158 0v1411h346v-1112h508v-299h-854zM160 1534l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="571" 
d="M123 1456h326v-1456h-326v1456zM145 1534l191 356h303l-283 -356h-211z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1060" 
d="M1012 299v-299h-854v1411h346v-1112h508zM459 -618l-148 86q71 96 99 186q27 88 30 231h262q-34 -327 -243 -503z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="571" 
d="M182 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503zM449 0h-326v1456h326v-1456z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1079" 
d="M1012 299v-299h-854v1411h346v-1112h508zM817 913l-127 78q69 102 96 189q30 88 33 231h238q-35 -325 -240 -498z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="571" 
d="M449 0h-326v1456h326v-1456zM631 958l-127 78q69 102 96 189q30 88 33 231h237q-35 -326 -239 -498z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1062" 
d="M596 606q0 79 56 133t136 54t137.5 -54t57.5 -133q0 -81 -57 -134.5t-138 -53.5q-82 0 -137 53.5t-55 134.5zM1012 299v-299h-854v1411h346v-1112h508z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="1040" 
d="M612 606q0 79 56.5 133t136.5 54t137 -54t57 -133q0 -81 -56.5 -134.5t-137.5 -53.5q-82 0 -137.5 53.5t-55.5 134.5zM449 0h-326v1456h326v-1456z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1079" 
d="M1030 299v-299h-854v543l-153 -94v190l153 94v678h346v-463l203 127v-190l-203 -127v-459h508z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="708" 
d="M221 0v565l-188 -116v190l188 117v655h342v-442l172 106v-190l-172 -107v-778h-342z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1388" 
d="M1231 1411v-1411h-299l-432 770v-770h-342v1411h309l422 -760v760h342zM553 1534l190 356h304l-283 -356h-211z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1208" 
d="M449 502v-502h-326v954h326v-149q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 82 -45 124t-111 42q-71 0 -119 -41t-48 -121zM436 1124l191 357h303l-283 -357h-211z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1388" 
d="M889 651v760h342v-1411h-299l-432 770v-770h-342v1411h309zM645 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="1208" 
d="M449 502v-502h-326v954h326v-149q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 82 -45 124t-111 42q-71 0 -119 -41t-48 -121zM510 -618l-148 86q71 96 99 186q28 92 31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1388" 
d="M1231 1411v-1411h-299l-432 770v-770h-342v1411h309l422 -760v760h342zM1077 1890l-225 -354h-320l-223 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1208" 
d="M449 502v-502h-326v954h326v-149q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508h-326v498q0 82 -45 124t-111 42q-71 0 -119 -41t-48 -121zM989 1481l-225 -355h-320l-223 355h219l164 -185l164 185h221z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1320" 
d="M561 502v-502h-325v954h325v-149q20 78 107 127q86 47 161 47q188 0 284.5 -121t96.5 -350v-508h-325v498q0 82 -45 124t-111 42q-72 0 -120 -41t-48 -121zM12 926l-147 86q70 94 98 186t31 232h262q-34 -328 -244 -504z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1388" 
d="M911 14v23l-411 733v-770h-342v1411h309l422 -760v760h342v-1411q-5 -218 -124 -344.5t-351 -126.5q-118 0 -228 39l31 287q73 -25 197 -25q65 0 110 55.5t45 128.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1208" 
d="M774 16v482q0 81 -45.5 123.5t-112.5 42.5q-71 0 -119 -41t-48 -121v-502h-326v954h326v-149q20 79 106 127q86 47 162 47q188 0 284.5 -121t96.5 -350v-508q-5 -218 -124 -344.5t-351 -126.5q-118 0 -228 39l31 264q73 -25 197 -25q69 0 110 56.5t41 152.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM1092 1774v-179h-590v179h590z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 618q-57 -57 -57 -138.5t57 -141.5q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60zM870 1364v-178
h-589v178h589z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM518 1835h182q0 -55 22 -78.5t75 -23.5q92 0 92 102h186q0 -121 -77.5 -195.5t-200.5 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 338q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60q-57 -57 -57 -138.5t57 -141.5zM297 1425h182
q0 -55 21.5 -78.5t74.5 -23.5q93 0 93 102h186q0 -121 -77.5 -195.5t-201.5 -74.5q-129 0 -203.5 77.5t-74.5 192.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1595" 
d="M78 705q0 300 209 512q213 213 510 213q300 0 510 -213q211 -211 211 -512t-211 -512q-208 -211 -510 -211q-297 0 -508 211t-211 512zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112q-108 -111 -108 -268q0 -158 108 -269
q107 -110 265 -110zM535 1530l190 354h215l-215 -354h-190zM827 1530l220 354h221l-236 -354h-205z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1153" 
d="M573 -18q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 361 -139t151 -361q0 -219 -151 -358t-361 -139zM432 338q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60q-57 -57 -57 -138.5t57 -141.5zM328 1120l190 355
h215l-215 -355h-190zM621 1120l219 355h221l-236 -355h-204z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2207" 
d="M2109 299v-299h-933v88q-172 -106 -379 -106q-297 0 -508 211t-211 512q0 300 209 512q213 213 510 213q199 0 379 -109v90h915v-299h-573v-270h503v-293h-503v-250h591zM797 326q159 0 266 110q111 111 111 269q0 157 -111 268q-109 112 -266 112q-159 0 -265 -112
q-108 -111 -108 -268q0 -158 108 -269q107 -110 265 -110z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1835" 
d="M1780 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q85 -58 110 -78q-135 -178 -432 -178q-210 0 -354 135q-147 -135 -357 -135q-207 0 -358 139q-147 138 -147 358q0 223 147 361q151 139 358 139q210 0 355 -133q145 133 356 133
t352 -135q144 -132 144 -352zM432 618q-57 -57 -57 -138.5t57 -141.5q60 -57 142 -57t139 57q60 60 60 141.5t-60 138.5q-57 60 -139 60t-142 -60zM1090 569h380q-19 73 -72.5 107.5t-117.5 34.5q-34 0 -68 -9q-37 -9 -74.5 -45.5t-47.5 -87.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1314" 
d="M158 0v1411h583q232 0 359 -139q125 -137 125 -332q0 -162 -88 -287q-85 -125 -250 -166l344 -487h-397l-304 471h-30v-471h-342zM725 1112h-225v-346h225q97 -5 137 84.5t0 178.5t-137 83zM451 1534l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="878" 
d="M145 1124l191 357h303l-283 -357h-211zM449 313v-313h-326v954h309v-235q34 125 116 192.5t185 67.5q83 0 141 -27l-79 -307q-70 25 -148 25q-103 0 -150.5 -80t-47.5 -277z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1314" 
d="M500 471v-471h-342v1411h583q232 0 359 -139q125 -137 125 -332q0 -162 -88 -287q-85 -125 -250 -166l344 -487h-397l-304 471h-30zM725 1112h-225v-346h225q97 -5 137 84.5t0 178.5t-137 83zM563 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="878" 
d="M449 313v-313h-326v954h309v-235q34 125 116 192.5t185 67.5q83 0 141 -27l-79 -307q-70 25 -148 25q-103 0 -150.5 -80t-47.5 -277zM174 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1314" 
d="M158 0v1411h583q232 0 359 -139q125 -137 125 -332q0 -162 -88 -287q-85 -125 -250 -166l344 -487h-397l-304 471h-30v-471h-342zM725 1112h-225v-346h225q97 -5 137 84.5t0 178.5t-137 83zM1026 1890l-225 -354h-320l-223 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="878" 
d="M827 1481l-225 -355h-319l-224 355h220l163 -185l164 185h221zM449 313v-313h-326v954h309v-235q34 125 116 192.5t185 67.5q83 0 141 -27l-79 -307q-70 25 -148 25q-103 0 -150.5 -80t-47.5 -277z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1200" 
d="M82 420h346q5 -64 48.5 -100.5t125.5 -36.5q75 0 124.5 34t49.5 93v5q0 13 -1.5 19.5t-11 17.5t-30.5 27q-33 26 -98 47l-166 56q-364 99 -369 415q0 190 140 312q139 121 354 121q216 0 350 -119q135 -117 135 -322h-342q0 131 -147 131q-69 0 -109.5 -30.5t-40.5 -81.5
q0 -75 131 -115l162 -51q391 -120 391 -430q0 -146 -84 -250q-82 -102 -196 -142q-107 -38 -242 -38q-240 0 -379 118q-141 120 -141 320zM436 1534l191 356h303l-283 -356h-211z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="985" 
d="M500 340l-121 33q-78 21 -139.5 56.5t-94.5 70.5q-32 36 -45 75q-3 11 -5.5 20.5t-4.5 16.5t-3 15.5t-1.5 12.5t-1 13.5t-0.5 11.5v14v15q0 111 115 197q116 88 274 88q80 0 151 -17.5t132.5 -54.5t98 -103.5t36.5 -154.5h-305q0 86 -97 86q-79 0 -79 -53q0 -46 71 -61
l170 -48q84 -23 143 -62t83 -85q41 -73 41 -141q0 -149 -132 -226q-133 -77 -301 -77q-162 0 -290 90q-125 91 -125 247h305q0 -40 29 -69t81 -29q42 0 71.5 17.5t29.5 46.5q0 20 -16.5 31t-69.5 24zM367 1124l190 357h303l-282 -357h-211z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1200" 
d="M82 420h346q5 -64 48.5 -100.5t125.5 -36.5q75 0 124.5 34t49.5 93q0 14 -4 30q-6 15 -43.5 41.5t-93.5 44.5l-166 56q-364 99 -369 415q0 190 140 312q139 121 354 121q216 0 350 -119q135 -117 135 -322h-342q0 131 -147 131q-69 0 -109.5 -30.5t-40.5 -81.5
q0 -75 131 -115l162 -51q391 -120 391 -430q0 -146 -84 -250q-153 -180 -438 -180q-240 0 -379 118q-141 120 -141 320zM438 1550h-219l223 355h320l225 -355h-221l-164 183z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="985" 
d="M500 340l-121 33q-78 21 -139.5 56.5t-94.5 70.5q-32 36 -45 75q-3 11 -5.5 20.5t-4.5 16.5t-3 15.5t-1.5 12.5t-1 13.5t-0.5 11.5v14v15q0 111 115 197q116 88 274 88q80 0 151 -17.5t132.5 -54.5t98 -103.5t36.5 -154.5h-305q0 86 -97 86q-79 0 -79 -53q0 -46 71 -61
l170 -48q84 -23 143 -62t83 -85q41 -73 41 -141q0 -149 -132 -226q-133 -77 -301 -77q-162 0 -290 90q-125 91 -125 247h305q0 -40 29 -69t81 -29q42 0 71.5 17.5t29.5 46.5q0 20 -16.5 31t-69.5 24zM330 1141h-219l223 354h319l226 -354h-222l-163 182z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1202" 
d="M664 -176l-82 158q-228 6 -364 124t-136 314h346q5 -64 48.5 -100.5t125.5 -36.5q75 0 124.5 34t49.5 93q0 14 -4 30q-6 15 -43.5 41.5t-93.5 44.5l-166 56q-364 99 -369 415q0 190 140 312q139 121 354 121q216 0 350 -119q135 -117 135 -322h-342q0 131 -147 131
q-69 0 -109.5 -30.5t-40.5 -81.5q0 -75 131 -115l162 -51q391 -120 391 -430q0 -183 -116 -287q-115 -106 -287 -133l80 -115q22 -42 22 -104q0 -92 -63 -152q-63 -63 -150 -63q-99 0 -163 70t-60 194l143 -8q-9 -85 52 -107q51 -15 77 27q25 36 5 90z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="989" 
d="M582 -12l73 -111q23 -44 23 -104q0 -91 -64 -152q-63 -63 -149 -63q-99 0 -163 70t-60 194l143 -8q-9 -85 51 -107q52 -16 78 27q25 35 4 90l-80 160q-150 12 -260 102q-106 87 -106 233h305q0 -40 29 -69t81 -29q42 0 71.5 17.5t29.5 46.5q0 20 -16.5 31t-69.5 24
l-121 33q-78 21 -139.5 56.5t-94.5 70.5q-32 36 -45 75q-3 11 -5.5 20.5t-4.5 16.5t-3 15.5t-1.5 12.5t-1 13.5t-0.5 11.5v14v15q0 111 115 197q116 88 274 88q80 0 151 -17.5t132.5 -54.5t98 -103.5t36.5 -154.5h-305q0 86 -96 86q-80 0 -80 -53q0 -46 71 -61l170 -48
q84 -23 143 -62t83 -85q41 -73 41 -141q0 -120 -94.5 -198t-243.5 -99z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1200" 
d="M82 420h346q5 -64 48.5 -100.5t125.5 -36.5q75 0 124.5 34t49.5 93v5q0 13 -1.5 19.5t-11 17.5t-30.5 27q-33 26 -98 47l-166 56q-364 99 -369 415q0 190 140 312q139 121 354 121q216 0 350 -119q135 -117 135 -322h-342q0 131 -147 131q-69 0 -109.5 -30.5t-40.5 -81.5
q0 -75 131 -115l162 -51q391 -120 391 -430q0 -146 -84 -250q-82 -102 -196 -142q-107 -38 -242 -38q-240 0 -379 118q-141 120 -141 320zM987 1890l-225 -354h-320l-223 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="985" 
d="M500 340l-121 33q-78 21 -139.5 56.5t-94.5 70.5q-32 36 -45 75q-3 11 -5.5 20.5t-4.5 16.5t-3 15.5t-1.5 12.5t-1 13.5t-0.5 11.5v14v15q0 111 115 197q116 88 274 88q80 0 151 -17.5t132.5 -54.5t98 -103.5t36.5 -154.5h-305q0 86 -97 86q-79 0 -79 -53q0 -46 71 -61
l170 -48q84 -23 143 -62t83 -85q41 -73 41 -141q0 -149 -132 -226q-133 -77 -301 -77q-162 0 -290 90q-125 91 -125 247h305q0 -40 29 -69t81 -29q42 0 71.5 17.5t29.5 46.5q0 20 -16.5 31t-69.5 24zM879 1481l-226 -355h-319l-223 355h219l164 -185l163 185h222z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1181" 
d="M420 0v1112h-365v299h1071v-299h-364v-1112h-342zM467 -618l-148 86q71 96 99 186q28 92 31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="774" 
d="M215 1296h322v-342h200v-239h-200v-715h-322v715h-174v239h174v342zM256 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1181" 
d="M55 1112v299h1071v-299h-364v-1112h-342v1112h-365zM975 1890l-225 -354h-320l-223 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="774" 
d="M770 1071l-127 78q70 103 96 188q30 88 33 232h238q-34 -325 -240 -498zM537 954h200v-239h-200v-715h-322v715h-174v239h174v342h322v-342z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1181" 
d="M420 0v580h-195v153h195v379h-365v299h1071v-299h-364v-379h194v-153h-194v-580h-342z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="774" 
d="M215 0v356h-111v154h111v205h-174v239h174v342h322v-342h200v-239h-200v-205h122v-154h-122v-356h-322z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1396" 
d="M608 1632q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 75 -41t36 22t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41q-58 43 -76 43zM141 514v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344
v-897q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1208" 
d="M528 1223q-19 0 -35.5 -24t-17.5 -64h-153q6 129 65.5 197.5t140.5 68.5q48 0 103 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h153q-6 -127 -65.5 -194.5t-140.5 -67.5q-42 0 -101 41q-58 43 -78 43zM760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43
q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1396" 
d="M141 514v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385zM993 1774v-179h-590v179h590z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM913 1364v-178h-589v178h589z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1396" 
d="M1255 514q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897zM420 1835h182q0 -55 21.5 -78.5t74.5 -23.5q93 0 93 102h186q0 -121 -77.5 -195.5t-201.5 -74.5
q-129 0 -203.5 77.5t-74.5 192.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM340 1425h182q0 -55 21.5 -78.5t74.5 -23.5q93 0 93 102h186q0 -121 -77.5 -195.5t-201.5 -74.5
q-129 0 -203.5 77.5t-74.5 192.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1396" 
d="M1255 514q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897zM438 1788q0 109 74 186q78 78 184 78q109 0 187 -78q75 -75 75 -186q0 -109 -75 -184
q-76 -76 -187 -76q-108 0 -184 76q-74 77 -74 184zM565 1788q0 -57 37.5 -95t93.5 -38q58 0 98 38t40 95t-40.5 98t-97.5 41q-56 0 -93.5 -39.5t-37.5 -99.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1208" 
d="M358 1378q0 110 74 187q77 77 184 77q110 0 187 -77q76 -76 76 -187q0 -108 -76 -184t-187 -76q-108 0 -184 76q-74 77 -74 184zM485 1378q0 -57 37.5 -95t93.5 -38q58 0 98 38t40 95t-40.5 98.5t-97.5 41.5q-56 0 -93.5 -40t-37.5 -100zM760 453v501h325v-954h-325v150
q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1396" 
d="M1255 514q0 -236 -165 -385q-164 -147 -392 -147q-226 0 -393 147q-164 150 -164 385v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897zM406 1530l190 354h215l-215 -354h-190zM698 1530l220 354h221l-236 -354h-205z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1208" 
d="M760 453v501h325v-954h-325v150q-20 -79 -107 -125q-82 -43 -161 -43q-188 0 -284.5 121t-96.5 350v501h325v-491q0 -82 45 -124t111 -42q74 0 121 38t47 118zM307 1120l191 355h215l-215 -355h-191zM600 1120l219 355h221l-235 -355h-205z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1400" 
d="M776 -12l-80 -164q-27 -77 25 -111q44 -26 84 11q30 30 24 90l144 8q4 -124 -60 -194t-163 -70q-87 0 -150 63q-63 60 -63 152q0 62 22 104l70 109q-205 24 -346 168q-140 146 -140 360v897h344v-897q0 -94 64 -152q65 -59 149 -59q83 0 148 59t65 152v897h344v-897
q0 -213 -139 -358q-138 -144 -342 -168z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1208" 
d="M1085 0l-88 -176q-27 -77 25 -111q44 -26 84 11q30 30 24 90l144 8q4 -124 -60 -194t-163 -70q-87 0 -150 63q-63 60 -63 152q0 62 22 104l80 123h-180v150q-20 -79 -107 -125q-82 -43 -161 -43q-189 0 -285 118.5t-96 345.5v508h325v-497q0 -81 44.5 -120.5t111.5 -39.5
q74 0 121 38t47 118v501h325v-954z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1808" 
d="M762 1208h285l157 -600l246 803h373l-480 -1411h-264l-178 686l-172 -686h-266l-475 1411h370l244 -803zM739 1550h-219l223 355h320l225 -355h-221l-164 183z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1554" 
d="M1049 428l143 526h348l-328 -954h-276l-160 571l-151 -571h-277l-338 954h348l144 -526l145 526h256zM610 1141h-219l223 354h320l225 -354h-221l-164 182z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1357" 
d="M850 629v-629h-344v635l-551 776h407l314 -512l319 512h406zM512 1550h-219l223 355h320l225 -355h-221l-164 183z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1060" 
d="M535 492l178 462h346l-590 -1425h-342l240 598l-375 827h356zM367 1141h-220l224 354h319l225 -354h-221l-164 182z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1357" 
d="M330 1722q0 62 45.5 105t111.5 43q63 0 108.5 -43.5t45.5 -104.5q0 -63 -44 -106t-110 -43t-111.5 43t-45.5 106zM715 1722q0 61 46.5 104.5t112.5 43.5q63 0 107.5 -43.5t44.5 -104.5q0 -63 -44.5 -106t-107.5 -43q-66 0 -112.5 43t-46.5 106zM850 629v-629h-344v635
l-551 776h407l314 -512l319 512h406z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1232" 
d="M84 0v252l604 862h-577v297h1026v-252l-613 -860h613v-299h-1053zM492 1534l190 356h303l-283 -356h-210z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1048" 
d="M109 954h843v-237l-409 -422h409v-295h-870v233l403 426h-376v295zM393 1124l191 357h303l-283 -357h-211z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1232" 
d="M84 0v252l604 862h-577v297h1026v-252l-613 -860h613v-299h-1053zM440 1735q0 69 49 116t121 47t121 -47t49 -116q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1048" 
d="M109 954h843v-237l-409 -422h409v-295h-870v233l403 426h-376v295zM346 1325q0 69 49 116.5t121 47.5t121 -47.5t49 -116.5q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1232" 
d="M84 0v252l604 862h-577v297h1026v-252l-613 -860h613v-299h-1053zM993 1890l-225 -354h-319l-224 354h219l164 -184l164 184h221z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1048" 
d="M109 954h843v-237l-409 -422h409v-295h-870v233l403 426h-376v295zM901 1481l-225 -355h-320l-223 355h219l164 -185l164 185h221z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="708" 
d="M678 1456h123v-309h-123q-61 0 -96.5 -40.5t-36.5 -117.5v-989h-326v715h-178v239h178v35q0 225 121 346t338 121z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="1292" 
d="M164 -397l-105 -2l64 313h98q138 0 168 160l127 641h-182l45 239h182l12 62q41 223 185 346.5t356 128.5h117l-62 -311h-98q-75 0 -119.5 -54t-64.5 -172h182l-45 -239h-180l-146 -738q-35 -179 -188 -272q-153 -99 -346 -102z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1366" 
d="M907 324h-444l-119 -324h-367l521 1411h372l519 -1411h-363zM555 618h264l-137 465zM580 2036l157 295h303l-241 -303q66 -32 104.5 -95t38.5 -141q0 -108 -76 -184t-186 -76q-108 0 -184 76q-74 77 -74 184q0 81 43 147t115 97zM549 1792q0 -57 37.5 -95t93.5 -38
q57 0 97 38t40 95t-40 98t-97 41q-56 0 -93.5 -39.5t-37.5 -99.5z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1245" 
d="M797 954h325v-954h-325v109q-89 -127 -265 -127q-188 0 -325 137q-139 139 -139 360q0 222 139 361t325 139q78 0 150.5 -33.5t114.5 -93.5v102zM522 1622l158 295h303l-242 -303q66 -32 105 -95t39 -141q0 -108 -76 -184t-186 -76q-109 0 -185 76q-73 76 -73 184
q0 82 42.5 147.5t114.5 96.5zM457 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5zM492 1378q0 -57 37.5 -95t93.5 -38q57 0 97 38t40 95t-40 98.5t-97 41.5q-56 0 -93.5 -40t-37.5 -100z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1916" 
d="M879 991l-197 -385h197v385zM834 1411h966v-299h-575v-270h504v-289h-504v-254h592v-299h-936v2l-2 -2v311h-328l-195 -311h-413zM883 1534l190 356h303l-282 -356h-211z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="2017" 
d="M850 1124l190 357h303l-282 -357h-211zM1962 492q0 -26 -6 -105h-684q7 -64 71 -104.5t148 -40.5q114 0 182 80q19 -17 115 -84q85 -58 110 -78q-135 -178 -432 -178q-202 0 -342 122v-104h-325v109q-89 -127 -264 -127q-189 0 -326 137q-139 139 -139 360q0 222 139 361
t326 139q77 0 149.5 -33.5t114.5 -93.5v102h325v-96q139 121 342 121q212 0 353 -135q143 -131 143 -352zM459 336q60 -57 141.5 -57t138.5 57q60 60 60 141.5t-60 138.5q-57 60 -138.5 60t-141.5 -60q-57 -57 -57 -138.5t57 -141.5zM1272 569h381q-13 41 -37.5 71.5
t-52.5 45t-53.5 21t-47.5 4.5q-66 0 -120 -34.5t-70 -107.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1593" 
d="M700 1534l191 356h303l-283 -356h-211zM795 324q156 0 266 110q110 113 110 271q0 119 -67 213l-518 -531q92 -63 209 -63zM422 705q0 -123 67 -224l523 535q-89 71 -217 71q-159 0 -265 -112q-108 -111 -108 -270zM348 139l31 31l-197 -201l-96 97l164 167
q-174 204 -174 472q0 300 209 512q213 213 510 213q263 0 456 -166l162 166l96 -95l-163 -168q170 -206 170 -462q0 -301 -211 -512q-208 -211 -510 -211q-252 0 -447 157z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1153" 
d="M440 1124l191 357h303l-283 -357h-211zM158 -39l-84 84l108 111q-114 135 -114 323q0 223 147 361q151 139 358 139q179 0 314 -100l104 106l84 -82l-102 -104q112 -133 112 -320q0 -219 -151 -358t-361 -139q-172 0 -303 94zM375 479q0 -59 26 -98l267 272
q-52 25 -95 25q-82 0 -140 -58.5t-58 -140.5zM573 281q82 0 140.5 58t58.5 140q0 41 -22 90l-263 -270q41 -18 86 -18z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1200" 
d="M82 420h346q5 -64 48.5 -100.5t125.5 -36.5q75 0 124.5 34t49.5 93q0 14 -4 30q-6 15 -43.5 41.5t-93.5 44.5l-166 56q-364 99 -369 415q0 190 140 312q139 121 354 121q216 0 350 -119q135 -117 135 -322h-342q0 131 -147 131q-69 0 -109.5 -30.5t-40.5 -81.5
q0 -75 131 -115l162 -51q391 -120 391 -430q0 -146 -84 -250q-153 -180 -438 -180q-240 0 -379 118q-141 120 -141 320zM516 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="985" 
d="M500 340l-121 33q-78 21 -139.5 56.5t-94.5 70.5q-32 36 -45 75q-3 11 -5.5 20.5t-4.5 16.5t-3 15.5t-1.5 12.5t-1 13.5t-0.5 11.5v14v15q0 111 115 197q116 88 274 88q80 0 151 -17.5t132.5 -54.5t98 -103.5t36.5 -154.5h-305q0 86 -97 86q-79 0 -79 -53q0 -46 71 -61
l170 -48q84 -23 143 -62t83 -85q41 -73 41 -141q0 -149 -132 -226q-133 -77 -301 -77q-162 0 -290 90q-125 91 -125 247h305q0 -40 29 -69t81 -29q42 0 71.5 17.5t29.5 46.5q0 20 -16.5 31t-69.5 24zM401 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1198" 
d="M434 1141h-219l223 354h320l225 -354h-221l-164 182z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1198" 
d="M983 1481l-225 -355h-320l-223 355h219l164 -185l164 185h221z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1064" 
d="M254 1425h182q0 -55 21.5 -78.5t74.5 -23.5q93 0 93 102h186q0 -121 -77.5 -195.5t-201.5 -74.5q-129 0 -203.5 77.5t-74.5 192.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="675" 
d="M168 1325q0 69 49 116.5t121 47.5t121 -47.5t49 -116.5q0 -72 -49 -119t-121 -47q-74 0 -122 46.5t-48 119.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="1175" 
d="M328 1378q0 111 73 187q77 77 185 77q109 0 186 -77q76 -76 76 -187q0 -108 -76 -184t-186 -76q-109 0 -185 76q-73 76 -73 184zM455 1378q0 -57 37.5 -95t93.5 -38q57 0 97 38t40 95t-40 98.5t-97 41.5q-56 0 -93.5 -40t-37.5 -100z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1093" 
d="M598 47l-111 -223q-27 -77 25 -111q44 -26 84 11q31 31 25 90l143 8q4 -124 -60 -194t-163 -70q-87 0 -150 63q-63 60 -63 152q0 62 22 104l111 170h137z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="919" 
d="M371 1223q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41q-58 43 -75 43z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1169" 
d="M164 1120l190 355h215l-215 -355h-190zM457 1120l219 355h221l-235 -355h-205z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1808" 
d="M762 1208h285l157 -600l246 803h373l-480 -1411h-264l-178 686l-172 -686h-266l-475 1411h370l244 -803zM766 1534l-283 356h303l191 -356h-211z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1554" 
d="M1049 428l143 526h348l-328 -954h-276l-160 571l-151 -571h-277l-338 954h348l144 -526l145 526h256zM668 1124l-283 357h303l191 -357h-211z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1808" 
d="M1204 608l246 803h373l-480 -1411h-264l-178 686l-172 -686h-266l-475 1411h370l244 -803l160 600h285zM811 1534l190 356h304l-283 -356h-211z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1554" 
d="M1540 954l-328 -954h-276l-160 571l-151 -571h-277l-338 954h348l144 -526l145 526h256l146 -526l143 526h348zM659 1124l191 357h303l-283 -357h-211z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1808" 
d="M1204 608l246 803h373l-480 -1411h-264l-178 686l-172 -686h-266l-475 1411h370l244 -803l160 600h285zM557 1722q0 62 46 105t112 43q63 0 108 -43.5t45 -104.5q0 -63 -43.5 -106t-109.5 -43t-112 43t-46 106zM942 1722q0 61 47 104.5t113 43.5q63 0 107 -43t44 -105
q0 -63 -44 -106t-107 -43q-66 0 -113 43t-47 106z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1554" 
d="M1540 954l-328 -954h-276l-160 571l-151 -571h-277l-338 954h348l144 -526l145 526h256l146 -526l143 526h348zM426 1313q0 61 46 104t112 43q63 0 108 -43t45 -104q0 -63 -43.5 -106.5t-109.5 -43.5t-112 43.5t-46 106.5zM811 1313q0 61 47 104t113 43q63 0 107 -43
t44 -104q0 -63 -44 -106.5t-107 -43.5q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1357" 
d="M850 629v-629h-344v635l-551 776h407l314 -512l319 512h406zM586 1534l-283 356h303l191 -356h-211z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1060" 
d="M535 492l178 462h346l-590 -1425h-342l240 598l-375 827h356zM449 1124l-283 357h303l190 -357h-210z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="28" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1339" 
d="M1221 629v-277h-1102v277h1102z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1781" 
d="M1663 627v-277h-1544v277h1544z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="544" 
d="M356 1493l133 -82q-131 -180 -131 -465h-254q0 187 76 324q79 142 176 223z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="526" 
d="M205 866l-133 82q131 180 131 465h254q0 -182 -78 -323q-81 -147 -174 -224z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="532" 
d="M150 -283l-134 82q131 180 131 465h254q0 -184 -77 -323q-81 -147 -174 -224z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="903" 
d="M715 1493l133 -82q-131 -180 -131 -465h-254q0 187 76 324q79 142 176 223zM356 1493l133 -82q-131 -180 -131 -465h-254q0 187 76 324q79 142 176 223z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="884" 
d="M205 866l-133 82q131 180 131 465h254q0 -182 -78 -323q-81 -147 -174 -224zM563 866l-133 82q131 180 131 465h254q0 -182 -78 -323q-81 -147 -174 -224z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="890" 
d="M150 -283l-134 82q131 180 131 465h254q0 -184 -77 -323q-81 -147 -174 -224zM508 -283l-133 82q131 180 131 465h254q0 -182 -78 -323q-81 -147 -174 -224z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="897" 
d="M307 1092v319h283v-319h233v-209h-233v-1116h-283v1116h-233v209h233z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="954" 
d="M336 1092v319h282v-319h234v-209h-234v-437h234v-204h-234v-475h-282v475h-234v204h234v437h-234v209h234z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="1003" 
d="M166 487q0 139 98 230q98 94 238 94q139 0 237 -94q99 -92 99 -230q0 -139 -99 -233q-100 -92 -237 -92q-138 0 -238 92q-98 94 -98 233z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1898" 
d="M100 172q0 79 56.5 132.5t136.5 53.5q82 0 139 -53.5t57 -132.5q0 -81 -57 -135.5t-139 -54.5q-80 0 -136.5 54.5t-56.5 135.5zM756 172q0 79 56 132.5t136 53.5q82 0 139.5 -53.5t57.5 -132.5q0 -81 -57.5 -135.5t-139.5 -54.5q-80 0 -136 54.5t-56 135.5zM1411 172
q0 79 56.5 132.5t136.5 53.5t137 -53.5t57 -132.5q0 -81 -57 -135.5t-137 -54.5t-136.5 54.5t-56.5 135.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2070" 
d="M958 1343h195l-676 -1343h-192zM350 1370q114 0 193 -82q82 -79 82 -192q0 -115 -80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80zM449 1096q0 38 -30.5 68t-68.5 30q-41 0 -68.5 -30t-27 -67.5t26.5 -70.5q33 -26 70.5 -26.5
t67.5 27.5t30 69zM1092 528q113 0 192 -79q82 -79 82 -195q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195t78 195q79 79 195 79zM1092 352q-41 0 -69 -28.5t-28 -69.5t28 -68.5t69 -27.5t69.5 27.5t28.5 68.5t-29.5 69.5t-68.5 28.5zM1726 528
q114 0 193 -79q82 -79 82 -195q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195t78 195q79 79 194 79zM1726 352q-41 0 -68.5 -28.5t-27.5 -69.5t27.5 -68.5t68.5 -27.5q42 0 70.5 27.5t28.5 68.5q0 40 -30 69t-69 29z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="681" 
d="M356 53l-282 424l282 424h244l-283 -424l283 -424h-244z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="681" 
d="M326 901l282 -424l-282 -424h-244l283 424l-283 424h244z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="368" 
d="M-381 -18l926 1384h190l-923 -1384h-193z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="739" 
d="M63 1395q0 415 310 415q303 0 303 -415q0 -412 -303 -412q-310 0 -310 412zM274 1395q0 -209 99 -209q90 0 90 209q0 211 -90 211q-99 0 -99 -211z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="778" 
d="M289 1362h127v250zM631 1800v-438h92v-178h-92v-191h-215v191h-404l330 616h289z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="743" 
d="M489 1292q0 45 -37 76t-92 31q-114 0 -221 -82l-69 41l61 442h500v-178h-332l-16 -111q67 48 149 48q107 0 184 -74q78 -75 78 -193q0 -133 -96 -223q-94 -88 -256 -88q-81 0 -162 40.5t-135 106.5l158 123q76 -82 139 -82q62 0 104.5 36.5t42.5 86.5z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="741" 
d="M295 1255q0 -40 26 -66t72 -26t74.5 27t28.5 65q0 34 -28 60t-75 26q-41 0 -69.5 -26.5t-28.5 -59.5zM444 1509q88 0 170 -61q84 -60 84 -180q0 -128 -83.5 -206.5t-233.5 -78.5q-116 0 -196 76.5t-103 206.5q-39 179 33 366q33 84 114 133t191 45q187 -9 260 -157
l-160 -82q-29 63 -106 63q-73 0 -110 -61t-42 -152q7 33 55.5 60.5t126.5 27.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="657" 
d="M119 993l233 617h-311v190h610l-301 -807h-231z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="757" 
d="M487 1253q0 38 -29 65.5t-77 27.5q-37 4 -64 -12.5t-37.5 -42.5t-5.5 -56.5t27 -52.5q27 -27 80 -27q50 0 78 27t28 71zM102 1593q0 98 82 156q82 61 197 61q112 0 188 -61t76 -156q0 -50 -22.5 -94.5t-57.5 -56.5q58 -9 97 -72q43 -64 43 -131q0 -115 -95 -186
q-91 -70 -229 -70q-137 0 -231 70q-95 71 -95 186q0 72 36 131t95 72q-36 18 -60 62.5t-24 88.5zM297 1585q0 -33 23 -54.5t61 -21.5q37 0 59.5 21.5t22.5 54.5q0 27 -23.5 48.5t-58.5 21.5q-84 0 -84 -70z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="739" 
d="M455 1538q0 38 -26.5 64t-72.5 26t-73 -26t-27 -64q0 -35 26.5 -60.5t73.5 -25.5q42 0 70.5 26.5t28.5 59.5zM305 1284q-25 0 -54.5 6t-66 23t-65.5 43t-48.5 70.5t-19.5 99.5q0 128 84 206t234 78q118 0 197 -75.5t102 -206.5q19 -96 9 -189.5t-44 -179.5
q-32 -82 -113 -131t-190 -45q-187 9 -260 158l159 80q27 -62 107 -62q73 0 109.5 61t41.5 152q-7 -32 -58 -60t-124 -28z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="739" 
d="M63 12q0 416 310 416q303 0 303 -416q0 -411 -303 -411q-310 0 -310 411zM274 12q0 -209 99 -209q90 0 90 209q0 211 -90 211q-99 0 -99 -211z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="495" 
d="M39 76v178l360 186v-829h-217v530z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="698" 
d="M57 -389v125l340 383q25 41 7.5 72.5t-62.5 31.5q-72 0 -84 -82l-201 29q41 258 301 258q110 0 191 -72q80 -71 80 -178q-3 -120 -84 -201l-174 -180h262v-186h-576z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="692" 
d="M299 -47h-86v151h86q92 0 92 76q0 26 -24 45t-66 19q-78 0 -123 -51l-112 116q82 119 272 119q110 0 186 -70q79 -70 76 -157q0 -97 -115 -156q144 -52 150 -180t-88 -195q-92 -69 -232 -69q-212 0 -280 139l110 110q54 -75 162 -75q59 0 91 26.5t32 69.5q0 36 -38 59
t-93 23z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="776" 
d="M289 -20h127v249zM631 418v-438h92v-179h-92v-190h-215v190h-404l330 617h289z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="741" 
d="M489 -90q0 45 -37 75.5t-92 30.5q-114 0 -221 -82l-69 41l61 443h500v-178h-332l-16 -111q65 47 149 47q107 0 184 -74q78 -75 78 -192q0 -131 -96 -221t-256 -90q-81 0 -162 40.5t-135 106.5l158 123q41 -47 85 -67t78.5 -15t63 23.5t44 45.5t15.5 54z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="739" 
d="M295 -127q0 -40 26 -66t72 -26t74.5 27t28.5 65q0 34 -28 60t-75 26q-41 0 -69.5 -26.5t-28.5 -59.5zM444 127q88 0 170 -61q84 -60 84 -181q0 -128 -83.5 -206t-233.5 -78q-116 0 -196 76t-103 206q-39 180 33 367q33 84 114 133t191 45q187 -9 260 -158l-160 -82
q-30 64 -106 64q-73 0 -110 -61t-42 -152q7 33 55.5 60.5t126.5 27.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="659" 
d="M119 -389l233 616h-311v191h610l-301 -807h-231z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="757" 
d="M487 -129q0 38 -29 65t-77 27q-37 4 -64 -13t-37.5 -43t-5.5 -55.5t27 -50.5q28 -28 80 -28q50 0 78 27t28 71zM102 211q0 98 82 156q82 61 197 61q112 0 188 -61t76 -156q0 -50 -22.5 -95t-57.5 -57q58 -9 97 -71q43 -64 43 -131q0 -116 -95 -187q-90 -69 -229 -69
t-231 69q-95 71 -95 187q0 72 36 130.5t95 71.5q-36 18 -60 63t-24 89zM297 203q0 -33 23 -54.5t61 -21.5q37 0 59.5 21.5t22.5 54.5q0 27 -23 48t-59 21q-84 0 -84 -69z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="741" 
d="M457 156q0 38 -26.5 64t-72.5 26t-73 -26t-27 -64q0 -35 26.5 -60.5t73.5 -25.5q42 0 70.5 26.5t28.5 59.5zM307 -98q-25 0 -54.5 6t-66 23t-65.5 42.5t-48.5 70t-19.5 99.5q0 128 84 206.5t234 78.5q118 0 197 -75.5t102 -207.5q19 -96 9 -189.5t-44 -176.5
q-32 -84 -112.5 -133t-190.5 -45q-187 9 -260 157l159 80q27 -61 107 -61q73 0 109.5 61t41.5 152q-7 -32 -58 -60t-124 -28z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="1472" 
d="M422 702q0 -119 47 -208l211 598q-117 -35 -187.5 -142t-70.5 -248zM834 1102l-263 -729q48 -42 109 -58l270 752q-86 30 -116 35zM705 -16l-74 -215l-127 45l69 196q-54 12 -116 41l-76 -213l-129 47l88 248q-125 100 -194.5 249.5t-69.5 319.5q0 309 209 521
q210 213 516 207l80 219l129 -50l-68 -186q51 -12 119 -39l72 205l127 -51l-76 -213q127 -83 209 -211l-285 -186q-20 37 -43 67l-246 -688q-32 4 -37 4q103 0 194 54.5t142 146.5l281 -187q-93 -155 -257 -245t-360 -90q-41 0 -77 4z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="1437" 
d="M307 272v183h-160v131h160v88h-160v131h160v174q0 204 121 328q123 123 322 123q489 0 489 -494h-324q0 166 -145 166q-60 0 -97.5 -44.5t-37.5 -111.5v-141h205v-131h-205v-88h205v-131h-205v-183h342q33 0 45 20t12 64v103h320v-176q0 -118 -86 -201q-82 -82 -197 -82
h-993v272h229z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="1576" 
d="M250 0v496h-148v145h148v115h-148v145h148v510h309l422 -760v760h342v-510h152v-145h-152v-115h152v-145h-152v-496h-299l-432 770v-770h-342z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="1558" 
d="M604 471v-471h-342v739h-164v146h164v114h-164v146h164v266h584q161 0 272.5 -70.5t167.5 -195.5h195v-146h-156q7 -60 0 -114h156v-146h-193q-52 -125 -166 -196.5t-276 -71.5h-242zM829 1112h-225v-346h225q97 -5 137 84.5t0 178.5t-137 83z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1351" 
d="M442 471h-323v295h518q109 0 143 104h-661v146h659q-38 96 -141 96h-518v299h1143v-145h-244q91 -101 112 -250h132v-146h-129q-19 -144 -105.5 -246.5t-228.5 -136.5l344 -487h-398z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="2015" 
d="M1921 1411l-174 -510h160v-145h-209l-39 -115h248v-145h-297l-168 -496h-264l-179 686l-172 -686h-266l-168 496h-280v145h231l-39 115h-192v145h143l-170 510h371l243 -803l160 600h285l158 -600l245 803h373z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="2097" 
d="M1475 680v-209h-330v135q0 71 -53.5 119.5t-139.5 48.5h-456v-774h-328v1110h815q227 0 359.5 -118t132.5 -312zM623 430v209h329v-135q0 -71 53.5 -119.5t139.5 -48.5h457v774h327v-1110h-815q-226 0 -358.5 118t-132.5 312z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1708" 
d="M84 662h995v-136h-432q44 -104 135 -163.5t207 -59.5q109 0 199.5 53t140.5 144l287 -183q-88 -156 -254.5 -245.5t-372.5 -89.5q-255 0 -450 151q-193 153 -252 393h-203v136zM655 899h424v-135h-995v135h207q59 234 252 381q193 150 446 150q202 0 366.5 -87
t252.5 -237l-289 -182q-50 87 -136 134.5t-194 47.5q-112 0 -199 -54.5t-135 -152.5z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="827" 
d="M541 227h153v-227h-264q-315 0 -315 356v715q0 356 315 356q311 0 311 -376v-379q0 -81 -12.5 -130.5t-46.5 -92.5q-69 -87 -287 -87q0 -21 2 -36.5t10 -35.5t23 -32.5t43.5 -21.5t67.5 -9zM385 1077v-491h18q24 0 39 7t20.5 22.5t7 27t1.5 33.5v403q0 105 -39 105
q-17 -3 -27 -10.5t-14 -24t-5 -30t-1 -42.5z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="2000" 
d="M1241 1411v-1411h-299l-432 770v-770h-342v1411h309l422 -760v760h342zM1640 1427q115 0 193 -81q82 -79 82 -193q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195t78 195q79 79 194 79zM1522 1153q0 -51 33.5 -85t84.5 -34q38 0 67.5 21.5t43 52
t7.5 66.5t-34 63q-27 28 -63 34t-66 -7.5t-51.5 -43t-21.5 -67.5zM1901 651h-521v137h521v-137z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1724" 
d="M860 662h-188l71 579h-194v-579h-189v579h-237v170h795l215 -440l210 440h162l90 -749h-190l-47 415l-156 -334h-135l-160 336z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="1312" 
d="M1233 571h-879v-395q135 -129 312 -129q148 0 245.5 63.5t184.5 196.5l63 -39q-64 -98 -129.5 -158.5t-156.5 -95t-207 -34.5q-244 0 -400 172q-155 174 -155 421q0 253 153 422q153 172 410 172q256 0 405 -174q154 -174 154 -422zM354 639h633v334q-139 127 -313 127
q-196 0 -320 -127v-334z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1028" 
d="M135 645h758v-225h-758v225z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1220" 
d="M1079 547v-209h-547l-102 -211h-143l102 211h-252v209h355l100 209h-455v209h559l90 186h146l-90 -186h237v-209h-340l-104 -209h444z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1292" 
d="M1149 555v-238l-1024 394v250l1024 391v-236l-723 -282zM1151 0h-1026v207h1026v-207z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1292" 
d="M1167 961v-250l-1024 -394v238l723 279l-723 282v236zM1167 0h-1026v207h1026v-207z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="675" 
d="M291 -618l-148 86q71 96 99 186q27 88 30 231h263q-34 -326 -244 -503z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="2539" 
d="M1743 88v-559h-326v1425h307v-137q37 77 115 119.5t168 42.5q189 0 326 -137q139 -139 139 -361q0 -223 -139 -362q-137 -137 -326 -137q-171 0 -264 106zM1942 682q-82 0 -140 -61q-59 -62 -59 -144q-4 -60 33 -108t90 -69.5t114 -12t101 54.5q47 41 56.5 102.5
t-12.5 114.5t-72 90t-111 33zM481 471v-471h-342v1411h584q231 0 358 -139q125 -137 125 -332q0 -162 -88 -287q-85 -125 -250 -166l344 -487h-397l-303 471h-31zM707 1112h-226v-346h226q97 -5 137 84.5t0 178.5t-137 83z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1734" 
d="M164 1407h1407v-1407h-1407v1407zM872 680l-536 539v-773h166l-152 154v365l522 -519l525 519v-365l-152 -154h172v773zM395 197l-28 73h-31v-108h18v76l27 -76h20l23 76v-76h18v108h-28zM508 270q-51 0 -51 -55q0 -24 13.5 -38.5t37.5 -14.5q22 0 34.5 15.5t12.5 37.5
q0 24 -12.5 39.5t-34.5 15.5zM508 182q-13 0 -24 11t-11 22q0 12 11 23.5t24 11.5q24 0 24 -35q0 -33 -24 -33zM582 203h-27q0 -41 45 -41t45 35q0 22 -31 28q-32 6 -32 13q0 12 18 12q21 0 21 -12h18q0 32 -39 32t-39 -32q0 -17 33 -31q27 0 27 -10q0 -15 -21 -15
q-18 0 -18 21zM645 270v-20h35v-88h16v88h31v20h-82zM815 162l-43 108h-18l-45 -108h24l8 26h45l7 -26h22zM760 244h6l14 -37h-32zM872 270h-57v-108h19v45h26q19 0 19 -19v-26h26q-6 6 -6 26q0 19 -14 27q14 6 14 23q0 32 -27 32zM860 221h-26v29h26q19 0 19 -12
q0 -17 -19 -17zM954 270h-41v-108h41q52 0 52 59q0 49 -52 49zM954 182h-16v68h8q35 0 35 -35q0 -12 -8.5 -22.5t-18.5 -10.5zM1087 250v20h-75v-108h82v20h-58v25h51v18h-51v25h51zM1112 203h-18q0 -41 45 -41t45 35q0 22 -31 28q-35 6 -35 13q0 12 15 12q24 0 24 -12h21
q0 32 -39 32q-11 0 -23 -5.5t-19.5 -13.5t1 -20t35.5 -24q24 0 24 -10q0 -15 -18 -15q-27 0 -27 21zM1206 162v108h-14v-108h14zM1296 176l9 -14h12v59h-45v-18h24q-9 -21 -24 -21q-13 0 -23 11t-10 22q0 13 10 24t23 11q18 0 18 -21h27q-9 41 -45 41q-23 0 -37 -15.5
t-14 -39.5q0 -21 14.5 -37t36.5 -16q18 0 24 14zM1391 203l-45 67h-17v-108h17v67l45 -67h26v108h-26v-67z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="CR" horiz-adv-x="681" 
 />
    <glyph glyph-name="published" horiz-adv-x="1622" 
d="M750 877v-166h112q28 0 47.5 17t24 41.5t0.5 49t-22.5 41.5t-45.5 17h-116zM578 1030h290q113 0 176.5 -66.5t63.5 -168.5q0 -101 -63.5 -168.5t-176.5 -67.5h-118v-217h-172v688zM303 190q-207 213 -207 512t207 512q210 213 508 213t508 -213q209 -212 209 -512
t-209 -512q-207 -210 -508 -210t-508 210zM418 1100q-162 -162 -162 -398q0 -235 162 -397q164 -164 393 -164t393 164q162 162 162 397q0 236 -162 398q-163 166 -393 166t-393 -166z" />
    <glyph glyph-name="frenchfranc" horiz-adv-x="1544" 
d="M1038 260v-260h-303v614h-209v-614h-348v1411h914v-299h-566v-217h512v-217q34 119 99 177t145 58q122 0 191 -34l-60 -293q-60 32 -147 32q-118 0 -173 -85t-55 -273z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="813" 
d="M70 705q0 407 198 684q200 280 486 280v-237q-92 0 -191 -111q-100 -109 -164 -281q-63 -166 -63 -335q0 -167 63 -330q62 -160 162 -271q98 -104 193 -104v-236q-285 0 -486 269q-198 268 -198 672z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="813" 
d="M743 705q0 -404 -198 -672q-201 -269 -486 -269v236q97 0 191 104q97 103 162 271t65 330q0 164 -65 335q-66 176 -162 281q-99 111 -191 111v237q284 0 484 -280t200 -684z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="964" 
d="M846 877v-277h-727v277h727z" />
    <glyph glyph-name="at.case" horiz-adv-x="1972" 
d="M1182 459v256q0 97 -68 162q-64 67 -160 67q-92 0 -159 -67q-68 -65 -68 -162q0 -90 68 -158t159 -68q90 0 146 33v-200q-52 -27 -146 -27q-175 0 -301 123q-123 123 -123 297q0 178 123 301q126 123 301 123t297 -119q123 -117 129 -285v-286q0 -58 34 -91.5t81 -33.5
q59 0 102 41t64 112q39 133 39 264q0 284 -213 476q-210 192 -512 192q-289 0 -490 -207q-198 -204 -198 -497q0 -294 198 -492q201 -201 490 -201h125v-198h-125q-374 0 -629 258q-256 259 -256 633t256 636q259 265 629 265q144 4 284 -38t255 -122.5t203 -187.5
t135.5 -241t45.5 -276q0 -207 -88 -393q-46 -97 -129 -155t-186 -58q-142 0 -227.5 81.5t-85.5 242.5z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="700" 
d="M637 -229h-498v1882h498v-221h-234v-1434h234v-227z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="700" 
d="M63 1432v221h498v-1882h-498v227h234v1434h-234z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="847" 
d="M55 625v174q50 0 87.5 34t37.5 91v315q-3 183 119 299q124 115 299 115h190v-221h-163q-90 0 -135.5 -56t-45.5 -151v-279q0 -101 -61 -162q-57 -60 -125 -73q65 -9 125 -72q61 -61 61 -162v-278q0 -95 45.5 -151t135.5 -56h163v-221h-190q-176 0 -299 114
q-122 116 -119 299v316q0 57 -37.5 91t-87.5 34z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="847" 
d="M793 799v-174q-50 0 -87.5 -34t-37.5 -91v-316q3 -184 -121 -299q-120 -114 -297 -114h-191v221h164q90 0 135 56t45 151v278q0 99 60 162q63 63 127 72q-66 12 -127 73q-60 63 -60 162v279q0 95 -45 151t-135 56h-164v221h191q176 0 297 -115q124 -115 121 -299v-315
q0 -57 37.5 -91t87.5 -34z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="604" 
d="M473 0h-342l37 938h268zM106 1241q0 81 56.5 136t136.5 55q82 0 139.5 -55t57.5 -136q0 -79 -57.5 -132.5t-139.5 -53.5q-80 0 -136.5 53.5t-56.5 132.5z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="1126" 
d="M375 295l-283 424l283 424h243l-282 -424l282 -424h-243zM774 295l-282 424l282 424h244l-283 -424l283 -424h-244z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="591" 
d="M102 680q0 79 56.5 132.5t136.5 53.5t137 -53.5t57 -132.5q0 -81 -56.5 -134.5t-137.5 -53.5q-82 0 -137.5 53.5t-55.5 134.5z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="1126" 
d="M752 1143l282 -424l-282 -424h-244l283 424l-283 424h244zM352 1143l283 -424l-283 -424h-243l282 424l-282 424h243z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="1015" 
d="M442 940h293v-25q0 -78 -5.5 -121t-24.5 -94q-38 -101 -183 -163l-76 -33q-69 -26 -69 -103q0 -53 36 -81.5t99 -28.5q55 0 93 37.5t38 93.5h295q0 -211 -115.5 -325.5t-316.5 -114.5q-193 0 -313 108q-119 107 -119 291q0 144 68.5 243t193.5 144q61 23 83.5 60.5
t22.5 111.5zM778 1239q0 -79 -56 -132.5t-136 -53.5q-82 0 -139.5 53.5t-57.5 132.5q0 81 57.5 136t139.5 55q80 0 136 -55t56 -136z" />
    <glyph glyph-name="endash.case" horiz-adv-x="1343" 
d="M1223 854v-276h-1102v276h1102z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1785" 
d="M1665 854v-276h-1544v276h1544z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="966" 
d="M147 705q0 137 99 229q98 94 237 94q140 0 238 -94q98 -91 98 -229q0 -140 -98 -234q-100 -92 -238 -92q-137 0 -237 92q-99 95 -99 234z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="727" 
d="M375 295l-283 424l283 424h243l-282 -424l282 -424h-243z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="727" 
d="M352 1143l283 -424l-283 -424h-243l282 424l-282 424h243z" />
    <glyph glyph-name="cent.case" horiz-adv-x="1110" 
d="M502 1190v242h157v-236q225 -21 367 -231l-266 -160q-58 94 -168 94q-82 0 -140.5 -58.5t-58.5 -140.5t58.5 -140t140.5 -58q103 0 164 88l272 -152q-143 -210 -369 -231v-225h-157v231q-185 30 -305 162q-121 133 -121 325q0 191 121 324q119 131 305 166z" />
    <glyph glyph-name="zero.tnum" 
d="M416 670q0 -172 45.5 -272.5t136.5 -100.5q174 0 174 373q0 377 -174 377q-91 0 -136.5 -101.5t-45.5 -275.5zM88 670q0 694 510 694q140 0 241.5 -52t155.5 -151q105 -186 105 -491q0 -688 -502 -688q-510 0 -510 688z" />
    <glyph glyph-name="one.tnum" 
d="M262 807v280l569 287v-1374h-333v913z" />
    <glyph glyph-name="two.tnum" 
d="M618 295h437v-295h-944v184l555 643q61 70 61 123q0 46 -36 77.5t-105 31.5q-78 0 -117 -45.5t-53 -136.5l-295 45q32 208 160 325t329 117q182 0 314 -117q131 -116 131 -297t-140 -336z" />
    <glyph glyph-name="three.tnum" 
d="M528 569h-137v252h137q46 0 78.5 18t44.5 48q19 49 19 84q0 33 -37 76q-33 45 -103 45q-101 0 -182 -84l-192 157q64 98 179.5 152t250.5 47q180 -9 295 -121q115 -109 112 -258q0 -165 -176 -260q110 -41 173 -127.5t63 -196.5q0 -191 -152 -307q-149 -110 -366 -110
q-148 0 -264 65.5t-171 181.5l209 185q28 -62 87 -105t139 -43q93 0 143.5 44t50.5 110q0 64 -53 105.5t-148 41.5z" />
    <glyph glyph-name="four.tnum" 
d="M981 580h160v-269h-160v-311h-332v311h-661l579 1032h414v-763zM649 580v448l-239 -448h239z" />
    <glyph glyph-name="five.tnum" 
d="M795 489q0 87 -62 138t-162 51q-191 0 -331 -143l-142 79l107 729h815v-284h-557l-29 -182q107 73 248 73q182 0 309 -127t127 -334q0 -221 -157 -366q-155 -143 -416 -143q-142 0 -271 69q-133 71 -217 187l254 184q110 -131 234 -131q107 0 176 63q74 65 74 137z" />
    <glyph glyph-name="six.tnum" 
d="M692 862q127 0 264 -90q70 -46 113 -134.5t43 -197.5q0 -202 -135 -331q-136 -127 -379 -127q-194 0 -330 127q-135 129 -162 342q-12 92 -14 181.5t9.5 182t36.5 173.5t70 152t106.5 121.5t150.5 78t197 24.5q151 -6 255 -66t170 -196l-266 -127q-49 104 -170 104
q-70 0 -121.5 -34.5t-78.5 -96.5q-50 -121 -50 -235q14 56 92.5 102.5t198.5 46.5zM449 434q0 -69 46.5 -115.5t120.5 -46.5t123 47t49 115q0 63 -48.5 109.5t-123.5 46.5q-70 0 -118.5 -47t-48.5 -109z" />
    <glyph glyph-name="seven.tnum" 
d="M643 1047h-530v296h1015l-536 -1343h-369z" />
    <glyph glyph-name="eight.tnum" 
d="M397 451q0 -76 55 -126.5t142 -50.5q86 0 140 50.5t54 126.5q0 69 -53.5 120.5t-130.5 51.5h-16q-83 0 -137 -50.5t-54 -121.5zM150 997q0 159 131 263t313 104q180 0 305 -104q125 -101 125 -263q0 -78 -32.5 -147t-86.5 -94q90 -27 154 -129q63 -100 63 -209
q0 -193 -153 -316q-153 -120 -375 -120q-221 0 -377 120q-154 124 -154 316q0 117 56 210t153 128q-55 35 -88.5 103t-33.5 138zM457 985q0 -54 38 -88.5t99 -34.5q60 0 96.5 34.5t36.5 88.5q0 46 -36.5 82.5t-88.5 36.5h-12q-56 0 -94.5 -35.5t-38.5 -83.5z" />
    <glyph glyph-name="nine.tnum" 
d="M498 483q-128 0 -265 90q-70 46 -112.5 134t-42.5 198q0 204 135 330q135 129 379 129q195 0 328 -127q136 -127 163 -342q12 -92 14 -181.5t-9.5 -182t-36 -173.5t-69.5 -152t-106.5 -121.5t-150.5 -78t-197 -24.5q-151 6 -255.5 66t-170.5 196l267 127
q49 -105 170 -105q71 0 122 35t76 96q51 117 51 236q-14 -56 -92.5 -103t-197.5 -47zM741 911q0 69 -47 115.5t-121 46.5t-123 -47t-49 -115q0 -62 48.5 -108.5t123.5 -46.5q70 0 119 47t49 108z" />
    <glyph glyph-name="zero.taboldstyle" 
d="M594 -18q-210 0 -363 147t-153 377q0 235 153 379q154 145 363 145q211 0 362 -145q152 -146 152 -379q0 -228 -152 -377q-150 -147 -362 -147zM594 268q85 0 156 68q69 66 69 170q0 107 -69 170q-71 65 -156 65q-88 0 -156 -65q-67 -64 -67 -170q0 -103 67 -170
q68 -68 156 -68z" />
    <glyph glyph-name="one.taboldstyle" 
d="M287 770l544 283v-1053h-290v621l-254 -119v268z" />
    <glyph glyph-name="two.taboldstyle" 
d="M649 270h369v-270h-856v150l477 458q45 42 45 84q0 30 -29 52t-75 22q-102 0 -117 -115l-297 31q26 166 126 257t279 91q194 0 316 -90q119 -88 119 -215q0 -84 -27 -136t-105 -120z" />
    <glyph glyph-name="six.taboldstyle" 
d="M692 862q127 0 264 -90q70 -46 113 -134.5t43 -197.5q0 -202 -135 -331q-136 -127 -379 -127q-194 0 -330 127q-135 129 -162 342q-12 92 -14 181.5t9.5 182t36.5 173.5t70 152t106.5 121.5t150.5 78t197 24.5q151 -6 255 -66t170 -196l-266 -127q-49 104 -170 104
q-70 0 -121.5 -34.5t-78.5 -96.5q-50 -121 -50 -235q14 56 92.5 102.5t198.5 46.5zM449 434q0 -69 46.5 -115.5t120.5 -46.5t123 47t49 115q0 63 -48.5 109.5t-123.5 46.5q-70 0 -118.5 -47t-48.5 -109z" />
    <glyph glyph-name="eight.taboldstyle" 
d="M399 451q0 -76 55 -126.5t142 -50.5t141 50.5t54 126.5q0 69 -54 120.5t-131 51.5h-16q-83 0 -137 -50.5t-54 -121.5zM152 997q0 159 131 263t313 104q180 0 305 -104q125 -101 125 -263q0 -78 -32.5 -147t-86.5 -94q90 -27 154 -129q63 -100 63 -209q0 -193 -153 -316
q-153 -120 -375 -120q-221 0 -377 120q-153 123 -153 316q0 118 55.5 210.5t152.5 127.5q-55 35 -88.5 103t-33.5 138zM459 985q0 -54 38 -88.5t99 -34.5q60 0 96.5 34.5t36.5 88.5q0 46 -36.5 82.5t-88.5 36.5h-12q-56 0 -94.5 -35.5t-38.5 -83.5z" />
    <glyph glyph-name="three.taboldstyle" 
d="M535 182h-138v252h138q46 0 78 18t44 48q19 49 19 84q0 32 -37 75q-34 46 -102 46q-102 0 -183 -84l-192 157q64 98 179.5 152t250.5 47q180 -9 295 -121q115 -109 112 -258q0 -165 -176 -260q110 -41 173 -127.5t63 -196.5q0 -193 -152 -305q-147 -112 -366 -112
q-148 0 -264 65.5t-171 181.5l209 185q28 -62 87 -105t139 -43q93 0 143.5 44t50.5 110q0 64 -52.5 105.5t-147.5 41.5z" />
    <glyph glyph-name="four.taboldstyle" 
d="M987 190h160v-268h-160v-311h-332v311h-661l579 1032h414v-764zM655 190v449l-239 -449h239z" />
    <glyph glyph-name="five.taboldstyle" 
d="M797 100q0 87 -62 138t-162 51q-190 0 -331 -144l-142 80l107 729h815v-284h-557l-29 -183q109 74 248 74q182 0 309 -127t127 -334q0 -219 -157 -364q-158 -146 -416 -146q-136 0 -271 72q-137 73 -217 184l254 185q110 -131 234 -131q107 0 176 63q74 65 74 137z" />
    <glyph glyph-name="seven.taboldstyle" 
d="M647 657h-530v297h1016l-537 -1343h-369z" />
    <glyph glyph-name="nine.taboldstyle" 
d="M502 98q-127 0 -264 90q-70 46 -113 134.5t-43 197.5q0 204 135 330q135 129 379 129q195 0 328 -127q136 -127 163 -342q12 -92 14 -181.5t-9.5 -182t-36 -173.5t-69.5 -152t-106.5 -121.5t-150.5 -78t-197 -24.5q-151 6 -255.5 66t-170.5 196l267 127q49 -105 170 -105
q71 0 122 35t76 96q52 119 52 236q-14 -56 -93 -103t-198 -47zM745 526q0 69 -46.5 115.5t-120.5 46.5t-123 -47t-49 -115q0 -62 48.5 -108.5t123.5 -46.5q70 0 118.5 46.5t48.5 108.5z" />
    <glyph glyph-name="zero.oldstyle" horiz-adv-x="1212" 
d="M606 -18q-209 0 -362 147q-154 148 -154 377q0 234 154 379t362 145q212 0 363 -145t151 -379q0 -229 -151 -377q-150 -147 -363 -147zM606 268q85 0 156 68q69 66 69 170q0 107 -69 170q-71 65 -156 65q-87 0 -155 -65t-68 -170q0 -102 68 -170t155 -68z" />
    <glyph glyph-name="one.oldstyle" horiz-adv-x="770" 
d="M78 770l545 283v-1053h-291v621l-254 -119v268z" />
    <glyph glyph-name="two.oldstyle" horiz-adv-x="1064" 
d="M588 270h368v-270h-856v150l478 458q45 42 45 84q0 30 -29.5 52t-75.5 22q-102 0 -117 -115l-297 31q26 166 126.5 257t279.5 91q193 0 315 -90q119 -88 119 -215q0 -85 -26.5 -137t-104.5 -119z" />
    <glyph glyph-name="three.oldstyle" horiz-adv-x="1081" 
d="M481 182h-137v252h137q46 0 78.5 18t44.5 48q19 49 19 84q0 32 -37 75q-34 46 -103 46q-101 0 -182 -84l-192 157q64 98 179.5 152t250.5 47q180 -9 295 -121q115 -109 112 -258q0 -165 -176 -260q110 -41 173 -127.5t63 -196.5q0 -193 -152 -305q-147 -112 -367 -112
q-148 0 -263.5 65t-170.5 182l209 185q28 -62 86.5 -105t138.5 -43q93 0 144 44t51 110q0 64 -53 105.5t-148 41.5z" />
    <glyph glyph-name="four.oldstyle" horiz-adv-x="1253" 
d="M1020 190h160v-268h-160v-311h-332v311h-661l579 1032h414v-764zM688 190v449l-239 -449h239z" />
    <glyph glyph-name="five.oldstyle" horiz-adv-x="1200" 
d="M803 100q0 87 -61.5 138t-161.5 51q-191 0 -332 -144l-142 80l107 729h815v-284h-557l-29 -183q109 74 248 74q182 0 309 -127t127 -334q0 -219 -157 -364q-158 -146 -416 -146q-135 0 -270 72q-137 73 -217 184l253 185q110 -131 234 -131q107 0 176 63q74 65 74 137z
" />
    <glyph glyph-name="six.oldstyle" horiz-adv-x="1224" 
d="M711 862q127 0 264 -90q70 -46 112.5 -134t42.5 -198q0 -202 -135 -331q-136 -127 -379 -127q-193 0 -329 127q-135 129 -162 342q-12 92 -14 181.5t9.5 182t36 173.5t69.5 152t106.5 121.5t150.5 78t197 24.5q151 -6 255.5 -66t170.5 -196l-266 -127q-49 104 -170 104
q-71 0 -122.5 -35t-78.5 -96q-49 -119 -49 -235q14 56 92.5 102.5t198.5 46.5zM467 434q0 -69 47 -115.5t121 -46.5t123 47t49 115q0 63 -48.5 109.5t-123.5 46.5q-70 0 -119 -47t-49 -109z" />
    <glyph glyph-name="seven.oldstyle" horiz-adv-x="1079" 
d="M594 657h-531v297h1016l-536 -1343h-369z" />
    <glyph glyph-name="eight.oldstyle" horiz-adv-x="1251" 
d="M430 451q0 -76 55 -126.5t142 -50.5q86 0 140 50.5t54 126.5q0 69 -53.5 120.5t-130.5 51.5h-16q-83 0 -137 -50.5t-54 -121.5zM182 997q0 159 131 263t314 104q180 0 305 -104q125 -101 125 -263q0 -78 -32.5 -147t-86.5 -94q90 -27 154 -129q63 -100 63 -209
q0 -192 -154 -316q-153 -120 -374 -120t-377 120q-154 124 -154 316q0 117 56 210t153 128q-55 35 -89 103t-34 138zM489 985q0 -54 38.5 -88.5t99.5 -34.5q60 0 96.5 34.5t36.5 88.5q0 46 -36.5 82.5t-88.5 36.5h-12q-56 0 -95 -35.5t-39 -83.5z" />
    <glyph glyph-name="nine.oldstyle" horiz-adv-x="1185" 
d="M502 98q-127 0 -264 90q-70 46 -113 134.5t-43 197.5q0 204 135 330q135 129 379 129q195 0 328 -127q136 -127 163 -342q12 -92 14 -181.5t-9.5 -182t-36 -173.5t-69.5 -152t-106.5 -121.5t-150.5 -78t-197 -24.5q-151 6 -255.5 66t-170.5 196l267 127q49 -105 170 -105
q71 0 122 35t76 96q52 119 52 236q-14 -56 -93 -103t-198 -47zM745 526q0 69 -46.5 115.5t-120.5 46.5t-123 -47t-49 -115q0 -62 48.5 -108.5t123.5 -46.5q70 0 118.5 46.5t48.5 108.5z" />
    <glyph glyph-name="Lslash.sc" horiz-adv-x="931" 
d="M481 270h426v-270h-741v438l-131 -80v177l131 79v594h315v-399l176 113v-177l-176 -114v-361z" />
    <glyph glyph-name="Zcaron.sc" horiz-adv-x="1097" 
d="M713 1614h200l-196 -314h-287l-197 314h201l139 -156zM88 0v225l500 713h-477v270h886v-227l-510 -711h510v-270h-909z" />
    <glyph glyph-name="exclam.sc" horiz-adv-x="624" 
d="M436 418h-246l-30 790h305zM137 158q0 71 49.5 118.5t122.5 47.5q74 0 125 -47.5t51 -118.5q0 -73 -51 -122.5t-125 -49.5q-70 0 -121 50t-51 122z" />
    <glyph glyph-name="ampersand.sc" horiz-adv-x="1273" 
d="M731 479v4h-166q-79 0 -114 -65q-25 -53 11 -100.5t118 -47.5q13 0 28 3.5t38 16.5t41 34t31 61.5t13 93.5zM473 -16q-171 0 -271 95.5t-100 252.5q0 105 54.5 180t140.5 106q-71 24 -120.5 102t-49.5 173q0 135 102 223q103 92 271 107q110 7 209 -33t168 -117
l-218 -186q-34 51 -88 51q-53 0 -84.5 -29.5t-31.5 -64.5q0 -111 133 -111h143v119h270v-131h154v-215h-154v-129q0 -176 76 -176q38 0 117 55v-215q-119 -55 -211 -55q-164 0 -205 145q-43 -67 -133 -108q-85 -39 -172 -39z" />
    <glyph glyph-name="question.sc" horiz-adv-x="894" 
d="M502 422h-258v61q0 173 155 258l43 25q48 25 71.5 45.5t26.5 31t3 29.5q0 78 -101 78q-49 0 -80 -29t-30 -77l-262 4q1 183 100.5 282t269.5 99q167 0 270 -93.5t103 -250.5q0 -123 -58.5 -209t-164.5 -125q-49 -19 -69.5 -48.5t-18.5 -80.5zM203 158q0 71 49.5 118.5
t122.5 47.5q74 0 125 -47.5t51 -118.5q0 -73 -51 -122.5t-125 -49.5q-72 0 -122 49.5t-50 122.5z" />
    <glyph glyph-name="A.sc" horiz-adv-x="1228" 
d="M1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="B.sc" horiz-adv-x="1169" 
d="M1022 887q0 -77 -38 -145.5t-116 -96.5q101 -24 160 -110q57 -83 57 -179q0 -148 -94 -252t-309 -104h-532v1208h497q172 0 273.5 -87.5t101.5 -233.5zM461 938v-190h139q43 0 68.5 23.5t25.5 64.5q0 102 -88 102h-145zM625 494h-164v-224h164q61 0 96 32t35 81
q0 48 -33.5 79.5t-97.5 31.5z" />
    <glyph glyph-name="C.sc" horiz-adv-x="1275" 
d="M961 442l256 -176q-92 -133 -227.5 -208.5t-291.5 -75.5q-254 0 -436 182t-182 436q0 258 182 440q183 183 436 183q158 0 296.5 -76.5t224.5 -206.5l-263 -180q-39 64 -110 104.5t-148 40.5q-125 0 -215 -90q-88 -88 -88 -215t88 -215t215 -88q168 0 263 145z" />
    <glyph glyph-name="D.sc" horiz-adv-x="1267" 
d="M631 0h-481v1208h481q254 0 401 -172q148 -171 148 -436q0 -261 -148 -432q-146 -168 -401 -168zM631 934h-170v-660h170q111 0 176 95q63 92 63 231t-63 236q-64 98 -176 98z" />
    <glyph glyph-name="E.sc" horiz-adv-x="1056" 
d="M459 270h497v-270h-806v1208h790v-270h-481v-209h424v-266h-424v-193z" />
    <glyph glyph-name="F.sc" horiz-adv-x="985" 
d="M838 461h-375v-461h-313v1208h784v-270h-471v-209h375v-268z" />
    <glyph glyph-name="G.sc" horiz-adv-x="1355" 
d="M1182 999l-230 -194q-99 129 -250 129q-131 0 -219 -98q-90 -96 -90 -236q0 -134 90 -238q87 -100 219 -100q204 0 254 158h-327v231h671q0 -331 -158 -500t-440 -169q-262 0 -442 180t-180 438q0 260 180 440q183 183 442 183q146 0 271.5 -59.5t208.5 -164.5z" />
    <glyph glyph-name="H.sc" horiz-adv-x="1263" 
d="M459 1208v-469h340v469h315v-1208h-315v461h-340v-461h-309v1208h309z" />
    <glyph glyph-name="I.sc" horiz-adv-x="608" 
d="M459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="J.sc" horiz-adv-x="612" 
d="M-152 -387l27 278q61 -20 176 -20q99 0 99 127v1210h313v-1210q0 -192 -104 -306t-308 -114q-110 0 -203 35z" />
    <glyph glyph-name="K.sc" horiz-adv-x="1157" 
d="M1071 1208l-455 -596l566 -612h-428l-295 346v-346h-309v1208h309v-321l219 321h393z" />
    <glyph glyph-name="L.sc" horiz-adv-x="913" 
d="M463 270h426v-270h-739v1208h313v-938z" />
    <glyph glyph-name="M.sc" horiz-adv-x="1632" 
d="M489 1208l330 -710l322 710h260l147 -1208h-321l-68 612l-225 -473h-234l-225 473l-72 -612h-319l147 1208h258z" />
    <glyph glyph-name="N.sc" horiz-adv-x="1224" 
d="M428 1208l336 -612v612h311v-1208h-270l-346 618v-618h-309v1208h278z" />
    <glyph glyph-name="O.sc" horiz-adv-x="1392" 
d="M393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438q-177 -180 -437 -180q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="P.sc" horiz-adv-x="1120" 
d="M633 938h-174v-274h178q119 0 119 137t-123 137zM651 397h-192v-397h-309v1208h501q203 0 312 -121q108 -120 108 -286q0 -165 -108 -285q-107 -119 -312 -119z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="1409" 
d="M1081 111l111 -189l-219 -129l-125 207q-66 -14 -146 -14q-262 0 -442 180t-180 438q0 260 180 440q183 183 442 183q260 0 443 -183q182 -182 182 -440q0 -141 -66.5 -273t-179.5 -220zM604 416l215 125l103 -168q92 92 92 231t-92 234q-88 98 -220 98q-131 0 -221 -96
q-88 -94 -88 -236q0 -127 79.5 -214t211.5 -111z" />
    <glyph glyph-name="R.sc" horiz-adv-x="1163" 
d="M637 938h-178v-274h180q119 0 119 137t-121 137zM469 397h-10v-397h-309v1208h501q203 0 312 -121q108 -120 108 -286q0 -137 -75.5 -243.5t-207.5 -143.5l293 -414h-356z" />
    <glyph glyph-name="S.sc" horiz-adv-x="1064" 
d="M84 365h313q9 -111 135 -111q63 0 100.5 22.5t37.5 63.5q0 56 -113 96l-139 49q-163 52 -240.5 149.5t-77.5 211.5q0 169 119 270q120 105 307 105q189 0 304.5 -103.5t115.5 -281.5h-309q0 104 -115 104q-54 0 -84 -22.5t-30 -61.5q0 -52 104 -86l135 -43
q88 -28 158.5 -70t105.5 -82q36 -39 50 -86q12 -30 17.5 -53.5t6 -36.5t0.5 -43q0 -80 -28 -144.5t-73 -106.5t-105 -70.5t-121.5 -40.5t-125.5 -12q-205 0 -326.5 102.5t-121.5 280.5z" />
    <glyph glyph-name="T.sc" horiz-adv-x="983" 
d="M645 0h-307v938h-307v270h921v-270h-307v-938z" />
    <glyph glyph-name="U.sc" horiz-adv-x="1234" 
d="M616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z" />
    <glyph glyph-name="V.sc" horiz-adv-x="1075" 
d="M1042 1208l-370 -1208h-271l-368 1208h323l181 -700l178 700h327z" />
    <glyph glyph-name="W.sc" horiz-adv-x="1589" 
d="M541 563l125 471h258l125 -473l196 647h338l-412 -1208h-235l-143 555l-138 -555h-239l-408 1208h334z" />
    <glyph glyph-name="X.sc" horiz-adv-x="1234" 
d="M424 1208l192 -319l195 319h373l-381 -565l416 -643h-367l-236 393l-235 -393h-365l414 643l-379 565h373z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="1212" 
d="M762 535v-535h-311v541l-474 667h371l256 -426l264 426h363z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="1097" 
d="M88 0v225l500 713h-477v270h886v-227l-510 -711h510v-270h-909z" />
    <glyph glyph-name="Scaron.sc" horiz-adv-x="1064" 
d="M672 1614h200l-196 -314h-287l-194 314h198l139 -156zM84 365h313q9 -111 135 -111q63 0 100.5 22.5t37.5 63.5q0 56 -113 96l-139 49q-163 52 -240.5 149.5t-77.5 211.5q0 169 119 270q120 105 307 105q189 0 304.5 -103.5t115.5 -281.5h-309q0 104 -115 104
q-54 0 -84 -22.5t-30 -61.5q0 -52 104 -86l135 -43q88 -28 158.5 -70t105.5 -82q36 -39 50 -86q12 -30 17.5 -53.5t6 -36.5t0.5 -43q0 -80 -28 -144.5t-73 -106.5t-105 -70.5t-121.5 -40.5t-125.5 -12q-205 0 -326.5 102.5t-121.5 280.5z" />
    <glyph glyph-name="OE.sc" horiz-adv-x="1912" 
d="M696 293q127 0 217 90q91 91 91 217q0 128 -91 219q-90 90 -217 90q-126 0 -215 -92q-88 -88 -88 -217t88 -217q90 -90 215 -90zM1313 270h499v-270h-808v57q-144 -75 -308 -75q-256 0 -436 180t-180 438q0 260 180 440q183 183 436 183q154 0 308 -80v65h792v-270h-483
v-209h426v-266h-426v-193z" />
    <glyph glyph-name="Ydieresis.sc" horiz-adv-x="1212" 
d="M301 1456q0 57 40.5 96t100.5 39q57 0 96.5 -39t39.5 -96q0 -55 -39.5 -93t-96.5 -38t-99 38.5t-42 92.5zM635 1456q0 57 40.5 96t100.5 39q57 0 96 -39t39 -96q0 -54 -39 -92.5t-96 -38.5t-99 38.5t-42 92.5zM762 535v-535h-311v541l-474 667h371l256 -426l264 426h363z
" />
    <glyph glyph-name="exclamdown.sc" horiz-adv-x="620" 
d="M463 0h-305l30 791h246zM135 1051q0 72 51 122t121 50q74 0 125 -49.5t51 -122.5q0 -71 -51 -118.5t-125 -47.5q-73 0 -122.5 47.5t-49.5 118.5z" />
    <glyph glyph-name="questiondown.sc" horiz-adv-x="894" 
d="M391 788h258v-61q0 -173 -155 -258l-43 -25q-48 -25 -71.5 -45.5t-26.5 -31t-3 -29.5q0 -78 101 -78q48 0 79.5 29.5t30.5 77.5l262 -5q-1 -183 -100.5 -281.5t-269.5 -98.5q-167 0 -270 93.5t-103 250.5q0 123 58.5 208.5t164.5 124.5q49 19 69.5 48.5t18.5 80.5z
M690 1053q0 -71 -49.5 -118.5t-122.5 -47.5q-74 0 -125 47.5t-51 118.5q0 73 51 122.5t125 49.5q72 0 122 -49.5t50 -122.5z" />
    <glyph glyph-name="Agrave.sc" horiz-adv-x="1228" 
d="M750 1296h-187l-241 318h272zM1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Aacute.sc" horiz-adv-x="1228" 
d="M467 1296l156 318h274l-242 -318h-188zM1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Acircumflex.sc" horiz-adv-x="1228" 
d="M614 1468l-139 -155h-201l197 311h287l196 -311h-202zM1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Atilde.sc" horiz-adv-x="1228" 
d="M1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217zM733 1532h139q-6 -117 -57 -177.5t-123 -60.5q-35 0 -84 37q-46 37 -67 37q-7 0 -16 -6t-18 -24t-9 -44h-138q6 117 57 177.5t124 60.5q36 0 82 -37q49 -37 69 -37q6 0 14.5 6
t17.5 24t9 44z" />
    <glyph glyph-name="Adieresis.sc" horiz-adv-x="1228" 
d="M315 1456q0 57 41.5 96t98.5 39t97 -39t40 -96q0 -54 -40 -92.5t-97 -38.5t-98.5 38.5t-41.5 92.5zM647 1456q0 57 42 96t102 39q57 0 95 -39t38 -96q0 -56 -38 -93.5t-95 -37.5q-60 0 -102 38.5t-42 92.5zM1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336z
M727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Aring.sc" horiz-adv-x="1228" 
d="M393 1520q0 95 64 159q66 66 153 66q94 0 159 -65.5t65 -159.5q0 -92 -65 -157t-159 -65q-91 0 -154 65t-63 157zM610 1409q50 0 82.5 31t32.5 80t-33.5 81.5t-81.5 32.5q-45 0 -75.5 -32.5t-30.5 -81.5t30.5 -80t75.5 -31zM1225 0h-328l-100 272h-363l-98 -272h-332
l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="AE.sc" horiz-adv-x="1691" 
d="M520 260l-164 -260h-376l743 1208h854v-270h-483v-209h422v-262h-422v-197h497v-270h-811v260h-260zM780 825l-155 -303h155v303z" />
    <glyph glyph-name="Ccedilla.sc" horiz-adv-x="1280" 
d="M963 442l256 -176q-82 -122 -203.5 -196t-261.5 -86q67 -70 67 -158q0 -85 -56 -136.5t-144 -51.5q-87 0 -138 68.5t-49 166.5l129 -6q-9 -77 37 -98q32 -13 62 7t30 60q0 88 -90 154q-221 35 -371 207q-149 174 -149 403q0 258 182 440q183 183 436 183
q158 0 296.5 -76.5t224.5 -206.5l-263 -180q-39 64 -110 104.5t-148 40.5q-125 0 -215 -90q-88 -88 -88 -215t88 -215t215 -88q168 0 263 145z" />
    <glyph glyph-name="Egrave.sc" horiz-adv-x="1056" 
d="M670 1296h-189l-241 318h274zM459 270h497v-270h-806v1208h790v-270h-481v-209h424v-266h-424v-193z" />
    <glyph glyph-name="Eacute.sc" horiz-adv-x="1056" 
d="M956 0h-806v1208h790v-270h-481v-209h424v-266h-424v-193h497v-270zM543 1614h274l-242 -318h-188z" />
    <glyph glyph-name="Ecircumflex.sc" horiz-adv-x="1056" 
d="M518 1466l-139 -153h-199l195 311h287l198 -311h-203zM459 270h497v-270h-806v1208h790v-270h-481v-209h424v-266h-424v-193z" />
    <glyph glyph-name="Edieresis.sc" horiz-adv-x="1056" 
d="M956 0h-806v1208h790v-270h-481v-209h424v-266h-424v-193h497v-270zM223 1456q0 57 41 96t98 39t97.5 -39t40.5 -96q0 -54 -40.5 -92.5t-97.5 -38.5t-98 38.5t-41 92.5zM555 1456q0 57 41.5 96t101.5 39q57 0 95 -39t38 -96q0 -55 -38.5 -93t-94.5 -38q-60 0 -101.5 38.5
t-41.5 92.5z" />
    <glyph glyph-name="Igrave.sc" horiz-adv-x="608" 
d="M465 1296h-189l-241 318h272zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Iacute.sc" horiz-adv-x="608" 
d="M147 1286l154 324h272l-239 -324h-187zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Icircumflex.sc" horiz-adv-x="608" 
d="M301 1466l-139 -153h-199l195 311h286l197 -311h-201zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Idieresis.sc" horiz-adv-x="608" 
d="M0 1456q0 57 41 96t98 39q59 0 98 -38.5t39 -96.5q0 -56 -38.5 -93.5t-98.5 -37.5q-57 0 -98 38.5t-41 92.5zM334 1456q0 57 40.5 96t100.5 39q57 0 96 -39t39 -96q0 -54 -39 -92.5t-96 -38.5t-99 38.5t-42 92.5zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Eth.sc" horiz-adv-x="1296" 
d="M659 0h-481v487h-164v191h164v530h481q255 0 402 -172q147 -169 147 -436q0 -262 -147 -432q-146 -168 -402 -168zM489 678h177v-191h-177v-213h170q112 0 177 95q63 92 63 231t-63 236q-64 98 -177 98h-170v-256z" />
    <glyph glyph-name="Ntilde.sc" horiz-adv-x="1224" 
d="M428 1208l336 -612v612h311v-1208h-270l-346 618v-618h-309v1208h278zM731 1532h139q-6 -117 -57 -177.5t-123 -60.5q-35 0 -84 37q-46 37 -67 37q-7 0 -16 -6t-18 -24t-9 -44h-138q6 117 57 177.5t124 60.5q36 0 82 -37q49 -37 69 -37q6 0 14.5 6t17.5 24t9 44z" />
    <glyph glyph-name="Ograve.sc" horiz-adv-x="1392" 
d="M795 1296h-189l-241 318h274zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438q-177 -180 -437 -180
q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Oacute.sc" horiz-adv-x="1392" 
d="M569 1296l160 318h272l-241 -318h-191zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438
q-177 -180 -437 -180q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Ocircumflex.sc" horiz-adv-x="1392" 
d="M696 1466l-139 -153h-201l197 311h285l198 -311h-202zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438
q-177 -180 -437 -180q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Otilde.sc" horiz-adv-x="1392" 
d="M393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438q-177 -180 -437 -180q-256 0 -436 180t-180 438zM815 1532
h139q-6 -117 -57 -177.5t-123 -60.5q-35 0 -84 37q-46 37 -67 37q-7 0 -16 -6t-18 -24t-9 -44h-138q6 117 57 177.5t124 60.5q36 0 82 -37q49 -37 69 -37q6 0 14.5 6t17.5 24t9 44z" />
    <glyph glyph-name="Odieresis.sc" horiz-adv-x="1392" 
d="M389 1456q0 57 41 96t98 39t97.5 -39t40.5 -96q0 -54 -40.5 -92.5t-97.5 -38.5t-98 38.5t-41 92.5zM723 1456q0 57 40.5 96t100.5 39q57 0 96 -39t39 -96q0 -54 -39 -92.5t-96 -38.5q-60 0 -100.5 38.5t-40.5 92.5zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217
t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438q-177 -180 -437 -180q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Oslash.sc" horiz-adv-x="1392" 
d="M696 -18q-208 0 -379 133l-141 -146l-88 90l139 142q-147 175 -147 399q0 260 180 440q183 183 436 183q211 0 389 -140l138 140l86 -86l-140 -144q144 -175 144 -393q0 -258 -180 -438q-177 -180 -437 -180zM393 600q0 -94 49 -176l426 434q-71 53 -172 53
q-126 0 -215 -92q-88 -88 -88 -219zM696 291q125 0 215 90q90 93 90 219q0 92 -51 166l-418 -424q77 -51 164 -51z" />
    <glyph glyph-name="Ugrave.sc" horiz-adv-x="1234" 
d="M743 1296h-186l-244 318h273zM616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z" />
    <glyph glyph-name="Uacute.sc" horiz-adv-x="1234" 
d="M506 1296l156 318h274l-242 -318h-188zM616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z" />
    <glyph glyph-name="Ucircumflex.sc" horiz-adv-x="1234" 
d="M616 1466l-139 -153h-198l194 311h287l196 -311h-202zM616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z" />
    <glyph glyph-name="Udieresis.sc" horiz-adv-x="1234" 
d="M313 1456q0 57 41.5 96t98.5 39t97 -39t40 -96q0 -54 -40 -92.5t-97 -38.5t-98.5 38.5t-41.5 92.5zM645 1456q0 57 41.5 96t101.5 39q57 0 95.5 -39t38.5 -96q0 -55 -39 -93t-95 -38q-60 0 -101.5 38.5t-41.5 92.5zM616 -18q-195 0 -337 127q-142 130 -142 331v764h309
v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z" />
    <glyph glyph-name="Yacute.sc" horiz-adv-x="1212" 
d="M483 1296l158 318h272l-241 -318h-189zM762 535v-535h-311v541l-474 667h371l256 -426l264 426h363z" />
    <glyph glyph-name="Thorn.sc" horiz-adv-x="1040" 
d="M688 606q0 72 -35 113t-98 41h-96v-303h112q54 0 85.5 42.5t31.5 106.5zM588 201h-129v-201h-309v1208h309v-192h129q171 0 284 -113q117 -111 117 -293q0 -181 -117 -295q-114 -114 -284 -114z" />
    <glyph glyph-name="Amacron.sc" horiz-adv-x="1228" 
d="M356 1501h516v-162h-516v162zM1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Abreve.sc" horiz-adv-x="1228" 
d="M373 1561h164q0 -46 16.5 -66t58.5 -20q74 0 74 86h168q0 -106 -68 -173t-174 -67q-111 0 -175 69.5t-64 170.5zM1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Cacute.sc" horiz-adv-x="1275" 
d="M1217 266q-92 -133 -227.5 -208.5t-291.5 -75.5q-254 0 -436 182t-182 436q0 258 182 440q183 183 436 183q158 0 296.5 -76.5t224.5 -206.5l-263 -180q-39 64 -110 104.5t-148 40.5q-125 0 -215 -90q-88 -88 -88 -215t88 -215t215 -88q168 0 263 145zM580 1296l155 318
h275l-242 -318h-188z" />
    <glyph glyph-name="Ccircumflex.sc" horiz-adv-x="1275" 
d="M696 1522l-139 -156h-199l195 311h287l196 -311h-202zM961 442l256 -176q-92 -133 -227.5 -208.5t-291.5 -75.5q-254 0 -436 182t-182 436q0 258 182 440q183 183 436 183q158 0 296.5 -76.5t224.5 -206.5l-263 -180q-39 64 -110 104.5t-148 40.5q-125 0 -215 -90
q-88 -88 -88 -215t88 -215t215 -88q168 0 263 145z" />
    <glyph glyph-name="Cdotaccent.sc" horiz-adv-x="1275" 
d="M1217 266q-92 -133 -227.5 -208.5t-291.5 -75.5q-254 0 -436 182t-182 436q0 258 182 440q183 183 436 183q158 0 296.5 -76.5t224.5 -206.5l-263 -180q-39 64 -110 104.5t-148 40.5q-125 0 -215 -90q-88 -88 -88 -215t88 -215t215 -88q168 0 263 145zM545 1468
q0 63 43.5 105.5t109.5 42.5q63 0 107.5 -42.5t44.5 -105.5q0 -65 -44 -107t-108 -42q-66 0 -109.5 41.5t-43.5 107.5z" />
    <glyph glyph-name="Ccaron.sc" horiz-adv-x="1275" 
d="M848 1665h203l-197 -311h-287l-196 311h200l140 -154zM961 442l256 -176q-92 -133 -227.5 -208.5t-291.5 -75.5q-254 0 -436 182t-182 436q0 258 182 440q183 183 436 183q158 0 296.5 -76.5t224.5 -206.5l-263 -180q-39 64 -110 104.5t-148 40.5q-125 0 -215 -90
q-88 -88 -88 -215t88 -215t215 -88q168 0 263 145z" />
    <glyph glyph-name="Dcaron.sc" horiz-adv-x="1265" 
d="M758 1614h200l-196 -314h-285l-196 314h200l137 -158zM629 0h-479v1206h479q254 0 401 -172q148 -171 148 -434q0 -261 -148 -432q-146 -168 -401 -168zM629 934h-170v-662h170q113 0 178 95q63 92 63 233q0 134 -65 234q-63 100 -176 100z" />
    <glyph glyph-name="Aogonek.sc" horiz-adv-x="1228" 
d="M446 1208h336l443 -1208q-97 -82 -97 -164q0 -40 30.5 -60t62.5 -7q45 20 36 98l129 6q2 -103 -46 -169t-140 -66q-88 0 -144.5 51.5t-56.5 136.5q0 92 72 164l10 10h-184l-100 272h-363l-98 -272h-332zM727 541l-115 381l-102 -381h217z" />
    <glyph glyph-name="Emacron.sc" horiz-adv-x="1056" 
d="M260 1501h516v-162h-516v162zM459 270h497v-270h-806v1208h790v-270h-481v-209h424v-266h-424v-193z" />
    <glyph glyph-name="Ebreve.sc" horiz-adv-x="1056" 
d="M956 0h-806v1208h790v-270h-481v-209h424v-266h-424v-193h497v-270zM291 1561h164q0 -46 16.5 -66t58.5 -20q74 0 74 86h168q0 -106 -68 -173t-174 -67q-111 0 -175 69.5t-64 170.5z" />
    <glyph glyph-name="Edotaccent.sc" horiz-adv-x="1056" 
d="M956 0h-806v1208h790v-270h-481v-209h424v-266h-424v-193h497v-270zM379 1468q0 63 43.5 105.5t109.5 42.5q63 0 107.5 -42.5t44.5 -105.5q0 -65 -44 -107t-108 -42q-66 0 -109.5 41.5t-43.5 107.5z" />
    <glyph glyph-name="Ecaron.sc" horiz-adv-x="1056" 
d="M657 1614h203l-198 -314h-287l-195 314h199l139 -158zM459 270h497v-270h-806v1208h790v-270h-481v-209h424v-266h-424v-193z" />
    <glyph glyph-name="Gcircumflex.sc" horiz-adv-x="1355" 
d="M1182 999l-230 -194q-99 129 -250 129q-131 0 -219 -98q-90 -96 -90 -236q0 -134 90 -238q87 -100 219 -100q204 0 254 158h-327v231h671q0 -331 -158 -500t-440 -169q-262 0 -442 180t-180 438q0 260 180 440q183 183 442 183q146 0 271.5 -59.5t208.5 -164.5zM702 1522
l-139 -156h-198l194 311h287l196 -311h-202z" />
    <glyph glyph-name="Gbreve.sc" horiz-adv-x="1355" 
d="M463 1561h166q0 -48 17 -67t59 -19q71 0 71 86h170q0 -106 -67.5 -173t-173.5 -67q-111 0 -176.5 69.5t-65.5 170.5zM1182 999l-230 -194q-99 129 -250 129q-131 0 -219 -98q-90 -96 -90 -236q0 -134 90 -238q87 -100 219 -100q204 0 254 158h-327v231h671
q0 -331 -158 -500t-440 -169q-262 0 -442 180t-180 438q0 260 180 440q183 183 442 183q146 0 271.5 -59.5t208.5 -164.5z" />
    <glyph glyph-name="Eogonek.sc" horiz-adv-x="1056" 
d="M956 270v-270q-96 -81 -96 -164q0 -40 30 -60t62 -7q46 21 37 98l129 6q2 -103 -45 -169t-141 -66q-88 0 -144.5 51.5t-56.5 136.5q0 92 82 174h-663v1208h790v-270h-481v-209h424v-266h-424v-193h497z" />
    <glyph glyph-name="Dcroat.sc" horiz-adv-x="1296" 
d="M659 0h-481v487h-164v191h164v530h481q255 0 402 -172q147 -169 147 -436q0 -262 -147 -432q-146 -168 -402 -168zM489 678h177v-191h-177v-213h170q112 0 177 95q63 92 63 231t-63 236q-64 98 -177 98h-170v-256z" />
    <glyph glyph-name="Gdotaccent.sc" horiz-adv-x="1355" 
d="M559 1468q0 63 44.5 105.5t107.5 42.5q66 0 108.5 -42.5t42.5 -105.5q0 -66 -42.5 -107.5t-108.5 -41.5q-64 0 -108 42t-44 107zM1182 999l-230 -194q-99 129 -250 129q-131 0 -219 -98q-90 -96 -90 -236q0 -134 90 -238q87 -100 219 -100q204 0 254 158h-327v231h671
q0 -331 -158 -500t-440 -169q-262 0 -442 180t-180 438q0 260 180 440q183 183 442 183q146 0 271.5 -59.5t208.5 -164.5z" />
    <glyph glyph-name="Hcircumflex.sc" horiz-adv-x="1263" 
d="M631 1468l-139 -155h-199l194 311h287l197 -311h-203zM459 1208v-469h340v469h315v-1208h-315v461h-340v-461h-309v1208h309z" />
    <glyph glyph-name="Hbar.sc" horiz-adv-x="1292" 
d="M473 463v-463h-311v885h-127v151h127v172h311v-172h340v172h315v-172h129v-151h-129v-885h-315v463h-340zM813 739v146h-340v-146h340z" />
    <glyph glyph-name="Itilde.sc" horiz-adv-x="608" 
d="M459 0h-309v1208h309v-1208zM414 1532h139q-6 -117 -57 -177.5t-123 -60.5q-35 0 -84 37q-46 37 -68 37q-7 0 -16 -6t-18 -24t-9 -44h-137q6 117 57 177.5t123 60.5q36 0 82 -37q49 -37 70 -37q6 0 14.5 6t17.5 24t9 44z" />
    <glyph glyph-name="Imacron.sc" horiz-adv-x="608" 
d="M47 1501h516v-162h-516v162zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Iogonek.sc" horiz-adv-x="608" 
d="M150 1208h309v-1208q-97 -82 -97 -164q0 -40 30.5 -60t62.5 -7q46 21 37 98l129 6q3 -235 -187 -235q-88 0 -144.5 51.5t-56.5 136.5q0 92 82 174h-165v1208z" />
    <glyph glyph-name="IJ.sc" horiz-adv-x="1220" 
d="M457 -387l26 278q61 -20 176 -20q99 0 99 127v1210h313v-1210q0 -192 -104 -306t-308 -114q-109 0 -202 35zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Jcircumflex.sc" horiz-adv-x="612" 
d="M301 1466l-137 -153h-201l197 311h284l197 -311h-201zM-152 -387l27 278q61 -20 176 -20q99 0 99 127v1210h313v-1210q0 -192 -104 -306t-308 -114q-110 0 -203 35z" />
    <glyph glyph-name="Lacute.sc" horiz-adv-x="913" 
d="M889 0h-739v1208h313v-938h426v-270zM150 1296l159 318h273l-242 -318h-190z" />
    <glyph glyph-name="Lcaron.sc" horiz-adv-x="974" 
d="M463 270h426v-270h-739v1208h313v-938zM567 891l160 317h272l-241 -317h-191z" />
    <glyph glyph-name="Ldot.sc" horiz-adv-x="927" 
d="M463 270h426v-270h-739v1208h313v-938zM582 584q0 63 43.5 105t109.5 42q63 0 107.5 -42t44.5 -105q0 -65 -44 -107.5t-108 -42.5q-66 0 -109.5 42t-43.5 108z" />
    <glyph glyph-name="Nacute.sc" horiz-adv-x="1224" 
d="M764 1208h311v-1208h-270l-346 618v-618h-309v1208h278l336 -612v612zM446 1296l156 318h275l-242 -318h-189z" />
    <glyph glyph-name="Ncaron.sc" horiz-adv-x="1224" 
d="M752 1614h202l-198 -314h-285l-197 314h201l137 -158zM428 1208l336 -612v612h311v-1208h-270l-346 618v-618h-309v1208h278z" />
    <glyph glyph-name="Omacron.sc" horiz-adv-x="1392" 
d="M438 1501h514v-162h-514v162zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438q-177 -180 -437 -180
q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Obreve.sc" horiz-adv-x="1392" 
d="M453 1561h163q0 -45 18 -65.5t60 -20.5q74 0 74 86h170q0 -104 -69.5 -172t-174.5 -68q-111 0 -176 69.5t-65 170.5zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440
q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438q-177 -180 -437 -180q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Ohungarumlaut.sc" horiz-adv-x="1392" 
d="M920 1292h-181l181 316h196zM659 1292h-167l155 316h189zM393 600q0 -129 88 -217q90 -90 215 -90t215 90t90 217t-90 217q-89 92 -215 92q-125 0 -215 -90q-88 -88 -88 -219zM80 600q0 260 180 440q183 183 436 183q257 0 437 -183q180 -180 180 -440q0 -258 -180 -438
q-177 -180 -437 -180q-256 0 -436 180t-180 438z" />
    <glyph glyph-name="Racute.sc" horiz-adv-x="1163" 
d="M393 1296l156 318h272l-239 -318h-189zM459 938v-274h180q119 0 119 137t-121 137h-178zM459 0h-309v1208h501q203 0 312 -121q108 -120 108 -286q0 -137 -75.5 -243.5t-207.5 -143.5l293 -414h-356l-256 397h-10v-397z" />
    <glyph glyph-name="Rcaron.sc" horiz-adv-x="1163" 
d="M705 1614h202l-198 -314h-287l-193 314h197l141 -156zM637 938h-178v-274h180q119 0 119 137t-121 137zM469 397h-10v-397h-309v1208h501q203 0 312 -121q108 -120 108 -286q0 -137 -75.5 -243.5t-207.5 -143.5l293 -414h-356z" />
    <glyph glyph-name="Sacute.sc" horiz-adv-x="1064" 
d="M397 365q9 -111 135 -111q63 0 100.5 22.5t37.5 63.5q0 56 -113 96l-139 49q-163 52 -240.5 149.5t-77.5 211.5q0 169 119 270q120 105 307 105q189 0 304.5 -103.5t115.5 -281.5h-309q0 104 -115 104q-54 0 -84 -22.5t-30 -61.5q0 -52 104 -86l135 -43q270 -93 314 -238
q12 -30 17.5 -53.5t6 -36.5t0.5 -43q0 -80 -28 -144.5t-73 -106.5t-105 -70.5t-121.5 -40.5t-125.5 -12q-205 0 -326.5 102.5t-121.5 280.5h313zM416 1296l155 318h273l-240 -318h-188z" />
    <glyph glyph-name="Scircumflex.sc" horiz-adv-x="1064" 
d="M532 1468l-139 -155h-200l196 311h287l196 -311h-200zM84 365h313q9 -111 135 -111q63 0 100.5 22.5t37.5 63.5q0 56 -113 96l-139 49q-163 52 -240.5 149.5t-77.5 211.5q0 169 119 270q120 105 307 105q189 0 304.5 -103.5t115.5 -281.5h-309q0 104 -115 104
q-54 0 -84 -22.5t-30 -61.5q0 -52 104 -86l135 -43q88 -28 158.5 -70t105.5 -82q36 -39 50 -86q12 -30 17.5 -53.5t6 -36.5t0.5 -43q0 -80 -28 -144.5t-73 -106.5t-105 -70.5t-121.5 -40.5t-125.5 -12q-205 0 -326.5 102.5t-121.5 280.5z" />
    <glyph glyph-name="Scedilla.sc" horiz-adv-x="1069" 
d="M86 365h313q9 -111 136 -111q63 0 100 22.5t37 63.5q0 56 -113 96l-139 49q-163 52 -240.5 149.5t-77.5 211.5q0 169 119 270q120 105 307 105q189 0 304.5 -103.5t115.5 -281.5h-309q0 104 -115 104q-54 0 -84 -22.5t-30 -61.5q0 -52 104 -86l135 -43q88 -28 158.5 -70
t105.5 -82q36 -39 50 -86q12 -30 17.5 -53.5t6 -36.5t0.5 -43q0 -163 -104 -254q-105 -91 -252 -112q71 -74 71 -164q0 -85 -56 -136.5t-144 -51.5q-87 0 -138 68.5t-49 166.5l129 -6q-9 -77 37 -98q32 -13 62 7t30 60q0 84 -79 148q-189 11 -298.5 112.5t-109.5 268.5z" />
    <glyph glyph-name="Utilde.sc" horiz-adv-x="1234" 
d="M616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127zM721 1532h139q-6 -117 -57 -177.5t-123 -60.5q-35 0 -84 37q-46 37 -68 37q-7 0 -16 -6t-18 -24t-9 -44h-137
q6 117 57 177.5t123 60.5q36 0 82 -37q49 -37 70 -37q6 0 14.5 6t17.5 24t9 44z" />
    <glyph glyph-name="Umacron.sc" horiz-adv-x="1234" 
d="M362 1501h515v-162h-515v162zM616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z" />
    <glyph glyph-name="Ubreve.sc" horiz-adv-x="1234" 
d="M377 1561h166q0 -46 16.5 -66t58.5 -20q72 0 72 86h170q0 -104 -69 -172t-173 -68q-112 0 -176.5 69t-64.5 171zM616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127z
" />
    <glyph glyph-name="Uring.sc" horiz-adv-x="1234" 
d="M616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127zM399 1520q0 95 64 159q66 66 153 66q94 0 159 -65.5t65 -159.5q0 -92 -65 -157t-159 -65q-91 0 -154 65t-63 157z
M616 1409q50 0 82.5 31t32.5 80t-33.5 81.5t-81.5 32.5q-45 0 -75.5 -32.5t-30.5 -81.5t30.5 -80t75.5 -31z" />
    <glyph glyph-name="Uhungarumlaut.sc" horiz-adv-x="1234" 
d="M616 -18q-195 0 -337 127q-142 130 -142 331v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -204 -142 -331t-340 -127zM805 1292h-180l180 316h196zM545 1292h-168l155 316h189z" />
    <glyph glyph-name="Uogonek.sc" horiz-adv-x="1234" 
d="M672 -14q-80 -65 -80 -150q0 -40 30 -60t62 -7q46 21 37 98l129 6q2 -103 -45 -169t-141 -66q-88 0 -144.5 51.5t-56.5 136.5q0 93 69 162q-169 27 -282 149t-113 303v764h309v-764q0 -70 51 -119t119 -49q69 0 119.5 48.5t50.5 119.5v764h312v-764q0 -187 -123 -313
q-123 -123 -303 -141z" />
    <glyph glyph-name="Wcircumflex.sc" horiz-adv-x="1589" 
d="M795 1468l-140 -155h-198l192 311h289l197 -311h-203zM541 563l125 471h258l125 -473l196 647h338l-412 -1208h-235l-143 555l-138 -555h-239l-408 1208h334z" />
    <glyph glyph-name="Ycircumflex.sc" horiz-adv-x="1212" 
d="M604 1468l-139 -155h-199l195 311h287l196 -311h-203zM762 535v-535h-311v541l-474 667h371l256 -426l264 426h363z" />
    <glyph glyph-name="Tcaron.sc" horiz-adv-x="983" 
d="M621 1614h202l-198 -314h-287l-195 314h199l139 -158zM645 0h-307v938h-307v270h921v-270h-307v-938z" />
    <glyph glyph-name="Tbar.sc" horiz-adv-x="987" 
d="M340 0v481h-213v170h213v287h-307v270h921v-270h-307v-287h205v-170h-205v-481h-307z" />
    <glyph glyph-name="Zacute.sc" horiz-adv-x="1097" 
d="M88 0v225l500 713h-477v270h886v-227l-510 -711h510v-270h-909zM440 1296l156 318h276l-241 -318h-191z" />
    <glyph glyph-name="Zdotaccent.sc" horiz-adv-x="1097" 
d="M88 0v225l500 713h-477v270h886v-227l-510 -711h510v-270h-909zM381 1468q0 63 44 105.5t110 42.5q63 0 107 -42.5t44 -105.5q0 -65 -43.5 -107t-107.5 -42q-67 0 -110.5 41.5t-43.5 107.5z" />
    <glyph glyph-name="Wgrave.sc" horiz-adv-x="1589" 
d="M879 1296h-189l-241 318h272zM541 563l125 471h258l125 -473l196 647h338l-412 -1208h-235l-143 555l-138 -555h-239l-408 1208h334z" />
    <glyph glyph-name="Wacute.sc" horiz-adv-x="1589" 
d="M924 1034l125 -473l196 647h338l-412 -1208h-235l-143 555l-138 -555h-239l-408 1208h334l199 -645l125 471h258zM868 1614h273l-242 -318h-188z" />
    <glyph glyph-name="Wdieresis.sc" horiz-adv-x="1589" 
d="M924 1034l125 -473l196 647h338l-412 -1208h-235l-143 555l-138 -555h-239l-408 1208h334l199 -645l125 471h258zM485 1456q0 57 41.5 96t98.5 39t97 -39t40 -96q0 -54 -40 -92.5t-97 -38.5t-98.5 38.5t-41.5 92.5zM817 1456q0 57 42 96t102 39q57 0 95 -39t38 -96
q0 -55 -38.5 -93t-94.5 -38q-60 0 -102 38.5t-42 92.5z" />
    <glyph glyph-name="Aringacute.sc" horiz-adv-x="1228" 
d="M1225 0h-328l-100 272h-363l-98 -272h-332l442 1208h336zM645 1989h275l-209 -269q57 -30 90 -82.5t33 -117.5q0 -92 -65 -157t-159 -65q-91 0 -154 65t-63 157q0 65 32.5 118.5t88.5 83.5zM727 541l-115 381l-102 -381h217zM610 1409q50 0 82.5 31t32.5 80t-33.5 81.5
t-81.5 32.5q-45 0 -75.5 -32.5t-30.5 -81.5t30.5 -80t75.5 -31z" />
    <glyph glyph-name="AEacute.sc" horiz-adv-x="1691" 
d="M743 1296l156 318h272l-239 -318h-189zM-20 0l743 1208h854v-270h-483v-209h422v-262h-422v-197h497v-270h-811v260h-260l-164 -260h-376zM780 522v303l-155 -303h155z" />
    <glyph glyph-name="Oslashacute.sc" horiz-adv-x="1392" 
d="M608 1296l156 318h274l-241 -318h-189zM696 -18q-208 0 -379 133l-141 -146l-88 90l139 142q-147 175 -147 399q0 260 180 440q183 183 436 183q211 0 389 -140l138 140l86 -86l-140 -144q144 -175 144 -393q0 -258 -180 -438q-177 -180 -437 -180zM393 600
q0 -94 49 -176l426 434q-71 53 -172 53q-126 0 -215 -92q-88 -88 -88 -219zM696 291q125 0 215 90q90 93 90 219q0 92 -51 166l-418 -424q77 -51 164 -51z" />
    <glyph glyph-name="Ibreve.sc" horiz-adv-x="608" 
d="M66 1561h165q0 -48 17 -67t59 -19q72 0 72 86h170q0 -104 -69 -172t-173 -68q-112 0 -176.5 69t-64.5 171zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="Eng.sc" horiz-adv-x="1222" 
d="M778 20l-319 603v-623h-309v1208h276l338 -635v635h309v-1212q0 -192 -103.5 -306t-307.5 -114q-110 0 -203 35l26 274q61 -20 177 -20q53 0 84.5 43t31.5 112z" />
    <glyph glyph-name="Ygrave.sc" horiz-adv-x="1212" 
d="M705 1296h-187l-244 318h273zM762 535v-535h-311v541l-474 667h371l256 -426l264 426h363z" />
    <glyph glyph-name="Gcommaaccent.sc" horiz-adv-x="1355" 
d="M596 -545l-139 86q125 174 125 340h241q0 -233 -227 -426zM1182 999l-230 -194q-99 129 -250 129q-131 0 -219 -98q-90 -96 -90 -236q0 -134 90 -238q87 -100 219 -100q204 0 254 158h-327v231h671q0 -331 -158 -500t-440 -169q-262 0 -442 180t-180 438q0 260 180 440
q183 183 442 183q146 0 271.5 -59.5t208.5 -164.5z" />
    <glyph glyph-name="Kcommaaccent.sc" horiz-adv-x="1157" 
d="M1071 1208l-455 -596l566 -612h-428l-295 346v-346h-309v1208h309v-321l219 321h393zM494 -545l-140 86q125 174 125 340h242q0 -233 -227 -426z" />
    <glyph glyph-name="Lcommaaccent.sc" horiz-adv-x="913" 
d="M463 270h426v-270h-739v1208h313v-938zM410 -545l-140 86q125 174 125 340h242q0 -233 -227 -426z" />
    <glyph glyph-name="Ncommaaccent.sc" horiz-adv-x="1224" 
d="M428 1208l336 -612v612h311v-1208h-270l-346 618v-618h-309v1208h278zM520 -545l-139 86q125 174 125 340h242q0 -233 -228 -426z" />
    <glyph glyph-name="Rcommaaccent.sc" horiz-adv-x="1163" 
d="M637 938h-178v-274h180q119 0 119 137t-121 137zM469 397h-10v-397h-309v1208h501q203 0 312 -121q108 -120 108 -286q0 -137 -75.5 -243.5t-207.5 -143.5l293 -414h-356zM485 -545l-139 86q125 174 125 340h242q0 -233 -228 -426z" />
    <glyph glyph-name="Tcommaaccent.sc" horiz-adv-x="983" 
d="M645 0h-307v938h-307v270h921v-270h-307v-938zM373 -545l-140 86q125 174 125 340h242q0 -233 -227 -426z" />
    <glyph glyph-name="Scommaaccent.sc" horiz-adv-x="1064" 
d="M84 365h313q9 -111 135 -111q63 0 100.5 22.5t37.5 63.5q0 56 -113 96l-139 49q-163 52 -240.5 149.5t-77.5 211.5q0 169 119 270q120 105 307 105q189 0 304.5 -103.5t115.5 -281.5h-309q0 104 -115 104q-54 0 -84 -22.5t-30 -61.5q0 -52 104 -86l135 -43
q88 -28 158.5 -70t105.5 -82q36 -39 50 -86q12 -30 17.5 -53.5t6 -36.5t0.5 -43q0 -80 -28 -144.5t-73 -106.5t-105 -70.5t-121.5 -40.5t-125.5 -12q-205 0 -326.5 102.5t-121.5 280.5zM440 -545l-139 86q125 174 125 340h242q0 -233 -228 -426z" />
    <glyph glyph-name="Idotaccent.sc" horiz-adv-x="608" 
d="M160 1475q0 61 42 102t105 41q62 0 104 -41t42 -102q0 -63 -42 -104.5t-104 -41.5q-63 0 -105 41.5t-42 104.5zM459 0h-309v1208h309v-1208z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="739" 
d="M63 938q0 416 310 416q303 0 303 -416q0 -412 -303 -412q-310 0 -310 412zM274 938q0 -209 99 -209q90 0 90 209q0 211 -90 211q-99 0 -99 -211z" />
    <glyph glyph-name="one.numr" horiz-adv-x="495" 
d="M39 1001v179l360 186v-829h-217v530z" />
    <glyph glyph-name="two.numr" horiz-adv-x="698" 
d="M57 537v125l340 382q25 41 7.5 73t-62.5 32q-72 0 -84 -82l-201 29q41 258 301 258q110 0 191 -72q80 -71 80 -178q-3 -120 -84 -201l-174 -180h262v-186h-576z" />
    <glyph glyph-name="three.numr" horiz-adv-x="692" 
d="M299 879h-86v151h86q92 0 92 76q0 26 -24 44.5t-66 18.5q-78 0 -123 -51l-112 117q82 119 272 119q110 0 186 -70q79 -70 76 -158q0 -96 -115 -155q144 -52 150 -180q6 -127 -88 -197q-95 -68 -232 -68q-212 0 -280 140l110 110q55 -76 162 -76q59 0 91 27t32 70
q0 37 -37.5 59.5t-93.5 22.5z" />
    <glyph glyph-name="four.numr" horiz-adv-x="776" 
d="M289 905h127v250zM631 1343v-438h92v-178h-92v-190h-215v190h-404l330 616h289z" />
    <glyph glyph-name="five.numr" horiz-adv-x="741" 
d="M489 836q0 45 -37 75.5t-92 30.5q-114 0 -221 -82l-69 41l61 442h500v-178h-332l-16 -110q65 47 149 47q107 0 184 -74q78 -75 78 -192q0 -134 -96 -224q-94 -88 -256 -88q-81 0 -161.5 40.5t-135.5 107.5l158 123q76 -82 139 -82q62 0 104.5 36.5t42.5 86.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="739" 
d="M295 799q0 -40 26 -66t72 -26t74.5 27t28.5 65q0 34 -28 60t-75 26q-41 0 -69.5 -26.5t-28.5 -59.5zM444 1053q87 0 170 -62q84 -60 84 -180q0 -128 -83.5 -206.5t-233.5 -78.5q-116 0 -196 76.5t-103 206.5q-39 180 33 367q33 84 114 133t191 45q187 -9 260 -158
l-160 -82q-30 64 -106 64q-73 0 -110 -61t-42 -152q7 33 55.5 60.5t126.5 27.5z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="659" 
d="M119 537l233 616h-311v190h610l-301 -806h-231z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="757" 
d="M487 797q0 38 -29 65t-77 27q-37 4 -64 -12.5t-37.5 -42.5t-5.5 -56.5t27 -52.5q27 -27 80 -27q50 0 78 27.5t28 71.5zM102 1137q0 97 82 155q83 62 197 62q112 0 188 -61t76 -156q0 -50 -22.5 -95t-57.5 -57q58 -9 97 -72q43 -64 43 -131q0 -115 -95 -186
q-91 -70 -229 -70q-137 0 -231 70q-95 71 -95 186q0 72 36 131t95 72q-36 18 -60 63t-24 89zM297 1128q0 -33 23 -54t61 -21t60 21t22 54q0 27 -23.5 48.5t-58.5 21.5q-84 0 -84 -70z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="741" 
d="M457 1081q0 38 -26.5 64t-72.5 26t-73 -26t-27 -64q0 -35 26.5 -60.5t73.5 -25.5q42 0 70.5 26.5t28.5 59.5zM307 827q-25 0 -54.5 6t-66 23t-65.5 43t-48.5 70.5t-19.5 99.5q0 128 84 206.5t234 78.5q118 0 197 -75.5t102 -207.5q19 -96 9 -189.5t-44 -179.5
q-32 -82 -113 -131t-190 -45q-187 9 -260 158l159 80q27 -62 107 -62q73 0 109.5 61t41.5 152q-7 -32 -58 -60t-124 -28z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="739" 
d="M63 401q0 416 310 416q303 0 303 -416q0 -411 -303 -411q-310 0 -310 411zM274 401q0 -208 99 -208q90 0 90 208q0 211 -90 211q-99 0 -99 -211z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="495" 
d="M39 465v178l360 186v-829h-217v530z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="698" 
d="M57 0v125l340 383q25 41 7.5 72.5t-62.5 31.5q-72 0 -84 -82l-201 29q41 258 301 258q110 0 191 -72q80 -71 80 -178q-3 -119 -84 -200l-174 -181h262v-186h-576z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="690" 
d="M297 342h-86v152h86q92 0 92 75q0 26 -24 45t-66 19q-78 0 -123 -51l-113 116q82 119 273 119q111 0 186 -69q79 -70 76 -158q0 -97 -115 -156q144 -52 150 -180q6 -127 -88 -197q-94 -67 -232 -67q-212 0 -280 139l110 111q55 -76 162 -76q59 0 91 26.5t32 69.5
q0 37 -37.5 59.5t-93.5 22.5z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="778" 
d="M291 369h127v249zM633 807v-438h92v-179h-92v-190h-215v190h-404l330 617h289z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="741" 
d="M487 299q0 45 -37 76t-92 31q-114 0 -221 -82l-69 41l61 442h500v-178h-332l-16 -111q65 47 149 47q108 0 184 -73q78 -75 78 -193q0 -133 -96 -223q-94 -88 -256 -88q-81 0 -162 40.5t-135 106.5l158 123q76 -82 139 -82q62 0 104.5 36.5t42.5 86.5z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="741" 
d="M295 262q0 -40 26 -66t72 -26t74.5 27t28.5 65q0 34 -28 60t-75 26q-41 0 -69.5 -26.5t-28.5 -59.5zM444 516q88 0 170 -61q84 -60 84 -181q0 -128 -83.5 -206t-233.5 -78q-116 0 -196 76t-103 206q-39 180 33 367q33 84 114 133t191 45q187 -9 260 -158l-160 -81
q-29 63 -106 63q-73 0 -110 -61t-42 -152q7 33 55.5 60.5t126.5 27.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="659" 
d="M119 0l233 616h-311v191h610l-301 -807h-231z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="757" 
d="M487 260q0 38 -29 65t-77 27q-37 4 -64 -12.5t-37.5 -42.5t-5.5 -56.5t27 -52.5q26 -26 80 -26q50 0 78 27t28 71zM102 600q0 98 82 156q82 61 197 61q112 0 188 -61t76 -156q0 -50 -22.5 -94.5t-57.5 -56.5q58 -9 97 -72q43 -64 43 -131q0 -116 -95 -187
q-90 -69 -229 -69t-231 69q-95 71 -95 187q0 72 36 131t95 72q-36 18 -60 62.5t-24 88.5zM297 592q0 -33 23 -54.5t61 -21.5q37 0 59.5 21.5t22.5 54.5q0 27 -23.5 48.5t-58.5 21.5q-84 0 -84 -70z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="739" 
d="M455 545q0 38 -26.5 64t-72.5 26t-73 -26t-27 -64q0 -35 26.5 -60.5t73.5 -25.5q42 0 70.5 26.5t28.5 59.5zM305 291q-25 0 -54.5 6t-66 23t-65.5 42.5t-48.5 70t-19.5 99.5q0 128 84 206.5t234 78.5q118 0 197 -75.5t102 -206.5q19 -96 9 -189.5t-44 -179.5
q-32 -82 -113 -131t-190 -45q-187 9 -260 157l159 80q27 -61 107 -61q73 0 109.5 61t41.5 152q-7 -32 -58 -60t-124 -28z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="677" 
d="M567 309h92v-309h-92q-217 0 -337.5 121t-120.5 346v35v954h325v-954v-35q1 -77 36.5 -117.5t96.5 -40.5z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="1044" 
d="M295 -59l61 159l-352 854h332l188 -489l191 489h332l-467 -1134q-72 -167 -160 -236q-81 -69 -236 -69q-65 0 -151 28v267q59 -15 129 -15q35 0 63 25q6 5 10.5 9.5t9 10t7 9t7 10.5t6 11t6.5 14t7 15t8 19.5t9 22.5z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="776" 
d="M664 309h92v-309h-92q-217 0 -338 121t-121 346v90l-176 -108v190l176 109v708h325v-506l201 125v-190l-201 -125v-293q1 -77 37 -117.5t97 -40.5z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="675" 
d="M113 1534l190 356h303l-282 -356h-211zM657 0h-92q-217 0 -338 121t-121 346v35v954h326v-954v-35q1 -77 36.5 -117.5t96.5 -40.5h92v-309z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="675" 
d="M565 309h92v-309h-92q-217 0 -338 121t-121 346v35v954h326v-954v-35q1 -77 36.5 -117.5t96.5 -40.5zM365 -618l-148 86q70 94 98 186t31 231h262q-34 -327 -243 -503z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="675" 
d="M614 958l-127 78q71 104 97 189q29 85 32 231h238q-34 -326 -240 -498zM657 0h-92q-217 0 -338 121t-121 346v35v954h326v-954v-35q1 -77 36.5 -117.5t96.5 -40.5h92v-309z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM414 1124l190 357h303l-282 -357h-211z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM485 1124l-282 357h303l190 -357h-211z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM395 1141h-219l223 354h320l225 -354h-221l-164 182z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM213 1313q0 61 46 104t112 43q63 0 108 -43t45 -104q0 -63 -43.5 -106.5t-109.5 -43.5t-112 43.5t-46 106.5zM598 1313q0 61 47 104t113 43q63 0 107 -43t44 -104q0 -63 -44 -106.5t-107 -43.5
q-66 0 -113 43.5t-47 106.5z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM477 1223q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 75 -41t36 22t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41q-58 43 -76 43z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM293 1378q0 110 74 187q77 77 184 77q109 0 186 -77q76 -76 76 -187q0 -108 -76 -184t-186 -76q-108 0 -184 76q-74 77 -74 184zM420 1378q0 -57 37.5 -95t93.5 -38q57 0 97 38t40 95t-40 98.5t-97 41.5
q-56 0 -93.5 -40t-37.5 -100z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM850 1364v-178h-590v178h590z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="1116" 
d="M483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207
t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM276 1425h183q0 -55 21.5 -78.5t74.5 -23.5q92 0 92 102h187q0 -121 -77.5 -195.5t-201.5 -74.5q-129 0 -204 77.5t-75 192.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="1116" 
d="M289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-179l-90 -176q-27 -77 25 -111q44 -26 84 11q30 30 24 90l144 8q4 -124 -60 -194t-163 -70q-87 0 -150 63q-63 60 -63 152q0 62 22 104l80 123v102q-42 -57 -114 -88.5t-142 -31.5
q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207t286 75q141 0 227 -51v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97zM483 203q61 0 116 37t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="1044" 
d="M424 1124l190 357h304l-283 -357h-211zM356 100l-352 854h332l188 -489l191 489h332l-467 -1134q-72 -167 -160 -236q-81 -69 -236 -69q-65 0 -151 28v267q59 -15 129 -15q34 0 61 21.5t39 48.5q2 4 7.5 16.5t12.5 28t13 31.5z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="1044" 
d="M174 1313q0 61 46 104t112 43q63 0 108 -43t45 -104q0 -63 -43.5 -106.5t-109.5 -43.5t-112 43.5t-46 106.5zM559 1313q0 61 47 104t113 43q63 0 107 -43t44 -104q0 -63 -44 -106.5t-107 -43.5q-66 0 -113 43.5t-47 106.5zM356 100l-352 854h332l188 -489l191 489h332
l-467 -1134q-72 -167 -160 -236q-81 -69 -236 -69q-65 0 -151 28v267q59 -15 129 -15q34 0 61 21.5t39 48.5q2 4 7.5 16.5t12.5 28t13 31.5z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="1755" 
d="M1004 563h380q-18 139 -190 139q-166 0 -190 -139zM457 -18q-186 0 -294 79.5t-108 225.5q0 130 106 205t285 75q157 0 228 -41v47q0 62 -47 104t-123 42q-134 0 -217 -105l-172 170q139 195 440 195q202 0 322 -105q135 105 321 105q211 0 352 -135q144 -132 144 -352
q0 -24 -6 -103h-684q7 -64 69 -101.5t150 -37.5q138 0 200 96l230 -151q-63 -106 -185.5 -159.5t-269.5 -53.5q-139 0 -236.5 51.5t-132.5 148.5q-30 -91 -133.5 -145.5t-238.5 -54.5zM492 213q134 0 180 129q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -29 32 -57.5
t79 -28.5z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="1755" 
d="M1004 563h380q-18 139 -190 139q-166 0 -190 -139zM457 -18q-186 0 -294 79.5t-108 225.5q0 130 106 205t285 75q157 0 228 -41v47q0 62 -47 104t-123 42q-134 0 -217 -105l-172 170q139 195 440 195q202 0 322 -105q135 105 321 105q211 0 352 -135q144 -132 144 -352
q0 -24 -6 -103h-684q7 -64 69 -101.5t150 -37.5q138 0 200 96l230 -151q-63 -106 -185.5 -159.5t-269.5 -53.5q-139 0 -236.5 51.5t-132.5 148.5q-30 -91 -133.5 -145.5t-238.5 -54.5zM492 213q134 0 180 129q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -29 32 -57.5
t79 -28.5zM768 1124l190 357h304l-283 -357h-211z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="1044" 
d="M295 -59l61 159l-352 854h332l188 -489l191 489h332l-467 -1134q-72 -167 -160 -236q-81 -69 -236 -69q-65 0 -151 28v267q59 -15 129 -15q35 0 63 25q6 5 10.5 9.5t9 10t7 9t7 10.5t6 11t6.5 14t7 15t8 19.5t9 22.5zM360 1141h-219l224 354h319l225 -354h-221l-164 182z
" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="956" 
d="M565 309h92v-309h-92q-217 0 -338 121t-121 346v35v954h326v-954v-35q1 -77 36.5 -117.5t96.5 -40.5zM543 606q0 79 56 133t136 54t137.5 -54t57.5 -133q0 -81 -57 -134.5t-138 -53.5q-82 0 -137 53.5t-55 134.5z" />
    <glyph glyph-name="t.alt1" horiz-adv-x="829" 
d="M670 309h67v-309h-108q-219 0 -320 84q-98 82 -98 307v324h-172v239h172v189l326 98v-287h198v-239h-198v-289q1 -70 31 -93.5t102 -23.5z" />
    <glyph glyph-name="tcaron.alt1" horiz-adv-x="829" 
d="M768 1071l-127 78q70 103 96 188q30 88 33 232h238q-34 -325 -240 -498zM737 0h-108q-219 0 -320 84q-98 82 -98 307v324h-172v239h172v189l326 98v-287h198v-239h-198v-289q6 -75 35.5 -97t97.5 -20h67v-309z" />
    <glyph glyph-name="tbar.alt1" horiz-adv-x="831" 
d="M672 309h67v-309h-108q-219 0 -320 84q-98 82 -98 307v60h-107v147h107v117h-172v239h172v189l326 98v-287h198v-239h-198v-117h123v-147h-123v-25q1 -70 31 -93.5t102 -23.5z" />
    <glyph glyph-name="tcommaaccent.alt1" horiz-adv-x="831" 
d="M672 309h67v-309h-108q-219 0 -320 84q-98 82 -98 307v324h-172v239h172v189l326 98v-287h198v-239h-198v-289q1 -70 31 -93.5t102 -23.5zM469 -618l-147 86q70 94 98 186t31 231h262q-34 -326 -244 -503z" />
    <glyph glyph-name="T.sc.alt1" horiz-adv-x="983" 
d="M645 0h-307v938h-307v270h921v-270h-307v-938z" />
    <glyph glyph-name="Tcaron.sc.alt1" horiz-adv-x="983" 
d="M621 1614h202l-198 -314h-287l-195 314h199l139 -158zM645 0h-307v938h-307v270h921v-270h-307v-938z" />
    <glyph glyph-name="Tbar.sc.alt1" horiz-adv-x="987" 
d="M340 0v481h-213v170h213v287h-307v270h921v-270h-307v-287h205v-170h-205v-481h-307z" />
    <glyph glyph-name="Tcommaaccent.sc.alt1" horiz-adv-x="983" 
d="M645 0h-307v938h-307v270h921v-270h-307v-938zM373 -545l-140 86q125 174 125 340h242q0 -233 -227 -426z" />
    <glyph glyph-name="aringacute.alt1" horiz-adv-x="1116" 
d="M459 1622l157 295h304l-242 -303q66 -32 104.5 -95t38.5 -141q0 -108 -76 -184t-186 -76q-108 0 -184 76q-74 77 -74 184q0 81 43 147t115 97zM428 1378q0 -57 37.5 -95t93.5 -38q57 0 97 38t40 95t-40 98.5t-97 41.5q-56 0 -93.5 -40t-37.5 -100zM483 203q61 0 116 37
t75 96q-61 49 -170 49q-62 0 -91.5 -27.5t-29.5 -64.5q0 -33 27 -61.5t73 -28.5zM289 614l-172 170q139 195 440 195q194 0 320 -105q129 -102 129 -307v-567h-326v102q-42 -57 -114 -88.5t-142 -31.5q-172 0 -269.5 80.5t-97.5 226.5q0 132 106 207t286 75q141 0 227 -51
v29q0 68 -51.5 115t-118.5 47q-138 0 -217 -97z" />
    <hkern u1="&#x20;" g2="lslash.alt1" k="96" />
    <hkern u1="&#x20;" g2="Dcroat.sc" k="29" />
    <hkern u1="&#x20;" g2="Eth.sc" k="29" />
    <hkern u1="&#x20;" g2="V.sc" k="55" />
    <hkern u1="&#x20;" u2="&#x2019;" k="31" />
    <hkern u1="&#x20;" u2="&#x142;" k="98" />
    <hkern u1="&#x20;" u2="&#x141;" k="20" />
    <hkern u1="&#x20;" u2="&#x110;" k="35" />
    <hkern u1="&#x20;" u2="&#xd0;" k="35" />
    <hkern u1="&#x20;" u2="v" k="49" />
    <hkern u1="&#x20;" u2="f" k="39" />
    <hkern u1="&#x20;" u2="V" k="63" />
    <hkern u1="&#x22;" u2="&#xee;" k="-29" />
    <hkern u1="&#x23;" g2="five.oldstyle" k="25" />
    <hkern u1="&#x23;" g2="four.oldstyle" k="119" />
    <hkern u1="&#x23;" u2="&#x34;" k="29" />
    <hkern u1="&#x24;" g2="four.oldstyle" k="37" />
    <hkern u1="&#x24;" g2="three.oldstyle" k="27" />
    <hkern u1="&#x26;" u2="X" k="-10" />
    <hkern u1="&#x26;" u2="V" k="39" />
    <hkern u1="&#x27;" u2="&#xee;" k="-29" />
    <hkern u1="&#x28;" g2="Jcircumflex.sc" k="-158" />
    <hkern u1="&#x28;" g2="Icircumflex.sc" k="-39" />
    <hkern u1="&#x28;" g2="M.sc" k="47" />
    <hkern u1="&#x28;" g2="J.sc" k="-158" />
    <hkern u1="&#x28;" g2="nine.oldstyle" k="20" />
    <hkern u1="&#x28;" g2="eight.oldstyle" k="43" />
    <hkern u1="&#x28;" g2="six.oldstyle" k="47" />
    <hkern u1="&#x28;" g2="four.oldstyle" k="70" />
    <hkern u1="&#x28;" g2="two.oldstyle" k="72" />
    <hkern u1="&#x28;" g2="one.oldstyle" k="68" />
    <hkern u1="&#x28;" g2="zero.oldstyle" k="90" />
    <hkern u1="&#x28;" u2="&#x135;" k="-133" />
    <hkern u1="&#x28;" u2="&#x134;" k="-211" />
    <hkern u1="&#x28;" u2="&#x12b;" k="-12" />
    <hkern u1="&#x28;" u2="&#x127;" k="-25" />
    <hkern u1="&#x28;" u2="&#xef;" k="-66" />
    <hkern u1="&#x28;" u2="&#xee;" k="-68" />
    <hkern u1="&#x28;" u2="&#x7b;" k="29" />
    <hkern u1="&#x28;" u2="x" k="43" />
    <hkern u1="&#x28;" u2="v" k="29" />
    <hkern u1="&#x28;" u2="j" k="-133" />
    <hkern u1="&#x28;" u2="f" k="27" />
    <hkern u1="&#x28;" u2="M" k="33" />
    <hkern u1="&#x28;" u2="J" k="-211" />
    <hkern u1="&#x28;" u2="&#x38;" k="45" />
    <hkern u1="&#x28;" u2="&#x36;" k="47" />
    <hkern u1="&#x28;" u2="&#x35;" k="20" />
    <hkern u1="&#x28;" u2="&#x34;" k="129" />
    <hkern u1="&#x28;" u2="&#x30;" k="37" />
    <hkern u1="&#x28;" u2="&#x28;" k="57" />
    <hkern u1="&#x29;" u2="&#x7d;" k="57" />
    <hkern u1="&#x29;" u2="]" k="39" />
    <hkern u1="&#x29;" u2="&#x29;" k="57" />
    <hkern u1="&#x2a;" g2="Hbar.sc" k="-49" />
    <hkern u1="&#x2a;" g2="Idieresis.sc" k="-16" />
    <hkern u1="&#x2a;" g2="X.sc" k="-31" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-100" />
    <hkern u1="&#x2a;" u2="&#x12b;" k="-29" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-45" />
    <hkern u1="&#x2a;" u2="&#x127;" k="-63" />
    <hkern u1="&#x2a;" u2="&#x126;" k="-41" />
    <hkern u1="&#x2a;" u2="&#x110;" k="-53" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-51" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-127" />
    <hkern u1="&#x2a;" u2="&#xd0;" k="-53" />
    <hkern u1="&#x2a;" u2="V" k="-14" />
    <hkern u1="&#x2b;" g2="four.oldstyle" k="70" />
    <hkern u1="&#x2b;" g2="three.oldstyle" k="55" />
    <hkern u1="&#x2b;" u2="&#x37;" k="31" />
    <hkern u1="&#x2d;" g2="four.oldstyle" k="51" />
    <hkern u1="&#x2d;" g2="three.oldstyle" k="41" />
    <hkern u1="&#x2d;" u2="&#x37;" k="43" />
    <hkern u1="&#x2f;" g2="M.sc" k="41" />
    <hkern u1="&#x2f;" g2="nine.oldstyle" k="100" />
    <hkern u1="&#x2f;" g2="eight.oldstyle" k="31" />
    <hkern u1="&#x2f;" g2="seven.oldstyle" k="23" />
    <hkern u1="&#x2f;" g2="six.oldstyle" k="27" />
    <hkern u1="&#x2f;" g2="five.oldstyle" k="94" />
    <hkern u1="&#x2f;" g2="four.oldstyle" k="201" />
    <hkern u1="&#x2f;" g2="three.oldstyle" k="90" />
    <hkern u1="&#x2f;" g2="two.oldstyle" k="78" />
    <hkern u1="&#x2f;" g2="one.oldstyle" k="66" />
    <hkern u1="&#x2f;" g2="zero.oldstyle" k="92" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-41" />
    <hkern u1="&#x2f;" u2="x" k="33" />
    <hkern u1="&#x2f;" u2="&#x38;" k="33" />
    <hkern u1="&#x2f;" u2="&#x36;" k="27" />
    <hkern u1="&#x2f;" u2="&#x34;" k="115" />
    <hkern u1="&#x2f;" u2="&#x30;" k="23" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="401" />
    <hkern u1="&#x30;" u2="&#x2044;" k="-127" />
    <hkern u1="&#x30;" u2="&#x7d;" k="33" />
    <hkern u1="&#x30;" u2="]" k="27" />
    <hkern u1="&#x30;" u2="\" k="20" />
    <hkern u1="&#x30;" u2="Y" k="41" />
    <hkern u1="&#x30;" u2="V" k="20" />
    <hkern u1="&#x30;" u2="&#x29;" k="37" />
    <hkern u1="&#x31;" u2="&#x2044;" k="-276" />
    <hkern u1="&#x32;" u2="&#x2044;" k="-326" />
    <hkern u1="&#x32;" u2="&#xc6;" k="-14" />
    <hkern u1="&#x32;" u2="Y" k="31" />
    <hkern u1="&#x33;" u2="&#x2044;" k="-160" />
    <hkern u1="&#x33;" u2="&#x7d;" k="29" />
    <hkern u1="&#x33;" u2="]" k="23" />
    <hkern u1="&#x33;" u2="\" k="23" />
    <hkern u1="&#x33;" u2="Y" k="41" />
    <hkern u1="&#x33;" u2="V" k="23" />
    <hkern u1="&#x33;" u2="&#x29;" k="33" />
    <hkern u1="&#x34;" u2="&#x2044;" k="-186" />
    <hkern u1="&#x34;" u2="&#xd0;" k="-25" />
    <hkern u1="&#x34;" u2="&#x7d;" k="41" />
    <hkern u1="&#x34;" u2="]" k="29" />
    <hkern u1="&#x34;" u2="\" k="25" />
    <hkern u1="&#x34;" u2="Y" k="33" />
    <hkern u1="&#x34;" u2="V" k="29" />
    <hkern u1="&#x34;" u2="T" k="23" />
    <hkern u1="&#x34;" u2="&#x29;" k="43" />
    <hkern u1="&#x35;" u2="&#x2044;" k="-141" />
    <hkern u1="&#x36;" u2="&#x2044;" k="-160" />
    <hkern u1="&#x36;" u2="Y" k="25" />
    <hkern u1="&#x37;" u2="&#x2212;" k="51" />
    <hkern u1="&#x37;" u2="&#xc6;" k="123" />
    <hkern u1="&#x37;" u2="&#xb7;" k="23" />
    <hkern u1="&#x37;" u2="&#xa2;" k="61" />
    <hkern u1="&#x37;" u2="Y" k="-72" />
    <hkern u1="&#x37;" u2="X" k="-14" />
    <hkern u1="&#x37;" u2="W" k="-59" />
    <hkern u1="&#x37;" u2="A" k="76" />
    <hkern u1="&#x37;" u2="&#x38;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="86" />
    <hkern u1="&#x37;" u2="&#x2f;" k="115" />
    <hkern u1="&#x37;" u2="&#x2d;" k="57" />
    <hkern u1="&#x37;" u2="&#x2b;" k="70" />
    <hkern u1="&#x37;" u2="&#x23;" k="55" />
    <hkern u1="&#x38;" u2="&#x2044;" k="-160" />
    <hkern u1="&#x38;" u2="&#x7d;" k="47" />
    <hkern u1="&#x38;" u2="]" k="33" />
    <hkern u1="&#x38;" u2="\" k="37" />
    <hkern u1="&#x38;" u2="Y" k="55" />
    <hkern u1="&#x38;" u2="V" k="29" />
    <hkern u1="&#x38;" u2="&#x29;" k="49" />
    <hkern u1="&#x39;" u2="&#x2044;" k="-115" />
    <hkern u1="&#x39;" u2="&#xc6;" k="25" />
    <hkern u1="&#x39;" u2="&#x7d;" k="37" />
    <hkern u1="&#x39;" u2="]" k="29" />
    <hkern u1="&#x39;" u2="\" k="23" />
    <hkern u1="&#x39;" u2="Y" k="43" />
    <hkern u1="&#x39;" u2="V" k="20" />
    <hkern u1="&#x39;" u2="&#x29;" k="39" />
    <hkern u1="&#x3d;" g2="four.oldstyle" k="47" />
    <hkern u1="&#x40;" u2="Y" k="70" />
    <hkern u1="&#x40;" u2="V" k="23" />
    <hkern u1="&#x40;" u2="T" k="27" />
    <hkern u1="B" g2="V.sc" k="12" />
    <hkern u1="B" g2="braceright.case" k="82" />
    <hkern u1="B" g2="bracketright.case" k="51" />
    <hkern u1="B" g2="parenright.case" k="61" />
    <hkern u1="B" u2="&#x7d;" k="51" />
    <hkern u1="B" u2="x" k="35" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="]" k="31" />
    <hkern u1="B" u2="X" k="27" />
    <hkern u1="B" u2="V" k="31" />
    <hkern u1="B" u2="&#x29;" k="49" />
    <hkern u1="C" u2="&#x135;" k="-53" />
    <hkern u1="C" u2="&#x127;" k="-51" />
    <hkern u1="C" u2="&#xef;" k="-14" />
    <hkern u1="C" u2="&#xee;" k="-80" />
    <hkern u1="C" u2="&#x34;" k="-27" />
    <hkern u1="D" u2="&#x141;" k="14" />
    <hkern u1="D" u2="&#x126;" k="14" />
    <hkern u1="E" u2="&#x135;" k="-14" />
    <hkern u1="E" u2="&#xee;" k="-41" />
    <hkern u1="F" g2="Jcircumflex.sc" k="-51" />
    <hkern u1="F" g2="Icircumflex.sc" k="-51" />
    <hkern u1="F" g2="M.sc" k="23" />
    <hkern u1="F" u2="&#x135;" k="-80" />
    <hkern u1="F" u2="&#x131;" k="51" />
    <hkern u1="F" u2="&#x12b;" k="-18" />
    <hkern u1="F" u2="&#x129;" k="-23" />
    <hkern u1="F" u2="&#x127;" k="-33" />
    <hkern u1="F" u2="&#xef;" k="-74" />
    <hkern u1="F" u2="&#xee;" k="-106" />
    <hkern u1="F" u2="&#xed;" k="20" />
    <hkern u1="F" u2="&#xdf;" k="20" />
    <hkern u1="F" u2="x" k="41" />
    <hkern u1="F" u2="v" k="10" />
    <hkern u1="F" u2="M" k="18" />
    <hkern u1="F" u2="&#x34;" k="45" />
    <hkern u1="F" u2="&#x2f;" k="86" />
    <hkern u1="F" u2="&#x2a;" k="-10" />
    <hkern u1="F" u2="&#x20;" k="49" />
    <hkern u1="K" u2="&#xef;" k="-35" />
    <hkern u1="K" u2="&#x32;" k="-20" />
    <hkern u1="L" g2="periodcentered.case" k="371" />
    <hkern u1="L" u2="&#xb7;" k="432" />
    <hkern u1="L" u2="&#x34;" k="-37" />
    <hkern u1="L" u2="&#x31;" k="45" />
    <hkern u1="M" g2="V.sc" k="12" />
    <hkern u1="M" u2="&#x7d;" k="29" />
    <hkern u1="M" u2="v" k="20" />
    <hkern u1="M" u2="V" k="31" />
    <hkern u1="M" u2="&#x29;" k="27" />
    <hkern u1="O" u2="&#x141;" k="14" />
    <hkern u1="O" u2="&#x126;" k="14" />
    <hkern u1="P" g2="M.sc" k="10" />
    <hkern u1="P" g2="braceright.case" k="84" />
    <hkern u1="P" g2="bracketright.case" k="51" />
    <hkern u1="P" g2="parenright.case" k="80" />
    <hkern u1="P" u2="&#x135;" k="-49" />
    <hkern u1="P" u2="&#x127;" k="-14" />
    <hkern u1="P" u2="&#xee;" k="-74" />
    <hkern u1="P" u2="X" k="45" />
    <hkern u1="P" u2="M" k="12" />
    <hkern u1="P" u2="&#x34;" k="39" />
    <hkern u1="P" u2="&#x2f;" k="82" />
    <hkern u1="P" u2="&#x20;" k="55" />
    <hkern u1="Q" u2="&#x141;" k="14" />
    <hkern u1="Q" u2="&#x126;" k="14" />
    <hkern u1="R" u2="&#xee;" k="-27" />
    <hkern u1="R" u2="&#x34;" k="23" />
    <hkern u1="T" g2="Jcircumflex.sc" k="-45" />
    <hkern u1="T" g2="Icircumflex.sc" k="-45" />
    <hkern u1="T" u2="&#x15d;" k="111" />
    <hkern u1="T" u2="&#x135;" k="-76" />
    <hkern u1="T" u2="&#x131;" k="111" />
    <hkern u1="T" u2="&#x12b;" k="-16" />
    <hkern u1="T" u2="&#x129;" k="-18" />
    <hkern u1="T" u2="&#x127;" k="-27" />
    <hkern u1="T" u2="&#xef;" k="-70" />
    <hkern u1="T" u2="&#xee;" k="-102" />
    <hkern u1="T" u2="&#xed;" k="27" />
    <hkern u1="T" u2="&#xdf;" k="23" />
    <hkern u1="T" u2="&#x40;" k="35" />
    <hkern u1="T" u2="&#x34;" k="92" />
    <hkern u1="U" u2="&#x131;" k="20" />
    <hkern u1="U" u2="&#xee;" k="-16" />
    <hkern u1="U" u2="&#xdf;" k="20" />
    <hkern u1="V" g2="Jcircumflex.sc" k="-49" />
    <hkern u1="V" g2="Hbar.sc" k="12" />
    <hkern u1="V" g2="Dcroat.sc" k="12" />
    <hkern u1="V" g2="Eth.sc" k="12" />
    <hkern u1="V" g2="Idieresis.sc" k="-35" />
    <hkern u1="V" g2="Icircumflex.sc" k="-49" />
    <hkern u1="V" g2="M.sc" k="43" />
    <hkern u1="V" g2="Lslash.sc" k="12" />
    <hkern u1="V" u2="&#x135;" k="-47" />
    <hkern u1="V" u2="&#x131;" k="78" />
    <hkern u1="V" u2="&#x12d;" k="-33" />
    <hkern u1="V" u2="&#x12b;" k="-33" />
    <hkern u1="V" u2="&#x129;" k="-12" />
    <hkern u1="V" u2="&#x127;" k="-18" />
    <hkern u1="V" u2="&#xef;" k="-86" />
    <hkern u1="V" u2="&#xee;" k="-68" />
    <hkern u1="V" u2="&#xed;" k="27" />
    <hkern u1="V" u2="&#xec;" k="-27" />
    <hkern u1="V" u2="&#xdf;" k="35" />
    <hkern u1="V" u2="&#xae;" k="20" />
    <hkern u1="V" u2="x" k="51" />
    <hkern u1="V" u2="v" k="14" />
    <hkern u1="V" u2="M" k="31" />
    <hkern u1="V" u2="&#x40;" k="29" />
    <hkern u1="V" u2="&#x38;" k="25" />
    <hkern u1="V" u2="&#x36;" k="20" />
    <hkern u1="V" u2="&#x34;" k="70" />
    <hkern u1="V" u2="&#x2f;" k="92" />
    <hkern u1="V" u2="&#x20;" k="63" />
    <hkern u1="W" g2="Jcircumflex.sc" k="-86" />
    <hkern u1="W" g2="Imacron.sc" k="-37" />
    <hkern u1="W" g2="Itilde.sc" k="-23" />
    <hkern u1="W" g2="Idieresis.sc" k="-74" />
    <hkern u1="W" g2="Icircumflex.sc" k="-86" />
    <hkern u1="W" u2="&#x159;" k="31" />
    <hkern u1="W" u2="&#x135;" k="-70" />
    <hkern u1="W" u2="&#x131;" k="72" />
    <hkern u1="W" u2="&#x12d;" k="-72" />
    <hkern u1="W" u2="&#x12b;" k="-70" />
    <hkern u1="W" u2="&#x129;" k="-14" />
    <hkern u1="W" u2="&#x127;" k="-53" />
    <hkern u1="W" u2="&#xef;" k="-115" />
    <hkern u1="W" u2="&#xee;" k="-92" />
    <hkern u1="W" u2="&#xed;" k="23" />
    <hkern u1="W" u2="&#xec;" k="-68" />
    <hkern u1="W" u2="&#xdf;" k="27" />
    <hkern u1="W" u2="&#x37;" k="-23" />
    <hkern u1="W" u2="&#x34;" k="59" />
    <hkern u1="X" g2="Jcircumflex.sc" k="-27" />
    <hkern u1="X" g2="Idieresis.sc" k="-14" />
    <hkern u1="X" g2="Icircumflex.sc" k="-31" />
    <hkern u1="X" g2="parenright.case" k="-10" />
    <hkern u1="X" u2="&#xef;" k="-61" />
    <hkern u1="X" u2="&#xae;" k="29" />
    <hkern u1="X" u2="v" k="92" />
    <hkern u1="X" u2="&#x2f;" k="-16" />
    <hkern u1="Y" g2="ydieresis.alt1" k="68" />
    <hkern u1="Y" g2="abreve.alt1" k="172" />
    <hkern u1="Y" g2="adieresis.alt1" k="115" />
    <hkern u1="Y" g2="agrave.alt1" k="158" />
    <hkern u1="Y" g2="Ibreve.sc" k="-2" />
    <hkern u1="Y" g2="Lacute.sc" k="35" />
    <hkern u1="Y" g2="Jcircumflex.sc" k="-88" />
    <hkern u1="Y" g2="Imacron.sc" k="-66" />
    <hkern u1="Y" g2="Itilde.sc" k="-51" />
    <hkern u1="Y" g2="Idieresis.sc" k="-102" />
    <hkern u1="Y" g2="Icircumflex.sc" k="-90" />
    <hkern u1="Y" g2="Iacute.sc" k="35" />
    <hkern u1="Y" g2="at.case" k="35" />
    <hkern u1="Y" u2="&#x17e;" k="76" />
    <hkern u1="Y" u2="&#x161;" k="55" />
    <hkern u1="Y" u2="&#x15d;" k="135" />
    <hkern u1="Y" u2="&#x159;" k="2" />
    <hkern u1="Y" u2="&#x135;" k="-35" />
    <hkern u1="Y" u2="&#x131;" k="154" />
    <hkern u1="Y" u2="&#x12d;" k="-96" />
    <hkern u1="Y" u2="&#x12b;" k="-80" />
    <hkern u1="Y" u2="&#x129;" k="-31" />
    <hkern u1="Y" u2="&#x127;" k="-45" />
    <hkern u1="Y" u2="&#x11b;" k="129" />
    <hkern u1="Y" u2="&#x115;" k="188" />
    <hkern u1="Y" u2="&#x10d;" k="121" />
    <hkern u1="Y" u2="&#xff;" k="72" />
    <hkern u1="Y" u2="&#xfc;" k="147" />
    <hkern u1="Y" u2="&#xf6;" k="137" />
    <hkern u1="Y" u2="&#xf2;" k="129" />
    <hkern u1="Y" u2="&#xf0;" k="131" />
    <hkern u1="Y" u2="&#xef;" k="-127" />
    <hkern u1="Y" u2="&#xee;" k="-55" />
    <hkern u1="Y" u2="&#xed;" k="57" />
    <hkern u1="Y" u2="&#xec;" k="-92" />
    <hkern u1="Y" u2="&#xeb;" k="127" />
    <hkern u1="Y" u2="&#xe8;" k="164" />
    <hkern u1="Y" u2="&#xe4;" k="164" />
    <hkern u1="Y" u2="&#xdf;" k="66" />
    <hkern u1="Y" u2="&#x40;" k="82" />
    <hkern u1="Y" u2="&#x39;" k="27" />
    <hkern u1="Y" u2="&#x38;" k="47" />
    <hkern u1="Y" u2="&#x37;" k="-29" />
    <hkern u1="Y" u2="&#x36;" k="45" />
    <hkern u1="Y" u2="&#x34;" k="137" />
    <hkern u1="Y" u2="&#x32;" k="33" />
    <hkern u1="Y" u2="&#x31;" k="25" />
    <hkern u1="Y" u2="&#x30;" k="39" />
    <hkern u1="Z" u2="&#x135;" k="-47" />
    <hkern u1="Z" u2="&#x131;" k="20" />
    <hkern u1="Z" u2="&#xef;" k="-29" />
    <hkern u1="Z" u2="&#xee;" k="-68" />
    <hkern u1="[" g2="Jcircumflex.sc" k="-156" />
    <hkern u1="[" g2="Icircumflex.sc" k="-37" />
    <hkern u1="[" g2="M.sc" k="35" />
    <hkern u1="[" g2="J.sc" k="-156" />
    <hkern u1="[" g2="eight.oldstyle" k="31" />
    <hkern u1="[" g2="six.oldstyle" k="33" />
    <hkern u1="[" g2="four.oldstyle" k="74" />
    <hkern u1="[" g2="two.oldstyle" k="59" />
    <hkern u1="[" g2="one.oldstyle" k="57" />
    <hkern u1="[" g2="zero.oldstyle" k="70" />
    <hkern u1="[" u2="&#x135;" k="-131" />
    <hkern u1="[" u2="&#x134;" k="-209" />
    <hkern u1="[" u2="&#x127;" k="-23" />
    <hkern u1="[" u2="&#xef;" k="-63" />
    <hkern u1="[" u2="&#xee;" k="-66" />
    <hkern u1="[" u2="&#x7b;" k="20" />
    <hkern u1="[" u2="x" k="31" />
    <hkern u1="[" u2="v" k="27" />
    <hkern u1="[" u2="j" k="-131" />
    <hkern u1="[" u2="f" k="25" />
    <hkern u1="[" u2="M" k="23" />
    <hkern u1="[" u2="J" k="-209" />
    <hkern u1="[" u2="&#x38;" k="33" />
    <hkern u1="[" u2="&#x36;" k="33" />
    <hkern u1="[" u2="&#x34;" k="63" />
    <hkern u1="[" u2="&#x30;" k="27" />
    <hkern u1="[" u2="&#x28;" k="39" />
    <hkern u1="\" g2="V.sc" k="66" />
    <hkern u1="\" u2="&#x2019;" k="92" />
    <hkern u1="\" u2="v" k="53" />
    <hkern u1="\" u2="X" k="-14" />
    <hkern u1="\" u2="V" k="92" />
    <hkern u1="\" u2="&#x31;" k="37" />
    <hkern u1="c" u2="Z" k="12" />
    <hkern u1="c" u2="Y" k="197" />
    <hkern u1="c" u2="X" k="14" />
    <hkern u1="c" u2="W" k="78" />
    <hkern u1="c" u2="V" k="88" />
    <hkern u1="c" u2="T" k="182" />
    <hkern u1="c" u2="S" k="18" />
    <hkern u1="d" u2="Z" k="12" />
    <hkern u1="f" u2="&#x2122;" k="-29" />
    <hkern u1="f" u2="&#x135;" k="-147" />
    <hkern u1="f" u2="&#x12d;" k="-72" />
    <hkern u1="f" u2="&#x12b;" k="-88" />
    <hkern u1="f" u2="&#x129;" k="-90" />
    <hkern u1="f" u2="&#xef;" k="-141" />
    <hkern u1="f" u2="&#xee;" k="-145" />
    <hkern u1="f" u2="&#xec;" k="-72" />
    <hkern u1="f" u2="&#xc6;" k="53" />
    <hkern u1="f" u2="&#x7d;" k="-20" />
    <hkern u1="f" u2="]" k="-18" />
    <hkern u1="f" u2="\" k="-27" />
    <hkern u1="f" u2="Y" k="-121" />
    <hkern u1="f" u2="X" k="-33" />
    <hkern u1="f" u2="W" k="-90" />
    <hkern u1="f" u2="V" k="-53" />
    <hkern u1="f" u2="T" k="-23" />
    <hkern u1="f" u2="&#x2a;" k="-49" />
    <hkern u1="f" u2="&#x29;" k="-18" />
    <hkern u1="f" u2="&#x20;" k="39" />
    <hkern u1="i" u2="&#x135;" k="-16" />
    <hkern u1="i" u2="&#xef;" k="-29" />
    <hkern u1="i" u2="&#xee;" k="-35" />
    <hkern u1="j" u2="&#x135;" k="-16" />
    <hkern u1="j" u2="&#xef;" k="-31" />
    <hkern u1="j" u2="&#xee;" k="-35" />
    <hkern u1="k" u2="Y" k="141" />
    <hkern u1="k" u2="W" k="51" />
    <hkern u1="k" u2="V" k="53" />
    <hkern u1="k" u2="T" k="152" />
    <hkern u1="k" u2="S" k="20" />
    <hkern u1="l" u2="&#xb7;" k="74" />
    <hkern u1="l" u2="Z" k="12" />
    <hkern u1="r" u2="&#xc6;" k="119" />
    <hkern u1="r" u2="Z" k="39" />
    <hkern u1="r" u2="Y" k="125" />
    <hkern u1="r" u2="X" k="98" />
    <hkern u1="r" u2="W" k="20" />
    <hkern u1="r" u2="V" k="20" />
    <hkern u1="r" u2="T" k="113" />
    <hkern u1="r" u2="M" k="27" />
    <hkern u1="s" u2="Z" k="14" />
    <hkern u1="s" u2="Y" k="184" />
    <hkern u1="s" u2="X" k="16" />
    <hkern u1="s" u2="W" k="96" />
    <hkern u1="s" u2="V" k="109" />
    <hkern u1="s" u2="T" k="137" />
    <hkern u1="s" u2="S" k="10" />
    <hkern u1="t" u2="&#xc6;" k="53" />
    <hkern u1="t" u2="Z" k="33" />
    <hkern u1="t" u2="Y" k="86" />
    <hkern u1="t" u2="X" k="63" />
    <hkern u1="t" u2="W" k="20" />
    <hkern u1="t" u2="V" k="25" />
    <hkern u1="t" u2="T" k="70" />
    <hkern u1="t" u2="M" k="18" />
    <hkern u1="v" u2="&#xc6;" k="88" />
    <hkern u1="v" u2="&#x7d;" k="27" />
    <hkern u1="v" u2="]" k="25" />
    <hkern u1="v" u2="Z" k="27" />
    <hkern u1="v" u2="Y" k="117" />
    <hkern u1="v" u2="X" k="92" />
    <hkern u1="v" u2="W" k="14" />
    <hkern u1="v" u2="V" k="14" />
    <hkern u1="v" u2="T" k="109" />
    <hkern u1="v" u2="M" k="20" />
    <hkern u1="v" u2="&#x2f;" k="53" />
    <hkern u1="v" u2="&#x29;" k="29" />
    <hkern u1="v" u2="&#x20;" k="51" />
    <hkern u1="w" u2="&#xc6;" k="74" />
    <hkern u1="w" u2="Z" k="27" />
    <hkern u1="w" u2="Y" k="117" />
    <hkern u1="w" u2="X" k="82" />
    <hkern u1="w" u2="W" k="18" />
    <hkern u1="w" u2="V" k="20" />
    <hkern u1="w" u2="T" k="117" />
    <hkern u1="w" u2="M" k="20" />
    <hkern u1="x" u2="&#x2122;" k="37" />
    <hkern u1="x" u2="&#x7d;" k="49" />
    <hkern u1="x" u2="]" k="33" />
    <hkern u1="x" u2="\" k="33" />
    <hkern u1="x" u2="Y" k="139" />
    <hkern u1="x" u2="W" k="49" />
    <hkern u1="x" u2="V" k="51" />
    <hkern u1="x" u2="T" k="147" />
    <hkern u1="x" u2="S" k="12" />
    <hkern u1="x" u2="&#x29;" k="43" />
    <hkern u1="y" u2="&#xc6;" k="86" />
    <hkern u1="y" u2="Z" k="29" />
    <hkern u1="y" u2="Y" k="119" />
    <hkern u1="y" u2="X" k="92" />
    <hkern u1="y" u2="W" k="16" />
    <hkern u1="y" u2="V" k="16" />
    <hkern u1="y" u2="T" k="113" />
    <hkern u1="y" u2="M" k="23" />
    <hkern u1="z" u2="Z" k="12" />
    <hkern u1="z" u2="Y" k="164" />
    <hkern u1="z" u2="W" k="61" />
    <hkern u1="z" u2="V" k="66" />
    <hkern u1="z" u2="T" k="141" />
    <hkern u1="z" u2="S" k="12" />
    <hkern u1="&#x7b;" g2="Jcircumflex.sc" k="-160" />
    <hkern u1="&#x7b;" g2="Icircumflex.sc" k="-41" />
    <hkern u1="&#x7b;" g2="M.sc" k="51" />
    <hkern u1="&#x7b;" g2="J.sc" k="-160" />
    <hkern u1="&#x7b;" g2="nine.oldstyle" k="20" />
    <hkern u1="&#x7b;" g2="eight.oldstyle" k="41" />
    <hkern u1="&#x7b;" g2="six.oldstyle" k="45" />
    <hkern u1="&#x7b;" g2="four.oldstyle" k="104" />
    <hkern u1="&#x7b;" g2="two.oldstyle" k="76" />
    <hkern u1="&#x7b;" g2="one.oldstyle" k="68" />
    <hkern u1="&#x7b;" g2="zero.oldstyle" k="90" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-135" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-213" />
    <hkern u1="&#x7b;" u2="&#x12b;" k="-14" />
    <hkern u1="&#x7b;" u2="&#x127;" k="-27" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-68" />
    <hkern u1="&#x7b;" u2="&#xee;" k="-70" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="27" />
    <hkern u1="&#x7b;" u2="x" k="49" />
    <hkern u1="&#x7b;" u2="v" k="27" />
    <hkern u1="&#x7b;" u2="j" k="-135" />
    <hkern u1="&#x7b;" u2="f" k="27" />
    <hkern u1="&#x7b;" u2="M" k="37" />
    <hkern u1="&#x7b;" u2="J" k="-213" />
    <hkern u1="&#x7b;" u2="&#x38;" k="43" />
    <hkern u1="&#x7b;" u2="&#x36;" k="45" />
    <hkern u1="&#x7b;" u2="&#x35;" k="20" />
    <hkern u1="&#x7b;" u2="&#x34;" k="117" />
    <hkern u1="&#x7b;" u2="&#x30;" k="35" />
    <hkern u1="&#x7b;" u2="&#x28;" k="57" />
    <hkern u1="&#x7c;" g2="Jcircumflex.sc" k="-68" />
    <hkern u1="&#x7c;" g2="J.sc" k="-68" />
    <hkern u1="&#x7c;" u2="&#x135;" k="-57" />
    <hkern u1="&#x7c;" u2="&#x134;" k="-115" />
    <hkern u1="&#x7c;" u2="&#x125;" k="-16" />
    <hkern u1="&#x7c;" u2="&#xee;" k="-23" />
    <hkern u1="&#x7c;" u2="j" k="-57" />
    <hkern u1="&#x7c;" u2="J" k="-115" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="27" />
    <hkern u1="&#x7d;" u2="]" k="20" />
    <hkern u1="&#x7d;" u2="&#x29;" k="29" />
    <hkern u1="&#xa1;" g2="Jcircumflex.sc" k="-98" />
    <hkern u1="&#xa1;" g2="J.sc" k="-98" />
    <hkern u1="&#xa1;" u2="&#x135;" k="-78" />
    <hkern u1="&#xa1;" u2="&#x134;" k="-145" />
    <hkern u1="&#xa1;" u2="j" k="-78" />
    <hkern u1="&#xa1;" u2="V" k="41" />
    <hkern u1="&#xa1;" u2="J" k="-145" />
    <hkern u1="&#xae;" u2="X" k="35" />
    <hkern u1="&#xae;" u2="V" k="23" />
    <hkern u1="&#xb0;" g2="nine.oldstyle" k="37" />
    <hkern u1="&#xb0;" g2="five.oldstyle" k="25" />
    <hkern u1="&#xb0;" g2="four.oldstyle" k="143" />
    <hkern u1="&#xb0;" g2="three.oldstyle" k="20" />
    <hkern u1="&#xb0;" g2="zero.oldstyle" k="23" />
    <hkern u1="&#xb0;" u2="&#x34;" k="72" />
    <hkern u1="&#xb7;" g2="l.alt1" k="74" />
    <hkern u1="&#xb7;" g2="four.oldstyle" k="117" />
    <hkern u1="&#xb7;" u2="l" k="74" />
    <hkern u1="&#xb7;" u2="&#x34;" k="35" />
    <hkern u1="&#xb7;" u2="&#x33;" k="37" />
    <hkern u1="&#xbf;" g2="Jcircumflex.sc" k="-125" />
    <hkern u1="&#xbf;" g2="V.sc" k="61" />
    <hkern u1="&#xbf;" g2="J.sc" k="-125" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-113" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-164" />
    <hkern u1="&#xbf;" u2="v" k="29" />
    <hkern u1="&#xbf;" u2="j" k="-113" />
    <hkern u1="&#xbf;" u2="V" k="88" />
    <hkern u1="&#xbf;" u2="J" k="-164" />
    <hkern u1="&#xce;" g2="braceright.case" k="-66" />
    <hkern u1="&#xce;" g2="bracketright.case" k="-61" />
    <hkern u1="&#xce;" g2="parenright.case" k="-66" />
    <hkern u1="&#xcf;" g2="braceright.case" k="-14" />
    <hkern u1="&#xcf;" g2="parenright.case" k="-23" />
    <hkern u1="&#xd0;" u2="&#x141;" k="14" />
    <hkern u1="&#xd0;" u2="&#x126;" k="14" />
    <hkern u1="&#xd2;" u2="&#x141;" k="14" />
    <hkern u1="&#xd2;" u2="&#x126;" k="14" />
    <hkern u1="&#xd3;" u2="&#x141;" k="14" />
    <hkern u1="&#xd3;" u2="&#x126;" k="14" />
    <hkern u1="&#xd4;" u2="&#x141;" k="14" />
    <hkern u1="&#xd4;" u2="&#x126;" k="14" />
    <hkern u1="&#xd5;" u2="&#x141;" k="14" />
    <hkern u1="&#xd5;" u2="&#x126;" k="14" />
    <hkern u1="&#xd6;" u2="&#x141;" k="14" />
    <hkern u1="&#xd6;" u2="&#x126;" k="14" />
    <hkern u1="&#xd8;" u2="&#x141;" k="12" />
    <hkern u1="&#xd8;" u2="&#x126;" k="12" />
    <hkern u1="&#xd9;" u2="&#xdf;" k="20" />
    <hkern u1="&#xda;" u2="&#xdf;" k="20" />
    <hkern u1="&#xdb;" u2="&#xdf;" k="20" />
    <hkern u1="&#xdc;" u2="&#xdf;" k="20" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="131" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="66" />
    <hkern u1="&#xde;" g2="AE.sc" k="61" />
    <hkern u1="&#xde;" g2="Y.sc" k="18" />
    <hkern u1="&#xde;" g2="X.sc" k="20" />
    <hkern u1="&#xde;" g2="A.sc" k="23" />
    <hkern u1="&#xde;" g2="braceright.case" k="98" />
    <hkern u1="&#xde;" g2="bracketright.case" k="55" />
    <hkern u1="&#xde;" g2="parenright.case" k="100" />
    <hkern u1="&#xde;" u2="&#x2122;" k="27" />
    <hkern u1="&#xde;" u2="&#x7d;" k="57" />
    <hkern u1="&#xde;" u2="x" k="12" />
    <hkern u1="&#xde;" u2="]" k="37" />
    <hkern u1="&#xde;" u2="\" k="23" />
    <hkern u1="&#xde;" u2="X" k="66" />
    <hkern u1="&#xde;" u2="V" k="31" />
    <hkern u1="&#xde;" u2="&#x2f;" k="27" />
    <hkern u1="&#xde;" u2="&#x29;" k="59" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="27" />
    <hkern u1="&#xdf;" u2="&#xc6;" k="10" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="49" />
    <hkern u1="&#xdf;" u2="x" k="33" />
    <hkern u1="&#xdf;" u2="v" k="23" />
    <hkern u1="&#xdf;" u2="]" k="39" />
    <hkern u1="&#xdf;" u2="\" k="39" />
    <hkern u1="&#xdf;" u2="Z" k="23" />
    <hkern u1="&#xdf;" u2="Y" k="115" />
    <hkern u1="&#xdf;" u2="X" k="39" />
    <hkern u1="&#xdf;" u2="W" k="59" />
    <hkern u1="&#xdf;" u2="V" k="72" />
    <hkern u1="&#xdf;" u2="U" k="25" />
    <hkern u1="&#xdf;" u2="T" k="59" />
    <hkern u1="&#xdf;" u2="S" k="12" />
    <hkern u1="&#xdf;" u2="M" k="14" />
    <hkern u1="&#xdf;" u2="&#x29;" k="49" />
    <hkern u1="&#xed;" u2="&#x159;" k="-41" />
    <hkern u1="&#xee;" g2="l.alt1" k="-41" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-70" />
    <hkern u1="&#xee;" u2="&#x201c;" k="-23" />
    <hkern u1="&#xee;" u2="&#x2018;" k="-23" />
    <hkern u1="&#xee;" u2="&#x133;" k="-33" />
    <hkern u1="&#xee;" u2="&#xfe;" k="-29" />
    <hkern u1="&#xee;" u2="&#x7d;" k="-66" />
    <hkern u1="&#xee;" u2="&#x7c;" k="-18" />
    <hkern u1="&#xee;" u2="l" k="-29" />
    <hkern u1="&#xee;" u2="k" k="-29" />
    <hkern u1="&#xee;" u2="j" k="-29" />
    <hkern u1="&#xee;" u2="i" k="-33" />
    <hkern u1="&#xee;" u2="h" k="-29" />
    <hkern u1="&#xee;" u2="b" k="-29" />
    <hkern u1="&#xee;" u2="]" k="-61" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-63" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-123" />
    <hkern u1="&#xee;" u2="&#x29;" k="-63" />
    <hkern u1="&#xee;" u2="&#x27;" k="-25" />
    <hkern u1="&#xee;" u2="&#x22;" k="-25" />
    <hkern u1="&#xee;" u2="&#x21;" k="-16" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-72" />
    <hkern u1="&#xef;" u2="&#x133;" k="-31" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-66" />
    <hkern u1="&#xef;" u2="j" k="-29" />
    <hkern u1="&#xef;" u2="i" k="-31" />
    <hkern u1="&#xef;" u2="]" k="-61" />
    <hkern u1="&#xef;" u2="\" k="-39" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-45" />
    <hkern u1="&#xef;" u2="&#x29;" k="-63" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="43" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="18" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="74" />
    <hkern u1="&#xf0;" u2="x" k="41" />
    <hkern u1="&#xf0;" u2="v" k="20" />
    <hkern u1="&#xf0;" u2="]" k="59" />
    <hkern u1="&#xf0;" u2="\" k="63" />
    <hkern u1="&#xf0;" u2="Z" k="29" />
    <hkern u1="&#xf0;" u2="Y" k="137" />
    <hkern u1="&#xf0;" u2="X" k="51" />
    <hkern u1="&#xf0;" u2="W" k="92" />
    <hkern u1="&#xf0;" u2="V" k="98" />
    <hkern u1="&#xf0;" u2="U" k="25" />
    <hkern u1="&#xf0;" u2="T" k="102" />
    <hkern u1="&#xf0;" u2="S" k="12" />
    <hkern u1="&#xf0;" u2="M" k="16" />
    <hkern u1="&#xf0;" u2="A" k="10" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="37" />
    <hkern u1="&#xf0;" u2="&#x29;" k="74" />
    <hkern u1="&#x104;" g2="Tcommaaccent.sc.alt1" k="35" />
    <hkern u1="&#x104;" g2="y.alt1" k="-236" />
    <hkern u1="&#x104;" g2="J.sc" k="-412" />
    <hkern u1="&#x104;" g2="braceright.case" k="-221" />
    <hkern u1="&#x104;" g2="bracketright.case" k="-217" />
    <hkern u1="&#x104;" g2="parenright.case" k="-223" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-250" />
    <hkern u1="&#x104;" u2="&#x201a;" k="-250" />
    <hkern u1="&#x104;" u2="&#x134;" k="-465" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-37" />
    <hkern u1="&#x104;" u2="&#xfe;" k="-145" />
    <hkern u1="&#x104;" u2="&#x7d;" k="-219" />
    <hkern u1="&#x104;" u2="&#x7c;" k="-141" />
    <hkern u1="&#x104;" u2="y" k="-59" />
    <hkern u1="&#x104;" u2="p" k="-145" />
    <hkern u1="&#x104;" u2="j" k="-401" />
    <hkern u1="&#x104;" u2="g" k="-176" />
    <hkern u1="&#x104;" u2="]" k="-215" />
    <hkern u1="&#x104;" u2="J" k="-465" />
    <hkern u1="&#x104;" u2="&#x3b;" k="-238" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-264" />
    <hkern u1="&#x104;" u2="&#x29;" k="-215" />
    <hkern u1="&#x105;" g2="y.alt1" k="-88" />
    <hkern u1="&#x105;" u2="&#x201e;" k="-102" />
    <hkern u1="&#x105;" u2="&#x201a;" k="-102" />
    <hkern u1="&#x105;" u2="&#x7d;" k="-61" />
    <hkern u1="&#x105;" u2="j" k="-254" />
    <hkern u1="&#x105;" u2="g" k="-39" />
    <hkern u1="&#x105;" u2="]" k="-57" />
    <hkern u1="&#x105;" u2="&#x3b;" k="-90" />
    <hkern u1="&#x105;" u2="&#x2c;" k="-117" />
    <hkern u1="&#x105;" u2="&#x29;" k="-59" />
    <hkern u1="&#x10e;" u2="&#x141;" k="14" />
    <hkern u1="&#x10e;" u2="&#x126;" k="14" />
    <hkern u1="&#x10f;" g2="t.alt1" k="-98" />
    <hkern u1="&#x10f;" g2="y.alt1" k="-133" />
    <hkern u1="&#x10f;" g2="l.alt1" k="-270" />
    <hkern u1="&#x10f;" g2="f_j" k="-96" />
    <hkern u1="&#x10f;" g2="f_f_j" k="-96" />
    <hkern u1="&#x10f;" g2="f_f_l" k="-96" />
    <hkern u1="&#x10f;" g2="f_f_i" k="-96" />
    <hkern u1="&#x10f;" g2="fl" k="-96" />
    <hkern u1="&#x10f;" g2="fi" k="-96" />
    <hkern u1="&#x10f;" g2="f_f" k="-96" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-328" />
    <hkern u1="&#x10f;" u2="&#x203a;" k="-35" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-176" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-172" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-176" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-172" />
    <hkern u1="&#x10f;" u2="&#x17f;" k="-96" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-229" />
    <hkern u1="&#x10f;" u2="&#x16f;" k="-29" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-262" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-258" />
    <hkern u1="&#x10f;" u2="&#xfa;" k="-29" />
    <hkern u1="&#x10f;" u2="&#xf0;" k="-137" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-147" />
    <hkern u1="&#x10f;" u2="&#xbb;" k="-35" />
    <hkern u1="&#x10f;" u2="&#xae;" k="-66" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-319" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-246" />
    <hkern u1="&#x10f;" u2="z" k="-31" />
    <hkern u1="&#x10f;" u2="y" k="-143" />
    <hkern u1="&#x10f;" u2="x" k="-72" />
    <hkern u1="&#x10f;" u2="w" k="-125" />
    <hkern u1="&#x10f;" u2="v" k="-141" />
    <hkern u1="&#x10f;" u2="u" k="-29" />
    <hkern u1="&#x10f;" u2="t" k="-135" />
    <hkern u1="&#x10f;" u2="r" k="-16" />
    <hkern u1="&#x10f;" u2="p" k="-16" />
    <hkern u1="&#x10f;" u2="n" k="-16" />
    <hkern u1="&#x10f;" u2="m" k="-16" />
    <hkern u1="&#x10f;" u2="l" k="-258" />
    <hkern u1="&#x10f;" u2="k" k="-258" />
    <hkern u1="&#x10f;" u2="j" k="-260" />
    <hkern u1="&#x10f;" u2="i" k="-262" />
    <hkern u1="&#x10f;" u2="h" k="-258" />
    <hkern u1="&#x10f;" u2="f" k="-96" />
    <hkern u1="&#x10f;" u2="b" k="-258" />
    <hkern u1="&#x10f;" u2="]" k="-315" />
    <hkern u1="&#x10f;" u2="\" k="-324" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-213" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-274" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-319" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-266" />
    <hkern u1="&#x10f;" u2="&#x26;" k="-152" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-266" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-250" />
    <hkern u1="&#x10f;" u2="&#x20;" k="-100" />
    <hkern u1="&#x110;" u2="&#x141;" k="14" />
    <hkern u1="&#x110;" u2="&#x126;" k="14" />
    <hkern u1="&#x111;" u2="&#x2122;" k="-23" />
    <hkern u1="&#x111;" u2="&#x7d;" k="-14" />
    <hkern u1="&#x111;" u2="&#x2a;" k="-53" />
    <hkern u1="&#x111;" u2="&#x29;" k="-12" />
    <hkern u1="&#x118;" g2="y.alt1" k="-115" />
    <hkern u1="&#x118;" g2="J.sc" k="-293" />
    <hkern u1="&#x118;" g2="braceright.case" k="-102" />
    <hkern u1="&#x118;" g2="bracketright.case" k="-96" />
    <hkern u1="&#x118;" g2="parenright.case" k="-100" />
    <hkern u1="&#x118;" u2="&#x201e;" k="-131" />
    <hkern u1="&#x118;" u2="&#x201a;" k="-131" />
    <hkern u1="&#x118;" u2="&#x134;" k="-344" />
    <hkern u1="&#x118;" u2="&#xfe;" k="-25" />
    <hkern u1="&#x118;" u2="&#x7d;" k="-98" />
    <hkern u1="&#x118;" u2="&#x7c;" k="-23" />
    <hkern u1="&#x118;" u2="p" k="-25" />
    <hkern u1="&#x118;" u2="j" k="-281" />
    <hkern u1="&#x118;" u2="g" k="-53" />
    <hkern u1="&#x118;" u2="]" k="-92" />
    <hkern u1="&#x118;" u2="J" k="-344" />
    <hkern u1="&#x118;" u2="&#x3b;" k="-117" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-145" />
    <hkern u1="&#x118;" u2="&#x29;" k="-94" />
    <hkern u1="&#x126;" u2="&#x2122;" k="-57" />
    <hkern u1="&#x126;" u2="&#x1fe;" k="12" />
    <hkern u1="&#x126;" u2="&#x152;" k="14" />
    <hkern u1="&#x126;" u2="&#x150;" k="14" />
    <hkern u1="&#x126;" u2="&#x14e;" k="14" />
    <hkern u1="&#x126;" u2="&#x14c;" k="14" />
    <hkern u1="&#x126;" u2="&#x141;" k="12" />
    <hkern u1="&#x126;" u2="&#x126;" k="12" />
    <hkern u1="&#x126;" u2="&#x122;" k="14" />
    <hkern u1="&#x126;" u2="&#x120;" k="14" />
    <hkern u1="&#x126;" u2="&#x11e;" k="14" />
    <hkern u1="&#x126;" u2="&#x11c;" k="14" />
    <hkern u1="&#x126;" u2="&#x10c;" k="14" />
    <hkern u1="&#x126;" u2="&#x10a;" k="14" />
    <hkern u1="&#x126;" u2="&#x108;" k="14" />
    <hkern u1="&#x126;" u2="&#x106;" k="14" />
    <hkern u1="&#x126;" u2="&#xd8;" k="12" />
    <hkern u1="&#x126;" u2="&#xd6;" k="14" />
    <hkern u1="&#x126;" u2="&#xd5;" k="14" />
    <hkern u1="&#x126;" u2="&#xd4;" k="14" />
    <hkern u1="&#x126;" u2="&#xd3;" k="14" />
    <hkern u1="&#x126;" u2="&#xd2;" k="14" />
    <hkern u1="&#x126;" u2="&#xc7;" k="14" />
    <hkern u1="&#x126;" u2="Q" k="14" />
    <hkern u1="&#x126;" u2="O" k="14" />
    <hkern u1="&#x126;" u2="G" k="14" />
    <hkern u1="&#x126;" u2="C" k="14" />
    <hkern u1="&#x126;" u2="&#x2a;" k="-43" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-18" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-12" />
    <hkern u1="&#x129;" u2="\" k="-12" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-31" />
    <hkern u1="&#x12b;" u2="&#x2122;" k="-18" />
    <hkern u1="&#x12b;" u2="&#x7d;" k="-12" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-25" />
    <hkern u1="&#x12d;" u2="&#x2a;" k="-12" />
    <hkern u1="&#x12e;" g2="y.alt1" k="-57" />
    <hkern u1="&#x12e;" g2="J.sc" k="-236" />
    <hkern u1="&#x12e;" g2="braceright.case" k="-43" />
    <hkern u1="&#x12e;" g2="bracketright.case" k="-39" />
    <hkern u1="&#x12e;" g2="parenright.case" k="-43" />
    <hkern u1="&#x12e;" u2="&#x201e;" k="-70" />
    <hkern u1="&#x12e;" u2="&#x201a;" k="-70" />
    <hkern u1="&#x12e;" u2="&#x134;" k="-289" />
    <hkern u1="&#x12e;" u2="&#x7d;" k="-41" />
    <hkern u1="&#x12e;" u2="j" k="-221" />
    <hkern u1="&#x12e;" u2="g" k="-4" />
    <hkern u1="&#x12e;" u2="]" k="-37" />
    <hkern u1="&#x12e;" u2="J" k="-289" />
    <hkern u1="&#x12e;" u2="&#x3b;" k="-49" />
    <hkern u1="&#x12e;" u2="&#x2c;" k="-61" />
    <hkern u1="&#x12e;" u2="&#x29;" k="-39" />
    <hkern u1="&#x12f;" g2="y.alt1" k="-88" />
    <hkern u1="&#x12f;" u2="&#x201e;" k="-100" />
    <hkern u1="&#x12f;" u2="&#x201a;" k="-100" />
    <hkern u1="&#x12f;" u2="&#x7d;" k="-63" />
    <hkern u1="&#x12f;" u2="j" k="-254" />
    <hkern u1="&#x12f;" u2="g" k="-31" />
    <hkern u1="&#x12f;" u2="]" k="-59" />
    <hkern u1="&#x12f;" u2="&#x3b;" k="-66" />
    <hkern u1="&#x12f;" u2="&#x2c;" k="-92" />
    <hkern u1="&#x12f;" u2="&#x29;" k="-61" />
    <hkern u1="&#x133;" u2="&#x135;" k="-18" />
    <hkern u1="&#x133;" u2="&#xef;" k="-31" />
    <hkern u1="&#x133;" u2="&#xee;" k="-35" />
    <hkern u1="&#x134;" g2="braceright.case" k="-70" />
    <hkern u1="&#x134;" g2="bracketright.case" k="-66" />
    <hkern u1="&#x134;" g2="parenright.case" k="-70" />
    <hkern u1="&#x135;" g2="l.alt1" k="-63" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-84" />
    <hkern u1="&#x135;" u2="&#x201c;" k="-45" />
    <hkern u1="&#x135;" u2="&#x2018;" k="-45" />
    <hkern u1="&#x135;" u2="&#x133;" k="-47" />
    <hkern u1="&#x135;" u2="&#xfe;" k="-51" />
    <hkern u1="&#x135;" u2="&#xdf;" k="-25" />
    <hkern u1="&#x135;" u2="&#x7d;" k="-88" />
    <hkern u1="&#x135;" u2="&#x7c;" k="-41" />
    <hkern u1="&#x135;" u2="l" k="-51" />
    <hkern u1="&#x135;" u2="k" k="-51" />
    <hkern u1="&#x135;" u2="j" k="-45" />
    <hkern u1="&#x135;" u2="i" k="-47" />
    <hkern u1="&#x135;" u2="h" k="-51" />
    <hkern u1="&#x135;" u2="b" k="-51" />
    <hkern u1="&#x135;" u2="]" k="-84" />
    <hkern u1="&#x135;" u2="\" k="-16" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-86" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-145" />
    <hkern u1="&#x135;" u2="&#x29;" k="-86" />
    <hkern u1="&#x135;" u2="&#x27;" k="-47" />
    <hkern u1="&#x135;" u2="&#x26;" k="-27" />
    <hkern u1="&#x135;" u2="&#x22;" k="-47" />
    <hkern u1="&#x135;" u2="&#x21;" k="-39" />
    <hkern u1="&#x13d;" g2="Tcommaaccent.sc.alt1" k="25" />
    <hkern u1="&#x13d;" g2="Tbar.sc.alt1" k="27" />
    <hkern u1="&#x13d;" g2="Tcaron.sc.alt1" k="25" />
    <hkern u1="&#x13d;" g2="T.sc.alt1" k="25" />
    <hkern u1="&#x13d;" g2="Y.sc" k="-25" />
    <hkern u1="&#x13d;" g2="W.sc" k="4" />
    <hkern u1="&#x13d;" g2="V.sc" k="29" />
    <hkern u1="&#x13d;" g2="T.sc" k="25" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="-10" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="-98" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="-68" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="-68" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="-68" />
    <hkern u1="&#x13d;" u2="&#x178;" k="-98" />
    <hkern u1="&#x13d;" u2="&#x176;" k="-98" />
    <hkern u1="&#x13d;" u2="&#x174;" k="-68" />
    <hkern u1="&#x13d;" u2="&#x166;" k="-2" />
    <hkern u1="&#x13d;" u2="&#x164;" k="-2" />
    <hkern u1="&#x13d;" u2="&#x162;" k="-2" />
    <hkern u1="&#x13d;" u2="&#x126;" k="-14" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="-98" />
    <hkern u1="&#x13d;" u2="&#x7d;" k="8" />
    <hkern u1="&#x13d;" u2="]" k="12" />
    <hkern u1="&#x13d;" u2="\" k="2" />
    <hkern u1="&#x13d;" u2="Y" k="-98" />
    <hkern u1="&#x13d;" u2="W" k="-68" />
    <hkern u1="&#x13d;" u2="V" k="-33" />
    <hkern u1="&#x13d;" u2="T" k="-2" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="31" />
    <hkern u1="&#x13d;" u2="&#x29;" k="10" />
    <hkern u1="&#x13d;" u2="&#x27;" k="61" />
    <hkern u1="&#x13d;" u2="&#x22;" k="61" />
    <hkern u1="&#x13d;" u2="&#x20;" k="74" />
    <hkern u1="&#x13e;" g2="t.alt1" k="-72" />
    <hkern u1="&#x13e;" g2="y.alt1" k="-106" />
    <hkern u1="&#x13e;" g2="l.alt1" k="-246" />
    <hkern u1="&#x13e;" g2="f_j" k="-70" />
    <hkern u1="&#x13e;" g2="f_f_j" k="-70" />
    <hkern u1="&#x13e;" g2="f_f_l" k="-70" />
    <hkern u1="&#x13e;" g2="f_f_i" k="-70" />
    <hkern u1="&#x13e;" g2="fl" k="-70" />
    <hkern u1="&#x13e;" g2="fi" k="-70" />
    <hkern u1="&#x13e;" g2="f_f" k="-70" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-303" />
    <hkern u1="&#x13e;" u2="&#x203a;" k="-20" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="-152" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="-150" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-152" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-150" />
    <hkern u1="&#x13e;" u2="&#x17f;" k="-70" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-205" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-225" />
    <hkern u1="&#x13e;" u2="&#x148;" k="-117" />
    <hkern u1="&#x13e;" u2="&#x133;" k="-242" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-158" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-233" />
    <hkern u1="&#x13e;" u2="&#xf4;" k="-72" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="-113" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-125" />
    <hkern u1="&#x13e;" u2="&#xbb;" k="-20" />
    <hkern u1="&#x13e;" u2="&#xae;" k="-49" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-295" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-221" />
    <hkern u1="&#x13e;" u2="y" k="-117" />
    <hkern u1="&#x13e;" u2="x" k="-45" />
    <hkern u1="&#x13e;" u2="w" k="-98" />
    <hkern u1="&#x13e;" u2="v" k="-115" />
    <hkern u1="&#x13e;" u2="t" k="-115" />
    <hkern u1="&#x13e;" u2="l" k="-233" />
    <hkern u1="&#x13e;" u2="k" k="-233" />
    <hkern u1="&#x13e;" u2="j" k="-240" />
    <hkern u1="&#x13e;" u2="i" k="-242" />
    <hkern u1="&#x13e;" u2="h" k="-233" />
    <hkern u1="&#x13e;" u2="f" k="-70" />
    <hkern u1="&#x13e;" u2="b" k="-233" />
    <hkern u1="&#x13e;" u2="]" k="-291" />
    <hkern u1="&#x13e;" u2="\" k="-299" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-190" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-254" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-295" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-242" />
    <hkern u1="&#x13e;" u2="&#x26;" k="-129" />
    <hkern u1="&#x13e;" u2="&#x22;" k="-242" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-225" />
    <hkern u1="&#x13e;" u2="&#x20;" k="-76" />
    <hkern u1="&#x142;" g2="t.alt1" k="-45" />
    <hkern u1="&#x142;" g2="y.alt1" k="-80" />
    <hkern u1="&#x142;" g2="f_j" k="-43" />
    <hkern u1="&#x142;" g2="f_f_j" k="-43" />
    <hkern u1="&#x142;" g2="f_f_l" k="-43" />
    <hkern u1="&#x142;" g2="f_f_i" k="-43" />
    <hkern u1="&#x142;" g2="fl" k="-43" />
    <hkern u1="&#x142;" g2="fi" k="-43" />
    <hkern u1="&#x142;" g2="f_f" k="-43" />
    <hkern u1="&#x142;" u2="&#x201d;" k="-16" />
    <hkern u1="&#x142;" u2="&#x2019;" k="-16" />
    <hkern u1="&#x142;" u2="&#x17f;" k="-43" />
    <hkern u1="&#x142;" u2="y" k="-90" />
    <hkern u1="&#x142;" u2="x" k="-18" />
    <hkern u1="&#x142;" u2="w" k="-72" />
    <hkern u1="&#x142;" u2="v" k="-86" />
    <hkern u1="&#x142;" u2="t" k="-43" />
    <hkern u1="&#x142;" u2="f" k="-43" />
    <hkern u1="&#x142;" u2="&#x3f;" k="-16" />
    <hkern u1="&#x142;" u2="&#x2a;" k="-88" />
    <hkern u1="&#x142;" u2="&#x20;" k="20" />
    <hkern u1="&#x14c;" u2="&#x141;" k="14" />
    <hkern u1="&#x14c;" u2="&#x126;" k="14" />
    <hkern u1="&#x14e;" u2="&#x141;" k="14" />
    <hkern u1="&#x14e;" u2="&#x126;" k="14" />
    <hkern u1="&#x150;" u2="&#x141;" k="14" />
    <hkern u1="&#x150;" u2="&#x126;" k="14" />
    <hkern u1="&#x162;" g2="Icircumflex.sc" k="-45" />
    <hkern u1="&#x162;" u2="&#xee;" k="-102" />
    <hkern u1="&#x162;" u2="&#xdf;" k="23" />
    <hkern u1="&#x164;" u2="&#xdf;" k="23" />
    <hkern u1="&#x165;" g2="yacute.alt1" k="-14" />
    <hkern u1="&#x165;" g2="adieresis.alt1" k="-37" />
    <hkern u1="&#x165;" g2="y.alt1" k="-14" />
    <hkern u1="&#x165;" g2="l.alt1" k="-168" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-219" />
    <hkern u1="&#x165;" u2="&#x201d;" k="-70" />
    <hkern u1="&#x165;" u2="&#x201c;" k="-51" />
    <hkern u1="&#x165;" u2="&#x2019;" k="-70" />
    <hkern u1="&#x165;" u2="&#x2018;" k="-51" />
    <hkern u1="&#x165;" u2="&#x165;" k="-29" />
    <hkern u1="&#x165;" u2="&#x161;" k="-166" />
    <hkern u1="&#x165;" u2="&#x148;" k="-55" />
    <hkern u1="&#x165;" u2="&#x133;" k="-141" />
    <hkern u1="&#x165;" u2="&#x11b;" k="-90" />
    <hkern u1="&#x165;" u2="&#x10d;" k="-98" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-154" />
    <hkern u1="&#x165;" u2="&#xfd;" k="-25" />
    <hkern u1="&#x165;" u2="&#xf0;" k="-31" />
    <hkern u1="&#x165;" u2="&#xed;" k="-29" />
    <hkern u1="&#x165;" u2="&#xdf;" k="-25" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-211" />
    <hkern u1="&#x165;" u2="&#x7c;" k="-158" />
    <hkern u1="&#x165;" u2="y" k="-25" />
    <hkern u1="&#x165;" u2="v" k="-20" />
    <hkern u1="&#x165;" u2="l" k="-154" />
    <hkern u1="&#x165;" u2="k" k="-154" />
    <hkern u1="&#x165;" u2="j" k="-141" />
    <hkern u1="&#x165;" u2="i" k="-141" />
    <hkern u1="&#x165;" u2="h" k="-154" />
    <hkern u1="&#x165;" u2="b" k="-154" />
    <hkern u1="&#x165;" u2="]" k="-209" />
    <hkern u1="&#x165;" u2="\" k="-217" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-70" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-147" />
    <hkern u1="&#x165;" u2="&#x29;" k="-213" />
    <hkern u1="&#x165;" u2="&#x27;" k="-158" />
    <hkern u1="&#x165;" u2="&#x26;" k="-29" />
    <hkern u1="&#x165;" u2="&#x22;" k="-158" />
    <hkern u1="&#x165;" u2="&#x21;" k="-141" />
    <hkern u1="&#x165;" u2="&#x20;" k="-12" />
    <hkern u1="&#x166;" u2="&#xdf;" k="23" />
    <hkern u1="&#x168;" u2="&#xdf;" k="20" />
    <hkern u1="&#x16a;" u2="&#xdf;" k="20" />
    <hkern u1="&#x16c;" u2="&#xdf;" k="20" />
    <hkern u1="&#x16e;" u2="&#xdf;" k="20" />
    <hkern u1="&#x170;" u2="&#xdf;" k="20" />
    <hkern u1="&#x172;" u2="&#xdf;" k="20" />
    <hkern u1="&#x173;" g2="y.alt1" k="-90" />
    <hkern u1="&#x173;" u2="&#x201e;" k="-104" />
    <hkern u1="&#x173;" u2="&#x201a;" k="-104" />
    <hkern u1="&#x173;" u2="&#x7d;" k="-63" />
    <hkern u1="&#x173;" u2="j" k="-256" />
    <hkern u1="&#x173;" u2="g" k="-39" />
    <hkern u1="&#x173;" u2="]" k="-59" />
    <hkern u1="&#x173;" u2="&#x3b;" k="-92" />
    <hkern u1="&#x173;" u2="&#x2c;" k="-119" />
    <hkern u1="&#x173;" u2="&#x29;" k="-61" />
    <hkern u1="&#x174;" u2="&#xdf;" k="27" />
    <hkern u1="&#x176;" u2="&#xf0;" k="131" />
    <hkern u1="&#x176;" u2="&#xdf;" k="66" />
    <hkern u1="&#x178;" u2="&#xf0;" k="131" />
    <hkern u1="&#x178;" u2="&#xdf;" k="66" />
    <hkern u1="&#x17f;" u2="&#x159;" k="-72" />
    <hkern u1="&#x17f;" u2="&#x149;" k="-205" />
    <hkern u1="&#x17f;" u2="&#x135;" k="-221" />
    <hkern u1="&#x17f;" u2="&#x131;" k="-2" />
    <hkern u1="&#x17f;" u2="&#x12d;" k="-145" />
    <hkern u1="&#x17f;" u2="&#x12b;" k="-162" />
    <hkern u1="&#x17f;" u2="&#x129;" k="-164" />
    <hkern u1="&#x17f;" u2="&#xef;" k="-215" />
    <hkern u1="&#x17f;" u2="&#xee;" k="-219" />
    <hkern u1="&#x17f;" u2="&#xed;" k="-20" />
    <hkern u1="&#x17f;" u2="&#xec;" k="-145" />
    <hkern u1="&#x1fe;" u2="&#x141;" k="12" />
    <hkern u1="&#x1fe;" u2="&#x126;" k="12" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="27" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="27" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="27" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="131" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="66" />
    <hkern u1="&#x2018;" g2="Jcircumflex.sc" k="-12" />
    <hkern u1="&#x2018;" g2="Icircumflex.sc" k="-12" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-47" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-33" />
    <hkern u1="&#x2019;" g2="Jcircumflex.sc" k="-25" />
    <hkern u1="&#x2019;" g2="Icircumflex.sc" k="-25" />
    <hkern u1="&#x2019;" u2="&#x135;" k="-31" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-53" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-49" />
    <hkern u1="&#x2019;" u2="&#x40;" k="39" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="123" />
    <hkern u1="&#x2019;" u2="&#x20;" k="37" />
    <hkern u1="&#x201c;" g2="Jcircumflex.sc" k="-12" />
    <hkern u1="&#x201c;" g2="Icircumflex.sc" k="-12" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-47" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-33" />
    <hkern u1="&#x201d;" g2="Jcircumflex.sc" k="-25" />
    <hkern u1="&#x201d;" g2="Icircumflex.sc" k="-25" />
    <hkern u1="&#x201d;" u2="&#x135;" k="-31" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-53" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-49" />
    <hkern u1="&#x2044;" g2="four.dnom" k="53" />
    <hkern u1="&#x2044;" g2="eight.oldstyle" k="-109" />
    <hkern u1="&#x2044;" g2="seven.oldstyle" k="-100" />
    <hkern u1="&#x2044;" g2="six.oldstyle" k="-102" />
    <hkern u1="&#x2044;" g2="four.oldstyle" k="90" />
    <hkern u1="&#x2044;" g2="two.oldstyle" k="-16" />
    <hkern u1="&#x2044;" g2="one.oldstyle" k="-35" />
    <hkern u1="&#x2044;" u2="&#x39;" k="-147" />
    <hkern u1="&#x2044;" u2="&#x38;" k="-102" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-348" />
    <hkern u1="&#x2044;" u2="&#x36;" k="-102" />
    <hkern u1="&#x2044;" u2="&#x35;" k="-201" />
    <hkern u1="&#x2044;" u2="&#x33;" k="-180" />
    <hkern u1="&#x2044;" u2="&#x32;" k="-129" />
    <hkern u1="&#x2044;" u2="&#x31;" k="-174" />
    <hkern u1="&#x2044;" u2="&#x30;" k="-113" />
    <hkern u1="&#x20ac;" g2="four.oldstyle" k="45" />
    <hkern u1="&#x20ac;" g2="three.oldstyle" k="41" />
    <hkern u1="&#x2122;" u2="&#x135;" k="-18" />
    <hkern u1="&#x2122;" u2="&#x110;" k="-20" />
    <hkern u1="&#x2122;" u2="&#xee;" k="-45" />
    <hkern u1="&#x2122;" u2="&#xd0;" k="-20" />
    <hkern u1="&#x2212;" g2="four.oldstyle" k="59" />
    <hkern u1="&#x2212;" g2="three.oldstyle" k="31" />
    <hkern u1="&#x2212;" u2="&#x37;" k="39" />
    <hkern g1="f_f" u2="&#x149;" k="-131" />
    <hkern g1="f_f" u2="&#x135;" k="-147" />
    <hkern g1="f_f" u2="&#x12d;" k="-72" />
    <hkern g1="f_f" u2="&#x12b;" k="-88" />
    <hkern g1="f_f" u2="&#x129;" k="-90" />
    <hkern g1="f_f" u2="&#xef;" k="-141" />
    <hkern g1="f_f" u2="&#xee;" k="-145" />
    <hkern g1="f_f" u2="&#xec;" k="-72" />
    <hkern g1="fi" u2="&#x135;" k="-16" />
    <hkern g1="fi" u2="&#xef;" k="-29" />
    <hkern g1="fi" u2="&#xee;" k="-35" />
    <hkern g1="fl" u2="&#x149;" k="-66" />
    <hkern g1="f_f_i" u2="&#x135;" k="-18" />
    <hkern g1="f_f_i" u2="&#xef;" k="-29" />
    <hkern g1="f_f_i" u2="&#xee;" k="-35" />
    <hkern g1="f_f_l" u2="&#x149;" k="-66" />
    <hkern g1="f_f_j" u2="&#x135;" k="-18" />
    <hkern g1="f_f_j" u2="&#xef;" k="-31" />
    <hkern g1="f_f_j" u2="&#xee;" k="-35" />
    <hkern g1="f_j" u2="&#x135;" k="-20" />
    <hkern g1="f_j" u2="&#xef;" k="-31" />
    <hkern g1="f_j" u2="&#xee;" k="-37" />
    <hkern g1="parenleft.case" u2="&#x1fe;" k="78" />
    <hkern g1="parenleft.case" u2="&#x134;" k="-193" />
    <hkern g1="parenleft.case" u2="&#xd8;" k="78" />
    <hkern g1="parenleft.case" u2="&#xcf;" k="-20" />
    <hkern g1="parenleft.case" u2="&#xce;" k="-66" />
    <hkern g1="parenleft.case" u2="X" k="-10" />
    <hkern g1="parenleft.case" u2="J" k="-193" />
    <hkern g1="at.case" u2="&#xc6;" k="37" />
    <hkern g1="at.case" u2="Y" k="29" />
    <hkern g1="bracketleft.case" u2="&#x134;" k="-188" />
    <hkern g1="bracketleft.case" u2="&#xce;" k="-61" />
    <hkern g1="bracketleft.case" u2="J" k="-188" />
    <hkern g1="braceleft.case" u2="&#x1fe;" k="86" />
    <hkern g1="braceleft.case" u2="&#x134;" k="-193" />
    <hkern g1="braceleft.case" u2="&#xd8;" k="86" />
    <hkern g1="braceleft.case" u2="&#xcf;" k="-12" />
    <hkern g1="braceleft.case" u2="&#xce;" k="-66" />
    <hkern g1="braceleft.case" u2="J" k="-193" />
    <hkern g1="questiondown.case" u2="&#x141;" k="-12" />
    <hkern g1="questiondown.case" u2="V" k="23" />
    <hkern g1="zero.oldstyle" g2="four.oldstyle" k="55" />
    <hkern g1="zero.oldstyle" u2="&#x2044;" k="-139" />
    <hkern g1="zero.oldstyle" u2="&#xb0;" k="20" />
    <hkern g1="zero.oldstyle" u2="&#x7d;" k="88" />
    <hkern g1="zero.oldstyle" u2="]" k="70" />
    <hkern g1="zero.oldstyle" u2="\" k="92" />
    <hkern g1="zero.oldstyle" u2="&#x29;" k="90" />
    <hkern g1="one.oldstyle" u2="&#x2044;" k="-293" />
    <hkern g1="one.oldstyle" u2="&#x7d;" k="43" />
    <hkern g1="one.oldstyle" u2="]" k="35" />
    <hkern g1="one.oldstyle" u2="\" k="31" />
    <hkern g1="one.oldstyle" u2="&#x29;" k="41" />
    <hkern g1="two.oldstyle" u2="&#x2044;" k="-334" />
    <hkern g1="two.oldstyle" u2="&#x7d;" k="76" />
    <hkern g1="two.oldstyle" u2="]" k="57" />
    <hkern g1="two.oldstyle" u2="\" k="80" />
    <hkern g1="two.oldstyle" u2="&#x29;" k="70" />
    <hkern g1="three.oldstyle" u2="&#x2044;" k="-375" />
    <hkern g1="three.oldstyle" u2="&#xb0;" k="47" />
    <hkern g1="three.oldstyle" u2="\" k="115" />
    <hkern g1="four.oldstyle" g2="one.oldstyle" k="23" />
    <hkern g1="four.oldstyle" u2="&#x2212;" k="27" />
    <hkern g1="four.oldstyle" u2="&#x2044;" k="-377" />
    <hkern g1="four.oldstyle" u2="&#xb7;" k="37" />
    <hkern g1="four.oldstyle" u2="&#xb0;" k="35" />
    <hkern g1="four.oldstyle" u2="&#x7d;" k="47" />
    <hkern g1="four.oldstyle" u2="]" k="37" />
    <hkern g1="four.oldstyle" u2="\" k="111" />
    <hkern g1="four.oldstyle" u2="&#x2d;" k="23" />
    <hkern g1="four.oldstyle" u2="&#x2b;" k="23" />
    <hkern g1="four.oldstyle" u2="&#x29;" k="45" />
    <hkern g1="five.oldstyle" u2="&#x2044;" k="-367" />
    <hkern g1="five.oldstyle" u2="&#x7d;" k="29" />
    <hkern g1="five.oldstyle" u2="]" k="25" />
    <hkern g1="five.oldstyle" u2="\" k="78" />
    <hkern g1="five.oldstyle" u2="&#x29;" k="29" />
    <hkern g1="six.oldstyle" g2="four.oldstyle" k="43" />
    <hkern g1="six.oldstyle" g2="three.oldstyle" k="25" />
    <hkern g1="six.oldstyle" u2="&#x2044;" k="-160" />
    <hkern g1="seven.oldstyle" g2="four.oldstyle" k="90" />
    <hkern g1="seven.oldstyle" u2="&#x2044;" k="-63" />
    <hkern g1="seven.oldstyle" u2="&#x7d;" k="25" />
    <hkern g1="seven.oldstyle" u2="]" k="25" />
    <hkern g1="seven.oldstyle" u2="&#x2f;" k="31" />
    <hkern g1="seven.oldstyle" u2="&#x29;" k="27" />
    <hkern g1="eight.oldstyle" g2="four.oldstyle" k="43" />
    <hkern g1="eight.oldstyle" g2="three.oldstyle" k="27" />
    <hkern g1="eight.oldstyle" u2="&#x2044;" k="-158" />
    <hkern g1="eight.oldstyle" u2="&#x7d;" k="45" />
    <hkern g1="eight.oldstyle" u2="]" k="35" />
    <hkern g1="eight.oldstyle" u2="\" k="37" />
    <hkern g1="eight.oldstyle" u2="&#x29;" k="49" />
    <hkern g1="nine.oldstyle" u2="&#x2044;" k="-326" />
    <hkern g1="nine.oldstyle" u2="&#xb0;" k="49" />
    <hkern g1="nine.oldstyle" u2="&#x7d;" k="53" />
    <hkern g1="nine.oldstyle" u2="]" k="43" />
    <hkern g1="nine.oldstyle" u2="\" k="111" />
    <hkern g1="nine.oldstyle" u2="&#x29;" k="55" />
    <hkern g1="ampersand.sc" g2="V.sc" k="27" />
    <hkern g1="B.sc" g2="X.sc" k="16" />
    <hkern g1="B.sc" g2="V.sc" k="23" />
    <hkern g1="B.sc" u2="&#x2122;" k="29" />
    <hkern g1="B.sc" u2="&#x7d;" k="72" />
    <hkern g1="B.sc" u2="]" k="53" />
    <hkern g1="B.sc" u2="\" k="51" />
    <hkern g1="B.sc" u2="&#x29;" k="72" />
    <hkern g1="D.sc" g2="Hbar.sc" k="12" />
    <hkern g1="D.sc" g2="Lslash.sc" k="12" />
    <hkern g1="F.sc" g2="M.sc" k="12" />
    <hkern g1="F.sc" u2="&#x2f;" k="59" />
    <hkern g1="F.sc" u2="&#x20;" k="43" />
    <hkern g1="L.sc" u2="&#xb7;" k="420" />
    <hkern g1="M.sc" g2="V.sc" k="25" />
    <hkern g1="M.sc" u2="&#x2122;" k="20" />
    <hkern g1="M.sc" u2="&#x7d;" k="51" />
    <hkern g1="M.sc" u2="]" k="35" />
    <hkern g1="M.sc" u2="\" k="41" />
    <hkern g1="M.sc" u2="&#x29;" k="47" />
    <hkern g1="O.sc" g2="Hbar.sc" k="12" />
    <hkern g1="O.sc" g2="Lslash.sc" k="12" />
    <hkern g1="P.sc" g2="X.sc" k="31" />
    <hkern g1="P.sc" g2="M.sc" k="10" />
    <hkern g1="P.sc" u2="&#x7d;" k="39" />
    <hkern g1="P.sc" u2="]" k="33" />
    <hkern g1="P.sc" u2="&#x2f;" k="61" />
    <hkern g1="P.sc" u2="&#x29;" k="41" />
    <hkern g1="P.sc" u2="&#x20;" k="47" />
    <hkern g1="Q.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Q.sc" g2="Lslash.sc" k="12" />
    <hkern g1="V.sc" g2="M.sc" k="25" />
    <hkern g1="V.sc" u2="&#x2f;" k="66" />
    <hkern g1="V.sc" u2="&#x2a;" k="-47" />
    <hkern g1="V.sc" u2="&#x20;" k="55" />
    <hkern g1="X.sc" u2="&#x2a;" k="-31" />
    <hkern g1="Y.sc" u2="&#x40;" k="27" />
    <hkern g1="Icircumflex.sc" u2="&#x2122;" k="-49" />
    <hkern g1="Icircumflex.sc" u2="&#x7d;" k="-37" />
    <hkern g1="Icircumflex.sc" u2="]" k="-33" />
    <hkern g1="Icircumflex.sc" u2="&#x29;" k="-35" />
    <hkern g1="Idieresis.sc" u2="&#x2122;" k="-14" />
    <hkern g1="Idieresis.sc" u2="&#x2a;" k="-18" />
    <hkern g1="Eth.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Eth.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Oacute.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Thorn.sc" g2="X.sc" k="45" />
    <hkern g1="Thorn.sc" g2="V.sc" k="23" />
    <hkern g1="Thorn.sc" u2="&#x2122;" k="37" />
    <hkern g1="Thorn.sc" u2="&#x7d;" k="70" />
    <hkern g1="Thorn.sc" u2="]" k="51" />
    <hkern g1="Thorn.sc" u2="\" k="59" />
    <hkern g1="Thorn.sc" u2="&#x29;" k="74" />
    <hkern g1="Aogonek.sc" g2="J.sc" k="-354" />
    <hkern g1="Aogonek.sc" u2="&#x201e;" k="-197" />
    <hkern g1="Aogonek.sc" u2="&#x201a;" k="-197" />
    <hkern g1="Aogonek.sc" u2="&#x7d;" k="-143" />
    <hkern g1="Aogonek.sc" u2="&#x7c;" k="-86" />
    <hkern g1="Aogonek.sc" u2="]" k="-143" />
    <hkern g1="Aogonek.sc" u2="&#x3b;" k="-195" />
    <hkern g1="Aogonek.sc" u2="&#x2c;" k="-223" />
    <hkern g1="Aogonek.sc" u2="&#x29;" k="-145" />
    <hkern g1="Eogonek.sc" g2="J.sc" k="-258" />
    <hkern g1="Eogonek.sc" u2="&#x201e;" k="-100" />
    <hkern g1="Eogonek.sc" u2="&#x201a;" k="-100" />
    <hkern g1="Eogonek.sc" u2="&#x7d;" k="-49" />
    <hkern g1="Eogonek.sc" u2="]" k="-47" />
    <hkern g1="Eogonek.sc" u2="&#x3b;" k="-100" />
    <hkern g1="Eogonek.sc" u2="&#x2c;" k="-127" />
    <hkern g1="Eogonek.sc" u2="&#x29;" k="-49" />
    <hkern g1="Hbar.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Gdotaccent.sc" k="12" />
    <hkern g1="Hbar.sc" g2="OE.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Q.sc" k="12" />
    <hkern g1="Hbar.sc" g2="O.sc" k="12" />
    <hkern g1="Hbar.sc" g2="G.sc" k="12" />
    <hkern g1="Hbar.sc" g2="C.sc" k="12" />
    <hkern g1="Hbar.sc" u2="&#x2a;" k="-51" />
    <hkern g1="Iogonek.sc" g2="J.sc" k="-207" />
    <hkern g1="Iogonek.sc" u2="&#x201e;" k="-51" />
    <hkern g1="Iogonek.sc" u2="&#x201a;" k="-51" />
    <hkern g1="Iogonek.sc" u2="&#x7d;" k="-16" />
    <hkern g1="Iogonek.sc" u2="&#x3b;" k="-49" />
    <hkern g1="Iogonek.sc" u2="&#x2c;" k="-76" />
    <hkern g1="Jcircumflex.sc" u2="&#x2122;" k="-45" />
    <hkern g1="Jcircumflex.sc" u2="&#x7d;" k="-33" />
    <hkern g1="Jcircumflex.sc" u2="]" k="-29" />
    <hkern g1="Jcircumflex.sc" u2="&#x29;" k="-31" />
    <hkern g1="Lcaron.sc" g2="Tcommaaccent.sc.alt1" k="-57" />
    <hkern g1="Lcaron.sc" g2="Tbar.sc.alt1" k="-57" />
    <hkern g1="Lcaron.sc" g2="Tcaron.sc.alt1" k="-57" />
    <hkern g1="Lcaron.sc" g2="T.sc.alt1" k="-57" />
    <hkern g1="Lcaron.sc" g2="Y.sc" k="-109" />
    <hkern g1="Lcaron.sc" g2="X.sc" k="-35" />
    <hkern g1="Lcaron.sc" g2="W.sc" k="-80" />
    <hkern g1="Lcaron.sc" g2="V.sc" k="-55" />
    <hkern g1="Lcaron.sc" g2="T.sc" k="-57" />
    <hkern g1="Lcaron.sc" u2="&#x2122;" k="-2" />
    <hkern g1="Lcaron.sc" u2="&#x201c;" k="63" />
    <hkern g1="Lcaron.sc" u2="&#x2018;" k="63" />
    <hkern g1="Lcaron.sc" u2="&#x7d;" k="-29" />
    <hkern g1="Lcaron.sc" u2="]" k="-25" />
    <hkern g1="Lcaron.sc" u2="\" k="66" />
    <hkern g1="Lcaron.sc" u2="&#x2a;" k="-41" />
    <hkern g1="Lcaron.sc" u2="&#x29;" k="-27" />
    <hkern g1="Lcaron.sc" u2="&#x27;" k="39" />
    <hkern g1="Lcaron.sc" u2="&#x22;" k="39" />
    <hkern g1="Lcaron.sc" u2="&#x20;" k="115" />
    <hkern g1="seven.numr" u2="&#x2044;" k="45" />
    <hkern g1="l.alt1" u2="&#xb7;" k="164" />
    <hkern g1="lslash.alt1" g2="y.alt1" k="-6" />
    <hkern g1="lslash.alt1" u2="y" k="-18" />
    <hkern g1="lslash.alt1" u2="x" k="-33" />
    <hkern g1="lslash.alt1" u2="w" k="-2" />
    <hkern g1="lslash.alt1" u2="v" k="-14" />
    <hkern g1="lslash.alt1" u2="&#x2a;" k="-37" />
    <hkern g1="lcaron.alt1" g2="l.alt1" k="-125" />
    <hkern g1="lcaron.alt1" u2="&#x2122;" k="-182" />
    <hkern g1="lcaron.alt1" u2="&#x201d;" k="-31" />
    <hkern g1="lcaron.alt1" u2="&#x201c;" k="-31" />
    <hkern g1="lcaron.alt1" u2="&#x2019;" k="-31" />
    <hkern g1="lcaron.alt1" u2="&#x2018;" k="-31" />
    <hkern g1="lcaron.alt1" u2="&#x17e;" k="-82" />
    <hkern g1="lcaron.alt1" u2="&#x161;" k="-106" />
    <hkern g1="lcaron.alt1" u2="&#x133;" k="-121" />
    <hkern g1="lcaron.alt1" u2="&#x10d;" k="-39" />
    <hkern g1="lcaron.alt1" u2="&#xfe;" k="-113" />
    <hkern g1="lcaron.alt1" u2="&#xdf;" k="-23" />
    <hkern g1="lcaron.alt1" u2="&#x7d;" k="-174" />
    <hkern g1="lcaron.alt1" u2="&#x7c;" k="-102" />
    <hkern g1="lcaron.alt1" u2="l" k="-113" />
    <hkern g1="lcaron.alt1" u2="k" k="-113" />
    <hkern g1="lcaron.alt1" u2="j" k="-119" />
    <hkern g1="lcaron.alt1" u2="i" k="-121" />
    <hkern g1="lcaron.alt1" u2="h" k="-113" />
    <hkern g1="lcaron.alt1" u2="b" k="-113" />
    <hkern g1="lcaron.alt1" u2="]" k="-172" />
    <hkern g1="lcaron.alt1" u2="\" k="-180" />
    <hkern g1="lcaron.alt1" u2="&#x3f;" k="-70" />
    <hkern g1="lcaron.alt1" u2="&#x2a;" k="-133" />
    <hkern g1="lcaron.alt1" u2="&#x29;" k="-176" />
    <hkern g1="lcaron.alt1" u2="&#x27;" k="-123" />
    <hkern g1="lcaron.alt1" u2="&#x26;" k="-31" />
    <hkern g1="lcaron.alt1" u2="&#x22;" k="-123" />
    <hkern g1="lcaron.alt1" u2="&#x21;" k="-104" />
    <hkern g1="aogonek.alt1" u2="j" k="-88" />
    <hkern g1="tcaron.alt1" g2="l.alt1" k="-111" />
    <hkern g1="tcaron.alt1" u2="&#x2122;" k="-162" />
    <hkern g1="tcaron.alt1" u2="&#x161;" k="-106" />
    <hkern g1="tcaron.alt1" u2="&#x133;" k="-82" />
    <hkern g1="tcaron.alt1" u2="&#x11b;" k="-33" />
    <hkern g1="tcaron.alt1" u2="&#x10d;" k="-41" />
    <hkern g1="tcaron.alt1" u2="&#xfe;" k="-94" />
    <hkern g1="tcaron.alt1" u2="&#x7d;" k="-154" />
    <hkern g1="tcaron.alt1" u2="&#x7c;" k="-100" />
    <hkern g1="tcaron.alt1" u2="l" k="-94" />
    <hkern g1="tcaron.alt1" u2="k" k="-94" />
    <hkern g1="tcaron.alt1" u2="j" k="-82" />
    <hkern g1="tcaron.alt1" u2="i" k="-82" />
    <hkern g1="tcaron.alt1" u2="h" k="-94" />
    <hkern g1="tcaron.alt1" u2="b" k="-94" />
    <hkern g1="tcaron.alt1" u2="]" k="-150" />
    <hkern g1="tcaron.alt1" u2="\" k="-160" />
    <hkern g1="tcaron.alt1" u2="&#x3f;" k="-33" />
    <hkern g1="tcaron.alt1" u2="&#x2a;" k="-90" />
    <hkern g1="tcaron.alt1" u2="&#x29;" k="-158" />
    <hkern g1="tcaron.alt1" u2="&#x27;" k="-102" />
    <hkern g1="tcaron.alt1" u2="&#x22;" k="-102" />
    <hkern g1="tcaron.alt1" u2="&#x21;" k="-84" />
    <hkern g1="space"
  g2="t,tcommaaccent,tcaron,tbar"
  k="37" />
    <hkern g1="space"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="51" />
    <hkern g1="space"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="47" />
    <hkern g1="space"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="66" />
    <hkern g1="space"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="55" />
    <hkern g1="space"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="86" />
    <hkern g1="space"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="61" />
    <hkern g1="space"
  g2="AE,AEacute"
  k="80" />
    <hkern g1="space"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="55" />
    <hkern g1="space"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="53" />
    <hkern g1="space"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="55" />
    <hkern g1="space"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="74" />
    <hkern g1="space"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="space"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="49" />
    <hkern g1="space"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="37" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t,tcommaaccent,tcaron,tbar"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="104" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="145" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="88" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="68" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="121" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="37" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="v"
  k="57" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V"
  k="92" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quotedbl,quotesingle"
  k="219" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one"
  k="53" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="seven"
  k="29" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteright,quotedblright"
  k="240" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteleft,quotedblleft"
  k="244" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one.oldstyle"
  k="39" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="three.oldstyle"
  k="41" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="seven.oldstyle"
  k="74" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V.sc"
  k="68" />
    <hkern g1="colon,semicolon"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="86" />
    <hkern g1="colon,semicolon"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="115" />
    <hkern g1="colon,semicolon"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="33" />
    <hkern g1="colon,semicolon"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="59" />
    <hkern g1="colon,semicolon"
  g2="V"
  k="43" />
    <hkern g1="quotedbl,quotesingle"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="55" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE,AEacute"
  k="129" />
    <hkern g1="quotedbl,quotesingle"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="59" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE.sc,AEacute.sc"
  k="129" />
    <hkern g1="quotedbl,quotesingle"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="quotedbl,quotesingle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="219" />
    <hkern g1="quotedbl,quotesingle"
  g2="four"
  k="63" />
    <hkern g1="quotedbl,quotesingle"
  g2="slash"
  k="92" />
    <hkern g1="quotedbl,quotesingle"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="quotedbl,quotesingle"
  g2="four.oldstyle"
  k="139" />
    <hkern g1="quotedbl,quotesingle"
  g2="five.oldstyle"
  k="27" />
    <hkern g1="quotedbl,quotesingle"
  g2="nine.oldstyle"
  k="25" />
    <hkern g1="quotedbl,quotesingle"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="exclamdown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="92" />
    <hkern g1="exclamdown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="113" />
    <hkern g1="exclamdown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="29" />
    <hkern g1="exclamdown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="55" />
    <hkern g1="bracketleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="bracketleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="27" />
    <hkern g1="bracketleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="31" />
    <hkern g1="bracketleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-51" />
    <hkern g1="bracketleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-23" />
    <hkern g1="bracketleft"
  g2="AE,AEacute"
  k="37" />
    <hkern g1="bracketleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="49" />
    <hkern g1="bracketleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="-25" />
    <hkern g1="bracketleft"
  g2="AE.sc,AEacute.sc"
  k="51" />
    <hkern g1="bracketleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="bracketleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="47" />
    <hkern g1="bracketleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="76" />
    <hkern g1="bracketleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="63" />
    <hkern g1="bracketleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="63" />
    <hkern g1="bracketleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="51" />
    <hkern g1="bracketleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="53" />
    <hkern g1="bracketleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="51" />
    <hkern g1="bracketleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="61" />
    <hkern g1="bracketleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="35" />
    <hkern g1="slash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="94" />
    <hkern g1="slash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-61" />
    <hkern g1="slash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-31" />
    <hkern g1="slash"
  g2="AE,AEacute"
  k="166" />
    <hkern g1="slash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="121" />
    <hkern g1="slash"
  g2="AE.sc,AEacute.sc"
  k="180" />
    <hkern g1="slash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="slash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="slash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="100" />
    <hkern g1="slash"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="92" />
    <hkern g1="slash"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="86" />
    <hkern g1="slash"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="57" />
    <hkern g1="slash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="51" />
    <hkern g1="slash"
  g2="z,zacute,zdotaccent,zcaron"
  k="55" />
    <hkern g1="slash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="63" />
    <hkern g1="slash"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="backslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="backslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="35" />
    <hkern g1="backslash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-43" />
    <hkern g1="backslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="98" />
    <hkern g1="backslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="143" />
    <hkern g1="backslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="86" />
    <hkern g1="backslash"
  g2="AE,AEacute"
  k="-76" />
    <hkern g1="backslash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="-10" />
    <hkern g1="backslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="55" />
    <hkern g1="backslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="63" />
    <hkern g1="backslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="106" />
    <hkern g1="backslash"
  g2="AE.sc,AEacute.sc"
  k="-33" />
    <hkern g1="backslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="45" />
    <hkern g1="backslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="37" />
    <hkern g1="backslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="backslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="backslash"
  g2="quotedbl,quotesingle"
  k="92" />
    <hkern g1="hyphen,endash,emdash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="96" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="129" />
    <hkern g1="hyphen,endash,emdash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="43" />
    <hkern g1="hyphen,endash,emdash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="61" />
    <hkern g1="hyphen,endash,emdash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="25" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="94" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE.sc,AEacute.sc"
  k="25" />
    <hkern g1="hyphen,endash,emdash"
  g2="V"
  k="51" />
    <hkern g1="hyphen,endash,emdash"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="hyphen,endash,emdash"
  g2="V.sc"
  k="29" />
    <hkern g1="hyphen,endash,emdash"
  g2="x"
  k="37" />
    <hkern g1="hyphen,endash,emdash"
  g2="X"
  k="27" />
    <hkern g1="hyphen,endash,emdash"
  g2="X.sc"
  k="35" />
    <hkern g1="registered"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="57" />
    <hkern g1="registered"
  g2="AE,AEacute"
  k="41" />
    <hkern g1="registered"
  g2="AE.sc,AEacute.sc"
  k="49" />
    <hkern g1="ampersand"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-33" />
    <hkern g1="ampersand"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="74" />
    <hkern g1="ampersand"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="98" />
    <hkern g1="ampersand"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="ampersand"
  g2="AE,AEacute"
  k="-49" />
    <hkern g1="ampersand"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="49" />
    <hkern g1="ampersand"
  g2="AE.sc,AEacute.sc"
  k="-20" />
    <hkern g1="quoteright,quotedblright"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="78" />
    <hkern g1="quoteright,quotedblright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-45" />
    <hkern g1="quoteright,quotedblright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-16" />
    <hkern g1="quoteright,quotedblright"
  g2="AE,AEacute"
  k="164" />
    <hkern g1="quoteright,quotedblright"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="84" />
    <hkern g1="quoteright,quotedblright"
  g2="AE.sc,AEacute.sc"
  k="160" />
    <hkern g1="quoteright,quotedblright"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="16" />
    <hkern g1="quoteright,quotedblright"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="74" />
    <hkern g1="quoteright,quotedblright"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="274" />
    <hkern g1="quoteright,quotedblright"
  g2="guillemotleft,guilsinglleft"
  k="76" />
    <hkern g1="quoteright,quotedblright"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="53" />
    <hkern g1="quoteright,quotedblright"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="49" />
    <hkern g1="quoteright,quotedblright"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="16" />
    <hkern g1="quoteright,quotedblright"
  g2="z,zacute,zdotaccent,zcaron"
  k="18" />
    <hkern g1="quoteright,quotedblright"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="quoteright,quotedblright"
  g2="M"
  k="18" />
    <hkern g1="quoteright,quotedblright"
  g2="colon,semicolon"
  k="29" />
    <hkern g1="quoteright,quotedblright"
  g2="hyphen,endash,emdash"
  k="59" />
    <hkern g1="quoteright,quotedblright"
  g2="M.sc"
  k="16" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="76" />
    <hkern g1="quoteleft,quotedblleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-59" />
    <hkern g1="quoteleft,quotedblleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-29" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE,AEacute"
  k="160" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="82" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE.sc,AEacute.sc"
  k="158" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="66" />
    <hkern g1="quoteleft,quotedblleft"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="264" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="45" />
    <hkern g1="quoteleft,quotedblleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="41" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M.sc"
  k="18" />
    <hkern g1="trademark"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="33" />
    <hkern g1="trademark"
  g2="AE,AEacute"
  k="90" />
    <hkern g1="trademark"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="37" />
    <hkern g1="trademark"
  g2="AE.sc,AEacute.sc"
  k="92" />
    <hkern g1="braceleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="braceleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="29" />
    <hkern g1="braceleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="braceleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-55" />
    <hkern g1="braceleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-27" />
    <hkern g1="braceleft"
  g2="AE,AEacute"
  k="84" />
    <hkern g1="braceleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="94" />
    <hkern g1="braceleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="-29" />
    <hkern g1="braceleft"
  g2="AE.sc,AEacute.sc"
  k="100" />
    <hkern g1="braceleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="braceleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="braceleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="98" />
    <hkern g1="braceleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="86" />
    <hkern g1="braceleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="braceleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="66" />
    <hkern g1="braceleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="61" />
    <hkern g1="braceleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="63" />
    <hkern g1="braceleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="80" />
    <hkern g1="braceleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="49" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-10" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="113" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="94" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="AE,AEacute"
  k="-23" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="33" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="V"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="135" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="160" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="66" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="68" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="47" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="121" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE.sc,AEacute.sc"
  k="33" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="v"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V"
  k="74" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quotedbl,quotesingle"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quoteright,quotedblright"
  k="39" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V.sc"
  k="49" />
    <hkern g1="guillemotright,guilsinglright"
  g2="x"
  k="45" />
    <hkern g1="guillemotright,guilsinglright"
  g2="X"
  k="20" />
    <hkern g1="guillemotright,guilsinglright"
  g2="X.sc"
  k="47" />
    <hkern g1="questiondown"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="27" />
    <hkern g1="questiondown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="119" />
    <hkern g1="questiondown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="152" />
    <hkern g1="questiondown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="78" />
    <hkern g1="questiondown"
  g2="AE,AEacute"
  k="-14" />
    <hkern g1="questiondown"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="37" />
    <hkern g1="questiondown"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="55" />
    <hkern g1="questiondown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="98" />
    <hkern g1="questiondown"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="27" />
    <hkern g1="questiondown"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="35" />
    <hkern g1="questiondown"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="45" />
    <hkern g1="questiondown"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="47" />
    <hkern g1="questiondown"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="questiondown"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="31" />
    <hkern g1="questiondown"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="37" />
    <hkern g1="questiondown"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="questiondown"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="23" />
    <hkern g1="questiondown"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="39" />
    <hkern g1="questiondown"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="39" />
    <hkern g1="asterisk"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="55" />
    <hkern g1="asterisk"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-51" />
    <hkern g1="asterisk"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-35" />
    <hkern g1="asterisk"
  g2="AE,AEacute"
  k="129" />
    <hkern g1="asterisk"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="asterisk"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="-25" />
    <hkern g1="asterisk"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="-12" />
    <hkern g1="asterisk"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="-80" />
    <hkern g1="asterisk"
  g2="AE.sc,AEacute.sc"
  k="131" />
    <hkern g1="asterisk"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="35" />
    <hkern g1="asterisk"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="27" />
    <hkern g1="asterisk"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="parenleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="parenleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="31" />
    <hkern g1="parenleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="53" />
    <hkern g1="parenleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-53" />
    <hkern g1="parenleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-25" />
    <hkern g1="parenleft"
  g2="AE,AEacute"
  k="59" />
    <hkern g1="parenleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="72" />
    <hkern g1="parenleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="-27" />
    <hkern g1="parenleft"
  g2="AE.sc,AEacute.sc"
  k="74" />
    <hkern g1="parenleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="parenleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="parenleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="98" />
    <hkern g1="parenleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="84" />
    <hkern g1="parenleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="parenleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="63" />
    <hkern g1="parenleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="61" />
    <hkern g1="parenleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="61" />
    <hkern g1="parenleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="82" />
    <hkern g1="parenleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="51" />
    <hkern g1="ampersand.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="37" />
    <hkern g1="ampersand.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="ampersand.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="84" />
    <hkern g1="ampersand.sc"
  g2="AE.sc,AEacute.sc"
  k="-16" />
    <hkern g1="questiondown.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="29" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="47" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE,AEacute"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE.sc,AEacute.sc"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="V"
  k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="x"
  k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="X"
  k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="parenright.case"
  k="45" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="bracketright.case"
  k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright.case"
  k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="111" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="123" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="59" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="94" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="v"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quotedbl,quotesingle"
  k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteright,quotedblright"
  k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteleft,quotedblleft"
  k="59" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V.sc"
  k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="slash"
  k="-39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright.case"
  k="-37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="bracketright.case"
  k="-23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright.case"
  k="-27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="space"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="bracketright"
  k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="backslash"
  k="96" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="trademark"
  k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="asterisk"
  k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright"
  k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="hyphen.case,endash.case,emdash.case"
  k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="43" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="29" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="84" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE,AEacute"
  k="66" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="18" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V"
  k="33" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="31" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="slash"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="x"
  k="31" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X"
  k="59" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X.sc"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="M"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright.case"
  k="96" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright.case"
  k="63" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright.case"
  k="102" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright"
  k="43" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="backslash"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="trademark"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright"
  k="59" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright"
  k="59" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="v"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="25" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t,tcommaaccent,tcaron,tbar"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="z,zacute,zdotaccent,zcaron"
  k="16" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="49" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="80" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="115" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="170" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="115" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="96" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="152" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="68" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="53" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="v"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V"
  k="127" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quotedbl,quotesingle"
  k="139" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteright,quotedblright"
  k="141" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteleft,quotedblleft"
  k="139" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V.sc"
  k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="space"
  k="55" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="bracketright"
  k="51" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="backslash"
  k="143" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="trademark"
  k="137" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="braceright"
  k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="asterisk"
  k="137" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="parenright"
  k="76" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen.case,endash.case,emdash.case"
  k="135" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft.case,guilsinglleft.case"
  k="78" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="18" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="parenright.case"
  k="35" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="bracketright.case"
  k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="braceright.case"
  k="49" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="trademark"
  k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="asterisk"
  k="-68" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="39" />
    <hkern g1="F"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="F"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="F"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="F"
  g2="AE,AEacute"
  k="127" />
    <hkern g1="F"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="98" />
    <hkern g1="F"
  g2="AE.sc,AEacute.sc"
  k="156" />
    <hkern g1="F"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="10" />
    <hkern g1="F"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="16" />
    <hkern g1="F"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="18" />
    <hkern g1="F"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="F"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="66" />
    <hkern g1="F"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="100" />
    <hkern g1="F"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="F"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="78" />
    <hkern g1="F"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="59" />
    <hkern g1="F"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="51" />
    <hkern g1="F"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="43" />
    <hkern g1="F"
  g2="z,zacute,zdotaccent,zcaron"
  k="49" />
    <hkern g1="F"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="F"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="12" />
    <hkern g1="F"
  g2="colon,semicolon"
  k="23" />
    <hkern g1="F"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="76" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="18" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE,AEacute"
  k="55" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="14" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE.sc,AEacute.sc"
  k="59" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V"
  k="33" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="slash"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="x"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X"
  k="53" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X.sc"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright.case"
  k="88" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright.case"
  k="59" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright"
  k="35" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="backslash"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="trademark"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright"
  k="51" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright"
  k="51" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="B"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="B"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="B"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="B"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="29" />
    <hkern g1="B"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="63" />
    <hkern g1="B"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="B"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="B"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="20" />
    <hkern g1="B"
  g2="AE.sc,AEacute.sc"
  k="18" />
    <hkern g1="B"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="B"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="B"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="B"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="B"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="B"
  g2="z,zacute,zdotaccent,zcaron"
  k="16" />
    <hkern g1="B"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="P"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="59" />
    <hkern g1="P"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="27" />
    <hkern g1="P"
  g2="AE,AEacute"
  k="127" />
    <hkern g1="P"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="P"
  g2="AE.sc,AEacute.sc"
  k="156" />
    <hkern g1="P"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="35" />
    <hkern g1="P"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="123" />
    <hkern g1="P"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="P"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="31" />
    <hkern g1="P"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="P"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="P"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="P"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="41" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="V"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="45" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="27" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="23" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="X"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t,tcommaaccent,tcaron,tbar"
  k="53" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="104" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="115" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="109" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE,AEacute"
  k="152" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="102" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE.sc,AEacute.sc"
  k="160" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="111" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="72" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="80" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="v"
  k="109" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="135" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="104" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="slash"
  k="98" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft,guilsinglleft"
  k="135" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="139" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="137" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="111" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="115" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="z,zacute,zdotaccent,zcaron"
  k="139" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="80" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="x"
  k="147" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M"
  k="27" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="colon,semicolon"
  k="86" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen,endash,emdash"
  k="96" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M.sc"
  k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="space"
  k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen.case,endash.case,emdash.case"
  k="88" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft.case,guilsinglleft.case"
  k="66" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotright,guilsinglright"
  k="113" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t,tcommaaccent,tcaron,tbar"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="29" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="v"
  k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="23" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="asterisk"
  k="-10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen.case,endash.case,emdash.case"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="61" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="68" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE,AEacute"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE.sc,AEacute.sc"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="x"
  k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X.sc"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright.case"
  k="74" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright.case"
  k="47" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright.case"
  k="82" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright"
  k="47" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="asterisk"
  k="-18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright"
  k="47" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE,AEacute"
  k="49" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE.sc,AEacute.sc"
  k="61" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="slash"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="25" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="z,zacute,zdotaccent,zcaron"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="x"
  k="27" />
    <hkern g1="K,Kcommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="55" />
    <hkern g1="K,Kcommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="88" />
    <hkern g1="K,Kcommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="84" />
    <hkern g1="K,Kcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="88" />
    <hkern g1="K,Kcommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="86" />
    <hkern g1="K,Kcommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="63" />
    <hkern g1="K,Kcommaaccent"
  g2="v"
  k="88" />
    <hkern g1="K,Kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="96" />
    <hkern g1="K,Kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="14" />
    <hkern g1="K,Kcommaaccent"
  g2="V.sc"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="45" />
    <hkern g1="K,Kcommaaccent"
  g2="slash"
  k="-59" />
    <hkern g1="K,Kcommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="84" />
    <hkern g1="K,Kcommaaccent"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="27" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen,endash,emdash"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="47" />
    <hkern g1="K,Kcommaaccent"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="31" />
    <hkern g1="K,Kcommaaccent"
  g2="parenright.case"
  k="-55" />
    <hkern g1="K,Kcommaaccent"
  g2="bracketright.case"
  k="-43" />
    <hkern g1="K,Kcommaaccent"
  g2="braceright.case"
  k="-47" />
    <hkern g1="K,Kcommaaccent"
  g2="space"
  k="25" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen.case,endash.case,emdash.case"
  k="63" />
    <hkern g1="K,Kcommaaccent"
  g2="guillemotleft.case,guilsinglleft.case"
  k="117" />
    <hkern g1="K,Kcommaaccent"
  g2="guillemotright,guilsinglright"
  k="-29" />
    <hkern g1="K,Kcommaaccent"
  g2="registered"
  k="35" />
    <hkern g1="M"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="M"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="M"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="M"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="27" />
    <hkern g1="M"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="45" />
    <hkern g1="M"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="M"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="10" />
    <hkern g1="M"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="18" />
    <hkern g1="M"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="M"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="M"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="18" />
    <hkern g1="M"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="M"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="M"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="M"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="M"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="14" />
    <hkern g1="V"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="V"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="V"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="V"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="V"
  g2="AE,AEacute"
  k="145" />
    <hkern g1="V"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="V"
  g2="AE.sc,AEacute.sc"
  k="141" />
    <hkern g1="V"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="V"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="V"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="V"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="V"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="102" />
    <hkern g1="V"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="92" />
    <hkern g1="V"
  g2="guillemotleft,guilsinglleft"
  k="74" />
    <hkern g1="V"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="106" />
    <hkern g1="V"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="100" />
    <hkern g1="V"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="78" />
    <hkern g1="V"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="72" />
    <hkern g1="V"
  g2="z,zacute,zdotaccent,zcaron"
  k="72" />
    <hkern g1="V"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="49" />
    <hkern g1="V"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="29" />
    <hkern g1="V"
  g2="colon,semicolon"
  k="43" />
    <hkern g1="V"
  g2="hyphen,endash,emdash"
  k="51" />
    <hkern g1="V"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="18" />
    <hkern g1="V"
  g2="hyphen.case,endash.case,emdash.case"
  k="23" />
    <hkern g1="V"
  g2="guillemotleft.case,guilsinglleft.case"
  k="45" />
    <hkern g1="V"
  g2="guillemotright,guilsinglright"
  k="25" />
    <hkern g1="X"
  g2="t,tcommaaccent,tcaron,tbar"
  k="53" />
    <hkern g1="X"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="84" />
    <hkern g1="X"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="78" />
    <hkern g1="X"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="12" />
    <hkern g1="X"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="86" />
    <hkern g1="X"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="86" />
    <hkern g1="X"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="63" />
    <hkern g1="X"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="X"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="51" />
    <hkern g1="X"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="X"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="X"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="X"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="47" />
    <hkern g1="X"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="59" />
    <hkern g1="X"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="X"
  g2="hyphen,endash,emdash"
  k="25" />
    <hkern g1="X"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="18" />
    <hkern g1="X"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="X"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="18" />
    <hkern g1="X"
  g2="hyphen.case,endash.case,emdash.case"
  k="55" />
    <hkern g1="X"
  g2="guillemotleft.case,guilsinglleft.case"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t,tcommaaccent,tcaron,tbar"
  k="74" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="111" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="123" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE,AEacute"
  k="193" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="139" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE.sc,AEacute.sc"
  k="209" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="119" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="v"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="201" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="145" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="slash"
  k="143" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft,guilsinglleft"
  k="160" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="184" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="182" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="154" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="160" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="z,zacute,zdotaccent,zcaron"
  k="172" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="123" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="x"
  k="139" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M"
  k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="colon,semicolon"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen,endash,emdash"
  k="129" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M.sc"
  k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="parenright.case"
  k="-31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="bracketright.case"
  k="-27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="braceright.case"
  k="-31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="space"
  k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="bracketright"
  k="-51" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="backslash"
  k="-59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="trademark"
  k="-72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="braceright"
  k="-55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="asterisk"
  k="-41" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="parenright"
  k="-53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen.case,endash.case,emdash.case"
  k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft.case,guilsinglleft.case"
  k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright,guilsinglright"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="registered"
  k="51" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="ampersand"
  k="23" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright.case,guilsinglright.case"
  k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="59" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE,AEacute"
  k="133" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="68" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE.sc,AEacute.sc"
  k="131" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="v"
  k="14" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="88" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="slash"
  k="86" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="96" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="z,zacute,zdotaccent,zcaron"
  k="68" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="x"
  k="49" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="colon,semicolon"
  k="31" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen,endash,emdash"
  k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M.sc"
  k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="space"
  k="63" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="bracketright"
  k="-25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="backslash"
  k="-33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="trademark"
  k="-47" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="braceright"
  k="-29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="parenright"
  k="-27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft.case,guilsinglleft.case"
  k="31" />
    <hkern g1="Thorn"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="23" />
    <hkern g1="Thorn"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="23" />
    <hkern g1="Thorn"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="82" />
    <hkern g1="Thorn"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="Thorn"
  g2="AE,AEacute"
  k="68" />
    <hkern g1="Thorn"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="47" />
    <hkern g1="Thorn"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="Thorn"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="25" />
    <hkern g1="d,dcaron,dslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="f,f_f"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="31" />
    <hkern g1="f,f_f"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="f,f_f"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="37" />
    <hkern g1="f,f_f"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="135" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="203" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="v"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="V"
  k="104" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quotedbl,quotesingle"
  k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteright,quotedblright"
  k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteleft,quotedblleft"
  k="41" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="x"
  k="43" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="X"
  k="53" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="M"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="bracketright"
  k="76" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="backslash"
  k="102" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="trademark"
  k="59" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="braceright"
  k="98" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="asterisk"
  k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="parenright"
  k="100" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="31" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="question"
  k="20" />
    <hkern g1="l,lacute,lcommaaccent,lcaron,lslash,fl,f_f_l"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="v"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="x"
  k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="bracketright"
  k="57" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="backslash"
  k="78" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="trademark"
  k="47" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="braceright"
  k="72" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="parenright"
  k="72" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="t,tcommaaccent,tcaron,tbar"
  k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="150" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="199" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="96" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="AE,AEacute"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="v"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="V"
  k="109" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteright,quotedblright"
  k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteleft,quotedblleft"
  k="27" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="x"
  k="27" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="X"
  k="27" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="M"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="bracketright"
  k="68" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="backslash"
  k="92" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="trademark"
  k="55" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="braceright"
  k="88" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="asterisk"
  k="27" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="parenright"
  k="88" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="k,kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="41" />
    <hkern g1="k,kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="55" />
    <hkern g1="k,kcommaaccent"
  g2="slash"
  k="-16" />
    <hkern g1="k,kcommaaccent"
  g2="guillemotleft,guilsinglleft"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="31" />
    <hkern g1="k,kcommaaccent"
  g2="hyphen,endash,emdash"
  k="39" />
    <hkern g1="k,kcommaaccent"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="k,kcommaaccent"
  g2="bracketright"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="backslash"
  k="33" />
    <hkern g1="k,kcommaaccent"
  g2="trademark"
  k="43" />
    <hkern g1="k,kcommaaccent"
  g2="braceright"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="parenright"
  k="35" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="v"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="quoteleft,quotedblleft"
  k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="x"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="bracketright"
  k="66" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="backslash"
  k="92" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="trademark"
  k="57" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="braceright"
  k="86" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="asterisk"
  k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="parenright"
  k="86" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="t,tcommaaccent,tcaron,tbar"
  k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="129" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="199" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="76" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="v"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="27" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="V"
  k="82" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quotedbl,quotesingle"
  k="23" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteright,quotedblright"
  k="27" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteleft,quotedblleft"
  k="31" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="X"
  k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="bracketright"
  k="68" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="backslash"
  k="100" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="trademark"
  k="59" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="braceright"
  k="92" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="asterisk"
  k="31" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="parenright"
  k="86" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="12" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="31" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="37" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="space"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="bracketright"
  k="23" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="braceright"
  k="25" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="parenright"
  k="25" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="80" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="109" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="slash"
  k="70" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="space"
  k="47" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="bracketright"
  k="31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="trademark"
  k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="braceright"
  k="31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="parenright"
  k="31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="v"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="v"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="v"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="57" />
    <hkern g1="v"
  g2="guillemotleft,guilsinglleft"
  k="23" />
    <hkern g1="v"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="v"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="bracketright"
  k="49" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="backslash"
  k="51" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="trademark"
  k="43" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="braceright"
  k="59" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="parenright"
  k="57" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="45" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="57" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="slash"
  k="53" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="guillemotleft,guilsinglleft"
  k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="space"
  k="49" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="bracketright"
  k="29" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="braceright"
  k="29" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="parenright"
  k="31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="35" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="43" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="slash"
  k="35" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="space"
  k="45" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="bracketright"
  k="31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="trademark"
  k="16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="braceright"
  k="31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="parenright"
  k="33" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="x"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="x"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="x"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="39" />
    <hkern g1="x"
  g2="guillemotleft,guilsinglleft"
  k="45" />
    <hkern g1="x"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="x"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="x"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="8" />
    <hkern g1="x"
  g2="hyphen,endash,emdash"
  k="37" />
    <hkern g1="x"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="x"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="111" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="154" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="72" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="V"
  k="78" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="X"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="bracketright"
  k="51" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="backslash"
  k="57" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="trademark"
  k="43" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="braceright"
  k="66" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="parenright"
  k="63" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="germandbls"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="germandbls"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="germandbls"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="germandbls"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="germandbls"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="18" />
    <hkern g1="germandbls"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="18" />
    <hkern g1="germandbls"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="germandbls"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="germandbls"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="germandbls"
  g2="quoteleft,quotedblleft"
  k="18" />
    <hkern g1="germandbls"
  g2="z,zacute,zdotaccent,zcaron"
  k="8" />
    <hkern g1="germandbls"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="23" />
    <hkern g1="eth"
  g2="t,tcommaaccent,tcaron,tbar"
  k="14" />
    <hkern g1="eth"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="eth"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="eth"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="eth"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="eth"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="14" />
    <hkern g1="eth"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="eth"
  g2="quotedbl,quotesingle"
  k="33" />
    <hkern g1="eth"
  g2="quoteright,quotedblright"
  k="39" />
    <hkern g1="eth"
  g2="quoteleft,quotedblleft"
  k="45" />
    <hkern g1="eth"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="eth"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="23" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="72" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="115" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quotedbl,quotesingle"
  k="59" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteright,quotedblright"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteleft,quotedblleft"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="V.sc"
  k="63" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="slash"
  k="-10" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="18" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="space"
  k="55" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="bracketright"
  k="49" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="backslash"
  k="121" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="trademark"
  k="82" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="braceright"
  k="94" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="asterisk"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="parenright"
  k="72" />
    <hkern g1="B.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="18" />
    <hkern g1="B.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="B.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="55" />
    <hkern g1="B.sc"
  g2="AE.sc,AEacute.sc"
  k="16" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="AE.sc,AEacute.sc"
  k="10" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="braceright"
  k="27" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="asterisk"
  k="-12" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="parenright"
  k="29" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="18" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="66" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="AE.sc,AEacute.sc"
  k="53" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="V.sc"
  k="25" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="X.sc"
  k="37" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="bracketright"
  k="57" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="backslash"
  k="61" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="trademark"
  k="39" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="braceright"
  k="76" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="parenright"
  k="78" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="14" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="20" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="F.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="F.sc"
  g2="AE.sc,AEacute.sc"
  k="123" />
    <hkern g1="F.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="59" />
    <hkern g1="F.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="8" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="20" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="61" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="33" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="V.sc"
  k="23" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="X.sc"
  k="23" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="bracketright"
  k="49" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="backslash"
  k="55" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="trademark"
  k="33" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="braceright"
  k="68" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="parenright"
  k="70" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="14" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="slash"
  k="-39" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="80" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="39" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="12" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="space"
  k="20" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="guillemotright,guilsinglright"
  k="-12" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="86" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="96" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="127" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quotedbl,quotesingle"
  k="100" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="94" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="94" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="V.sc"
  k="94" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="14" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="space"
  k="53" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="bracketright"
  k="51" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="backslash"
  k="135" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="trademark"
  k="109" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="braceright"
  k="98" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="asterisk"
  k="98" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="parenright"
  k="80" />
    <hkern g1="M.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="16" />
    <hkern g1="M.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="M.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="41" />
    <hkern g1="M.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="8" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="23" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="72" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="AE.sc,AEacute.sc"
  k="59" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="X.sc"
  k="41" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="M.sc"
  k="8" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="bracketright"
  k="61" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="backslash"
  k="63" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="trademark"
  k="41" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="braceright"
  k="80" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="parenright"
  k="82" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="18" />
    <hkern g1="P.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="55" />
    <hkern g1="P.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="25" />
    <hkern g1="P.sc"
  g2="AE.sc,AEacute.sc"
  k="125" />
    <hkern g1="P.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="80" />
    <hkern g1="P.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="8" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="V.sc"
  k="16" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="bracketright"
  k="37" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="backslash"
  k="35" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="trademark"
  k="23" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="braceright"
  k="55" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="parenright"
  k="49" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="41" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="10" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="V.sc"
  k="18" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="X.sc"
  k="10" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="bracketright"
  k="39" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="backslash"
  k="41" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="trademark"
  k="33" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="braceright"
  k="57" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="parenright"
  k="57" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="72" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="AE.sc,AEacute.sc"
  k="123" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="slash"
  k="55" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotleft,guilsinglleft"
  k="68" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="hyphen,endash,emdash"
  k="61" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="M.sc"
  k="16" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="space"
  k="53" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="asterisk"
  k="-27" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="AE.sc,AEacute.sc"
  k="43" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="X.sc"
  k="8" />
    <hkern g1="V.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="63" />
    <hkern g1="V.sc"
  g2="AE.sc,AEacute.sc"
  k="129" />
    <hkern g1="V.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="V.sc"
  g2="guillemotleft,guilsinglleft"
  k="49" />
    <hkern g1="V.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="V.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="V.sc"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="AE.sc,AEacute.sc"
  k="123" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="slash"
  k="63" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="guillemotleft,guilsinglleft"
  k="47" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="20" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="8" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="hyphen,endash,emdash"
  k="25" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="M.sc"
  k="20" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="space"
  k="55" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="asterisk"
  k="-16" />
    <hkern g1="X.sc"
  g2="guillemotleft,guilsinglleft"
  k="47" />
    <hkern g1="X.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="41" />
    <hkern g1="X.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="X.sc"
  g2="hyphen,endash,emdash"
  k="35" />
    <hkern g1="X.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="8" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="115" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="AE.sc,AEacute.sc"
  k="176" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="121" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="slash"
  k="109" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotleft,guilsinglleft"
  k="121" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="74" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="colon,semicolon"
  k="59" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="hyphen,endash,emdash"
  k="94" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="M.sc"
  k="41" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="space"
  k="74" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="bracketright"
  k="-20" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="braceright"
  k="-25" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="asterisk"
  k="-80" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="parenright"
  k="-23" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotright,guilsinglright"
  k="35" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="ampersand.sc"
  k="25" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="Thorn.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="Thorn.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="16" />
    <hkern g1="Thorn.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="Thorn.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="72" />
    <hkern g1="Thorn.sc"
  g2="AE.sc,AEacute.sc"
  k="61" />
    <hkern g1="Thorn.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="27" />
    <hkern g1="Thorn.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="16" />
    <hkern g1="seven"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="109" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="23" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="88" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="80" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="AE,AEacute"
  k="59" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="V"
  k="23" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="X"
  k="55" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="20" />
    <hkern g1="questiondown.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="25" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="31" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="AE,AEacute"
  k="27" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="45" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="66" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="113" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="39" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="AE,AEacute"
  k="96" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="V"
  k="51" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="X"
  k="94" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="M"
  k="20" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="59" />
    <hkern g1="three.oldstyle"
  g2="quotedbl,quotesingle"
  k="39" />
    <hkern g1="four.oldstyle"
  g2="quotedbl,quotesingle"
  k="39" />
    <hkern g1="seven.oldstyle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="53" />
    <hkern g1="nine.oldstyle"
  g2="quotedbl,quotesingle"
  k="41" />
    <hkern g1="longs"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="-61" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="31" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="29" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="14" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="v"
  k="33" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="quotedbl,quotesingle"
  k="18" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="quoteleft,quotedblleft"
  k="20" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="space"
  k="27" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="backslash"
  k="20" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="trademark"
  k="16" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="braceright"
  k="35" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="asterisk"
  k="18" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="parenright"
  k="29" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="t,tcommaaccent,tcaron,tbar"
  k="8" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quotedbl,quotesingle"
  k="25" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteright,quotedblright"
  k="29" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteleft,quotedblleft"
  k="35" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="bracketright"
  k="70" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="backslash"
  k="102" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="trademark"
  k="59" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="braceright"
  k="96" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="asterisk"
  k="31" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="parenright"
  k="90" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="18" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="55" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="slash"
  k="49" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="guillemotleft,guilsinglleft"
  k="20" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="space"
  k="49" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="bracketright"
  k="27" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="braceright"
  k="27" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="parenright"
  k="25" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="bracketright"
  k="43" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="backslash"
  k="45" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="trademark"
  k="33" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="braceright"
  k="55" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="parenright"
  k="51" />
    <hkern g1="parenleft.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-37" />
    <hkern g1="parenleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-31" />
    <hkern g1="parenleft.case"
  g2="AE,AEacute"
  k="-70" />
    <hkern g1="parenleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="96" />
    <hkern g1="parenleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="47" />
    <hkern g1="bracketleft.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-25" />
    <hkern g1="bracketleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-27" />
    <hkern g1="bracketleft.case"
  g2="AE,AEacute"
  k="-57" />
    <hkern g1="bracketleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="61" />
    <hkern g1="bracketleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="39" />
    <hkern g1="braceleft.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-29" />
    <hkern g1="braceleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-31" />
    <hkern g1="braceleft.case"
  g2="AE,AEacute"
  k="-63" />
    <hkern g1="braceleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="102" />
    <hkern g1="braceleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="55" />
  </font>
</defs></svg>
