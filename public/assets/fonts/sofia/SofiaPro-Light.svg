<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Mon Dec 14 13:22:27 2020
 By <PERSON><PERSON><PERSON>,,,
Copyright (c) Olivier <PERSON> - Mostardesign Studio, 2012. All rights reserved.
</metadata>
<defs>
<font id="SofiaPro-Light" horiz-adv-x="1142" >
  <font-face 
    font-family="Sofia Pro Light"
    font-weight="300"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 0 0 0 0 0 0 0 0"
    ascent="1548"
    descent="-500"
    x-height="956"
    cap-height="1411"
    bbox="-348 -528 2290 2222"
    underline-thickness="102"
    underline-position="-307"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="1386" 
d="M1292 1475h103v-129h-103q-128 0 -168 -95q-28 -64 -28 -184v-111h235v-122h-235v-834h-138v834h-575v-834h-137v834h-187v122h187v111q0 408 334 408h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h575v111q0 408 334 408z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1222" 
d="M926 0v834h-543v-834h-137v834h-187v122h187v111q0 408 334 408h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h678v-956h-135zM922 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1196" 
d="M580 1475h450v-1475h-135v1346h-315q-128 0 -168 -95q-29 -66 -29 -184v-111h235v-122h-235v-834h-137v834h-187v122h187v111q0 408 334 408z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="1935" 
d="M1638 0v834h-542v-834h-138v834h-575v-834h-137v834h-187v122h187v111q0 408 334 408h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h575v111q0 408 334 408h103v-129h-103q-128 0 -168 -95q-28 -64 -28 -184v-111h678v-956h-136zM1634 1241q-31 29 -31 72t31 69
q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="1908" 
d="M1743 0h-135v1346h-316q-128 0 -168 -95q-28 -64 -28 -184v-111h235v-122h-235v-834h-138v834h-575v-834h-137v834h-187v122h187v111q0 408 334 408h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h575v111q0 408 334 408h451v-1475z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="1937" 
d="M1442 -463h-103v129h103q128 0 168 94q28 64 28 185v889h-542v-834h-138v834h-575v-834h-137v834h-187v122h187v111q0 408 334 408h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h575v111q0 408 334 408h103v-129h-103q-128 0 -168 -95q-28 -64 -28 -184v-111
h680v-1011q0 -408 -334 -408zM1634 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="1224" 
d="M729 -463h-102v129h102q128 0 168 94q29 66 29 185v889h-543v-834h-137v834h-187v122h187v111q0 408 334 408h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h680v-1011q0 -408 -334 -408zM922 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69
t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="544" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="532" 
d="M186 10q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31zM199 1411h135l-12 -1032h-111z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="667" 
d="M162 856v555h108v-555h-108zM397 856v555h109v-555h-109z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1343" 
d="M1139 1411l-101 -360h230l-35 -127h-229l-121 -439h229l-35 -127h-229l-98 -358h-127l98 358h-295l-98 -358h-125l98 358h-225l35 127h225l119 439h-224l33 127h225l101 360h127l-101 -360h295l101 360h127zM877 924h-295l-121 -439h295z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1134" 
d="M625 -16v-234h-103v232q-185 12 -303 139q-117 126 -117 285h136q0 -113 90 -203q88 -88 231 -88q142 0 234 66t112 165q18 117 -54.5 192t-213.5 107l-191 43q-331 73 -331 354q0 156 121 265t286 118v195h103v-197q165 -21 270 -129q104 -107 104 -270h-135
q0 120 -86 194t-221 74q-120 0 -215 -71q-92 -71 -92 -179q0 -170 225 -223l193 -43q191 -44 293 -155q105 -107 77 -299q-26 -133 -135.5 -227t-277.5 -111z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1339" 
d="M956 1378h97l-674 -1378h-96zM627 1104q0 -115 -80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q80 -80 80 -192zM510 1104q0 64 -46.5 111t-111.5 47q-66 0 -110.5 -46t-44.5 -112t44.5 -111t110.5 -45t112 45
t46 111zM1262 252q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q80 -80 80 -192zM1145 252q0 64 -46.5 111t-111.5 47q-66 0 -111 -46t-45 -112t45 -111t111 -45t112 45t46 111z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1359" 
d="M582 870h319v181h133v-183h189v-120h-189v-318q0 -164 49.5 -242.5t176.5 -78.5v-129q-116 0 -199 51.5t-113 138.5q-47 -85 -151 -137.5t-222 -52.5q-200 0 -329 129q-129 132 -129 321q0 121 64 225t173 164q-123 102 -123 264q0 141 103 244q103 100 258 100
q103 0 188 -47.5t133 -128.5l-114 -71q-63 112 -207 112q-98 0 -161.5 -59t-63.5 -150t61 -152t154 -61zM901 416v332h-323q-137 0 -232 -95q-94 -94 -94 -223q0 -135 90 -225t233 -90q141 0 230 86q93 87 96 215z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="432" 
d="M162 856v555h108v-555h-108z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="739" 
d="M655 -342v-135q-248 0 -397 254q-147 252 -147 698q0 449 147 701q149 251 397 251v-135q-185 0 -299 -217q-110 -213 -110 -600q0 -384 110 -600q114 -217 299 -217z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="739" 
d="M84 -477v135q186 0 297 217q113 216 113 600q0 387 -113 600q-111 217 -297 217v135q249 0 395 -251q150 -252 150 -701q0 -446 -150 -698q-146 -254 -395 -254z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="997" 
d="M504 948l-172 -250l-115 86l189 232l-289 82q6 22 24.5 75.5t20.5 61.5l278 -107l-8 146l-8 153h149l-18 -299l283 107l43 -137l-289 -78l188 -238l-114 -82z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1069" 
d="M117 604h352v352h129v-352h354v-129h-354v-354h-129v354h-352v129z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="499" 
d="M141 -270l-94 28q135 190 129 432h158q-11 -156 -65 -277.5t-128 -182.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="993" 
d="M147 414v129h699v-129h-699z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="520" 
d="M180 10q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="968" 
d="M756 1411h149l-690 -1411h-149z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1249" 
d="M139 678q0 303 97 493q112 207 389 207q104 3 186.5 -29t137 -91.5t88.5 -136t48 -169.5q23 -131 23 -274q0 -161 -22 -278t-74 -214q-52 -101 -150 -153q-100 -53 -237 -53q-138 0 -238 53q-72 43 -121 107.5t-75.5 150t-38 177t-13.5 210.5zM274 678q0 -261 78 -414
q76 -149 273 -149q203 0 280 166q68 151 68 397q0 261 -78 414q-77 151 -270 151q-187 0 -269 -144t-82 -421z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="659" 
d="M453 0h-136v1186l-231 -80v123l367 133v-1362z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1075" 
d="M125 0v47l569 690q111 132 111 252q0 114 -80 184t-203 70q-112 0 -192.5 -60.5t-93.5 -158.5l-125 33q24 147 139 233q113 88 272 88q178 0 297 -110q121 -109 121 -279q0 -179 -147 -348l-428 -506h577v-135h-817z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1095" 
d="M274 1120l-108 68q56 95 159.5 147t223.5 45q188 -15 282 -125q97 -109 82 -262q-6 -82 -58.5 -153.5t-133.5 -106.5q116 -27 186 -141q71 -115 62 -236q-15 -168 -158 -278q-144 -108 -330 -96q-130 10 -234 78t-151 173l123 56q31 -70 105.5 -117.5t167.5 -54.5h-3
q131 -9 232 67t110 193q3 116 -88 201q-89 86 -217 86h-131v129h131q104 -3 174 61q73 67 76 160q5 98 -56.5 160t-178.5 69q-85 5 -158.5 -29t-108.5 -94z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1200" 
d="M1112 291h-205v-291h-135v291h-713l748 1071h100v-946h205v-125zM772 1092l-465 -676h465v676z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1148" 
d="M98 266l117 84q41 -100 135 -164q100 -65 205 -65q157 0 249.5 85.5t92.5 221.5q0 137 -94 213q-96 78 -213 78q-92 0 -176 -23q-65 -19 -199 -84l-53 72l119 678h667v-137h-551l-82 -451q173 84 275 84q188 0 317 -117q131 -113 131 -313q0 -206 -131 -328
q-132 -120 -352 -120q-143 0 -268 79q-127 80 -189 207z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1138" 
d="M999 1149l-118 -51q-65 136 -242 145q-184 6 -266 -153q-80 -157 -74 -400q21 55 73.5 95t117.5 58t133 15q182 -9 301 -143q119 -131 110 -314q-9 -185 -137 -307q-126 -123 -309 -114q-179 8 -293 130t-131 328q-15 311 22 533q30 184 154 299q126 114 305 108
q130 -6 216 -58t138 -171zM301 432q-3 -130 82 -221q88 -91 211 -94q127 -3 211 78q88 82 94 213q3 131 -76 219q-82 91 -205 94q-126 3 -221 -80q-93 -84 -96 -209z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1060" 
d="M121 1362h903l-750 -1362h-159l678 1227h-672v135z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1157" 
d="M229 1044q0 142 101 236q101 98 248 98q150 0 251 -98q103 -95 103 -236q0 -155 -125 -253q106 -57 168.5 -158t62.5 -219q0 -183 -133 -310q-130 -124 -327 -124q-198 0 -328 124q-131 125 -131 310q0 118 61.5 219t167.5 158q-119 94 -119 253zM254 414q0 -124 92 -213
q95 -86 232 -86q136 0 231 86q94 88 94 213q0 127 -94 215q-96 90 -231 90h-15q-130 -3 -221 -94q-88 -88 -88 -211zM365 1044q0 -82 58 -141.5t144 -60.5h19q90 1 150.5 60t60.5 142q0 85 -62.5 142t-156.5 57q-91 0 -152 -57t-61 -142z" />
    <glyph glyph-name="nine" unicode="9" 
d="M154 207l118 51q65 -133 254 -145q185 -6 265 151q81 156 75 402q-31 -83 -124.5 -128t-198.5 -40q-181 9 -301 141q-120 135 -111 315q9 185 135 308q129 123 312 114q179 -8 292.5 -130t130.5 -328q15 -311 -22 -533q-30 -185 -156 -299q-124 -115 -303 -109
q-271 12 -366 230zM864 924q3 131 -84 221q-85 91 -209 94t-213 -80q-86 -83 -92 -211q-3 -131 76 -219q82 -91 205 -94q129 -3 219 78q95 83 98 211z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="520" 
d="M180 10q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31zM180 795q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="567" 
d="M217 795q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31zM182 -270l-94 28q135 190 129 432h158q-11 -156 -65 -277.5t-128 -182.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1288" 
d="M1143 227v-145l-1043 399v148l1043 399v-147l-885 -326z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1255" 
d="M158 659v129h940v-129h-940zM158 240v129h940v-129h-940z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1288" 
d="M1188 629v-148l-1043 -399v145l885 328l-885 326v147z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="997" 
d="M231 1030h-135q0 176 115 285q116 110 285 110q170 0 292 -110q123 -108 123 -285q0 -138 -81.5 -241.5t-211.5 -141.5q-96 -28 -136 -67t-40 -121v-80h-135v80q0 130 67 203.5t204 113.5q87 27 142.5 95.5t55.5 158.5q0 115 -84 187q-82 73 -196 73q-112 0 -188.5 -72.5
t-76.5 -187.5zM295 10q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="2021" 
d="M1264 256v270v2q0 117 -84 201q-82 82 -199 82q-119 0 -201 -82t-82 -201q0 -118 82 -200t201 -82q111 0 197 86v-146q-100 -65 -197 -65q-170 0 -289 119t-119 288q0 168 119 287q121 121 289 121q164 0 283 -113t125 -276v-305q0 -152 163 -152q72 0 127 45.5t82 122.5
q49 140 49 295q0 320 -239 534q-239 216 -570 216q-322 0 -546 -230t-224 -557q0 -324 222 -551q224 -227 548 -227h37v-125h-37q-374 0 -636 262q-259 262 -259 641q0 377 259 645q260 266 636 266q183 4 355.5 -64t301 -184t205.5 -281t74 -345q0 -203 -84 -381
q-42 -93 -121 -148t-180 -55q-132 0 -210 70t-78 217z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1306" 
d="M326 371l-142 -371h-145l543 1411h143l543 -1411h-146l-143 371h-653zM375 498h557l-279 725z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1271" 
d="M1085 1061q0 -245 -172 -309q106 -18 177 -127t71 -230q0 -168 -106 -280q-109 -115 -289 -115h-561v1411h514q170 0 268 -94t98 -256zM342 1278v-467h393q101 0 158 71t57 179q0 103 -65.5 160t-179.5 57h-363zM756 684h-414v-549h422q118 0 188 76q74 77 74 184
q0 104 -78 197q-77 92 -192 92z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1359" 
d="M1204 287l94 -97q-204 -210 -503 -210q-297 0 -490 208q-194 212 -194 514q0 308 192 515q192 210 492 210q297 0 501 -210l-94 -95q-170 170 -407 170q-240 0 -396 -172q-153 -170 -153 -418t153 -417q156 -170 396 -170t409 172z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1478" 
d="M735 0h-530v1411h530q291 0 457 -199q168 -198 168 -510q0 -310 -168 -505q-167 -197 -457 -197zM735 1276h-395v-1141h395q237 0 363 156q127 157 127 411t-127 414t-363 160z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1144" 
d="M205 0v1411h805v-135h-670v-516h586v-135h-586v-490h692v-135h-827z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1064" 
d="M205 0v1411h792v-135h-657v-516h522v-135h-522v-625h-135z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1548" 
d="M911 565v127h564q0 -348 -171.5 -530t-484.5 -182q-300 0 -504 210t-204 512q0 305 204 515t504 210q255 0 451 -163l-96 -97q-152 125 -355 125q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q226 0 359 119.5t145 330.5h-412z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1454" 
d="M1114 1411h135v-1411h-135v637h-774v-637h-135v1411h135v-647h774v647z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="544" 
d="M205 1411h135v-1411h-135v1411z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="544" 
d="M-207 -512l12 133q41 -14 115 -14q143 0 214 84t71 229v1491h135v-1491q0 -197 -102 -323q-101 -125 -318 -125q-52 0 -127 16z" />
    <glyph glyph-name="K" unicode="K" 
d="M1032 1411l-549 -706l668 -705h-182l-629 664v-664h-135v1411h135v-686l532 686h160z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="997" 
d="M205 0v1411h135v-1276h598v-135h-733z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1935" 
d="M432 1411l537 -1100l534 1100h135l172 -1411h-133l-143 1169l-498 -1015h-135l-500 1015l-143 -1169h-133l172 1411h135z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1402" 
d="M1198 1411v-1411h-139l-719 1182v-1182h-135v1411h145l713 -1173v1173h135z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1230" 
d="M205 0v1411h555q201 0 299 -125t98 -307q0 -180 -98 -305q-99 -123 -299 -123h-418v-551h-137zM760 1276h-418v-590h408q134 0 203 81t69 212q0 130 -68 213.5t-194 83.5z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1640" 
d="M1182 78l153 -238l-114 -73l-162 253q-116 -40 -240 -40q-300 0 -504 210t-204 512q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -195 -93.5 -360.5t-254.5 -263.5zM823 393l113 74l172 -277q134 78 210.5 214.5t76.5 297.5q0 246 -166 418t-410 172
q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q94 0 164 20z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1245" 
d="M745 551l390 -551h-166l-389 551h-238v-551h-137v1411h524q201 0 295 -125q96 -128 96 -307q0 -177 -92 -301q-89 -121 -283 -127zM729 1276h-387v-590h377q133 0 199.5 80.5t66.5 212.5q0 131 -65.5 214t-190.5 83z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1163" 
d="M117 406h135q0 -113 90 -203q88 -88 231 -88q142 0 234.5 66t112.5 165q18 117 -55 192t-214 107l-190 43q-332 74 -332 354q0 166 133 275q131 110 309 110q192 0 316 -112q127 -112 127 -291h-135q0 120 -86.5 194t-221.5 74q-120 0 -215 -71q-92 -71 -92 -179
q0 -170 225 -223l193 -43q191 -44 293 -155q105 -108 78 -299q-27 -139 -152 -240q-126 -102 -328 -102q-199 0 -327 131q-129 132 -129 295z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1159" 
d="M59 1276v135h1041v-135h-453v-1276h-135v1276h-453z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1128" 
d="M70 1411h139l354 -1190l355 1190h139l-422 -1411h-141z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1859" 
d="M1274 213l395 1198h143l-477 -1411h-129l-276 946l-277 -946h-129l-477 1411h143l396 -1198l293 1012h102z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1210" 
d="M1124 1411l-438 -651l496 -760h-166l-412 645l-411 -645h-164l493 760l-438 651h166l354 -534l357 534h163z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1259" 
d="M1235 1411l-537 -756v-655h-139v655l-530 756h161l439 -608l442 608h164z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1146" 
d="M86 0v135l797 1139h-768v137h934v-135l-799 -1139h799v-137h-963z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="612" 
d="M518 -463h-336v1874h336v-137h-199v-1598h199v-139z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="968" 
d="M903 0h-149l-691 1411h150z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="612" 
d="M94 1411h336v-1874h-336v139h199v1598h-199v137z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1456" 
d="M147 -250v129h1162v-129h-1162z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="937" 
d="M575 1137h-139l-172 274h168z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1253" 
d="M303 0h-137v1475h137v-676q50 84 148.5 131t212.5 47q211 0 348 -137q141 -135 141 -359t-141 -362q-139 -139 -348 -139q-112 0 -211.5 49t-149.5 135v-164zM1016 481q0 162 -101 260q-102 99 -251 99q-138 0 -252 -97q-109 -95 -109 -262q0 -168 103 -268
q107 -96 258 -96q147 0 249 102q103 106 103 262z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1032" 
d="M848 207l98 -96q-140 -131 -346 -131q-209 0 -354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q203 0 344 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1253" 
d="M950 1475h137v-1475h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v676zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM240 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="673" 
d="M580 1475h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-111h235v-122h-235v-834h-137v834h-187v122h187v111q0 408 334 408z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1251" 
d="M238 475q0 -163 100 -260q101 -98 252 -98q140 0 250 96q110 94 110 262q0 172 -104 266q-102 99 -256 99q-150 0 -252 -105q-100 -100 -100 -260zM164 -240l117 68q102 -170 294 -170q185 0 280.5 123t94.5 393q-43 -88 -144.5 -141t-215.5 -53q-209 0 -350 135
q-140 140 -140 360q0 223 140 363q142 139 350 139q112 0 211 -49t149 -135v163h137v-804q0 -629 -512 -629q-128 0 -235.5 60.5t-175.5 176.5z" />
    <glyph glyph-name="h" unicode="h" 
d="M303 522v-522h-137v1475h137v-676q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="466" 
d="M301 0h-135v956h135v-956zM162 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="468" 
d="M162 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM-31 -463h-102v129h102q128 0 168 94q29 66 29 185v1011h137v-1011q0 -408 -334 -408z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="993" 
d="M795 0l-494 522v-522h-135v1475h135v-897l379 378h192l-409 -409l518 -547h-186z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="466" 
d="M166 1475h135v-1475h-135v1475z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1828" 
d="M989 522v-522h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240v-522h-137v956h137v-157q42 85 139 131q100 47 179 47q245 0 331 -209q102 209 355 209q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80
q-94 -76 -94 -240z" />
    <glyph glyph-name="n" unicode="n" 
d="M303 522v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1200" 
d="M100 481q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM340 217q108 -102 260 -102q153 0 258 102q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101
q-104 -98 -104 -260q0 -163 104 -264z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1253" 
d="M303 -463h-137v1419h137v-165q50 86 150 136t211 50q210 0 348 -141q141 -138 141 -363q0 -223 -141 -358q-138 -135 -348 -135q-114 0 -212.5 46t-148.5 130v-619zM1016 473q0 156 -103 262q-102 105 -249 105q-153 0 -258 -99q-103 -100 -103 -268q0 -167 109 -262
q110 -94 252 -94q147 0 251 96q101 98 101 260z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1253" 
d="M950 956h137v-1419h-137v619q-50 -84 -148 -130t-212 -46q-209 0 -350 135q-140 137 -140 358q0 223 140 363q141 141 350 141q111 0 210.5 -50t149.5 -136v165zM238 473q0 -163 100 -260q104 -96 252 -96q143 0 250 94q110 94 110 262q0 170 -104 268q-102 99 -256 99
q-150 0 -252 -105q-100 -103 -100 -262z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="772" 
d="M733 940l-16 -133q-62 33 -139 33q-135 0 -207 -140q-68 -139 -68 -344v-356h-137v956h137v-217q68 238 275 238q45 0 93 -12t62 -25z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="868" 
d="M90 274h121q43 -165 225 -165q92 0 147.5 41.5t55.5 109.5q0 114 -137 148l-133 30q-96 21 -174 80q-83 71 -68 232q13 99 102 163t209 64q122 0 202.5 -63.5t98.5 -184.5h-133q-12 54 -58 85.5t-110 31.5q-69 0 -120 -33t-58 -84q-21 -120 141 -160l131 -30
q242 -58 242 -279q0 -125 -100 -203q-103 -77 -250 -77q-128 0 -229 79q-99 82 -105 215z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="630" 
d="M367 834v-834h-138v834h-172v122h172v359h138v-359h202v-122h-202z" />
    <glyph glyph-name="u" unicode="u" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1034" 
d="M444 0l-395 956h144l325 -796l324 796h145l-397 -956h2h-148z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1646" 
d="M1591 956l-358 -956h-115l-297 768l-286 -768h-115l-369 956h146l276 -739l275 739h147l276 -739l275 739h145z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="989" 
d="M412 526l-312 430h168l226 -315l227 315h168l-311 -430l380 -526h-165l-299 412l-299 -412h-164z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1034" 
d="M987 956l-594 -1419h-141l190 465l-393 954h144l323 -778l326 778h145z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1013" 
d="M119 956h794v-122l-645 -699h645v-135h-829v125l635 696h-600v135z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="808" 
d="M350 281v-349q0 -116 76.5 -193t187.5 -77h111v-125h-111q-167 0 -284 111q-115 109 -115 284v349q0 56 -39 98.5t-92 42.5v117q53 0 92 42.5t39 98.5v352q-3 176 115 285q116 110 284 110h111v-124h-111q-111 0 -187.5 -77.5t-76.5 -193.5v-352q0 -64 -33.5 -121
t-87.5 -80q50 -19 85.5 -77.5t35.5 -120.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="466" 
d="M182 1620h103v-1870h-103v1870z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="808" 
d="M459 -68v349q0 62 35.5 120.5t85.5 77.5q-54 23 -87.5 80t-33.5 121v352q0 116 -76.5 193.5t-187.5 77.5h-111v124h111q168 0 284 -110q118 -109 115 -285v-352q0 -56 39 -98.5t92 -42.5v-117q-53 0 -92 -42.5t-39 -98.5v-349q0 -176 -117 -284q-114 -111 -282 -111h-111
v125h111q111 0 187.5 77t76.5 193z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="479" 
d="M319 926q32 -31 32 -76t-32 -76t-79.5 -31t-79.5 31t-32 76t32 76t79.5 31t79.5 -31zM184 557h111l12 -1020h-135z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1071" 
d="M524 1194h103v-217q191 -9 323 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92l98 -96q-134 -122 -325 -131v-230h-103v236q-182 27 -301 161q-117 135 -117 334q0 198 117 330q121 133 301 160v223
z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1308" 
d="M135 688h166v354q0 177 123 285q123 111 293 111q168 0 282 -111q117 -108 117 -285h-135q0 115 -76 188t-188 73q-114 0 -197 -74q-84 -72 -84 -187v-354h332v-117h-332v-436h584q19 0 31 14t12 37v121h135v-121q0 -79 -52 -132.5t-126 -53.5h-920v135h201v436h-166v117
z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1245" 
d="M430 899q-78 -78 -78 -190t78 -193q81 -78 193 -78t190 78q80 80 80 193t-80 190q-77 80 -190 80t-193 -80zM383 393l-147 -149l-78 78l147 149q-78 107 -78 238q0 134 80 237l-149 152l78 78l149 -152q103 80 238 80q131 0 237 -80l150 152l77 -78l-149 -152
q80 -109 80 -237q0 -135 -80 -238l149 -149l-77 -78l-150 149q-103 -80 -237 -80t-240 80z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1341" 
d="M598 557h-330v96h330l-530 758h161l439 -608l442 608h164l-537 -758h332v-96h-332v-203h332v-96h-332v-258h-139v258h-330v96h330v203z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="475" 
d="M289 848h-103v772h103v-772zM186 520h103v-770h-103v770z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1058" 
d="M270 1032v-272q0 -115 76 -186.5t189 -71.5q118 0 182 55v-151q-85 -39 -182 -39q-168 0 -285 108q-115 109 -115 285v272q0 176 115 285q116 110 285 110q130 0 231.5 -67.5t142.5 -183.5l-131 -37q-31 72 -96 112.5t-147 40.5q-112 0 -188.5 -72.5t-76.5 -187.5z
M788 240v272q0 115 -75.5 186.5t-188.5 71.5q-118 0 -182 -55v151q85 39 182 39q169 0 283 -108q117 -108 117 -285v-272q0 -177 -117 -285q-114 -111 -283 -111q-130 0 -231.5 68t-142.5 184l131 37q31 -72 96 -112.5t147 -40.5q112 0 188 72.5t76 187.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="1062" 
d="M276 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM639 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1669" 
d="M326 190q-207 213 -207 512t207 512q210 213 508 213q297 0 507 -213q209 -212 209 -512t-209 -512q-207 -210 -507 -210q-301 0 -508 210zM1112 449q-117 -123 -270 -123q-154 0 -260 106q-105 108 -105 260t105 260q107 107 260 107q154 0 268 -123l-76 -76
q-88 88 -192 88q-107 0 -180 -76q-74 -74 -74 -180q0 -105 74 -182q74 -74 180 -74q104 0 196 92zM408 1133q-175 -178 -175 -431q0 -252 175 -430q176 -176 426 -176t426 176q174 177 174 430q0 254 -174 431q-175 178 -426 178t-426 -178z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="974" 
d="M698 1356h101v-625h-101v90q-82 -104 -225 -104q-136 0 -229 90q-92 92 -92 237q0 146 90 236q93 90 231 90q161 0 225 -98v84zM254 1044q0 -97 63 -161t156 -64q96 0 160.5 61.5t64.5 163.5q0 101 -66.5 163.5t-158.5 62.5q-95 0 -157 -62.5t-62 -163.5zM805 516h-625
v84h625v-84z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="927" 
d="M385 53l-283 424l283 424h127l-283 -424l283 -424h-127zM692 53l-282 424l282 424h127l-282 -424l282 -424h-127z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1318" 
d="M158 721h1003v-463h-129v334h-874v129z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1669" 
d="M1118 336h-135l-186 260h-76v-260h-113v717h273q105 0 165 -66t60 -164q0 -83 -47 -146.5t-129 -76.5zM721 940v-233h156q73 -3 103.5 56.5t1 119.5t-100.5 57h-160zM326 190q-207 213 -207 512t207 512q210 213 508 213q297 0 507 -213q209 -212 209 -512t-209 -512
q-207 -210 -507 -210q-301 0 -508 210zM408 1133q-175 -178 -175 -431q0 -252 175 -430q176 -176 426 -176t426 176q174 177 174 430q0 254 -174 431q-175 178 -426 178t-426 -178z" />
    <glyph glyph-name="overscore" unicode="&#xaf;" horiz-adv-x="978" 
d="M246 1241v129h487v-129h-487z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="749" 
d="M193 989q-74 77 -74 182q0 107 74 181q75 75 182 75q108 0 180 -75q76 -73 76 -181q0 -106 -76 -182q-74 -74 -180 -74q-105 0 -182 74zM266 1278q-43 -43 -43 -107t43 -108q44 -43 108 -43t107 43q44 44 44 108t-44 107q-43 44 -107 44t-108 -44z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1118" 
d="M150 690h344v324h129v-324h346v-129h-346v-325h-129v325h-344v129zM969 129v-129h-819v129h819z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="657" 
d="M571 963h-493v49l325 393q60 75 60 133q0 57 -41 93t-103 36q-58 0 -101 -35.5t-50 -91.5l-100 25q13 96 83 153.5t168 57.5q63 2 117.5 -23.5t87 -71.5t44 -102.5t-9.5 -122.5t-75 -125l-219 -260h307v-108z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="665" 
d="M100 1659q33 58 96 90.5t136 28.5q96 -6 159.5 -71.5t61.5 -152.5q-3 -103 -96 -159q59 -20 97 -84t32 -131q-9 -103 -97 -170t-198 -58q-78 6 -142.5 51.5t-95.5 114.5q6 3 25 14.5t37 22t28 14.5q43 -93 156 -108q68 -5 121.5 34.5t54.5 98.5q2 57 -45.5 101t-112.5 44
h-90v105h90q43 0 75 26.5t38 67.5q8 51 -22.5 87t-85.5 42q-90 9 -144 -72q-7 5 -32.5 26.5t-45.5 37.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="937" 
d="M408 1411h167l-172 -274h-139z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1140" 
d="M315 -334h-137v1290h137v-557q0 -134 61 -209t171 -75q127 0 223 78q94 79 94 241v522h137v-956h-137v158q-39 -84 -129 -131t-188 -47q-161 0 -232 86v-400z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1253" 
d="M516 1411h135v-1532h-135v647q-184 0 -313 129t-129 314q0 182 129 311q128 131 313 131zM766 -121v1532h135q113 0 193 -80t80 -192v-162h-136v162q0 57 -40 97t-97 40v-1364q0 -15 -4 -43q-17 -99 -93 -164t-175 -65h-113v120h113q52 0 94.5 36t42.5 83z" />
    <glyph glyph-name="middot" unicode="&#xb7;" horiz-adv-x="520" 
d="M180 463q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="1056" 
d="M616 -182l-100 235h115l80 -192q35 -82 4 -157t-111 -107q-78 -32 -150.5 -1t-107.5 109q-18 47 -18 88l108 -8q2 -21 8 -39q17 -36 51.5 -51t69.5 -2q37 18 52.5 53.5t-1.5 71.5z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="413" 
d="M297 1774v-811h-109v665l-131 -47v105z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="972" 
d="M156 1049q0 147 94 235q96 90 233 90q136 0 232 -90t96 -235q0 -143 -96 -236q-95 -92 -232 -92q-138 0 -233 92q-94 91 -94 236zM713 1049q0 101 -65.5 164t-164.5 63q-98 0 -162.5 -63t-64.5 -164q0 -98 66 -163t161 -65t162.5 65t67.5 163zM801 516h-625v84h625v-84z
" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="927" 
d="M543 901l282 -424l-282 -424h-127l282 424l-282 424h127zM236 901l282 -424l-282 -424h-127l282 424l-282 424h127z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1568" 
d="M1260 268v338l-232 -338h232zM1485 268v-102h-117v-166h-108v166h-418l444 637h82v-535h117zM1085 1378h136l-918 -1398h-135zM340 1370v-811h-109v666l-131 -47v104z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1572" 
d="M1462 0h-493v49l325 393q60 75 60 133q0 57 -41 93.5t-103 36.5q-58 0 -101 -35.5t-50 -91.5l-101 24q13 96 83.5 153.5t168.5 57.5q79 3 142 -38t91.5 -104.5t13 -146t-82.5 -155.5l-219 -260h307v-109zM1063 1378h135l-917 -1398h-136zM346 1370v-811h-108v666
l-132 -47v104z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1658" 
d="M1114 268h232v338zM1454 166v-166h-108v166h-418l444 637h82v-535h117v-102h-117zM137 1255q33 58 96 90.5t136 28.5q96 -6 159.5 -71t61.5 -152q-3 -104 -96 -160q59 -20 97 -84t32 -131q-9 -103 -97 -170q-87 -66 -198 -57q-78 6 -142.5 51.5t-95.5 114.5q11 5 45 25.5
t45 25.5q43 -94 156 -109q68 -5 121.5 35t54.5 99q2 57 -45.5 101t-112.5 44h-90v104h90q43 0 75 27t38 68q7 51 -23 87t-86 42q-89 9 -143 -72q-6 4 -16.5 13t-28 23t-33.5 27zM453 -20h-136l918 1398h135z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="970" 
d="M754 -74h135q0 -176 -117 -284q-114 -111 -283 -111t-292 111q-123 108 -123 284q0 138 81.5 241.5t211.5 141.5q88 26 132 75t44 134v60h135v-60q0 -256 -270 -338q-87 -27 -143 -95.5t-56 -158.5q0 -114 84 -186q83 -74 196 -74q112 0 188.5 72.5t76.5 187.5zM690 946
q32 -31 32 -75.5t-32 -75.5t-80 -31t-80 31t-32 75.5t32 75.5t80 31t80 -31z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1306" 
d="M39 0l543 1411h143l543 -1411h-146l-143 371h-653l-142 -371h-145zM653 1223l-278 -725h557zM723 1546h-139l-172 275h168z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1306" 
d="M326 371l-142 -371h-145l543 1411h143l543 -1411h-146l-143 371h-653zM375 498h557l-279 725zM721 1821h168l-172 -275h-139z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1306" 
d="M326 371l-142 -371h-145l543 1411h143l543 -1411h-146l-143 371h-653zM375 498h557l-279 725zM936 1546h-139l-146 176l-143 -176h-139l194 275h178z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1306" 
d="M326 371l-142 -371h-145l543 1411h143l543 -1411h-146l-143 371h-653zM375 498h557l-279 725zM559 1692q-26 0 -47.5 -25.5t-22.5 -67.5h-104q6 110 54.5 166t119.5 56q50 0 105 -43t81 -43q27 0 48 22.5t22 63.5h105q-6 -109 -54 -162t-121 -53q-49 0 -104 43t-82 43z
" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1306" 
d="M39 0l543 1411h143l543 -1411h-146l-143 371h-653l-142 -371h-145zM653 1223l-278 -725h557zM397 1651q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM760 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69
t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1306" 
d="M39 0l543 1411h143l543 -1411h-146l-143 371h-653l-142 -371h-145zM653 1223l-278 -725h557zM651 1944q85 0 143 -58t58 -143q0 -82 -58.5 -139.5t-142.5 -57.5q-82 0 -139 56.5t-57 140.5q0 85 57 143t139 58zM561 1745q0 -41 26.5 -66.5t65.5 -25.5q38 0 65 27t27 65
q4 56 -44 79.5t-96 0t-44 -79.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1929" 
d="M989 371h-573l-236 -371h-160l891 1411h883v-135h-670v-516h586v-135h-586v-490h693v-135h-828v371zM989 1276l-493 -778h493v778z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1361" 
d="M1206 287l94 -97q-195 -201 -479 -210l49 -119q35 -82 4.5 -157t-110.5 -107q-78 -32 -150.5 -1t-107.5 109q-19 49 -19 88l109 -8q0 -26 8 -39q17 -36 51.5 -51t69.5 -2q37 18 52.5 53.5t-1.5 71.5l-71 168q-261 35 -426 235q-166 202 -166 481q0 308 192 515
q192 210 492 210q297 0 501 -210l-94 -95q-170 170 -407 170q-240 0 -396 -172q-153 -170 -153 -418t153 -417q156 -170 396 -170t409 172z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1144" 
d="M1010 1411v-135h-670v-516h586v-135h-586v-490h692v-135h-827v1411h805zM702 1546h-139l-172 275h168z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1144" 
d="M205 0v1411h805v-135h-670v-516h586v-135h-586v-490h692v-135h-827zM631 1821h168l-172 -275h-140z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1144" 
d="M205 0v1411h805v-135h-670v-516h586v-135h-586v-490h692v-135h-827zM862 1546h-139l-145 176l-144 -176h-139l194 275h179z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1144" 
d="M1010 1411v-135h-670v-516h586v-135h-586v-490h692v-135h-827v1411h805zM322 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM684 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72
q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="544" 
d="M340 0h-135v1411h135v-1411zM340 1546h-139l-172 275h168z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="544" 
d="M346 1821h168l-172 -275h-139zM205 1411h135v-1411h-135v1411z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="544" 
d="M557 1546h-139l-146 176l-143 -176h-139l194 275h178zM205 1411h135v-1411h-135v1411z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="544" 
d="M340 0h-135v1411h135v-1411zM18 1651q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM381 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1488" 
d="M745 0h-530v643h-201v125h201v643h530q291 0 457 -199q168 -198 168 -510q0 -310 -168 -505q-167 -197 -457 -197zM350 1276v-508h352v-125h-352v-508h395q237 0 363 156q127 157 127 411t-127 414t-363 160h-395z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1402" 
d="M1198 1411v-1411h-139l-719 1182v-1182h-135v1411h145l713 -1173v1173h135zM608 1692q-26 0 -47 -25.5t-22 -67.5h-105q6 110 54.5 166t119.5 56q50 0 105 -43t82 -43t47.5 22t21.5 64h105q-6 -109 -53.5 -162t-120.5 -53q-50 0 -105 43t-82 43z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM903 1546
h-139l-172 275h168z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM868 1821h168
l-172 -275h-139z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM1104 1546
h-139l-146 176l-143 -176h-139l194 275h178z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM727 1692
q-26 0 -47.5 -25.5t-22.5 -67.5h-104q6 110 54.5 166t119.5 56q49 0 104 -43t82 -43t48 22.5t22 63.5h104q-6 -109 -53.5 -162t-120.5 -53q-49 0 -104 43t-82 43z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM565 1651
q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM928 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1001" 
d="M139 293l271 270l-271 271l92 90l269 -269l272 271l90 -92l-270 -271l270 -270l-90 -92l-272 270l-269 -268z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1636" 
d="M358 147l-163 -170l-76 76l164 170q-174 201 -174 479q0 305 204 515t504 210q267 0 467 -176l178 183l76 -76l-178 -184q168 -204 168 -472q0 -299 -207 -512q-204 -210 -504 -210q-265 0 -459 167zM244 702q0 -218 133 -383l813 834q-165 139 -373 139
q-241 0 -407 -172t-166 -418zM817 115q243 0 410 170q166 172 166 417q0 212 -129 375l-811 -833q159 -129 364 -129z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262zM764 1546h-139l-172 275h168z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262zM745 1821h168l-172 -275h-139z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262zM973 1546h-139l-146 176l-143 -176h-139l194 275h178z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262zM434 1651q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72
q-31 -26 -74 -26t-74 26zM797 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1259" 
d="M1235 1411l-537 -756v-655h-139v655l-530 756h161l439 -608l442 608h164zM696 1821h168l-172 -275h-139z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1171" 
d="M674 295h-332v-295h-137v1411h137v-262h332q179 0 299 -117q121 -118 121 -307q0 -190 -121 -311q-119 -119 -299 -119zM342 1014v-584h332q121 0 203 82q81 81 81 213q0 131 -81 209q-83 80 -203 80h-332z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1161" 
d="M301 0h-135v1030q0 194 110.5 308t290.5 114h21q156 0 256 -100q104 -101 104 -244q0 -85 -42 -156t-115 -108q117 -26 196 -150q76 -119 76 -250q0 -193 -129 -325q-128 -131 -330 -131q-109 -3 -211 41v129q90 -41 207 -41q143 0 236 96q92 95 92 231q0 130 -94 228
q-93 100 -232 100h-70v123h66q93 0 154 61t61 152q0 90 -63 150t-162 65h-21q-117 0 -191.5 -80t-74.5 -213v-1030z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM713 1137h-140l-172 274h168z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM664 1411h167l-172 -274h-139z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM907 1137h-139l-145 176l-144 -176h-139l195 274h178z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM530 1282q-26 0 -47 -25t-22 -67h-105q6 110 54.5 165.5t119.5 55.5q50 0 105 -43t82 -43t47.5 22t21.5 64h105q-6 -109 -53.5 -162t-120.5 -53q-50 0 -105 43t-82 43z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1253" 
d="M369 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM731 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49
q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97q-150 0 -252 -99q-100 -97 -100 -260z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM623 1534q85 0 142.5 -58t57.5 -143q0 -82 -58 -139t-142 -57q-83 0 -140 56t-57 140t57.5 142.5t139.5 58.5zM532 1335q0 -40 27 -66t66 -26q38 0 65 27t27 65q4 56 -44.5 79.5t-96.5 0t-44 -79.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1992" 
d="M1087 121v-121h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157h137v-118q133 139 349 139q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44
t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5q-214 0 -349 141zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97q-150 0 -252 -99q-100 -97 -100 -260zM1092 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1034" 
d="M647 -18l51 -121q35 -82 4.5 -157t-110.5 -107q-78 -32 -150.5 -1t-107.5 109q-19 49 -19 88l109 -8q1 -3 1.5 -11.5t2 -16t4.5 -11.5q17 -36 51.5 -51t69.5 -2q37 18 52.5 53.5t-1.5 71.5l-72 166q-185 21 -309 157q-123 135 -123 340q0 221 144 359q143 137 356 137
q203 0 344 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92l98 -96q-123 -114 -299 -129z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM913 545q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216h673zM657 1137h-139l-172 274h168z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM240 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216zM637 1411h168l-172 -274h-139z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM240 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216zM860 1137h-139l-146 176l-143 -176h-139l194 274h179z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM913 545q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216h673zM322 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM684 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="466" 
d="M301 0h-135v956h135v-956zM299 1137h-139l-172 274h168z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="466" 
d="M307 1411h168l-172 -274h-139zM166 956h135v-956h-135v956z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="466" 
d="M520 1137h-139l-145 176l-144 -176h-139l194 274h179zM166 956h135v-956h-135v956z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="466" 
d="M301 0h-135v956h135v-956zM-18 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM344 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1202" 
d="M442 1475l107 -123l139 88l62 -72l-142 -88l398 -498q94 -125 94 -301q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362q0 220 142 359q143 137 342 137q62 0 114 -21l-202 252l-181 -114l-51 77l174 111l-164 193h168zM340 217q108 -102 260 -102
q153 0 258 102q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M303 522v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240zM479 1282q-26 0 -47 -25t-22 -67h-105q6 110 54.5 165.5t119.5 55.5q50 0 105 -43t82 -43t47.5 22
t21.5 64h105q-6 -109 -53.5 -162t-120.5 -53q-50 0 -105 43t-82 43z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1200" 
d="M1100 481q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359zM858 217q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264
q108 -102 260 -102q153 0 258 102zM676 1137h-139l-172 274h167z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1200" 
d="M100 481q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM340 217q108 -102 260 -102q153 0 258 102q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101
q-104 -98 -104 -260q0 -163 104 -264zM659 1411h168l-172 -274h-139z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1200" 
d="M100 481q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM340 217q108 -102 260 -102q153 0 258 102q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101
q-104 -98 -104 -260q0 -163 104 -264zM883 1137h-140l-145 176l-143 -176h-140l195 274h178z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1200" 
d="M100 481q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM340 217q108 -102 260 -102q153 0 258 102q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101
q-104 -98 -104 -260q0 -163 104 -264zM506 1282q-27 0 -48 -25t-22 -67h-104q6 110 54.5 165.5t119.5 55.5q49 0 104 -43t82 -43t48 22.5t22 63.5h104q-6 -109 -53.5 -162t-120.5 -53q-49 0 -104 43t-82 43z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1200" 
d="M1100 481q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359zM858 217q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264
q108 -102 260 -102q153 0 258 102zM344 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM707 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1187" 
d="M514 137q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31zM514 795q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31zM1030 606v-129h-872v129h872z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1196" 
d="M281 86l-105 -109l-76 76l105 109q-107 137 -107 319q0 221 144 359q143 137 356 137q176 0 307 -98l103 106l75 -76l-100 -102q115 -136 115 -326q0 -222 -146 -362q-145 -139 -354 -139q-184 0 -317 106zM598 115q153 0 258 102q107 101 107 264q0 134 -76 228
l-510 -525q92 -69 221 -69zM598 842q-153 0 -260 -101q-105 -99 -105 -260q0 -126 68 -221l508 520q-89 62 -211 62z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM653 1137h-139l-172 274h168z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM623 1411h168l-173 -274h-139z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM870 1137h-139l-145 176l-144 -176h-139l195 274h178z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM332 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26
t-73.5 26zM694 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1034" 
d="M987 956l-594 -1419h-141l190 465l-393 954h144l323 -778l326 778h145zM559 1411h168l-172 -274h-139z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1253" 
d="M303 -463h-137v1938h137v-674q50 84 148.5 131t212.5 47q211 0 348 -137q141 -135 141 -359t-141 -362q-139 -139 -348 -139q-112 0 -211.5 49t-149.5 135v-629zM1016 483q0 162 -101 260q-102 99 -251 99q-138 0 -252 -97q-109 -95 -109 -262q0 -168 103 -268
q107 -96 258 -96q147 0 249 102q103 106 103 262z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1034" 
d="M252 -463l190 465l-393 954h144l323 -778l326 778h145l-594 -1419h-141zM268 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM631 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72
q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1306" 
d="M326 371l-142 -371h-145l543 1411h143l543 -1411h-146l-143 371h-653zM375 498h557l-279 725zM410 1696v129h487v-129h-487z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM381 1241v129h487v-129h-487z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1306" 
d="M39 0l543 1411h143l543 -1411h-146l-143 371h-653l-142 -371h-145zM653 1223l-278 -725h557zM399 1866h107q11 -65 49 -103.5t98 -38.5q65 0 103.5 39.5t44.5 102.5h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1253" 
d="M950 956h137v-956h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM371 1411h106q11 -65 49.5 -103t98.5 -38q65 0 103 39t44 102h107q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1306" 
d="M375 498h557l-279 725zM1067 -139l55 139l-143 371h-653l-142 -371h-145l543 1411h143l543 -1411h-56l-51 -182q-11 -39 2.5 -73.5t48.5 -51.5q35 -13 69.5 2t51.5 51q8 13 8 39l109 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-80 32 -111 107t4 157z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1253" 
d="M950 956h137v-956h-49l-37 -182q-18 -91 52 -125q35 -13 69.5 2t51.5 51q8 13 8 39l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-76 30 -104 103.5t-3 160.5l43 139v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137
q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97q-150 0 -252 -99q-100 -97 -100 -260z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1359" 
d="M1204 287l94 -97q-204 -210 -503 -210q-297 0 -490 208q-194 212 -194 514q0 308 192 515q192 210 492 210q297 0 501 -210l-94 -95q-170 170 -407 170q-240 0 -396 -172q-153 -170 -153 -418t153 -417q156 -170 396 -170t409 172zM793 1866h168l-173 -275h-139z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1032" 
d="M848 207l98 -96q-140 -131 -346 -131q-209 0 -354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q203 0 344 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92zM569 1411h168l-172 -274h-139z
" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1359" 
d="M1204 287l94 -97q-204 -210 -503 -210q-297 0 -490 208q-194 212 -194 514q0 308 192 515q192 210 492 210q297 0 501 -210l-94 -95q-170 170 -407 170q-240 0 -396 -172q-153 -170 -153 -418t153 -417q156 -170 396 -170t409 172zM1061 1591h-139l-146 176l-143 -176
h-139l194 275h178z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1032" 
d="M848 207l98 -96q-140 -131 -346 -131q-209 0 -354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q203 0 344 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92zM852 1137h-139l-146 176
l-143 -176h-139l194 274h178z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1359" 
d="M1298 190q-204 -210 -503 -210q-297 0 -490 208q-194 212 -194 514q0 308 192 515q192 210 492 210q297 0 501 -210l-94 -95q-170 170 -407 170q-240 0 -396 -172q-153 -170 -153 -418t153 -417q156 -170 396 -170t409 172zM678 1696q-31 29 -31 72t31 69q31 29 73.5 29
t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1032" 
d="M848 207l98 -96q-140 -131 -346 -131q-209 0 -354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q203 0 344 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92zM512 1241q-31 29 -31 72t31 69
q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1359" 
d="M1204 287l94 -97q-204 -210 -503 -210q-297 0 -490 208q-194 212 -194 514q0 308 192 515q192 210 492 210q297 0 501 -210l-94 -95q-170 170 -407 170q-240 0 -396 -172q-153 -170 -153 -418t153 -417q156 -170 396 -170t409 172zM477 1866h139l146 -176l143 176h139
l-194 -275h-178z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1032" 
d="M848 207l98 -96q-140 -131 -346 -131q-209 0 -354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q203 0 344 -129l-96 -96q-104 90 -248 90q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102q145 0 248 92zM279 1411h139l145 -176
l144 176h139l-195 -274h-178z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1478" 
d="M735 0h-530v1411h530q291 0 457 -199q168 -198 168 -510q0 -310 -168 -505q-167 -197 -457 -197zM735 1276h-395v-1141h395q237 0 363 156q127 157 127 411t-127 414t-363 160zM438 1866h140l145 -176l143 176h140l-195 -275h-178z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1253" 
d="M950 1475h137v-1475h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v676zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97
q-150 0 -252 -99q-100 -97 -100 -260zM1276 1073l-98 27q115 161 88 327h155q3 -233 -145 -354z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1488" 
d="M745 0h-530v643h-201v125h201v643h530q291 0 457 -199q168 -198 168 -510q0 -310 -168 -505q-167 -197 -457 -197zM350 1276v-508h352v-125h-352v-508h395q237 0 363 156q127 157 127 411t-127 414t-363 160h-395z" />
    <glyph glyph-name="dslash" unicode="&#x111;" horiz-adv-x="1253" 
d="M238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97q-150 0 -252 -99q-100 -97 -100 -260zM950 799v379h-323v102h323v195h137v-195h168v-102h-168v-1178h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139
q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1144" 
d="M205 0v1411h805v-135h-670v-516h586v-135h-586v-490h692v-135h-827zM334 1696v129h487v-129h-487z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM240 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216zM332 1241v129h487v-129h-487z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1144" 
d="M1010 1411v-135h-670v-516h586v-135h-586v-490h692v-135h-827v1411h805zM324 1866h106q11 -65 49.5 -103.5t98.5 -38.5q65 0 103 39.5t44 102.5h106q0 -116 -69 -186t-184 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM913 545q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216h673zM322 1411h106q11 -65 49 -103t98 -38q65 0 103.5 39t44.5 102h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -187.5 72t-65.5 184z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1144" 
d="M1010 1411v-135h-670v-516h586v-135h-586v-490h692v-135h-827v1411h805zM545 1696q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM913 545q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216h673zM502 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1144" 
d="M860 -139l78 139h-733v1411h805v-135h-670v-516h586v-135h-586v-490h692v-135l-78 -182q-17 -36 -1 -71.5t53 -53.5q35 -13 69 2t51 51q9 15 9 39l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-83 33 -115 109.5t8 154.5z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" 
d="M684 -182q-14 -36 0.5 -72t50.5 -53q35 -13 69.5 2t51.5 51q8 13 8 39h109q0 -31 -19 -80q-35 -78 -107.5 -109t-150.5 1q-82 34 -116 110q-36 79 10 154l84 131q-27 -12 -90 -12q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135
q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-19 -38 -47 -66q-22 -24 -80.5 -81t-77.5 -78q-90 -103 -131 -207zM240 545h673q-20 136 -109 216.5t-220 80.5q-135 0 -229.5 -81t-114.5 -216z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1144" 
d="M205 0v1411h805v-135h-670v-516h586v-135h-586v-490h692v-135h-827zM328 1866h139l145 -176l144 176h139l-195 -275h-178z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1140" 
d="M584 -20q-207 0 -346 139q-138 138 -138 362t138 359q137 137 346 137q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5zM240 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216zM293 1411h139l146 -176l143 176h139l-194 -274h-179z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1548" 
d="M911 565v127h564q0 -348 -171.5 -530t-484.5 -182q-300 0 -504 210t-204 512q0 305 204 515t504 210q255 0 451 -163l-96 -97q-152 125 -355 125q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q226 0 359 119.5t145 330.5h-412zM1067 1591h-139
l-146 176l-143 -176h-139l194 275h178z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1251" 
d="M238 475q0 -163 100 -260q101 -98 252 -98q140 0 250 96q110 94 110 262q0 172 -104 266q-102 99 -256 99q-150 0 -252 -105q-100 -100 -100 -260zM164 -240l117 68q102 -170 294 -170q377 0 377 494l-2 22q-43 -88 -144.5 -141t-215.5 -53q-209 0 -350 135
q-140 140 -140 360q0 223 140 363q142 139 350 139q112 0 211 -49t149 -135v163h137v-804q0 -629 -512 -629q-128 0 -235.5 60.5t-175.5 176.5zM907 1137h-139l-145 176l-144 -176h-139l195 274h178z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1548" 
d="M1475 692q0 -348 -171.5 -530t-484.5 -182q-300 0 -504 210t-204 512q0 305 204 515t504 210q255 0 451 -163l-96 -97q-152 125 -355 125q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q226 0 359 119.5t145 330.5h-412v127h564zM530 1866h107
q11 -65 49 -103.5t98 -38.5q65 0 103.5 39.5t44.5 102.5h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1251" 
d="M238 475q0 -163 100 -260q101 -98 252 -98q140 0 250 96q110 94 110 262q0 172 -104 266q-102 99 -256 99q-150 0 -252 -105q-100 -100 -100 -260zM164 -240l117 68q102 -170 294 -170q185 0 280.5 123t94.5 393q-43 -88 -144.5 -141t-215.5 -53q-209 0 -350 135
q-140 140 -140 360q0 223 140 363q142 139 350 139q112 0 211 -49t149 -135v163h137v-804q0 -629 -512 -629q-128 0 -235.5 60.5t-175.5 176.5zM371 1411h106q11 -65 49.5 -103t98.5 -38q65 0 103 39t44 102h107q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z
" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1548" 
d="M1475 692q0 -348 -171.5 -530t-484.5 -182q-300 0 -504 210t-204 512q0 305 204 515t504 210q255 0 451 -163l-96 -97q-152 125 -355 125q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q226 0 359 119.5t145 330.5h-412v127h564zM711 1696
q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1251" 
d="M238 475q0 -163 100 -260q101 -98 252 -98q140 0 250 96q110 94 110 262q0 172 -104 266q-102 99 -256 99q-150 0 -252 -105q-100 -100 -100 -260zM164 -240l117 68q102 -170 294 -170q185 0 280.5 123t94.5 393q-43 -88 -144.5 -141t-215.5 -53q-209 0 -350 135
q-140 140 -140 360q0 223 140 363q142 139 350 139q112 0 211 -49t149 -135v163h137v-804q0 -629 -512 -629q-128 0 -235.5 60.5t-175.5 176.5zM551 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1548" 
d="M911 565v127h564q0 -348 -171.5 -530t-484.5 -182q-300 0 -504 210t-204 512q0 305 204 515t504 210q255 0 451 -163l-96 -97q-152 125 -355 125q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q226 0 359 119.5t145 330.5h-412zM760 -506l-98 27
q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1251" 
d="M238 475q0 -163 100 -260q101 -98 252 -98q140 0 250 96q110 94 110 262q0 172 -104 266q-102 99 -256 99q-150 0 -252 -105q-100 -100 -100 -260zM164 -240l117 68q102 -170 294 -170q185 0 280.5 123t94.5 393q-43 -88 -144.5 -141t-215.5 -53q-209 0 -350 135
q-140 140 -140 360q0 223 140 363q142 139 350 139q112 0 211 -49t149 -135v163h137v-804q0 -629 -512 -629q-128 0 -235.5 60.5t-175.5 176.5zM623 1470l98 -26q-118 -164 -88 -348h-156q-3 252 146 374z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1454" 
d="M1114 1411h135v-1411h-135v637h-774v-637h-135v1411h135v-647h774v647zM1010 1591h-140l-145 176l-143 -176h-140l195 275h178z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" 
d="M303 522v-522h-137v1475h137v-676q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240zM516 1550h-139l-146 176l-143 -176h-139l194 275h179z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1478" 
d="M1262 1411v-250h149v-110h-149v-1051h-136v637h-774v-637h-135v1051h-147v110h147v250h135v-250h774v250h136zM352 1051v-287h774v287h-774z" />
    <glyph glyph-name="hbar" unicode="&#x127;" 
d="M627 1180h-324v-381q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240v-522h-137v1180h-168v100h168v195h137v-195h324v-100z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="544" 
d="M180 1737q-26 0 -47 -25t-22 -67h-105q6 110 54.5 165.5t119.5 55.5q50 0 105 -43t82 -43t47.5 22t21.5 64h105q-6 -109 -53.5 -162t-120.5 -53q-50 0 -105 43t-82 43zM205 1411h135v-1411h-135v1411z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="466" 
d="M143 1282q-26 0 -47 -25t-22 -67h-105q6 110 54.5 165.5t119.5 55.5q50 0 105 -43t82 -43t47.5 22t21.5 64h105q-6 -109 -53.5 -162t-120.5 -53q-50 0 -105 43t-82 43zM166 956h135v-956h-135v956z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="544" 
d="M205 1411h135v-1411h-135v1411zM29 1696v129h487v-129h-487z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="466" 
d="M166 956h135v-956h-135v956zM-8 1241v129h487v-129h-487z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="544" 
d="M340 0h-135v1411h135v-1411zM18 1866h107q11 -65 49 -103.5t98 -38.5q65 0 103.5 39.5t44.5 102.5h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="466" 
d="M301 0h-135v956h135v-956zM-18 1411h106q11 -65 49.5 -103t98.5 -38q65 0 103 39t44 102h106q0 -116 -69 -186t-184 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="544" 
d="M178 -139l58 139v1411h135v-1411h-66l-33 -182q-18 -91 52 -125q35 -13 69 2t51 51q9 15 9 39l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-80 32 -111 107t4 157z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="466" 
d="M139 -139l58 139v956h135v-956h-62l-37 -182q-18 -91 52 -125q35 -13 69.5 2t51.5 51q8 13 8 39l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-80 32 -111 107t4 157zM193 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72
q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Idot" unicode="&#x130;" horiz-adv-x="544" 
d="M340 0h-135v1411h135v-1411zM199 1696q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="466" 
d="M166 956h135v-956h-135v956z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1089" 
d="M338 -512l12 133q41 -14 115 -14q143 0 214 84t71 229v1491h135v-1491q0 -196 -103 -323q-101 -125 -317 -125q-52 0 -127 16zM205 1411h135v-1411h-135v1411z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="935" 
d="M629 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM436 -463h-102v129h102q128 0 168 94q29 66 29 185v1011h137v-1011q0 -408 -334 -408zM301 0h-135v956h135v-956zM162 1241q-31 29 -31 72t31 69q31 29 73.5 29
t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="544" 
d="M-207 -512l12 133q41 -14 115 -14q143 0 214 84t71 229v1491h135v-1491q0 -197 -102 -323q-101 -125 -318 -125q-52 0 -127 16zM535 1591h-140l-145 176l-144 -176h-139l195 275h178z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="468" 
d="M-31 -463h-102v129h102q128 0 168 94q29 66 29 185v1011h137v-1011q0 -408 -334 -408zM520 1137h-139l-145 176l-144 -176h-139l194 274h179z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" 
d="M1032 1411l-549 -706l668 -705h-182l-629 664v-664h-135v1411h135v-686l532 686h160zM596 -506l-98 27q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="993" 
d="M795 0l-494 522v-522h-135v1475h135v-897l379 378h192l-409 -409l518 -547h-186zM475 -506l-98 27q118 164 88 348h156q3 -253 -146 -375z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="980" 
d="M762 0l-461 449v-449h-135v956h135v-450l430 450h178l-446 -473l487 -483h-188z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="997" 
d="M348 1866h168l-172 -275h-139zM205 0v1411h135v-1276h598v-135h-733z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="466" 
d="M313 1866h168l-172 -275h-139zM166 1475h135v-1475h-135v1475z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="997" 
d="M205 0v1411h135v-1276h598v-135h-733zM485 -506l-98 27q118 164 88 348h156q3 -253 -146 -375z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="466" 
d="M170 -506l-98 27q118 164 88 348h155q3 -254 -145 -375zM166 1475h135v-1475h-135v1475z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="997" 
d="M205 0v1411h135v-1276h598v-135h-733zM604 975l-98 26q122 172 119 410h155q-12 -146 -61.5 -264t-114.5 -172z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="466" 
d="M166 1475h135v-1475h-135v1475zM489 991l-98 27q122 172 119 409h156q-12 -146 -62 -264t-115 -172z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="997" 
d="M205 0v1411h135v-1276h598v-135h-733zM565 463q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="643" 
d="M449 463q-32 31 -32 75.5t32 75.5t79.5 31t79.5 -31t32 -75.5t-32 -75.5t-79.5 -31t-79.5 31zM166 1475h135v-1475h-135v1475z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1009" 
d="M352 1411v-516l221 139v-121l-221 -139v-639h598v-135h-733v688l-139 -86v121l139 86v602h135z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="493" 
d="M319 1475v-588l168 108v-121l-168 -108v-766h-135v680l-147 -94v121l147 94v674h135z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1402" 
d="M1198 1411v-1411h-139l-719 1182v-1182h-135v1411h145l713 -1173v1173h135zM776 1866h168l-172 -275h-139z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M303 522v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240zM621 1411h167l-172 -274h-139z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1402" 
d="M1198 1411v-1411h-139l-719 1182v-1182h-135v1411h145l713 -1173v1173h135zM639 -506l-98 27q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" 
d="M303 522v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240zM510 -506l-98 27q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1402" 
d="M1198 1411v-1411h-139l-719 1182v-1182h-135v1411h145l713 -1173v1173h135zM418 1866h139l145 -176l144 176h139l-194 -275h-179z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M303 522v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240zM289 1411h139l145 -176l144 176h139l-194 -274h-179z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" 
d="M303 522v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-557h-137v557q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240zM43 993l-98 27q115 161 88 328h155q3 -234 -145 -355z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1402" 
d="M1063 -80v80l-723 1182v-1182h-135v1411h145l713 -1184v1184h135v-1411v-80q0 -197 -102 -323q-101 -125 -318 -125q-52 0 -127 16l13 133q41 -14 114 -14q143 0 214 84t71 229z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M852 -29v586q0 134 -60.5 209.5t-170.5 75.5q-129 0 -224 -80q-94 -76 -94 -240v-522h-137v956h137v-157q39 84 129.5 131t188.5 47q173 0 271 -112t97 -308v-506v-80q0 -197 -102 -323q-101 -125 -318 -125q-52 0 -127 16l13 133q41 -14 114 -14q143 0 213 83.5
t70 229.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM575 1696v129
h488v-129h-488z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1200" 
d="M100 481q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM340 217q108 -102 260 -102q153 0 258 102q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101
q-104 -98 -104 -260q0 -163 104 -264zM356 1241v129h488v-129h-488z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1640" 
d="M111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172zM565 1866h107
q11 -65 49 -103.5t98 -38.5q65 0 103.5 39.5t44.5 102.5h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1200" 
d="M1100 481q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359zM858 217q107 101 107 264q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264
q108 -102 260 -102q153 0 258 102zM346 1411h107q11 -65 49 -103t98 -38q65 0 103.5 39t44.5 102h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1640" 
d="M991 1866h168l-172 -275h-139zM741 1866h152l-143 -275h-148zM111 702q0 305 204 515t504 210t504 -210q207 -213 207 -515q0 -299 -207 -512q-204 -210 -504 -210t-504 210t-204 512zM819 1292q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170
q243 0 410 170q166 172 166 417q0 246 -166 418t-410 172z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1200" 
d="M760 1411h168l-172 -274h-140zM510 1411h152l-144 -274h-147zM100 481q0 221 144 359q143 137 356 137q211 0 354 -137q146 -140 146 -359q0 -222 -146 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM340 217q108 -102 260 -102q153 0 258 102q107 101 107 264
q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2334" 
d="M1395 1133v278h805v-135h-670v-516h586v-135h-586v-490h692v-135h-827v274q-99 -138 -250 -216t-326 -78q-300 0 -504 210t-204 512q0 305 204 515t504 210q175 0 326 -78t250 -216zM819 115q241 0 402 161q165 165 174 402v49q-9 237 -176 401q-164 164 -400 164
q-241 0 -407 -172t-166 -418q0 -244 164 -417q167 -170 409 -170z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="2004" 
d="M1448 -20q-136 0 -246 62t-172 173q-66 -110 -180.5 -172.5t-249.5 -62.5q-209 0 -354 139q-146 140 -146 362q0 221 144 359q143 137 356 137q135 0 249.5 -61.5t180.5 -170.5q62 109 172 170.5t246 61.5q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811
q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q37 -11 70 -17l67 -14q-57 -127 -173 -198.5t-263 -71.5zM600 115q145 0 252 96q107 98 113 254v16q0 162 -107 260q-104 101 -258 101q-153 0 -260 -101q-104 -98 -104 -260q0 -163 104 -264q108 -102 260 -102z
M1104 545h674q-20 136 -109.5 216.5t-220.5 80.5q-135 0 -229.5 -81t-114.5 -216z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1245" 
d="M745 551l390 -551h-166l-389 551h-238v-551h-137v1411h524q201 0 295 -125q96 -128 96 -307q0 -177 -92 -301q-89 -121 -283 -127zM729 1276h-387v-590h377q133 0 199.5 80.5t66.5 212.5q0 131 -65.5 214t-190.5 83zM612 1866h168l-172 -275h-139z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="772" 
d="M733 940l-16 -133q-62 33 -139 33q-135 0 -207 -140q-68 -139 -68 -344v-356h-137v956h137v-217q68 238 275 238q45 0 93 -12t62 -25zM322 1411h167l-172 -274h-139z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1245" 
d="M745 551l390 -551h-166l-389 551h-238v-551h-137v1411h524q201 0 295 -125q96 -128 96 -307q0 -177 -92 -301q-89 -121 -283 -127zM729 1276h-387v-590h377q133 0 199.5 80.5t66.5 212.5q0 131 -65.5 214t-190.5 83zM600 -506l-98 27q118 164 88 348h155
q3 -254 -145 -375z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="772" 
d="M733 940l-16 -133q-62 33 -139 33q-135 0 -207 -140q-68 -139 -68 -344v-356h-137v956h137v-217q68 238 275 238q45 0 93 -12t62 -25zM172 -506l-98 27q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1245" 
d="M745 551l390 -551h-166l-389 551h-238v-551h-137v1411h524q201 0 295 -125q96 -128 96 -307q0 -177 -92 -301q-89 -121 -283 -127zM729 1276h-387v-590h377q133 0 199.5 80.5t66.5 212.5q0 131 -65.5 214t-190.5 83zM340 1866h139l146 -176l143 176h139l-194 -275h-178z
" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="772" 
d="M733 940l-16 -133q-62 33 -139 33q-135 0 -207 -140q-68 -139 -68 -344v-356h-137v956h137v-217q68 238 275 238q45 0 93 -12t62 -25zM123 1411h139l146 -176l143 176h139l-194 -274h-179z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1163" 
d="M117 406h135q0 -113 90 -203q88 -88 231 -88q142 0 234.5 66t112.5 165q18 117 -55 192t-214 107l-190 43q-332 74 -332 354q0 166 133 275q131 110 309 110q192 0 316 -112q127 -112 127 -291h-135q0 120 -86.5 194t-221.5 74q-120 0 -215 -71q-92 -71 -92 -179
q0 -170 225 -223l193 -43q191 -44 293 -155q105 -108 78 -299q-27 -139 -152 -240q-126 -102 -328 -102q-199 0 -327 131q-129 132 -129 295zM623 1866h168l-173 -275h-139z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="868" 
d="M90 274h121q43 -165 225 -165q92 0 147.5 41.5t55.5 109.5q0 114 -137 148l-133 30q-96 21 -174 80q-83 71 -68 232q13 99 102 163t209 64q122 0 202.5 -63.5t98.5 -184.5h-133q-12 54 -58 85.5t-110 31.5q-69 0 -120 -33t-58 -84q-21 -120 141 -160l131 -30
q242 -58 242 -279q0 -125 -100 -203q-103 -77 -250 -77q-128 0 -229 79q-99 82 -105 215zM473 1411h168l-172 -274h-139z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1163" 
d="M117 406h135q0 -113 90 -203q88 -88 231 -88q142 0 234.5 66t112.5 165q18 117 -55 192t-214 107l-190 43q-332 74 -332 354q0 166 133 275q131 110 309 110q192 0 316 -112q127 -112 127 -291h-135q0 120 -86.5 194t-221.5 74q-120 0 -215 -71q-92 -71 -92 -179
q0 -170 225 -223l193 -43q191 -44 293 -155q105 -108 78 -299q-27 -139 -152 -240q-126 -102 -328 -102q-199 0 -327 131q-129 132 -129 295zM870 1591h-139l-145 176l-144 -176h-139l195 275h178z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="868" 
d="M90 274h121q43 -165 225 -165q92 0 147.5 41.5t55.5 109.5q0 114 -137 148l-133 30q-96 21 -174 80q-83 71 -68 232q13 99 102 163t209 64q122 0 202.5 -63.5t98.5 -184.5h-133q-12 54 -58 85.5t-110 31.5q-69 0 -120 -33t-58 -84q-21 -120 141 -160l131 -30
q242 -58 242 -279q0 -125 -100 -203q-103 -77 -250 -77q-128 0 -229 79q-99 82 -105 215zM719 1137h-139l-146 176l-143 -176h-139l194 274h178z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1165" 
d="M117 406h135q0 -113 90 -203q88 -88 231 -88q142 0 234.5 66t112.5 165q18 117 -55 192t-214 107l-190 43q-332 74 -332 354q0 166 133 275q131 110 309 110q192 0 316 -112q127 -112 127 -291h-135q0 120 -86.5 194t-221.5 74q-120 0 -215 -71q-92 -71 -92 -179
q0 -170 225 -223l193 -43q191 -44 293 -155q105 -108 78 -299q-24 -120 -118 -211t-239 -119l60 -131q35 -82 4 -157t-111 -107q-78 -32 -150.5 -1t-107.5 109q-18 47 -18 88l108 -8q0 -26 8 -39q17 -36 51.5 -51t69.5 -2q37 18 53 53.5t-1 71.5l-62 162h-27
q-199 0 -327 131q-129 132 -129 295z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="870" 
d="M512 -182l-55 164q-6 -2 -31 -2q-128 0 -229 79q-99 82 -105 215h121q43 -165 225 -165q92 0 147.5 41.5t55.5 109.5q0 114 -137 148l-133 30q-96 21 -174 80q-83 71 -68 232q13 99 102 163t209 64q122 0 202.5 -63.5t98.5 -184.5h-133q-12 54 -58 85.5t-110 31.5
q-69 0 -120 -33t-58 -84q-21 -120 141 -160l132 -30q241 -58 241 -279q0 -96 -64 -167.5t-169 -98.5l63 -133q37 -79 6.5 -155.5t-112.5 -108.5q-78 -32 -150.5 -1t-107.5 109q-19 49 -19 88l109 -8q0 -26 8 -39q17 -36 51.5 -51t69.5 -2q36 17 49.5 52t1.5 73z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1163" 
d="M117 406h135q0 -113 90 -203q88 -88 231 -88q142 0 234.5 66t112.5 165q18 117 -55 192t-214 107l-190 43q-332 74 -332 354q0 166 133 275q131 110 309 110q192 0 316 -112q127 -112 127 -291h-135q0 120 -86.5 194t-221.5 74q-120 0 -215 -71q-92 -71 -92 -179
q0 -170 225 -223l193 -43q191 -44 293 -155q105 -108 78 -299q-27 -139 -152 -240q-126 -102 -328 -102q-199 0 -327 131q-129 132 -129 295zM303 1866h139l146 -176l143 176h139l-194 -275h-178z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="868" 
d="M90 274h121q43 -165 225 -165q92 0 147.5 41.5t55.5 109.5q0 114 -137 148l-133 30q-96 21 -174 80q-83 71 -68 232q13 99 102 163t209 64q122 0 202.5 -63.5t98.5 -184.5h-133q-12 54 -58 85.5t-110 31.5q-69 0 -120 -33t-58 -84q-21 -120 141 -160l131 -30
q242 -58 242 -279q0 -125 -100 -203q-103 -77 -250 -77q-128 0 -229 79q-99 82 -105 215zM152 1411h139l145 -176l144 176h139l-195 -274h-178z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1159" 
d="M59 1276v135h1041v-135h-453v-1276h-135v1276h-453zM518 -506l-98 27q118 164 88 348h156q3 -253 -146 -375z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="630" 
d="M367 834v-834h-138v834h-172v122h172v359h138v-359h202v-122h-202zM236 -506l-99 27q118 164 88 348h156q3 -254 -145 -375z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1159" 
d="M59 1276v135h1041v-135h-453v-1276h-135v1276h-453zM295 1866h139l146 -176l143 176h139l-194 -275h-179z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="630" 
d="M229 0v834h-172v122h172v359h138v-359h202v-122h-202v-834h-138zM535 1073l-99 27q115 161 88 327h156q3 -233 -145 -354z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1159" 
d="M647 1276v-451h285v-108h-285v-717h-135v717h-283v108h283v451h-453v135h1041v-135h-453z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="638" 
d="M371 834v-256h166v-95h-166v-483h-138v483h-149v95h149v256h-172v122h172v359h138v-359h202v-122h-202z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1380" 
d="M596 1737q-27 0 -48 -25t-22 -67h-104q6 110 54.5 165.5t119.5 55.5q49 0 104 -43t82 -43t48 22.5t22 63.5h104q-6 -109 -53.5 -162t-120.5 -53q-49 0 -104 43t-82 43zM1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360
v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM494 1282q-27 0 -48 -25t-22 -67h-105q6 110 55 165.5t120 55.5q49 0 104 -43t82 -43t48 22.5
t22 63.5h104q-6 -109 -53.5 -162t-120.5 -53q-50 0 -105 43t-81 43z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262zM446 1696v129h488v-129h-488z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM342 1241v129h487v-129h-487z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262zM436 1866h107q11 -65 49 -103.5t98 -38.5q65 0 103.5 39.5t44.5 102.5h106
q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241zM332 1411h106q11 -65 49.5 -103t98.5 -38q65 0 103 39t44 102h107q0 -116 -69.5 -186t-184.5 -70
q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1380" 
d="M688 1989q85 0 143 -58t58 -143q0 -82 -58.5 -139.5t-142.5 -57.5q-82 0 -139 56.5t-57 140.5q0 85 57 143t139 58zM598 1790q0 -41 26.5 -66.5t65.5 -25.5q38 0 65 27t27 65q4 56 -44 79.5t-96 0t-44 -79.5zM1055 477v934h135v-934q0 -220 -146 -360
q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M584 1534q85 0 142.5 -58t57.5 -143q0 -82 -58 -139t-142 -57q-83 0 -140 56t-57 140t57.5 142.5t139.5 58.5zM494 1335q0 -41 26.5 -66.5t65.5 -25.5q38 0 65 27t27 65q4 56 -44 79.5t-96 0t-44 -79.5zM842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47
q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1380" 
d="M846 1866h168l-172 -275h-140zM596 1866h152l-144 -275h-147zM1055 477v934h135v-934q0 -220 -146 -360q-143 -137 -354 -137q-213 0 -356 137q-144 138 -144 360v934h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M696 1411h168l-172 -274h-139zM446 1411h152l-143 -274h-148zM842 434v522h137v-956h-137v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1380" 
d="M1055 477v934h135v-934q0 -201 -121 -334t-303 -157l-66 -168q-17 -36 -1 -71.5t53 -53.5q35 -13 69 2t51 51q3 4 4.5 11.5t2.5 16.5t2 11l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-82 32 -113.5 108t6.5 156l60 119q-201 9 -340 145q-136 139 -136 352v934
h136v-934q0 -161 104 -262q106 -100 260 -100q155 0 258 100q107 101 107 262z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M842 434v522h137v-956h-43l-64 -182q-17 -36 -1 -71.5t53 -53.5q35 -13 69 2t51 51q9 15 9 39l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-80 32 -111 107t4 157l64 139v158q-39 -84 -129.5 -131t-188.5 -47q-173 0 -271 111.5t-97 307.5v557h137v-557
q0 -134 60.5 -209t170.5 -75q128 0 224 78q94 79 94 241z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1859" 
d="M1274 213l395 1198h143l-477 -1411h-129l-276 946l-277 -946h-129l-477 1411h143l396 -1198l293 1012h102zM1212 1591h-139l-145 176l-144 -176h-139l195 275h178z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1646" 
d="M1591 956l-358 -956h-115l-297 768l-286 -768h-115l-369 956h146l276 -739l275 739h147l276 -739l275 739h145zM1104 1137h-139l-146 176l-143 -176h-139l194 274h178z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1259" 
d="M1235 1411l-537 -756v-655h-139v655l-530 756h161l439 -608l442 608h164zM915 1591h-139l-145 176l-144 -176h-139l195 275h178z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1034" 
d="M987 956l-594 -1419h-141l190 465l-393 954h144l323 -778l326 778h145zM807 1137h-139l-146 176l-143 -176h-139l194 274h178z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1259" 
d="M1235 1411l-537 -756v-655h-139v655l-530 756h161l439 -608l442 608h164zM377 1651q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM739 1651q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72
q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1146" 
d="M86 0v135l797 1139h-768v137h934v-135l-799 -1139h799v-137h-963zM596 1866h168l-172 -275h-139z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1013" 
d="M119 956h794v-122l-645 -699h645v-135h-829v125l635 696h-600v135zM524 1411h168l-172 -274h-139z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1146" 
d="M86 0v135l797 1139h-768v137h934v-135l-799 -1139h799v-137h-963zM494 1696q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1013" 
d="M913 834l-645 -699h645v-135h-829v125l635 696h-600v135h794v-122zM426 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1146" 
d="M86 0v135l797 1139h-768v137h934v-135l-799 -1139h799v-137h-963zM283 1821h139l145 -176l144 176h139l-195 -275h-178z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1013" 
d="M119 956h794v-122l-645 -699h645v-135h-829v125l635 696h-600v135zM215 1411h139l146 -176l143 176h139l-194 -274h-178z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="589" 
d="M580 1475h102v-129h-102q-128 0 -168 -95q-29 -66 -29 -184v-1067h-137v834h-187v122h187v111q0 408 334 408z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="1269" 
d="M449 0l182 834h-187l27 122h186l25 111q88 408 422 408h102l-28 -129h-103q-125 0 -188 -95q-49 -79 -68 -184l-24 -111h235l-26 -122h-236l-182 -834q-88 -408 -422 -408h-103l29 129h103q123 0 186 95q46 69 70 184z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1306" 
d="M326 371l-142 -371h-145l543 1411h143l543 -1411h-146l-143 371h-653zM375 498h557l-279 725zM715 2222h168l-158 -250q58 -24 92.5 -74t34.5 -112q0 -82 -58.5 -139.5t-142.5 -57.5q-82 0 -139 57.5t-57 139.5q0 66 35.5 117t95.5 71zM588 1853q-26 -26 -26 -65.5
t26 -65.5t65.5 -26t65.5 26t26 65.5t-26 65.5t-65.5 26t-65.5 -26z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1253" 
d="M686 1769h168l-158 -249q58 -23 92.5 -74t34.5 -113q0 -82 -58 -139t-142 -57q-83 0 -140 56t-57 140q0 66 35.5 117.5t95.5 71.5zM532 1335q0 -40 27 -66t66 -26q38 0 65 27t27 65q4 56 -44.5 79.5t-96.5 0t-44 -79.5zM950 956h137v-956h-137v164q-50 -86 -149 -135
t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97q-150 0 -252 -99q-100 -97 -100 -260z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1929" 
d="M989 371h-573l-236 -371h-160l891 1411h883v-135h-670v-516h586v-135h-586v-490h693v-135h-828v371zM989 1276l-493 -778h493v778zM1061 1866h168l-172 -275h-139z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1992" 
d="M1087 121v-121h-137v164q-50 -86 -149 -135t-211 -49q-208 0 -350 139q-140 140 -140 362t140 359t350 137q113 0 211.5 -47t148.5 -131v157h137v-118q133 139 349 139q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811q19 -136 114 -219.5t230 -83.5q97 0 176 44
t123 122q40 -11 69 -17l68 -14q-57 -127 -173 -198.5t-263 -71.5q-214 0 -349 141zM238 481q0 -159 100 -262q102 -102 252 -102q152 0 256 96q104 98 104 268q0 168 -110 262q-111 97 -250 97q-150 0 -252 -99q-100 -97 -100 -260zM1092 545h673q-20 136 -109 216.5
t-220 80.5q-135 0 -229.5 -81t-114.5 -216zM1063 1411h168l-172 -274h-139z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1636" 
d="M358 147l-163 -170l-76 76l164 170q-174 201 -174 479q0 305 204 515t504 210q267 0 467 -176l178 183l76 -76l-178 -184q168 -204 168 -472q0 -299 -207 -512q-204 -210 -504 -210q-265 0 -459 167zM244 702q0 -218 133 -383l813 834q-165 139 -373 139
q-241 0 -407 -172t-166 -418zM817 115q243 0 410 170q166 172 166 417q0 212 -129 375l-811 -833q159 -129 364 -129zM860 1866h168l-172 -275h-139z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1196" 
d="M281 86l-105 -109l-76 76l105 109q-107 137 -107 319q0 221 144 359q143 137 356 137q176 0 307 -98l103 106l75 -76l-100 -102q115 -136 115 -326q0 -222 -146 -362q-145 -139 -354 -139q-184 0 -317 106zM598 115q153 0 258 102q107 101 107 264q0 134 -76 228
l-510 -525q92 -69 221 -69zM598 842q-153 0 -260 -101q-105 -99 -105 -260q0 -126 68 -221l508 520q-89 62 -211 62zM641 1411h168l-172 -274h-139z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1163" 
d="M117 406h135q0 -113 90 -203q88 -88 231 -88q142 0 234.5 66t112.5 165q18 117 -55 192t-214 107l-190 43q-332 74 -332 354q0 166 133 275q131 110 309 110q192 0 316 -112q127 -112 127 -291h-135q0 120 -86.5 194t-221.5 74q-120 0 -215 -71q-92 -71 -92 -179
q0 -170 225 -223l193 -43q191 -44 293 -155q105 -108 78 -299q-27 -139 -152 -240q-126 -102 -328 -102q-199 0 -327 131q-129 132 -129 295zM496 -506l-99 27q118 164 88 348h156q3 -254 -145 -375z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="868" 
d="M90 274h121q43 -165 225 -165q92 0 147.5 41.5t55.5 109.5q0 114 -137 148l-133 30q-96 21 -174 80q-83 71 -68 232q13 99 102 163t209 64q122 0 202.5 -63.5t98.5 -184.5h-133q-12 54 -58 85.5t-110 31.5q-69 0 -120 -33t-58 -84q-21 -120 141 -160l131 -30
q242 -58 242 -279q0 -125 -100 -203q-103 -77 -250 -77q-128 0 -229 79q-99 82 -105 215zM383 -506l-98 27q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="997" 
d="M782 1137h-139l-145 176l-144 -176h-139l195 274h178z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1017" 
d="M225 1411h140l145 -176l143 176h140l-195 -274h-178z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="958" 
d="M225 1411h107q11 -65 49 -103t98 -38q65 0 103.5 39t44.5 102h106q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="1028" 
d="M440 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="1052" 
d="M524 1534q85 0 143 -58t58 -143q0 -82 -58 -139t-143 -57q-82 0 -139 56t-57 140q0 85 57 143t139 58zM434 1335q0 -41 26.5 -66.5t65.5 -25.5q38 0 65 27t27 65q4 56 -44 79.5t-96 0t-44 -79.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1056" 
d="M457 53h114l-100 -235q-17 -36 -1.5 -71.5t52.5 -53.5q35 -13 69.5 2t51.5 51q6 18 8 39l109 8q0 -39 -19 -88q-35 -78 -107.5 -109t-150.5 1q-80 32 -110.5 107t4.5 157z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="862" 
d="M338 1282q-27 0 -48 -25t-22 -67h-104q6 110 54.5 165.5t119.5 55.5q49 0 104 -43t82 -43t48 22.5t22 63.5h104q-6 -109 -53.5 -162t-120.5 -53q-49 0 -104 43t-82 43z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="884" 
d="M553 1411h168l-172 -274h-139zM303 1411h152l-144 -274h-147z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1859" 
d="M1812 1411l-477 -1411h-129l-276 946l-277 -946h-129l-477 1411h143l396 -1198l293 1012h102l293 -1012l395 1198h143zM983 1591h-139l-172 275h168z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1646" 
d="M1118 0l-297 768l-286 -768h-115l-369 956h146l276 -739l275 739h147l276 -739l275 739h145l-358 -956h-115zM901 1137h-139l-172 274h168z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1859" 
d="M1274 213l395 1198h143l-477 -1411h-129l-276 946l-277 -946h-129l-477 1411h143l396 -1198l293 1012h102zM1026 1866h168l-172 -275h-139z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1646" 
d="M1591 956l-358 -956h-115l-297 768l-286 -768h-115l-369 956h146l276 -739l275 739h147l276 -739l275 739h145zM889 1411h168l-172 -274h-140z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1859" 
d="M1812 1411l-477 -1411h-129l-276 946l-277 -946h-129l-477 1411h143l396 -1198l293 1012h102l293 -1012l395 1198h143zM674 1696q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM1036 1696q-31 29 -31 72t31 69
q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1646" 
d="M1118 0l-297 768l-286 -768h-115l-369 956h146l276 -739l275 739h147l276 -739l275 739h145l-358 -956h-115zM565 1241q-31 29 -31 72t31 69q31 29 74 29t74 -29q31 -26 31 -69t-31 -72q-31 -26 -74 -26t-74 26zM928 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29
q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1259" 
d="M1235 1411l-537 -756v-655h-139v655l-530 756h161l439 -608l442 608h164zM725 1591h-139l-172 275h168z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1034" 
d="M252 -463l190 465l-393 954h144l323 -778l326 778h145l-594 -1419h-141zM608 1137h-139l-172 274h168z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="28" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1222" 
d="M150 414v121h923v-121h-923z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1665" 
d="M150 414v121h1366v-121h-1366z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="485" 
d="M311 1427l78 -26q-122 -172 -119 -410h-135q12 146 61.5 264t114.5 172z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="481" 
d="M195 991l-78 27q122 172 119 409h135q-12 -146 -61.5 -264t-114.5 -172z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="477" 
d="M147 -137l-77 26q121 171 118 410h136q-12 -146 -62 -264t-115 -172z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="722" 
d="M549 1427l78 -26q-122 -172 -119 -410h-135q12 146 61.5 264t114.5 172zM311 1427l78 -26q-122 -172 -119 -410h-135q12 146 61.5 264t114.5 172z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="718" 
d="M195 991l-78 27q122 172 119 409h135q-12 -146 -61.5 -264t-114.5 -172zM432 991l-78 27q122 172 119 409h135q-12 -146 -61.5 -264t-114.5 -172z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="714" 
d="M147 -137l-77 26q121 171 118 410h136q-12 -146 -62 -264t-115 -172zM385 -137l-78 26q122 172 119 410h135q-12 -146 -61.5 -264t-114.5 -172z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="841" 
d="M487 1411v-389h263v-135h-263v-1149h-135v1149h-260v135h260v389h135z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="903" 
d="M518 1411v-389h262v-135h-262v-572h262v-135h-262v-442h-135v442h-260v135h260v572h-260v135h260v389h135z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="937" 
d="M260 289q-86 80 -86 200q0 121 86 201t209 80t209 -80t86 -201q0 -120 -86 -200t-209 -80t-209 80z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1503" 
d="M180 10q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31zM672 10q-32 31 -32 76t32 76t79.5 31t79.5 -31t32 -76t-32 -76t-79.5 -31t-79.5 31zM1163 10q-32 31 -32 76t32 76t80 31t80 -31t32 -76t-32 -76t-80 -31t-80 31z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1980" 
d="M961 1378h96l-674 -1378h-96zM631 1104q0 -115 -80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q80 -80 80 -192zM514 1104q0 64 -46.5 111t-111.5 47q-66 0 -110.5 -46t-44.5 -112t44.5 -111t110.5 -45t112 45
t46 111zM1266 252q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q80 -80 80 -192zM1149 252q0 64 -46.5 111t-111.5 47q-66 0 -110.5 -46t-44.5 -112t44.5 -111t110.5 -45t112 45t46 111zM1632 526
q114 0 193 -82q82 -79 82 -192q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195q0 113 78 194q80 80 194 80zM1790 252q0 63 -47.5 110.5t-110.5 47.5q-48 3 -87 -26.5t-56 -72t-9.5 -90.5t42.5 -80q32 -35 80 -42t90 10t71.5 55.5t26.5 87.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="620" 
d="M385 51l-283 424l283 424h127l-283 -424l283 -424h-127z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="620" 
d="M236 899l282 -424l-282 -424h-127l282 424l-282 424h127z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="368" 
d="M569 1378h136l-918 -1398h-135z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="745" 
d="M186 1362q0 -307 187 -307q186 0 186 307q0 309 -186 309q-187 0 -187 -309zM86 1362q0 410 287 410q153 0 219.5 -104.5t66.5 -305.5q0 -199 -66.5 -302.5t-219.5 -103.5q-287 0 -287 406z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="741" 
d="M457 1231v338l-232 -338h232zM682 1231v-103h-117v-165h-108v165h-418l444 637h82v-534h117z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="698" 
d="M61 1133l89 63q23 -60 73 -98.5t105 -38.5q85 0 139.5 46t54.5 116q0 66 -48 105.5t-114 39.5q-64 0 -110 -14q-54 -16 -137 -60l-23 25l80 448h408v-104h-318l-41 -219q67 33 141 33q119 0 194 -67.5t75 -186.5q0 -122 -83.5 -196.5t-217.5 -74.5q-81 0 -153 50
t-114 133z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="692" 
d="M616 1626l-96 -41q-47 92 -137 92q-88 0 -139.5 -84.5t-34.5 -210.5q30 103 166 103q106 0 178 -84q73 -82 70 -191q-3 -108 -82 -184q-76 -73 -187 -70q-107 5 -176.5 78.5t-79.5 196.5q-3 55 -3.5 99t3.5 103t14.5 104.5t32 93t52.5 78.5t79.5 51t110.5 20
q164 -9 229 -154zM209 1227q0 -66 43.5 -111.5t107.5 -50.5q63 -2 107.5 40.5t48.5 108.5t-38 113t-105 49q-66 5 -114.5 -39t-49.5 -110z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="659" 
d="M80 1657v108h553l-440 -802h-127l383 694h-369z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="708" 
d="M248 1571q0 -44 31 -76t77 -31q32 -1 58 19t38 48t7 58.5t-29 48.5q-36 28 -76.5 28.5t-73 -27t-32.5 -68.5zM233 1323q-47 -47 -47 -111t47 -108q49 -44 121 -44t119 44q49 44 49 108t-49 111q-47 47 -118.5 47t-121.5 -47zM141 1571q0 87 60.5 146t152.5 59
q91 0 154 -60q61 -58 61 -145q0 -86 -61 -141q123 -76 123 -218q0 -107 -80 -184q-79 -76 -197 -76q-116 0 -196 74q-80 77 -80 186q0 142 123 218q-60 54 -60 141z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="692" 
d="M88 1104l96 41q47 -92 138 -92q88 0 139.5 84.5t34.5 210.5q-30 -103 -166 -103q-107 0 -180 82q-71 83 -68 193q3 108 82 184q76 73 186 70q107 -5 176.5 -78.5t79.5 -196.5q3 -54 3.5 -97.5t-3.5 -103t-14.5 -105.5t-32 -93.5t-52.5 -79t-79.5 -51t-110.5 -19.5
q-164 9 -229 154zM496 1503q0 66 -44 111.5t-108 50.5q-63 2 -107.5 -40.5t-48.5 -108.5t38.5 -113t105.5 -49q66 -5 114.5 39t49.5 110z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="747" 
d="M186 82q0 -307 187 -307q186 0 186 307q0 309 -186 309q-187 0 -187 -309zM86 82q0 410 287 410q153 0 219.5 -104.5t66.5 -305.5q0 -199 -66.5 -302.5t-219.5 -103.5q-287 0 -287 406z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="411" 
d="M295 494v-811h-109v665l-131 -47v105z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="665" 
d="M575 -317h-493v49l326 393q59 74 59 133q0 57 -40.5 93t-102.5 36q-58 0 -101.5 -35.5t-50.5 -91.5l-100 25q13 96 83.5 153.5t168.5 57.5q63 2 117.5 -23.5t86.5 -71.5t44 -102.5t-9.5 -122.5t-75.5 -125l-219 -260h307v-108z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="667" 
d="M102 379q33 58 96 90.5t136 28.5q96 -6 159.5 -71.5t61.5 -152.5q-3 -103 -96 -159q59 -20 97 -84t32 -131q-9 -105 -96 -168q-87 -69 -199 -60q-78 6 -142.5 51.5t-95.5 114.5q6 3 25 14.5t37 22t28 14.5q43 -93 156 -108q68 -5 121.5 34.5t54.5 98.5q2 57 -45.5 101
t-112.5 44h-90v105h90q43 0 75 26.5t38 67.5q8 51 -22.5 87t-85.5 42q-90 9 -144 -72q-7 5 -32.5 26.5t-45.5 37.5z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="741" 
d="M457 -49v338l-232 -338h232zM682 -49v-103h-117v-165h-108v165h-418l444 637h82v-534h117z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="696" 
d="M61 -147l89 63q23 -60 73 -98.5t105 -38.5q85 0 139.5 46t54.5 116q0 66 -48 105.5t-114 39.5q-64 0 -110 -14q-54 -16 -137 -60l-23 25l80 448h408v-104h-318l-41 -219q67 33 141 33q119 0 194 -67.5t75 -186.5q0 -122 -83.5 -196.5t-217.5 -74.5q-81 0 -153 50
t-114 133z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="692" 
d="M616 346l-96 -41q-47 92 -137 92q-88 0 -139.5 -84.5t-34.5 -210.5q30 103 166 103q106 0 178 -84q73 -82 70 -191q-3 -108 -82 -184q-76 -73 -187 -70q-107 5 -176.5 78.5t-79.5 196.5q-3 54 -3.5 97.5t3.5 103t14.5 105.5t32 93.5t52.5 79t79.5 51t110.5 19.5
q164 -9 229 -154zM209 -53q0 -66 43.5 -111.5t107.5 -50.5q63 -2 107.5 40.5t48.5 108.5t-38 113t-105 49q-66 5 -114.5 -39t-49.5 -110z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="653" 
d="M82 377v108h553l-440 -802h-127l383 694h-369z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="704" 
d="M246 291q0 -44 31 -76t77 -31q32 -1 58 19t38 48t7 58.5t-29 48.5q-36 28 -76.5 28.5t-73 -27t-32.5 -68.5zM231 43q-47 -47 -47 -111t47 -108q49 -44 121 -44t119 44q49 44 49 108t-49 111q-47 47 -119 47t-121 -47zM139 291q0 87 60.5 146t152.5 59q91 0 154 -60
q61 -58 61 -145q0 -86 -61 -141q123 -76 123 -218q0 -107 -80 -184q-79 -76 -197 -76q-117 0 -196 76q-80 74 -80 184q0 142 123 218q-60 54 -60 141z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="692" 
d="M88 -176l96 41q47 -92 138 -92q88 0 139.5 84.5t34.5 210.5q-30 -103 -166 -103q-107 0 -180 82q-71 83 -68 193q3 108 82 184q76 73 186 70q107 -5 176.5 -78.5t79.5 -196.5q3 -55 3.5 -99t-3.5 -103t-14.5 -104.5t-32 -93t-52.5 -78.5t-79.5 -51t-110.5 -20
q-164 9 -229 154zM496 223q0 66 -44 111.5t-108 50.5q-63 2 -107.5 -40.5t-48.5 -108.5t38.5 -113t105.5 -49q66 -5 114.5 39t49.5 110z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="1392" 
d="M637 -4l-78 -219l-96 35l76 213q-88 37 -156 88l-92 -260l-96 32l106 301q-195 207 -195 516q0 292 179 498q183 207 458 225l80 224l97 -35l-68 -189q80 -8 172 -39l68 187l96 -33l-70 -194q95 -50 174 -129l-94 -95q-48 48 -125 95l-391 -1092q65 -10 109 -10
q240 0 409 172l94 -97q-204 -210 -503 -210q-72 0 -154 16zM805 1292l-373 -1044q65 -57 154 -94l393 1106q-83 29 -174 32zM242 702q0 -215 112 -368l338 948q-198 -34 -325 -199q-125 -162 -125 -381z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="1316" 
d="M309 135v271h-166v116h166v140h-166v116h166v264q0 177 123 285q123 111 293 111q169 0 283 -111q116 -107 116 -285h-135q0 115 -76 188t-188 73q-114 0 -197 -74q-84 -72 -84 -187v-264h332v-116h-332v-140h332v-116h-332v-271h584q19 0 31 14t12 37v121h135v-121
q0 -79 -52 -132.5t-126 -53.5h-919v135h200z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="1482" 
d="M248 614v189h-123v102h123v506h145l307 -506h406v506h135v-506h117v-102h-117v-189h117v-102h-117v-512h-139l-311 512h-408v-512h-135v512h-123v102h123zM612 803h-229v-189h344zM1106 803h-344l115 -189h229v189zM551 905l-168 277v-277h168zM1106 512h-166l166 -274
v274z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="1445" 
d="M274 0v807h-157v102h157v121h-157v103h157v278h555q149 0 244.5 -74.5t132.5 -203.5h144v-103h-125q0 -5 1 -25.5t1 -25.5q0 -8 -4 -70h127v-102h-150q-40 -121 -133 -188.5t-238 -67.5h-417v-551h-138zM829 1276h-417v-143h649q-64 143 -232 143zM1092 979q0 5 -2 22
t-3 29h-675v-121h673q7 40 7 70zM412 807v-121h407q161 0 232 121h-639z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1415" 
d="M150 551v135h444q133 0 199.5 80.5t66.5 212.5q0 31 -2 39h-708v94h690q-59 164 -236 164h-454v135h1165v-94h-445q82 -75 111 -205h334v-94h-322q2 -10 2 -39q0 -177 -92 -301q-89 -121 -282 -127l389 -551h-166l-389 551h-305z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="2013" 
d="M1890 1411l-139 -412h137v-102h-172l-63 -188h235v-103h-270l-205 -606h-129l-178 606h-199l-176 -606h-129l-205 606h-274v103h239l-63 188h-176v102h141l-139 412h143l135 -412h488l65 226h103l65 -226h488l135 412h143zM1577 897h-424l55 -188h308zM860 897h-422
l62 -188h307zM1481 606h-244l115 -393zM776 606h-241l129 -393zM1022 897h-29l-55 -188h139z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="1499" 
d="M991 692v-332h-135v332q0 121 -82 203t-213 82h-219v-977h-135v1112h354q188 0 309 -121t121 -299zM508 420v332h135v-332q0 -121 82 -203t213 -82h219v977h135v-1112h-354q-190 0 -311 121q-119 119 -119 299z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1632" 
d="M1067 862v-96h-956v96h215q53 251 233 406q181 159 436 159q298 0 502 -210l-94 -95q-170 170 -408 170q-197 0 -342 -121q-141 -118 -188 -309h602zM111 535v96h956v-96h-600q46 -186 188 -304q143 -116 340 -116q241 0 410 172l94 -97q-204 -210 -504 -210
q-254 0 -434 155t-233 400h-217z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="790" 
d="M426 115h205v-135h-205q-268 0 -268 317v815q0 318 268 318q262 0 262 -318v-385q0 -317 -262 -317h-133v-113q0 -182 133 -182zM293 1112v-567h133q127 0 127 182v385q0 182 -127 182q-133 0 -133 -182z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1941" 
d="M1061 0l-719 1182v-1182h-135v1411h145l713 -1173v1173h135v-1411h-139zM1397 989q-74 77 -74 182q0 107 74 181q75 75 182 75q108 0 180 -75q76 -73 76 -181q0 -106 -76 -182q-74 -74 -180 -74q-105 0 -182 74zM1470 1278q-43 -43 -43 -107t43 -108q44 -43 108.5 -43
t107.5 43q44 44 44 108t-44 107q-43 44 -107.5 44t-108.5 -44zM1825 743h-492v74h492v-74z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1677" 
d="M725 651h-104l79 656h-223v-656h-104v656h-234v104h672l262 -571l262 571h99l90 -760h-103l-69 572l-232 -506h-92l-235 502z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="1361" 
d="M1257 571h-878v-395q135 -129 311 -129q148 0 245.5 63.5t184.5 196.5l64 -39q-63 -98 -129.5 -158.5t-157.5 -95t-207 -34.5q-243 0 -399 172q-156 176 -156 421q0 251 154 422q153 172 409 172q257 0 406 -174q153 -172 153 -422zM379 639h633v334q-139 127 -314 127
q-195 0 -319 -127v-334z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1187" 
d="M158 475v129h872v-129h-872z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1234" 
d="M750 659l-142 -290h479v-129h-542l-117 -240h-106l116 240h-291v129h355l141 290h-496v129h560l116 240h107l-117 -240h274v-129h-337z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1374" 
d="M1196 518v-145l-1042 399v148l1042 399v-148l-885 -325zM166 0v129h1022v-129h-1022z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1374" 
d="M1221 920v-148l-1043 -399v145l885 328l-885 325v148zM1208 129v-129h-1022v129h1022z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="532" 
d="M242 -506l-99 27q118 164 88 348h156q3 -254 -145 -375z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="2363" 
d="M696 551l389 -551h-165l-390 551h-237v-551h-137v1411h524q201 0 295 -125q96 -128 96 -307q0 -177 -92 -301q-89 -121 -283 -127zM680 1276h-387v-590h377q133 0 199.5 80.5t66.5 212.5q0 131 -65.5 214t-190.5 83zM1440 -463h-137v1419h135l2 -165q50 86 149.5 136
t210.5 50q210 0 348 -141q142 -139 142 -363q0 -222 -142 -358q-138 -135 -348 -135q-114 0 -212 46t-148 130v-619zM2152 473q0 157 -102 262t-250 105q-153 0 -258 -99q-102 -99 -102 -268q0 -167 108 -262q110 -94 252 -94q148 0 252 96q100 97 100 260z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1734" 
d="M1571 1407v-1407h-1407v1407h1407zM872 680l-536 539v-773h166l-152 154v365l522 -519l525 519v-365l-152 -154h172v773zM395 197l-28 73h-31v-108h18v76l27 -76h20l23 76v-76h18v108h-28zM508 270q-51 0 -51 -55q0 -24 13.5 -38.5t37.5 -14.5q22 0 34.5 15.5t12.5 37.5
q0 24 -12.5 39.5t-34.5 15.5zM508 182q-13 0 -24 11t-11 22q0 12 11 23.5t24 11.5q24 0 24 -35q0 -33 -24 -33zM582 203h-27q0 -41 45 -41t45 35q0 22 -31 28q-32 6 -32 13q0 12 18 12q21 0 21 -12h18q0 32 -39 32t-39 -32q0 -17 33 -31q27 0 27 -10q0 -15 -21 -15
q-18 0 -18 21zM645 270v-20h35v-88h16v88h31v20h-82zM815 162l-43 108h-18l-45 -108h24l8 26h45l7 -26h22zM760 244h6l14 -37h-32zM872 270h-57v-108h19v45h26q19 0 19 -19v-26h26q-6 6 -6 26q0 19 -14 27q14 6 14 23q0 32 -27 32zM860 221h-26v29h26q19 0 19 -12
q0 -17 -19 -17zM954 270h-41v-108h41q52 0 52 59q0 49 -52 49zM954 182h-16v68h8q35 0 35 -35q0 -12 -8.5 -22.5t-18.5 -10.5zM1087 250v20h-75v-108h82v20h-58v25h51v18h-51v25h51zM1112 203h-18q0 -41 45 -41t45 35q0 22 -31 28q-35 6 -35 13q0 12 15 12q24 0 24 -12h21
q0 32 -39 32q-11 0 -23 -5.5t-19.5 -13.5t1 -20t35.5 -24q24 0 24 -10q0 -15 -18 -15q-27 0 -27 21zM1206 162v108h-14v-108h14zM1296 176l9 -14h12v59h-45v-18h24q-9 -21 -24 -21q-13 0 -23 11t-10 22q0 13 10 24t23 11q18 0 18 -21h27q-9 41 -45 41q-23 0 -37 -15.5
t-14 -39.5q0 -21 14.5 -37t36.5 -16q18 0 24 14zM1391 203l-45 67h-17v-108h17v67l45 -67h26v108h-26v-67z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="CR" horiz-adv-x="681" 
 />
    <glyph glyph-name="published" horiz-adv-x="1669" 
d="M831 594h-75v-260h-113v717h272q105 0 165.5 -66t60.5 -164q0 -85 -60 -157q-58 -70 -166 -70h-84zM756 938v-233h155q73 -3 103.5 56.5t1 119.5t-100.5 57h-159zM326 190q-207 213 -207 512t207 512q210 213 508 213q297 0 507 -213q209 -212 209 -512t-209 -512
q-207 -210 -507 -210q-301 0 -508 210zM408 1133q-175 -178 -175 -431q0 -252 175 -430q176 -176 426 -176t426 176q174 177 174 430q0 254 -174 431q-175 178 -426 178t-426 -178z" />
    <glyph glyph-name="frenchfranc" horiz-adv-x="1486" 
d="M1407 813l-16 -133q-62 33 -140 33q-133 0 -204 -156q-70 -155 -70 -369v-188h-137v694h-498v-694h-135v1411h792v-135h-657v-447h635v-217q68 238 274 238q45 0 93.5 -12t62.5 -25z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="739" 
d="M655 -127v-135q-248 0 -397 254q-147 252 -147 698q0 449 147 701q149 251 397 251v-135q-185 0 -299 -217q-110 -213 -110 -600q0 -384 110 -600q114 -217 299 -217z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="739" 
d="M84 -262v135q186 0 297 217q113 216 113 600q0 387 -113 600q-111 217 -297 217v135q249 0 395 -251q150 -252 150 -701q0 -446 -150 -698q-146 -254 -395 -254z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="993" 
d="M147 629v129h699v-129h-699z" />
    <glyph glyph-name="at.case" horiz-adv-x="2021" 
d="M1264 471v270v2q0 117 -84 201q-82 82 -199 82q-119 0 -201 -82t-82 -201q0 -118 82 -200t201 -82q111 0 197 86v-146q-100 -65 -197 -65q-170 0 -289 119t-119 288q0 170 119 289t289 119q164 0 283 -113t125 -276v-305q0 -152 163 -152q72 0 127 45.5t82 122.5
q49 140 49 295q0 321 -239 535q-238 215 -570 215q-322 0 -546 -230t-224 -557q0 -323 222 -553q225 -225 548 -225h37v-125h-37q-374 0 -636 262q-259 262 -259 641q0 377 259 645q260 266 636 266q183 4 355.5 -64t301 -184t205.5 -281t74 -345q0 -203 -84 -381
q-42 -93 -121 -148t-180 -55q-132 0 -210 70t-78 217z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="612" 
d="M518 -248h-336v1874h336v-137h-199v-1598h199v-139z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="612" 
d="M94 1626h336v-1874h-336v139h199v1598h-199v137z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="811" 
d="M352 496v-349q0 -116 76.5 -193t187.5 -77h111v-125h-111q-167 0 -284 111q-115 109 -115 284v349q0 56 -39 98.5t-92 42.5v117q53 0 92 42.5t39 98.5v352q-3 176 115 285q116 110 284 110h111v-124h-111q-111 0 -187.5 -77.5t-76.5 -193.5v-352q0 -64 -33.5 -121
t-87.5 -80q50 -19 85.5 -77.5t35.5 -120.5z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="811" 
d="M459 147v349q0 62 35.5 120.5t85.5 77.5q-54 23 -87.5 80t-33.5 121v352q0 116 -76.5 193.5t-187.5 77.5h-111v124h111q168 0 284 -110q118 -109 115 -285v-352q0 -56 39 -98.5t92 -42.5v-117q-53 0 -92 -42.5t-39 -98.5v-349q0 -176 -117 -284q-114 -111 -282 -111h-111
v125h111q111 0 187.5 77t76.5 193z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="524" 
d="M342 1389q32 -31 32 -76t-32 -76t-80 -31t-80 31t-32 76t32 76t80 31t80 -31zM207 1020h110l13 -1020h-135z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="980" 
d="M406 266l-283 424l283 424h126l-282 -424l282 -424h-126zM713 266l-283 424l283 424h127l-283 -424l283 -424h-127z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="520" 
d="M180 678q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="980" 
d="M575 1114l283 -424l-283 -424h-126l282 424l-282 424h126zM268 1114l283 -424l-283 -424h-127l283 424l-283 424h127z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="997" 
d="M766 377h135q0 -176 -117 -287q-114 -108 -282 -108q-170 0 -293 108q-123 111 -123 287q0 138 81.5 241.5t211.5 141.5q88 26 132 75t44 134v59h135v-59q0 -256 -270 -338q-87 -27 -143 -95.5t-56 -158.5q0 -114 84 -189q83 -71 197 -71q112 0 188 72.5t76 187.5z
M702 1397q32 -31 32 -76t-32 -76t-79.5 -31t-79.5 31t-32 76t32 76t79.5 31t79.5 -31z" />
    <glyph glyph-name="endash.case" horiz-adv-x="1222" 
d="M150 629v121h923v-121h-923z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1665" 
d="M150 629v121h1366v-121h-1366z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="937" 
d="M260 504q-86 80 -86 201q0 120 86 200t209 80t209 -80t86 -200q0 -121 -86 -201t-209 -80t-209 80z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="673" 
d="M406 266l-283 424l283 424h126l-282 -424l282 -424h-126z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="673" 
d="M268 1114l283 -424l-283 -424h-127l283 424l-283 424h127z" />
    <glyph glyph-name="cent.case" horiz-adv-x="1071" 
d="M522 1421h103v-217q191 -9 323 -129l-96 -96q-104 90 -248 90q-154 0 -260 -100q-104 -98 -104 -260q0 -164 104 -265q108 -102 260 -102q145 0 248 92l98 -96q-134 -122 -325 -131v-230h-103v236q-181 27 -301 162q-117 135 -117 334q0 197 117 329q121 133 301 160v223
z" />
    <glyph glyph-name="zero.tnum" horiz-adv-x="1187" 
d="M111 678q0 305 96 493q112 207 389 207q104 3 186.5 -29t137.5 -91.5t89 -136t48 -169.5q22 -126 22 -274q0 -161 -22 -278t-74 -214q-52 -102 -149 -153q-100 -53 -238 -53t-238 53q-72 43 -121 107t-75.5 150t-37.5 177.5t-13 210.5zM246 678q0 -261 78 -414
q76 -149 272 -149q204 0 281 166q67 149 67 397q0 261 -78 414q-77 151 -270 151q-187 0 -268.5 -144t-81.5 -421z" />
    <glyph glyph-name="one.tnum" horiz-adv-x="1187" 
d="M717 0h-135v1186l-232 -80v123l367 133v-1362z" />
    <glyph glyph-name="two.tnum" horiz-adv-x="1187" 
d="M180 0v47l570 690q110 131 110 252q0 114 -79.5 184t-202.5 70q-112 0 -193 -60.5t-94 -158.5l-125 33q24 147 139 233q113 88 273 88q177 0 296 -110q121 -109 121 -279q0 -179 -147 -348l-428 -506h577v-135h-817z" />
    <glyph glyph-name="three.tnum" horiz-adv-x="1187" 
d="M322 1120l-109 68q56 95 159.5 147t223.5 45q189 -15 283 -125q97 -109 82 -262q-6 -82 -59 -153.5t-134 -106.5q116 -27 186 -141q71 -115 62 -236q-15 -168 -158 -278q-144 -108 -330 -96q-130 10 -234 78t-151 173l123 56q31 -70 105.5 -117.5t167.5 -54.5h-2
q130 -9 231 67q102 77 111 193q3 116 -88 201q-89 86 -218 86h-131v129h131q105 -3 175 61q72 66 75 160q5 98 -56.5 160t-178.5 69q-85 5 -158 -29t-108 -94z" />
    <glyph glyph-name="four.tnum" horiz-adv-x="1187" 
d="M1106 291h-205v-291h-135v291h-713l748 1071h100v-946h205v-125zM766 1092l-465 -676h465v676z" />
    <glyph glyph-name="five.tnum" horiz-adv-x="1187" 
d="M117 266l116 84q40 -99 136 -164q100 -65 204 -65q157 0 249.5 85.5t92.5 221.5q0 137 -94 213q-96 78 -213 78q-92 0 -176 -23q-65 -19 -199 -84l-53 72l119 678h668v-137h-551l-82 -451q173 84 274 84q189 0 318 -117q131 -113 131 -313q0 -206 -131 -328
q-132 -120 -353 -120q-143 0 -268 79q-126 79 -188 207z" />
    <glyph glyph-name="six.tnum" horiz-adv-x="1187" 
d="M1024 1149l-119 -51q-65 136 -241 145q-184 6 -267 -153q-79 -155 -73 -400q31 83 124.5 128t198.5 40q182 -9 301 -143q120 -132 111 -314q-9 -185 -137 -307q-126 -123 -310 -114q-179 8 -293 130t-131 328q-15 306 23 533q30 184 154 299q126 114 305 108
q130 -6 216 -58t138 -171zM326 432q-3 -130 82 -221q88 -91 210 -94q127 -3 211 78q89 83 95 213q3 131 -76 219q-82 91 -205 94q-126 3 -221 -80q-93 -84 -96 -209z" />
    <glyph glyph-name="seven.tnum" horiz-adv-x="1187" 
d="M1085 1362l-55 -105l-694 -1257h-160l678 1227h-672v135h903z" />
    <glyph glyph-name="eight.tnum" horiz-adv-x="1187" 
d="M244 1044q0 143 100 236q101 98 248 98q151 0 252 -98q102 -95 102 -236q0 -155 -125 -253q107 -57 169.5 -158t62.5 -219q0 -183 -133 -310q-130 -124 -328 -124t-328 124q-131 125 -131 310q0 118 61.5 219t167.5 158q-118 93 -118 253zM268 414q0 -124 92 -213
q95 -86 232 -86q136 0 231 86q95 89 95 213q0 126 -95 215q-96 90 -231 90h-14q-131 -3 -222 -94q-88 -88 -88 -211zM379 1044q0 -82 58.5 -141.5t144.5 -60.5h18q90 1 150.5 60t60.5 142q0 85 -62.5 142t-156.5 57q-91 0 -152 -57t-61 -142z" />
    <glyph glyph-name="nine.tnum" horiz-adv-x="1187" 
d="M176 207l119 51q65 -133 254 -145q184 -6 264 151q82 158 76 402q-21 -55 -73.5 -95t-117.5 -58t-133 -15q-181 9 -301 141q-119 134 -110 315q9 185 135 308q129 123 311 114q179 -8 293 -130t131 -328q15 -306 -23 -533q-30 -186 -155 -299q-124 -115 -303 -109
q-272 12 -367 230zM887 924q3 131 -84 221q-85 91 -209 94t-213 -80q-86 -83 -92 -211q-3 -131 76 -219q82 -91 204 -94q129 -3 219 78q96 84 99 211z" />
    <glyph glyph-name="zero.taboldstyle" horiz-adv-x="1187" 
d="M592 -20q-205 0 -352 147q-146 152 -146 385q0 237 146 383q145 145 352 145q209 0 354 -145q146 -146 146 -383q0 -234 -148 -385q-144 -147 -352 -147zM592 115q151 0 258 110q106 109 106 287q0 179 -106 285q-108 108 -258 108t-258 -108q-105 -108 -105 -285
t107 -287t256 -110z" />
    <glyph glyph-name="one.taboldstyle" horiz-adv-x="1187" 
d="M317 858l453 191v-1049h-135v846l-318 -121v133z" />
    <glyph glyph-name="two.taboldstyle" horiz-adv-x="1187" 
d="M211 0v47l504 545q86 83 86 164q0 72 -58.5 118t-150.5 46q-88 0 -145.5 -41.5t-63.5 -112.5q-129 27 -131 27q12 117 111 190.5t237 73.5q146 0 246 -86q98 -82 98 -215q0 -141 -125 -260l-346 -361h473v-135h-735z" />
    <glyph glyph-name="six.taboldstyle" horiz-adv-x="1187" 
d="M1024 1149l-119 -51q-65 136 -241 145q-184 6 -267 -153q-79 -155 -73 -400q31 83 124.5 128t198.5 40q182 -9 301 -143q120 -132 111 -314q-9 -185 -137 -307q-126 -123 -310 -114q-179 8 -293 130t-131 328q-15 306 23 533q30 184 154 299q126 114 305 108
q130 -6 216 -58t138 -171zM326 432q-3 -130 82 -221q88 -91 210 -94q127 -3 211 78q89 83 95 213q3 131 -76 219q-82 91 -205 94q-126 3 -221 -80q-93 -84 -96 -209z" />
    <glyph glyph-name="eight.taboldstyle" horiz-adv-x="1187" 
d="M244 1044q0 143 100 236q101 98 248 98q151 0 252 -98q102 -95 102 -236q0 -155 -125 -253q107 -57 169.5 -158t62.5 -219q0 -183 -133 -310q-130 -124 -328 -124t-328 124q-131 125 -131 310q0 118 61.5 219t167.5 158q-118 93 -118 253zM268 414q0 -124 92 -213
q95 -86 232 -86q136 0 231 86q95 89 95 213q0 126 -95 215q-96 90 -231 90h-14q-131 -3 -222 -94q-88 -88 -88 -211zM379 1044q0 -82 58.5 -141.5t144.5 -60.5h18q90 1 150.5 60t60.5 142q0 85 -62.5 142t-156.5 57q-91 0 -152 -57t-61 -142z" />
    <glyph glyph-name="three.taboldstyle" horiz-adv-x="1187" 
d="M326 709l-109 67q56 95 159.5 147.5t223.5 45.5q189 -15 283 -125q97 -109 82 -262q-6 -82 -59 -153.5t-134 -106.5q115 -27 186 -142t62 -235q-15 -170 -158 -277q-143 -110 -330 -98q-130 10 -234 78.5t-151 173.5l123 55q31 -70 105.5 -117.5t167.5 -54.5h-2
q129 -9 231 68t111 192q3 116 -88 201q-89 86 -217 86h-132v129h132q104 -3 174 61q72 66 75 160q5 98 -56.5 160t-178.5 69q-85 5 -158 -28.5t-108 -93.5z" />
    <glyph glyph-name="four.taboldstyle" horiz-adv-x="1187" 
d="M1106 -119h-205v-291h-135v291h-713l748 1071h100v-946h205v-125zM766 682l-465 -676h465v676z" />
    <glyph glyph-name="five.taboldstyle" horiz-adv-x="1187" 
d="M117 -139l116 84q40 -99 136 -164q101 -66 204 -66q157 0 249.5 86t92.5 222q0 137 -94 213q-95 77 -213 77q-96 0 -176 -22q-65 -19 -199 -84l-53 72l119 677h668v-137h-551l-82 -450q173 84 274 84q189 0 318 -117q131 -113 131 -313q0 -207 -131 -326
q-132 -123 -353 -123q-141 0 -268 80q-126 79 -188 207z" />
    <glyph glyph-name="seven.taboldstyle" horiz-adv-x="1187" 
d="M182 952h903l-749 -1362h-160l678 1227h-672v135z" />
    <glyph glyph-name="nine.taboldstyle" horiz-adv-x="1187" 
d="M182 -203l119 51q65 -133 254 -145q184 -6 264 154q82 155 76 399q-21 -55 -73.5 -95t-117.5 -58t-133 -15q-181 9 -301 141q-119 134 -110 316q9 184 135 307q130 124 311 115q179 -8 293 -130.5t131 -328.5q15 -311 -22 -533q-30 -185 -156 -299q-123 -114 -303 -108
q-273 12 -367 229zM893 514q3 131 -84 221q-85 91 -209 94q-125 3 -213 -79q-86 -83 -92 -211q-3 -132 76 -220q82 -91 204 -94q130 -3 220 78q95 83 98 211z" />
    <glyph glyph-name="zero.oldstyle" horiz-adv-x="1226" 
d="M612 -20q-205 0 -352 147q-145 151 -145 385q0 238 145 383t352 145q210 0 355 -145t145 -383q0 -235 -147 -385q-144 -147 -353 -147zM612 115q151 0 258 110t107 287q0 178 -107 285q-108 108 -258 108t-258 -108q-104 -107 -104 -285t106 -287q107 -110 256 -110z" />
    <glyph glyph-name="one.oldstyle" horiz-adv-x="724" 
d="M86 858l453 191v-1049h-136v846l-317 -121v133z" />
    <glyph glyph-name="two.oldstyle" horiz-adv-x="962" 
d="M98 0v47l504 545q86 83 86 164q0 72 -58.5 118t-150.5 46q-88 0 -145.5 -41.5t-63.5 -112.5q-129 27 -131 27q12 117 111 190.5t237 73.5q146 0 246 -86q98 -82 98 -215q0 -142 -124 -260l-347 -361h474v-135h-736z" />
    <glyph glyph-name="three.oldstyle" horiz-adv-x="1054" 
d="M260 709l-108 67q56 95 159.5 147.5t223.5 45.5q188 -15 282 -125q97 -109 82 -262q-6 -82 -58.5 -153.5t-133.5 -106.5q115 -27 186 -142q70 -113 61 -235q-15 -171 -157 -277q-143 -110 -330 -98q-130 10 -234 78.5t-151 173.5l123 55q31 -70 105 -117.5t167 -54.5h-2
q130 -9 232 68q101 76 110 192q3 116 -88 201q-89 86 -217 86h-131v129h131q104 -3 174 61q73 67 76 160q5 98 -57 160t-179 69q-85 5 -158 -28.5t-108 -93.5z" />
    <glyph glyph-name="four.oldstyle" horiz-adv-x="1177" 
d="M1102 -119h-205v-291h-135v291h-713l748 1071h100v-946h205v-125zM762 682l-465 -676h465v676z" />
    <glyph glyph-name="five.oldstyle" horiz-adv-x="1116" 
d="M82 -139l117 84q41 -100 135 -164q101 -66 205 -66q157 0 249.5 86t92.5 222t-95 213t-213 77q-96 0 -176 -22q-62 -18 -198 -84l-54 72l119 677h668v-137h-551l-82 -450q173 84 274 84q189 0 318 -117q131 -113 131 -313q0 -207 -131 -326q-132 -123 -352 -123
q-142 0 -269 80q-126 79 -188 207z" />
    <glyph glyph-name="six.oldstyle" horiz-adv-x="1138" 
d="M999 1149l-118 -51q-65 136 -242 145q-184 6 -266 -153q-80 -157 -74 -400q21 55 73.5 95t117.5 58t133 15q182 -9 301 -143q119 -131 110 -314q-9 -185 -137 -307q-126 -123 -309 -114q-179 8 -293 130t-131 328q-15 311 22 533q30 184 154 299q126 114 305 108
q130 -6 216 -58t138 -171zM301 432q-3 -130 82 -221q88 -91 211 -94q127 -3 211 78q88 82 94 213q3 131 -76 219q-82 91 -205 94q-126 3 -221 -80q-93 -84 -96 -209z" />
    <glyph glyph-name="seven.oldstyle" horiz-adv-x="1021" 
d="M100 952h904l-750 -1362h-160l678 1227h-672v135z" />
    <glyph glyph-name="eight.oldstyle" horiz-adv-x="1157" 
d="M229 1044q0 142 101 236q101 98 248 98q150 0 251 -98q103 -95 103 -236q0 -155 -125 -253q106 -57 168.5 -158t62.5 -219q0 -183 -133 -310q-130 -124 -327 -124q-198 0 -328 124q-131 125 -131 310q0 118 61.5 219t167.5 158q-119 94 -119 253zM254 414q0 -124 92 -213
q95 -86 232 -86q136 0 231 86q94 88 94 213q0 127 -94 215q-96 90 -231 90h-15q-130 -3 -221 -94q-88 -88 -88 -211zM365 1044q0 -82 58 -141.5t144 -60.5h19q90 1 150.5 60t60.5 142q0 85 -62.5 142t-156.5 57q-91 0 -152 -57t-61 -142z" />
    <glyph glyph-name="nine.oldstyle" horiz-adv-x="1095" 
d="M137 -203l119 51q65 -133 254 -145q184 -6 264 154q82 155 76 399q-21 -55 -73.5 -95t-117.5 -58t-133 -15q-181 9 -301 141q-119 134 -110 316q9 184 135 307q130 124 311 115q179 -8 293 -130.5t131 -328.5q15 -311 -22 -533q-30 -185 -156 -299q-123 -114 -303 -108
q-273 12 -367 229zM848 514q3 131 -84 221q-85 91 -209 94q-125 3 -213 -79q-86 -83 -92 -211q-3 -132 76 -220q82 -91 204 -94q130 -3 220 78q95 83 98 211z" />
    <glyph glyph-name="Lslash.sc" horiz-adv-x="888" 
d="M203 1200h123v-434l186 117v-105l-186 -119v-536h505v-123h-628v582l-117 -72v104l117 74v512z" />
    <glyph glyph-name="Zcaron.sc" horiz-adv-x="1019" 
d="M94 0v119l670 958h-645v123h796v-121l-671 -956h671v-123h-821zM258 1561h123l123 -152l125 152h123l-170 -240h-154z" />
    <glyph glyph-name="exclam.sc" horiz-adv-x="520" 
d="M211 322l-12 878h123l-13 -878h-98zM360 78q0 -41 -28.5 -67.5t-71.5 -26.5q-42 0 -70 26.5t-28 67.5q-4 56 47 79.5t102.5 0t48.5 -79.5z" />
    <glyph glyph-name="ampersand.sc" horiz-adv-x="1198" 
d="M518 739h266v154h119v-156h158v-106h-158v-264q0 -137 42 -203t149 -66v-114q-98 0 -170 42.5t-97 112.5q-41 -69 -128 -112t-185 -43q-172 0 -283 108q-108 111 -108 275q0 102 53 189t146 138q-103 83 -103 224q0 124 88 206q89 86 219 86q89 0 162 -41.5t115 -111.5
l-103 -64q-60 97 -174 97q-81 0 -133.5 -49t-52.5 -123q0 -76 50.5 -127.5t127.5 -51.5zM784 354v277h-270q-112 0 -192 -80q-78 -78 -78 -184q0 -113 76 -188t194 -75q114 0 189.5 71.5t80.5 178.5z" />
    <glyph glyph-name="question.sc" horiz-adv-x="882" 
d="M221 870h-123q0 153 99 248q98 94 241 94q144 0 248 -94q107 -94 107 -244q0 -115 -70.5 -203.5t-179.5 -121.5q-79 -25 -112.5 -57t-33.5 -97v-73h-121v73q0 110 56.5 172t173.5 99q71 21 117.5 78t46.5 130q0 95 -68 155.5t-164 60.5q-93 0 -155 -61.5t-62 -158.5z
M238 76q0 41 28 67.5t70 26.5q43 0 71.5 -26.5t28.5 -67.5q3 -56 -48.5 -79.5t-102.5 0t-47 79.5z" />
    <glyph glyph-name="A.sc" horiz-adv-x="1161" 
d="M582 1030l-232 -602h461zM854 313h-547l-121 -313h-131l461 1200h131l459 -1200h-131z" />
    <glyph glyph-name="B.sc" horiz-adv-x="1124" 
d="M946 899q0 -205 -147 -260q89 -18 149 -109q62 -89 62 -192q0 -142 -92 -242q-90 -96 -246 -96h-479v1200h440q146 0 229.5 -80.5t83.5 -220.5zM317 1079v-387h326q85 0 132.5 58t47.5 149q0 85 -55.5 132.5t-149.5 47.5h-301zM664 578h-347v-457h353q95 0 156 62.5
t61 154.5q0 86 -64 162q-66 78 -159 78z" />
    <glyph glyph-name="C.sc" horiz-adv-x="1200" 
d="M1044 252l86 -86q-175 -184 -432 -184q-250 0 -419 178q-166 181 -166 438q0 258 166 436q169 178 419 178q257 0 430 -182l-86 -84q-146 146 -344 146q-199 0 -331 -144q-131 -143 -131 -350q0 -209 131 -352q133 -142 331 -142q201 0 346 148z" />
    <glyph glyph-name="D.sc" horiz-adv-x="1294" 
d="M643 1077h-328v-954h328q195 0 302.5 128.5t107.5 346.5q0 215 -107 346q-109 133 -303 133zM643 0h-450v1200h450q248 0 389 -170q144 -170 144 -432q0 -260 -144 -430q-140 -168 -389 -168z" />
    <glyph glyph-name="E.sc" horiz-adv-x="1015" 
d="M899 0h-706v1200h688v-121h-566v-430h496v-121h-496v-407h584v-121z" />
    <glyph glyph-name="F.sc" horiz-adv-x="946" 
d="M315 0h-122v1200h677v-121h-555v-430h441v-121h-441v-528z" />
    <glyph glyph-name="G.sc" horiz-adv-x="1355" 
d="M1137 477h-349v113h486q0 -297 -146.5 -452.5t-412.5 -155.5q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q217 0 387 -141l-88 -86q-128 107 -299 107q-202 0 -342 -146q-137 -143 -137 -348q0 -207 137 -350q138 -144 342 -144q188 0 299.5 99.5
t122.5 273.5z" />
    <glyph glyph-name="H.sc" horiz-adv-x="1275" 
d="M961 653v547h122v-1200h-122v539h-646v-539h-122v1200h122v-547h646z" />
    <glyph glyph-name="I.sc" horiz-adv-x="507" 
d="M193 0v1200h122v-1200h-122z" />
    <glyph glyph-name="J.sc" horiz-adv-x="507" 
d="M-156 -432l13 121q41 -15 100 -15q118 0 177 70.5t59 192.5v1263h122v-1263q0 -177 -90 -280t-268 -103q-64 0 -113 14z" />
    <glyph glyph-name="K.sc" horiz-adv-x="1017" 
d="M903 1200l-465 -600l568 -600h-166l-525 555v-555h-122v1200h122v-573l443 573h145z" />
    <glyph glyph-name="L.sc" horiz-adv-x="878" 
d="M821 0h-628v1200h122v-1079h506v-121z" />
    <glyph glyph-name="M.sc" horiz-adv-x="1679" 
d="M389 1200l451 -926l448 926h121l147 -1200h-120l-121 979l-414 -848h-121l-415 848l-121 -979h-121l145 1200h121z" />
    <glyph glyph-name="N.sc" horiz-adv-x="1232" 
d="M918 1200h122v-1200h-125l-600 987v-987h-122v1200h129l596 -981v981z" />
    <glyph glyph-name="O.sc" horiz-adv-x="1433" 
d="M236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 1212q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180q-254 0 -428 180t-174 436
q0 257 174 434q174 180 428 180z" />
    <glyph glyph-name="P.sc" horiz-adv-x="1089" 
d="M666 1079h-349v-493h340q111 0 168.5 67.5t57.5 177.5q0 108 -56.5 178t-160.5 70zM317 0h-124v1200h473q174 0 256 -108q84 -110 84 -261q0 -152 -84 -262q-82 -104 -256 -104h-349v-465z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="1433" 
d="M1026 68l129 -201l-100 -66l-137 215q-98 -34 -203 -34q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q256 0 430 -180q176 -179 176 -434q0 -164 -79 -304t-216 -226zM715 338l100 65l146 -233q110 67 173.5 180.5t63.5 247.5q0 203 -139 348
q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348q0 -204 135 -348q138 -141 340 -144q78 0 137 15z" />
    <glyph glyph-name="R.sc" horiz-adv-x="1101" 
d="M639 1079h-322v-493h314q111 0 167 67.5t56 177.5t-54.5 179t-160.5 69zM659 465l330 -465h-151l-328 465h-193v-465h-124v1200h448q173 0 254 -106q82 -107 82 -263q0 -159 -81 -259t-237 -107z" />
    <glyph glyph-name="S.sc" horiz-adv-x="1032" 
d="M119 344h123q0 -97 75 -172q76 -76 195 -76q118 0 194.5 58.5t90.5 142.5q27 190 -224 248l-159 37q-285 64 -285 303q0 144 113 235q115 92 266 92q163 0 270 -94t107 -250h-123q0 99 -71.5 161.5t-182.5 62.5q-102 0 -180 -60q-76 -58 -76 -147q0 -146 188 -187
l164 -36q168 -38 253 -135.5t61 -250.5q-20 -116 -130 -206q-104 -88 -276 -88q-170 0 -283 110q-110 113 -110 252z" />
    <glyph glyph-name="T.sc" horiz-adv-x="999" 
d="M438 1077h-381v123h885v-123h-381v-1077h-123v1077z" />
    <glyph glyph-name="U.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220z" />
    <glyph glyph-name="V.sc" horiz-adv-x="1001" 
d="M438 0l-360 1200h127l297 -997l295 997h127l-359 -1200h-127z" />
    <glyph glyph-name="W.sc" horiz-adv-x="1626" 
d="M860 1040l244 -845l330 1005h131l-406 -1200h-115l-231 795l-231 -795h-115l-406 1200h132l329 -1005l244 845h94z" />
    <glyph glyph-name="X.sc" horiz-adv-x="1077" 
d="M838 1200h147l-373 -555l422 -645h-149l-346 543l-346 -543h-148l420 645l-373 555h150l297 -450z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="1118" 
d="M1079 1200l-456 -643v-557h-127v557l-451 643h148l366 -512l373 512h147z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="1019" 
d="M94 0v119l670 958h-645v123h796v-121l-671 -956h671v-123h-821z" />
    <glyph glyph-name="Scaron.sc" horiz-adv-x="1032" 
d="M272 1591h123l123 -149l125 149h121l-168 -239h-154zM242 344q0 -97 75 -172q76 -76 195 -76q118 0 194.5 58.5t90.5 142.5q27 190 -224 248l-159 37q-285 64 -285 303q0 144 113 235q115 92 266 92q163 0 270 -94t107 -250h-123q0 99 -71.5 161.5t-182.5 62.5
q-102 0 -180 -60q-76 -58 -76 -147q0 -146 188 -187l164 -36q168 -38 253 -135.5t61 -250.5q-20 -116 -130 -206q-104 -88 -276 -88q-170 0 -283 110q-110 113 -110 252h123z" />
    <glyph glyph-name="OE.sc" horiz-adv-x="2019" 
d="M715 106q197 0 336 136q136 133 145 333v43q-9 198 -145 334q-138 138 -336 138q-204 0 -342 -144q-137 -143 -137 -348t137 -348q138 -144 342 -144zM1196 971v229h688v-123h-565v-428h493v-123h-493v-403h584v-123h-707v225q-86 -114 -211 -178.5t-270 -64.5
q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q146 0 270.5 -63.5t210.5 -177.5z" />
    <glyph glyph-name="Ydieresis.sc" horiz-adv-x="1118" 
d="M1079 1200l-456 -643v-557h-127v557l-451 643h148l366 -512l373 512h147zM317 1466q-4 54 44 76.5t96.5 0t44.5 -76.5q0 -37 -27 -61.5t-65 -24.5q-39 0 -66 25t-27 61zM715 1554q39 0 65.5 -25t26.5 -63q0 -37 -27 -61.5t-65 -24.5q-37 0 -64.5 25t-27.5 61t27.5 62
t64.5 26z" />
    <glyph glyph-name="exclamdown.sc" horiz-adv-x="518" 
d="M307 879l12 -879h-122l12 879h98zM358 1122q3 -56 -48.5 -79.5t-102.5 0t-47 79.5q0 41 28 68t70 27q43 0 71.5 -27t28.5 -68z" />
    <glyph glyph-name="questiondown.sc" horiz-adv-x="870" 
d="M680 -59h123q0 -152 -98 -246t-242 -94q-146 0 -250 94q-104 91 -104 242q0 119 68.5 205.5t180.5 119.5q146 44 146 170v60h121v-60q0 -215 -230 -285q-71 -21 -118.5 -79t-47.5 -131q0 -92 69 -154t165 -62q93 0 155 61.5t62 158.5zM664 737q0 -41 -29 -67.5t-72 -26.5
q-42 0 -70 26.5t-28 67.5q-4 56 47.5 79.5t103 0t48.5 -79.5z" />
    <glyph glyph-name="Agrave.sc" horiz-adv-x="1161" 
d="M514 1561l131 -240h-125l-155 240h149zM582 1030l-232 -602h461zM854 313h-547l-121 -313h-131l461 1200h131l459 -1200h-131z" />
    <glyph glyph-name="Aacute.sc" horiz-adv-x="1161" 
d="M643 1561h150l-156 -240h-125zM811 428l-229 602l-232 -602h461zM186 0h-131l461 1200h131l459 -1200h-131l-121 313h-547z" />
    <glyph glyph-name="Acircumflex.sc" horiz-adv-x="1161" 
d="M657 1561l170 -240h-122l-125 149l-123 -149h-123l170 240h153zM582 1030l-232 -602h461zM854 313h-547l-121 -313h-131l461 1200h131l459 -1200h-131z" />
    <glyph glyph-name="Atilde.sc" horiz-adv-x="1161" 
d="M444 1362h-92q6 95 47.5 142.5t102.5 47.5q43 0 88 -36q46 -37 69 -37q22 0 39.5 18.5t18.5 54.5h92q-6 -94 -47 -140t-103 -46q-44 0 -90 37t-67 37q-22 0 -39.5 -21t-18.5 -57zM582 1030l-232 -602h461zM854 313h-547l-121 -313h-131l461 1200h131l459 -1200h-131z" />
    <glyph glyph-name="Adieresis.sc" horiz-adv-x="1161" 
d="M334 1466q-4 54 44 76.5t96 0t44 -76.5q0 -37 -27 -61.5t-65 -24.5t-65 24.5t-27 61.5zM731 1554q39 0 65.5 -25t26.5 -63q0 -37 -27 -61.5t-65 -24.5q-37 0 -64.5 25t-27.5 61t27.5 62t64.5 26zM811 428l-229 602l-232 -602h461zM186 0h-131l461 1200h131l459 -1200
h-131l-121 313h-547z" />
    <glyph glyph-name="Aring.sc" horiz-adv-x="1161" 
d="M578 1659q73 0 123.5 -50t50.5 -122t-50 -121t-124 -49q-72 0 -121 49t-49 121t49 122t121 50zM659 1487q0 35 -23 58.5t-56 23.5q-35 0 -58 -25t-22.5 -56t22.5 -58q28 -23 58.5 -23t54.5 23.5t24 56.5zM811 428l-229 602l-232 -602h461zM186 0h-131l461 1200h131
l459 -1200h-131l-121 313h-547z" />
    <glyph glyph-name="AE.sc" horiz-adv-x="1685" 
d="M862 428v637l-405 -637h405zM862 0v313h-477l-199 -313h-145l758 1200h751v-123h-565v-428h494v-123h-494v-403h584v-123h-707z" />
    <glyph glyph-name="Ccedilla.sc" horiz-adv-x="1204" 
d="M1047 252l86 -86q-167 -176 -410 -182l43 -101q17 -41 14 -88q-6 -67 -61 -111.5t-127 -37.5q-63 6 -108.5 58.5t-45.5 123.5l97 -8q-2 -43 33.5 -69t72.5 -9q30 12 42 43.5t-1 60.5l-59 142q-224 27 -367 198q-141 176 -141 412q0 258 166 436q169 178 419 178
q257 0 430 -182l-86 -84q-146 146 -344 146q-199 0 -331 -144q-131 -143 -131 -350q0 -209 131 -352q133 -142 331 -142q202 0 347 148z" />
    <glyph glyph-name="Egrave.sc" horiz-adv-x="1015" 
d="M492 1561l129 -240h-125l-156 240h152zM899 0h-706v1200h688v-121h-566v-430h496v-121h-496v-407h584v-121z" />
    <glyph glyph-name="Eacute.sc" horiz-adv-x="1015" 
d="M557 1561h150l-156 -240h-125zM193 1200h688v-121h-566v-430h496v-121h-496v-407h584v-121h-706v1200z" />
    <glyph glyph-name="Ecircumflex.sc" horiz-adv-x="1015" 
d="M588 1561l170 -240h-123l-123 149l-125 -149h-121l170 240h152zM899 0h-706v1200h688v-121h-566v-430h496v-121h-496v-407h584v-121z" />
    <glyph glyph-name="Edieresis.sc" horiz-adv-x="1015" 
d="M266 1466q0 38 26.5 63t65.5 25q37 0 65 -26t28 -62t-28 -61t-65 -25q-38 0 -65 24.5t-27 61.5zM571 1466q-4 54 44.5 76.5t96.5 0t44 -76.5q0 -37 -27 -61.5t-65 -24.5q-39 0 -66 25t-27 61zM193 1200h688v-121h-566v-430h496v-121h-496v-407h584v-121h-706v1200z" />
    <glyph glyph-name="Igrave.sc" horiz-adv-x="507" 
d="M186 1561l129 -240h-125l-155 240h151zM193 0v1200h122v-1200h-122z" />
    <glyph glyph-name="Iacute.sc" horiz-adv-x="507" 
d="M319 1561h152l-156 -240h-125zM315 1200v-1200h-122v1200h122z" />
    <glyph glyph-name="Icircumflex.sc" horiz-adv-x="507" 
d="M332 1561l170 -240h-123l-125 149l-123 -149h-123l170 240h154zM193 0v1200h122v-1200h-122z" />
    <glyph glyph-name="Idieresis.sc" horiz-adv-x="507" 
d="M10 1466q0 38 26.5 63t65.5 25q37 0 65 -26t28 -62t-28 -61t-65 -25q-38 0 -65 24.5t-27 61.5zM408 1554q39 0 65.5 -25t26.5 -63q0 -37 -27 -61.5t-65 -24.5q-37 0 -65 25t-28 61t28 62t65 26zM315 1200v-1200h-122v1200h122z" />
    <glyph glyph-name="Eth.sc" horiz-adv-x="1302" 
d="M651 1077h-327v-424h297v-108h-297v-422h327q195 0 302.5 128.5t107.5 346.5q0 215 -107 346q-109 133 -303 133zM651 0h-450v545h-170v108h170v547h450q248 0 389 -170q144 -170 144 -432q0 -260 -144 -430q-140 -168 -389 -168z" />
    <glyph glyph-name="Ntilde.sc" horiz-adv-x="1232" 
d="M537 1440q-21 0 -39 -21t-19 -57h-90q6 95 47 142.5t101 47.5q43 0 88 -36q46 -37 69 -37t40.5 19t17.5 54h92q-6 -94 -47 -140t-103 -46q-44 0 -90 37t-67 37zM918 1200h122v-1200h-125l-600 987v-987h-122v1200h129l596 -981v981z" />
    <glyph glyph-name="Ograve.sc" horiz-adv-x="1433" 
d="M662 1561l129 -240h-123l-158 240h152zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 1212q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436
q-174 -180 -430 -180q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180z" />
    <glyph glyph-name="Oacute.sc" horiz-adv-x="1433" 
d="M762 1561h149l-155 -240h-125zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 -18q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q256 0 430 -180
q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180z" />
    <glyph glyph-name="Ocircumflex.sc" horiz-adv-x="1433" 
d="M793 1561l170 -240h-123l-125 149l-123 -149h-123l170 240h154zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 1212q256 0 430 -180q176 -179 176 -434
q0 -254 -176 -436q-174 -180 -430 -180q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180z" />
    <glyph glyph-name="Otilde.sc" horiz-adv-x="1433" 
d="M637 1440q-21 0 -38.5 -21t-18.5 -57h-91q6 95 47 142.5t101 47.5q43 0 88 -36q46 -37 70 -37q23 0 40 18.5t17 54.5h92q-6 -94 -47 -140t-102 -46q-44 0 -90 37t-68 37zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348
q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 1212q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180z" />
    <glyph glyph-name="Odieresis.sc" horiz-adv-x="1433" 
d="M473 1466q0 38 26.5 63t65.5 25q37 0 64.5 -26t27.5 -62t-27.5 -61t-64.5 -25q-38 0 -65 24.5t-27 61.5zM778 1466q-4 54 44 76.5t96.5 0t44.5 -76.5q0 -36 -27 -61t-66 -25q-38 0 -65 24.5t-27 61.5zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144
q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 -18q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180z" />
    <glyph glyph-name="Oslash.sc" horiz-adv-x="1431" 
d="M713 104q206 0 344 144q139 145 139 350q0 178 -106 311l-678 -698q128 -107 301 -107zM233 598q0 -181 111 -319l680 698q-139 115 -311 115q-202 0 -342 -146q-138 -144 -138 -348zM326 123l-140 -143l-67 67l139 143q-147 176 -147 408q0 257 174 434q174 180 428 180
q227 0 397 -147l147 154l68 -68l-149 -156q143 -168 143 -397q0 -254 -176 -436q-174 -180 -430 -180q-217 0 -387 141z" />
    <glyph glyph-name="Ugrave.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM707 1321h-123l-158 240h152z" />
    <glyph glyph-name="Uacute.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM653 1561h152l-158 -240h-123z" />
    <glyph glyph-name="Ucircumflex.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM688 1561l170 -240h-123l-125 149l-123 -149h-122l170 240h153z" />
    <glyph glyph-name="Udieresis.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM453 1554q39 0 65.5 -25t26.5 -63q0 -37 -27 -61.5t-65 -24.5q-37 0 -64 25t-27 61t27 62
t64 26zM668 1466q0 38 26.5 63t65.5 25q37 0 64.5 -26t27.5 -62t-27.5 -61t-64.5 -25q-38 0 -65 24.5t-27 61.5z" />
    <glyph glyph-name="Yacute.sc" horiz-adv-x="1118" 
d="M1079 1200l-456 -643v-557h-127v557l-451 643h148l366 -512l373 512h147zM618 1561h150l-156 -240h-125z" />
    <glyph glyph-name="Thorn.sc" horiz-adv-x="1036" 
d="M317 856v-485h275q101 0 168 68.5t67 176.5q0 107 -67.5 173.5t-169.5 66.5h-273zM592 250h-275v-250h-124v1200h124v-221h275q156 0 256 -100q102 -102 102 -263q0 -164 -102 -266q-100 -100 -256 -100z" />
    <glyph glyph-name="Amacron.sc" horiz-adv-x="1161" 
d="M791 1434h-420v114h420v-114zM582 1030l-232 -602h461zM854 313h-547l-121 -313h-131l461 1200h131l459 -1200h-131z" />
    <glyph glyph-name="Abreve.sc" horiz-adv-x="1161" 
d="M362 1583h97q6 -52 38.5 -84.5t82.5 -32.5q53 0 84 33.5t36 83.5h97q0 -98 -59.5 -157.5t-157.5 -59.5q-105 0 -161.5 61t-56.5 156zM811 428l-229 602l-232 -602h461zM186 0h-131l461 1200h131l459 -1200h-131l-121 313h-547z" />
    <glyph glyph-name="Cacute.sc" horiz-adv-x="1200" 
d="M707 1591h151l-158 -239h-122zM1044 252l86 -86q-175 -184 -432 -184q-250 0 -419 178q-166 181 -166 438q0 258 166 436q169 178 419 178q257 0 430 -182l-86 -84q-146 146 -344 146q-199 0 -331 -144q-131 -143 -131 -350q0 -209 131 -352q133 -142 331 -142
q201 0 346 148z" />
    <glyph glyph-name="Ccircumflex.sc" horiz-adv-x="1200" 
d="M762 1591l170 -239h-123l-125 151l-123 -151h-121l168 239h154zM1044 252l86 -86q-175 -184 -432 -184q-250 0 -419 178q-166 181 -166 438q0 258 166 436q169 178 419 178q257 0 430 -182l-86 -84q-146 146 -344 146q-199 0 -331 -144q-131 -143 -131 -350
q0 -209 131 -352q133 -142 331 -142q201 0 346 148z" />
    <glyph glyph-name="Cdotaccent.sc" horiz-adv-x="1200" 
d="M664 1589q39 0 65.5 -25t26.5 -63q0 -37 -27 -61.5t-65 -24.5q-37 0 -65 25t-28 61t28 62t65 26zM1044 252l86 -86q-175 -184 -432 -184q-250 0 -419 178q-166 181 -166 438q0 258 166 436q169 178 419 178q257 0 430 -182l-86 -84q-146 146 -344 146q-199 0 -331 -144
q-131 -143 -131 -350q0 -209 131 -352q133 -142 331 -142q201 0 346 148z" />
    <glyph glyph-name="Ccaron.sc" horiz-adv-x="1200" 
d="M424 1591h123l125 -149l123 149h123l-170 -239h-154zM1044 252l86 -86q-175 -184 -432 -184q-250 0 -419 178q-166 181 -166 438q0 258 166 436q169 178 419 178q257 0 430 -182l-86 -84q-146 146 -344 146q-199 0 -331 -144q-131 -143 -131 -350q0 -209 131 -352
q133 -142 331 -142q201 0 346 148z" />
    <glyph glyph-name="Dcaron.sc" horiz-adv-x="1294" 
d="M373 1591h123l125 -149l122 149h123l-170 -239h-153zM315 123h328q195 0 302.5 128.5t107.5 346.5q0 215 -107 346q-109 133 -303 133h-328v-954zM643 0h-450v1200h450q248 0 389 -170q144 -170 144 -432q0 -260 -144 -430q-140 -168 -389 -168z" />
    <glyph glyph-name="Aogonek.sc" horiz-adv-x="1161" 
d="M350 428h461l-229 602zM924 -129q23 66 51 129l-121 313h-547l-121 -313h-131l461 1200h131l459 -1200h-51l-45 -154q-11 -33 0.5 -62.5t40.5 -41.5q37 -17 72.5 9t33.5 69l96 8q0 -72 -44.5 -125t-106.5 -59q-104 -9 -156 67t-22 160z" />
    <glyph glyph-name="Emacron.sc" horiz-adv-x="1015" 
d="M725 1434h-418v114h418v-114zM899 0h-706v1200h688v-121h-566v-430h496v-121h-496v-407h584v-121z" />
    <glyph glyph-name="Ebreve.sc" horiz-adv-x="1015" 
d="M293 1583h96q6 -52 38.5 -84.5t82.5 -32.5q53 0 84.5 33.5t36.5 83.5h98q0 -98 -60.5 -157.5t-158.5 -59.5q-105 0 -161 61t-56 156zM193 1200h688v-121h-566v-430h496v-121h-496v-407h584v-121h-706v1200z" />
    <glyph glyph-name="Edotaccent.sc" horiz-adv-x="1015" 
d="M547 1589q37 0 64.5 -26t27.5 -62t-27.5 -61t-64.5 -25q-39 0 -66.5 25t-27.5 61q0 37 28 62.5t66 25.5zM193 1200h688v-121h-566v-430h496v-121h-496v-407h584v-121h-706v1200z" />
    <glyph glyph-name="Ecaron.sc" horiz-adv-x="1015" 
d="M289 1591h121l125 -149l122 149h123l-170 -239h-153zM193 1200h688v-121h-566v-430h496v-121h-496v-407h584v-121h-706v1200z" />
    <glyph glyph-name="Gcircumflex.sc" horiz-adv-x="1355" 
d="M762 1591l170 -239h-123l-125 151l-123 -151h-123l170 239h154zM1137 477h-349v113h486q0 -297 -146.5 -452.5t-412.5 -155.5q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q217 0 387 -141l-88 -86q-128 107 -299 107q-202 0 -342 -146q-137 -143 -137 -348
q0 -207 137 -350q138 -144 342 -144q188 0 299.5 99.5t122.5 273.5z" />
    <glyph glyph-name="Gbreve.sc" horiz-adv-x="1355" 
d="M469 1583h98q6 -52 38.5 -84.5t82.5 -32.5q53 0 83.5 32.5t35.5 84.5h98q0 -98 -59.5 -157.5t-157.5 -59.5q-105 0 -162 61t-57 156zM788 590h486q0 -297 -146.5 -452.5t-412.5 -155.5q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q217 0 387 -141l-88 -86
q-128 107 -299 107q-202 0 -342 -146q-137 -143 -137 -348q0 -207 137 -350q138 -144 342 -144q188 0 299.5 99.5t122.5 273.5h-349v113z" />
    <glyph glyph-name="Eogonek.sc" horiz-adv-x="1015" 
d="M752 -113l63 113h-622v1200h688v-121h-566v-430h496v-121h-496v-407h584v-121l-63 -152q-12 -30 0.5 -60t42.5 -42q37 -17 71.5 8.5t34.5 69.5l94 6q0 -71 -44.5 -123.5t-106.5 -58.5q-72 -9 -131 41q-58 46 -64 112q-3 44 19 86z" />
    <glyph glyph-name="Dcroat.sc" horiz-adv-x="1302" 
d="M651 1077h-327v-424h297v-108h-297v-422h327q195 0 302.5 128.5t107.5 346.5q0 215 -107 346q-109 133 -303 133zM651 0h-450v545h-170v108h170v547h450q248 0 389 -170q144 -170 144 -432q0 -260 -144 -430q-140 -168 -389 -168z" />
    <glyph glyph-name="Gdotaccent.sc" horiz-adv-x="1355" 
d="M692 1589q37 0 64.5 -26t27.5 -62t-27.5 -61t-64.5 -25q-39 0 -66.5 25t-27.5 61q0 37 28 62.5t66 25.5zM788 590h486q0 -297 -146.5 -452.5t-412.5 -155.5q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q217 0 387 -141l-88 -86q-128 107 -299 107
q-202 0 -342 -146q-137 -143 -137 -348q0 -207 137 -350q138 -144 342 -144q188 0 299.5 99.5t122.5 273.5h-349v113z" />
    <glyph glyph-name="Hcircumflex.sc" horiz-adv-x="1275" 
d="M713 1591l170 -239h-121l-125 151l-123 -151h-123l170 239h152zM961 653v547h122v-1200h-122v539h-646v-539h-122v1200h122v-547h646z" />
    <glyph glyph-name="Hbar.sc" horiz-adv-x="1294" 
d="M969 901h-643v-248h643v248zM969 1200h123v-213h127v-86h-127v-901h-123v537h-643v-537h-123v901h-125v86h125v213h123v-213h643v213z" />
    <glyph glyph-name="Itilde.sc" horiz-adv-x="507" 
d="M119 1393h-92q6 95 47 142.5t102 47.5q42 0 88 -37q47 -35 70 -35q22 0 39 18.5t18 53.5h92q-6 -93 -47.5 -138.5t-101.5 -45.5q-43 0 -90 35q-45 36 -68 36q-22 0 -39 -20.5t-18 -56.5zM193 0v1200h122v-1200h-122z" />
    <glyph glyph-name="Imacron.sc" horiz-adv-x="507" 
d="M463 1434h-418v114h418v-114zM193 0v1200h122v-1200h-122z" />
    <glyph glyph-name="Iogonek.sc" horiz-adv-x="507" 
d="M221 0v1200h123v-1200h-57l-33 -154q-15 -83 43 -104q36 -17 71 9t35 69l95 8q0 -72 -44.5 -125t-107.5 -59q-105 -9 -156 65q-50 73 -22 162q6 19 27 70q25 56 26 59z" />
    <glyph glyph-name="IJ.sc" horiz-adv-x="1015" 
d="M352 -432l13 121q41 -15 100 -15q117 0 176 70.5t59 192.5v1263h123v-1263q0 -177 -90 -280t-268 -103q-64 0 -113 14zM193 0v1200h122v-1200h-122z" />
    <glyph glyph-name="Jcircumflex.sc" horiz-adv-x="507" 
d="M-156 -432l13 121q41 -15 100 -15q118 0 177 70.5t59 192.5v1263h122v-1263q0 -177 -90 -280t-268 -103q-64 0 -113 14zM328 1591l170 -239h-121l-125 151l-123 -151h-123l170 239h152z" />
    <glyph glyph-name="Lacute.sc" horiz-adv-x="878" 
d="M324 1589h151l-156 -239h-124zM193 1200h122v-1079h506v-121h-628v1200z" />
    <glyph glyph-name="Lcaron.sc" horiz-adv-x="878" 
d="M821 0h-628v1200h122v-1079h506v-121zM539 829l-76 23q103 145 100 348h125q-21 -275 -149 -371z" />
    <glyph glyph-name="Ldot.sc" horiz-adv-x="878" 
d="M821 0h-628v1200h122v-1079h506v-121zM469 496q0 38 26.5 63t65.5 25q37 0 64.5 -26t27.5 -62t-27.5 -61t-64.5 -25q-38 0 -65 24.5t-27 61.5z" />
    <glyph glyph-name="Nacute.sc" horiz-adv-x="1232" 
d="M676 1591h149l-155 -239h-125zM1040 0h-125l-600 987v-987h-122v1200h129l596 -981v981h122v-1200z" />
    <glyph glyph-name="Ncaron.sc" horiz-adv-x="1232" 
d="M369 1591h123l124 -149l123 149h123l-170 -239h-153zM1040 0h-125l-600 987v-987h-122v1200h129l596 -981v981h122v-1200z" />
    <glyph glyph-name="Omacron.sc" horiz-adv-x="1433" 
d="M926 1434h-420v114h420v-114zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 1212q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180
q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180z" />
    <glyph glyph-name="Obreve.sc" horiz-adv-x="1433" 
d="M500 1583h96q6 -52 38.5 -84.5t82.5 -32.5q53 0 84.5 33.5t36.5 83.5h96q0 -98 -59.5 -157.5t-157.5 -59.5q-105 0 -161 61t-56 156zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146
q-137 -143 -137 -348zM715 -18q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180z" />
    <glyph glyph-name="Ohungarumlaut.sc" horiz-adv-x="1433" 
d="M870 1591h146l-152 -239h-123zM651 1591h133l-127 -239h-129zM236 598q0 -207 137 -350q138 -144 342 -144q206 0 344 144q139 145 139 350q0 203 -139 348q-140 146 -344 146q-202 0 -342 -146q-137 -143 -137 -348zM715 -18q-254 0 -428 180t-174 436q0 257 174 434
q174 180 428 180q256 0 430 -180q176 -179 176 -434q0 -254 -176 -436q-174 -180 -430 -180z" />
    <glyph glyph-name="Racute.sc" horiz-adv-x="1101" 
d="M543 1591h149l-155 -239h-125zM639 1079h-322v-493h314q111 0 167 67.5t56 177.5t-54.5 179t-160.5 69zM659 465l330 -465h-151l-328 465h-193v-465h-124v1200h448q173 0 254 -106q82 -107 82 -263q0 -159 -81 -259t-237 -107z" />
    <glyph glyph-name="Rcaron.sc" horiz-adv-x="1101" 
d="M305 1591h123l125 -149l123 149h123l-170 -239h-154zM639 1079h-322v-493h314q111 0 167 67.5t56 177.5t-54.5 179t-160.5 69zM659 465l330 -465h-151l-328 465h-193v-465h-124v1200h448q173 0 254 -106q82 -107 82 -263q0 -159 -81 -259t-237 -107z" />
    <glyph glyph-name="Sacute.sc" horiz-adv-x="1032" 
d="M549 1591h151l-157 -239h-123zM242 344q0 -94 75 -166q77 -74 195 -74t194.5 55.5t90.5 137.5q27 190 -224 248l-159 37q-285 64 -285 303q0 144 113 235q115 92 266 92q163 0 270 -94t107 -250h-123q0 99 -71.5 161.5t-182.5 62.5q-102 0 -180 -60q-76 -58 -76 -147
q0 -146 188 -187l164 -36q168 -38 253 -135.5t61 -250.5q-20 -116 -130 -206q-104 -88 -276 -88q-170 0 -283 110q-110 113 -110 252h123z" />
    <glyph glyph-name="Scircumflex.sc" horiz-adv-x="1032" 
d="M594 1591l170 -239h-123l-125 151l-123 -151h-123l170 239h154zM119 344h123q0 -94 75 -166q77 -74 195 -74t194.5 55.5t90.5 137.5q27 190 -224 248l-159 37q-285 64 -285 303q0 144 113 235q115 92 266 92q163 0 270 -94t107 -250h-123q0 99 -71.5 161.5t-182.5 62.5
q-102 0 -180 -60q-76 -58 -76 -147q0 -146 188 -187l164 -36q168 -38 253 -135.5t61 -250.5q-20 -116 -130 -206q-104 -88 -276 -88q-170 0 -283 110q-110 113 -110 252z" />
    <glyph glyph-name="Scedilla.sc" horiz-adv-x="1036" 
d="M121 344h123q0 -94 75 -166q77 -74 195 -74t194.5 55.5t90.5 137.5q27 190 -224 248l-161 37q-283 65 -283 303q0 144 113 235q115 92 266 92q163 0 270 -94t107 -250h-123q0 99 -71.5 161.5t-182.5 62.5q-102 0 -180 -60q-76 -58 -76 -147q0 -146 188 -187l164 -36
q168 -38 253 -135.5t61 -250.5q-20 -104 -99.5 -182t-202.5 -100l52 -111q2 -2 2 -12q23 -68 -7.5 -131t-99.5 -86q-84 -27 -153 24q-70 52 -70 150l94 -8q0 -43 35 -69t72 -9q30 12 42 42t1 62l-51 136h-13q-175 0 -289 108q-112 109 -112 254z" />
    <glyph glyph-name="Utilde.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM467 1362h-92q6 95 47 142.5t102 47.5q43 0 88 -36q46 -37 70 -37q22 0 39 18.5t18 54.5h92
q-6 -94 -47 -140t-102 -46q-44 0 -90 37t-68 37t-39 -21t-18 -57z" />
    <glyph glyph-name="Umacron.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM825 1434h-419v114h419v-114z" />
    <glyph glyph-name="Ubreve.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM389 1583h98q6 -52 37.5 -84.5t81.5 -32.5q53 0 84.5 33.5t36.5 83.5h98q0 -98 -59.5 -157.5
t-159.5 -59.5q-105 0 -161 61t-56 156z" />
    <glyph glyph-name="Uring.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM436 1518q0 74 49 124t121 50t122 -50.5t50 -123.5q0 -72 -50 -121t-122 -49t-121 49t-49 121z
M606 1440q30 -1 51 15.5t25.5 40.5t0 47.5t-25.5 40t-51 15.5q-29 1 -49 -15.5t-24.5 -40t0 -47.5t24.5 -40.5t49 -15.5z" />
    <glyph glyph-name="Uhungarumlaut.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -189 -125 -308q-121 -118 -301 -118q-181 0 -305 118q-123 120 -123 308v792h123v-792q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220zM743 1591h148l-152 -239h-123zM526 1591h133l-127 -239h-129z" />
    <glyph glyph-name="Uogonek.sc" horiz-adv-x="1214" 
d="M911 408v792h123v-792q0 -173 -102 -287q-100 -111 -260 -133l-56 -142q-13 -29 -1 -60.5t42 -43.5q40 -17 69.5 7t37.5 71l96 8q0 -72 -44 -124t-107 -58q-73 -7 -130 38.5t-63 112.5q-3 49 16 86l52 101q-173 9 -289 125q-115 118 -115 299v792h123v-792
q0 -138 88 -220q90 -84 217 -84q125 0 215 84q88 82 88 220z" />
    <glyph glyph-name="Wcircumflex.sc" horiz-adv-x="1626" 
d="M889 1591l170 -239h-123l-125 151l-123 -151h-123l170 239h154zM860 1040l244 -845l330 1005h131l-406 -1200h-115l-231 795l-231 -795h-115l-406 1200h132l329 -1005l244 845h94z" />
    <glyph glyph-name="Ycircumflex.sc" horiz-adv-x="1118" 
d="M1079 1200l-456 -643v-557h-127v557l-451 643h148l366 -512l373 512h147zM637 1591l170 -239h-123l-125 151l-123 -151h-123l170 239h154z" />
    <glyph glyph-name="Tcaron.sc" horiz-adv-x="1001" 
d="M252 1591h123l125 -149l123 149h122l-170 -239h-153zM561 0h-123v1077h-381v123h887v-123h-383v-1077z" />
    <glyph glyph-name="Tbar.sc" horiz-adv-x="999" 
d="M944 1077h-383v-372h232v-84h-232v-621h-123v621h-227v84h227v372h-381v123h887v-123z" />
    <glyph glyph-name="Zacute.sc" horiz-adv-x="1019" 
d="M94 0v119l670 958h-645v123h796v-121l-671 -956h671v-123h-821zM535 1591h151l-158 -239h-122z" />
    <glyph glyph-name="Zdotaccent.sc" horiz-adv-x="1019" 
d="M94 0v119l670 958h-645v123h796v-121l-671 -956h671v-123h-821zM414 1501q0 38 26.5 63t65.5 25q37 0 64.5 -26t27.5 -62t-27.5 -61t-64.5 -25q-38 0 -65 24.5t-27 61.5z" />
    <glyph glyph-name="Wgrave.sc" horiz-adv-x="1626" 
d="M735 1591l129 -239h-125l-155 239h151zM860 1040l244 -845l330 1005h131l-406 -1200h-115l-231 795l-231 -795h-115l-406 1200h132l329 -1005l244 845h94z" />
    <glyph glyph-name="Wacute.sc" horiz-adv-x="1626" 
d="M895 1591h152l-158 -239h-123zM1434 1200h131l-406 -1200h-115l-231 795l-231 -795h-115l-406 1200h132l329 -1005l244 845h94l244 -845z" />
    <glyph glyph-name="Wdieresis.sc" horiz-adv-x="1626" 
d="M659 1585q37 0 64 -25t27 -61t-27 -61t-64 -25q-56 -4 -79.5 41t0 90t79.5 41zM899 1438q-26 25 -26 61.5t26 61.5t65.5 25t65.5 -25t26 -61.5t-26 -61.5t-65.5 -25t-65.5 25zM1434 1200h131l-406 -1200h-115l-231 795l-231 -795h-115l-406 1200h132l329 -1005l244 845
h94l244 -845z" />
    <glyph glyph-name="Aringacute.sc" horiz-adv-x="1161" 
d="M637 1890h149l-141 -215q106 -49 100 -176q-4 -66 -51.5 -108.5t-115.5 -42.5q-72 0 -121 49t-49 119q0 57 31.5 101.5t82.5 61.5zM580 1599q-49 3 -69.5 -39t0 -84t69.5 -38q35 0 57 22.5t22 57.5t-23 58t-56 23zM811 428l-229 602l-232 -602h461zM186 0h-131l461 1200
h131l459 -1200h-131l-121 313h-547z" />
    <glyph glyph-name="AEacute.sc" horiz-adv-x="1685" 
d="M979 1591h149l-155 -239h-125zM457 428h405v637zM385 313l-199 -313h-145l758 1200h751v-123h-565v-428h494v-123h-494v-403h584v-123h-707v313h-477z" />
    <glyph glyph-name="Oslashacute.sc" horiz-adv-x="1431" 
d="M758 1591h151l-155 -239h-125zM713 104q206 0 344 144q139 145 139 350q0 178 -106 311l-678 -698q128 -107 301 -107zM233 598q0 -181 111 -319l680 698q-139 115 -311 115q-202 0 -342 -146q-138 -144 -138 -348zM326 123l-140 -143l-67 67l139 143q-147 176 -147 408
q0 257 174 434q174 180 428 180q227 0 397 -147l147 154l68 -68l-149 -156q143 -168 143 -397q0 -254 -176 -436q-174 -180 -430 -180q-217 0 -387 141z" />
    <glyph glyph-name="Ibreve.sc" horiz-adv-x="507" 
d="M37 1583h96q8 -52 40.5 -84.5t80.5 -32.5q53 0 84.5 33.5t36.5 83.5h98q0 -98 -59.5 -157.5t-159.5 -59.5q-105 0 -161 61t-56 156zM315 1200v-1200h-122v1200h122z" />
    <glyph glyph-name="Eng.sc" horiz-adv-x="1232" 
d="M918 -63v65l-603 983v-985h-122v1200h129l596 -989v989h122v-1198q0 -101 -14.5 -173t-50.5 -136q-76 -139 -295 -139q-45 0 -111 16l11 121q37 -13 98 -13q119 -1 179.5 69t60.5 190z" />
    <glyph glyph-name="Ygrave.sc" horiz-adv-x="1118" 
d="M1079 1200l-456 -643v-557h-127v557l-451 643h148l366 -512l373 512h147zM657 1321h-125l-155 240h149z" />
    <glyph glyph-name="Gcommaaccent.sc" horiz-adv-x="1355" 
d="M1137 477h-349v113h486q0 -297 -146.5 -452.5t-412.5 -155.5q-254 0 -428 180t-174 436q0 257 174 434q174 180 428 180q217 0 387 -141l-88 -86q-128 107 -299 107q-202 0 -342 -146q-137 -143 -137 -348q0 -207 137 -350q138 -144 342 -144q188 0 299.5 99.5
t122.5 273.5zM643 -430l-88 24q100 133 76 297h137q3 -214 -125 -321z" />
    <glyph glyph-name="Kcommaaccent.sc" horiz-adv-x="1017" 
d="M903 1200l-465 -600l568 -600h-166l-525 555v-555h-122v1200h122v-573l443 573h145zM530 -430l-88 24q100 133 76 297h137q3 -214 -125 -321z" />
    <glyph glyph-name="Lcommaaccent.sc" horiz-adv-x="878" 
d="M821 0h-628v1200h122v-1079h506v-121zM446 -430l-88 24q100 133 76 297h137q3 -214 -125 -321z" />
    <glyph glyph-name="Ncommaaccent.sc" horiz-adv-x="1232" 
d="M918 1200h122v-1200h-125l-600 987v-987h-122v1200h129l596 -981v981zM580 -430l-88 24q99 131 75 297h138q3 -214 -125 -321z" />
    <glyph glyph-name="Rcommaaccent.sc" horiz-adv-x="1101" 
d="M639 1079h-322v-493h314q111 0 167 67.5t56 177.5t-54.5 179t-160.5 69zM659 465l330 -465h-151l-328 465h-193v-465h-124v1200h448q173 0 254 -106q82 -107 82 -263q0 -159 -81 -259t-237 -107zM512 -430l-88 24q100 133 76 297h137q3 -214 -125 -321z" />
    <glyph glyph-name="Tcommaaccent.sc" horiz-adv-x="999" 
d="M438 1077h-381v123h885v-123h-381v-1077h-123v1077zM455 -430l-88 24q99 131 75 297h138q3 -214 -125 -321z" />
    <glyph glyph-name="Scommaaccent.sc" horiz-adv-x="1032" 
d="M119 344h123q0 -97 75 -172q76 -76 195 -76q118 0 194.5 58.5t90.5 142.5q27 190 -224 248l-159 37q-285 64 -285 303q0 144 113 235q115 92 266 92q163 0 270 -94t107 -250h-123q0 99 -71.5 161.5t-182.5 62.5q-102 0 -180 -60q-76 -58 -76 -147q0 -146 188 -187
l164 -36q168 -38 253 -135.5t61 -250.5q-20 -116 -130 -206q-104 -88 -276 -88q-170 0 -283 110q-110 113 -110 252zM469 -430l-88 24q100 133 76 297h137q3 -214 -125 -321z" />
    <glyph glyph-name="Idotaccent.sc" horiz-adv-x="507" 
d="M162 1501q-4 54 44 76.5t96 0t44 -76.5q0 -37 -27 -61.5t-65 -24.5t-65 24.5t-27 61.5zM315 1200v-1200h-122v1200h122z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="747" 
d="M186 958q0 -307 187 -307q186 0 186 307q0 310 -186 310q-187 0 -187 -310zM86 958q0 410 287 410q153 0 219.5 -104.5t66.5 -305.5q0 -199 -66.5 -302t-219.5 -103q-287 0 -287 405z" />
    <glyph glyph-name="one.numr" horiz-adv-x="411" 
d="M295 1370v-811h-109v666l-131 -47v104z" />
    <glyph glyph-name="two.numr" horiz-adv-x="661" 
d="M573 559h-493v49l326 393q59 74 59 134q0 57 -40.5 93t-102.5 36q-58 0 -101.5 -35.5t-50.5 -91.5l-100 24q13 96 83.5 153.5t168.5 57.5q63 2 117.5 -23.5t86.5 -71.5t44 -102.5t-9.5 -122t-75.5 -124.5l-219 -260h307v-109z" />
    <glyph glyph-name="three.numr" horiz-adv-x="667" 
d="M102 1255q33 58 96 90.5t136 28.5q96 -6 159.5 -71t61.5 -152q-3 -104 -96 -160q59 -20 97 -84t32 -131q-9 -104 -96 -170t-199 -57q-78 6 -142.5 51.5t-95.5 114.5q11 5 45 25.5t45 25.5q43 -94 156 -109q68 -5 121.5 35t54.5 99q2 57 -45.5 101t-112.5 44h-90v104h90
q43 0 75 27t38 68q8 51 -22.5 87t-85.5 42q-90 9 -144 -72q-6 4 -16.5 13t-28 23t-33.5 27z" />
    <glyph glyph-name="four.numr" horiz-adv-x="741" 
d="M457 827v338l-232 -338h232zM682 827v-102h-117v-166h-108v166h-418l444 637h82v-535h117z" />
    <glyph glyph-name="five.numr" horiz-adv-x="696" 
d="M61 729l89 64q23 -60 73 -99t105 -39q85 0 139.5 46t54.5 116q0 66 -48 106t-114 40q-60 0 -110 -15q-58 -18 -137 -59l-23 24l80 449h408v-105h-318l-41 -219q67 33 141 33q119 0 194 -67.5t75 -186.5q0 -122 -83.5 -196t-217.5 -74q-81 0 -153 49.5t-114 132.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="692" 
d="M616 1223l-96 -41q-47 92 -137 92q-88 0 -139.5 -84.5t-34.5 -210.5q30 102 166 102q106 0 178 -84q73 -82 70 -190t-82 -184q-76 -73 -187 -70q-107 5 -176.5 78t-79.5 196q-3 54 -3.5 97.5t3.5 103t14.5 105.5t32 93.5t52.5 79t79.5 51t110.5 19.5q164 -9 229 -153z
M209 823q0 -66 43.5 -111t107.5 -50q63 -2 107.5 40.5t48.5 108.5t-38 113t-105 49q-66 4 -114 -40t-50 -110z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="651" 
d="M80 1253v109h553l-440 -803h-127l383 694h-369z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="704" 
d="M246 1167q0 -44 31 -75.5t77 -30.5q32 -1 58 19t38 48t7 58.5t-29 48.5q-36 28 -76.5 28t-73 -27.5t-32.5 -68.5zM231 920q-47 -47 -47 -111.5t47 -108.5q49 -44 121 -44t119 44q49 44 49 108.5t-49 111.5q-47 47 -119 47t-121 -47zM139 1167q0 87 60.5 146t152.5 59
t154 -59q61 -58 61 -146q0 -86 -61 -141q123 -76 123 -217q0 -107 -80 -184q-79 -76 -197 -76q-116 0 -196 74q-80 77 -80 186q0 141 123 217q-60 54 -60 141z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="692" 
d="M88 700l96 41q47 -92 138 -92q88 0 139.5 84.5t34.5 210.5q-30 -102 -166 -102q-107 0 -180 82q-71 83 -68 192q3 108 82 184q76 73 186 70q107 -5 176.5 -78t79.5 -196q3 -55 3.5 -99t-3.5 -103t-14.5 -104.5t-32 -93t-52.5 -78.5t-79.5 -51t-110.5 -20q-164 9 -229 153
zM496 1100q0 66 -44 111.5t-108 50.5q-63 1 -107 -41.5t-49 -108.5q-4 -66 38.5 -113t105.5 -49q66 -4 114 40t50 110z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="745" 
d="M186 399q0 -307 187 -307q186 0 186 307q0 310 -186 310q-187 0 -187 -310zM86 399q0 410 287 410q153 0 219.5 -104.5t66.5 -305.5q0 -199 -66.5 -302t-219.5 -103q-287 0 -287 405z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="413" 
d="M297 811v-811h-109v666l-131 -48v105z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="659" 
d="M573 0h-493v49l326 393q59 74 59 133q0 57 -40.5 93.5t-102.5 36.5q-58 0 -101.5 -35.5t-50.5 -91.5l-100 24q13 96 83.5 153.5t168.5 57.5q63 2 117.5 -23.5t86.5 -71.5t44 -102.5t-9.5 -122t-75.5 -124.5l-219 -260h307v-109z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="667" 
d="M102 696q33 58 96 90.5t136 28.5q96 -6 159.5 -71t61.5 -152q-3 -104 -96 -160q59 -20 97 -84t32 -131q-9 -104 -96 -170t-199 -57q-78 6 -142.5 51.5t-95.5 114.5q10 4 28 14.5t37 22t25 14.5q43 -94 156 -109q68 -5 121.5 34.5t54.5 98.5q2 58 -45.5 102t-112.5 44h-90
v104h90q43 0 75 26.5t38 67.5q8 51 -22.5 87.5t-85.5 42.5q-90 9 -144 -72q-6 4 -16.5 13t-28 23t-33.5 27z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="731" 
d="M449 268v338l-232 -338h232zM674 268v-102h-117v-166h-108v166h-418l444 637h82v-535h117z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="698" 
d="M61 170l89 63q23 -60 73 -98.5t105 -38.5q85 0 139.5 46t54.5 116q0 66 -48 105.5t-114 39.5q-64 0 -110 -14q-58 -18 -137 -59l-23 24l80 449h408v-105h-318l-41 -219q67 33 141 33q119 0 194 -67.5t75 -186.5q0 -122 -83.5 -196t-217.5 -74q-81 0 -153 49.5t-114 132.5
z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="692" 
d="M616 664l-96 -41q-47 92 -137 92q-88 0 -139.5 -84.5t-34.5 -210.5q30 102 166 102q106 0 178 -84q73 -82 70 -190q-3 -109 -82 -185q-75 -72 -187 -69q-107 5 -176.5 78t-79.5 196q-3 55 -3.5 99t3.5 103t14.5 104.5t32 93t52.5 78.5t79.5 51t110.5 20q164 -9 229 -153z
M209 264q0 -66 43.5 -111.5t107.5 -50.5q63 -1 107 41.5t49 108.5q4 66 -38 113t-105 49q-66 4 -114 -40t-50 -110z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="655" 
d="M78 694v109h553l-441 -803h-127l383 694h-368z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="704" 
d="M246 608q0 -44 31 -75.5t77 -30.5q32 -1 58 19t38 48t7 58.5t-29 48.5q-36 28 -76.5 28t-73 -27.5t-32.5 -68.5zM231 360q-47 -47 -47 -111t47 -108q49 -44 121 -44t119 44q49 44 49 108t-49 111q-47 47 -119 47t-121 -47zM139 608q0 87 60.5 146t152.5 59t154 -59
q61 -58 61 -146q0 -86 -61 -141q123 -76 123 -217q0 -107 -80 -184q-79 -76 -197 -76q-117 0 -196 73q-80 77 -80 187q0 140 123 217q-60 54 -60 141z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="692" 
d="M88 141l96 41q47 -92 138 -92q88 0 139.5 84.5t34.5 210.5q-30 -102 -166 -102q-107 0 -180 82q-71 83 -68 192q3 108 82 184q76 73 186 70q107 -5 176.5 -78t79.5 -196q3 -54 3.5 -97.5t-3.5 -103t-14.5 -105.5t-32 -93.5t-52.5 -79t-79.5 -51t-110.5 -19.5
q-164 9 -229 153zM496 541q0 66 -44 111t-108 50q-63 2 -107.5 -40.5t-48.5 -108.5t38.5 -113t105.5 -49q66 -4 114 40t50 110z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="550" 
d="M487 129h82v-129h-82q-333 0 -333 408v1067h137v-1067q0 -121 28 -187q40 -92 168 -92z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="1105" 
d="M446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="1040" 
d="M399 -115l45 115l-397 956h146l325 -786l328 786h145l-465 -1122q-75 -168 -160.5 -238.5t-224.5 -70.5q-45 0 -92 12v121q72 -10 88 -10q102 0 154 55q52 52 108 182z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="569" 
d="M504 129h82v-129h-82q-334 0 -334 408v268l-141 -90v121l141 90v678h137v-590l172 110v-121l-172 -110v-356q0 -119 29 -187q40 -92 168 -92z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="550" 
d="M301 1866h168l-172 -275h-139zM487 129h82v-129h-82q-333 0 -333 408v1067h137v-1067q0 -121 28 -187q40 -92 168 -92z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="550" 
d="M487 129h82v-129h-82q-333 0 -333 408v1067h137v-1067q0 -121 28 -187q40 -92 168 -92zM379 -506l-98 27q118 164 88 348h155q3 -254 -145 -375z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="550" 
d="M483 991l-98 27q122 172 119 409h155q-12 -146 -61.5 -264t-114.5 -172zM487 129h82v-129h-82q-333 0 -333 408v1067h137v-1067q0 -121 28 -187q40 -92 168 -92z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="1105" 
d="M446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM618 1411h168l-172 -274h-139z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="1105" 
d="M446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM623 1137h-140l-172 274h168z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="1105" 
d="M446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM821 1137h-139l-145 176l-144 -176h-139l195 274h178z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="1105" 
d="M279 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM641 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM446 113q94 0 203 47q111 46 164 133v82
q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211q0 136 101.5 214.5t304.5 78.5q126 0 321 -63
v82q0 122 -79.5 192t-219.5 70z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="1103" 
d="M444 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM514 842q-164 0 -281 -123l-92 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM461 1282q-27 0 -48 -25t-22 -67h-104q6 110 54.5 165.5t119.5 55.5q49 0 104 -43t82 -43t48 22.5t22 63.5h104q-6 -109 -53.5 -162t-120.5 -53q-49 0 -104 43t-82 43z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="1105" 
d="M446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM537 1534q85 0 142.5 -58t57.5 -143q0 -82 -58 -139t-142 -57q-83 0 -140 56t-57 140t57.5 142.5t139.5 58.5zM446 1335q0 -40 27 -66t66 -26q38 0 65 27t27 65q4 56 -44.5 79.5t-96.5 0
t-44 -79.5z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="1103" 
d="M444 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM514 842q-164 0 -281 -123l-92 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM291 1241v129h487v-129h-487z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="1105" 
d="M446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211
q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM289 1411h106q11 -65 49.5 -103t98.5 -38q65 0 103 39t44 102h107q0 -116 -69.5 -186t-184.5 -70q-122 0 -188 72.5t-66 183.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="1103" 
d="M514 842q-164 0 -281 -123l-92 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-47l-37 -182q-18 -91 52 -125q35 -13 69 2t51 51q9 15 9 39l108 8q0 -41 -18 -88q-35 -78 -107.5 -109t-150.5 1q-76 30 -104 103.5t-3 160.5l41 139v133q-64 -73 -170 -113
q-107 -40 -203 -40q-162 0 -258 77t-96 211q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82q0 122 -79.5 192t-219.5 70zM444 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="1040" 
d="M399 -115l45 115l-397 956h146l325 -786l328 786h145l-465 -1122q-75 -168 -160.5 -238.5t-224.5 -70.5q-45 0 -92 12v121q72 -10 88 -10q102 0 154 55q52 52 108 182zM608 1411h168l-172 -274h-139z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="1040" 
d="M279 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM641 1241q-31 29 -31 72t31 69q31 29 73.5 29t73.5 -29q31 -26 31 -69t-31 -72q-31 -26 -73.5 -26t-73.5 26zM444 0l-397 956h146l325 -786l328 786h145
l-465 -1122q-75 -168 -160.5 -238.5t-224.5 -70.5q-45 0 -92 12v121q72 -10 88 -10q102 0 154 55q52 52 108 182z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="1855" 
d="M954 545h674q-20 136 -109.5 216.5t-220.5 80.5q-135 0 -229.5 -81t-114.5 -216zM446 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM907 788q139 189 391 189q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811
q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q37 -11 70 -17l68 -14q-57 -127 -173.5 -198.5t-263.5 -71.5q-213 0 -346 139v-119h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82
q0 122 -79.5 192t-219.5 70q-163 0 -280 -123l-93 94q126 164 387 164q285 0 377 -189z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="1853" 
d="M952 545h674q-20 136 -109.5 216.5t-220.5 80.5q-135 0 -229.5 -81t-114.5 -216zM444 113q94 0 203 47q111 46 164 133v82q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM905 788q139 189 391 189q206 0 338 -135q131 -134 131 -361q0 -51 -2 -63h-811
q19 -136 114 -219.5t230 -83.5q97 0 176 44t123 122q37 -11 70 -17l68 -14q-57 -127 -173.5 -198.5t-263.5 -71.5q-213 0 -346 139v-119h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211q0 136 101.5 214.5t304.5 78.5q126 0 321 -63v82
q0 122 -79.5 192t-219.5 70q-164 0 -281 -123l-92 94q126 164 387 164q285 0 377 -189zM993 1411h168l-172 -274h-139z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="1040" 
d="M399 -115l45 115l-397 956h146l325 -786l328 786h145l-465 -1122q-75 -168 -160.5 -238.5t-224.5 -70.5q-45 0 -92 12v121q72 -10 88 -10q102 0 154 55q52 52 108 182zM807 1137h-139l-146 176l-143 -176h-139l194 274h178z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="608" 
d="M487 129h82v-129h-82q-333 0 -333 408v1067h137v-1067q0 -121 28 -187q40 -92 168 -92zM436 463q-32 31 -32 75.5t32 75.5t80 31t80 -31t32 -75.5t-32 -75.5t-80 -31t-80 31z" />
    <glyph glyph-name="t.alt1" horiz-adv-x="673" 
d="M571 129h41v-129h-41q-180 0 -256.5 84t-76.5 266v484h-172v122h172v256l137 39v-295h203v-122h-203v-484q-3 -112 35 -168q38 -53 161 -53z" />
    <glyph glyph-name="tcaron.alt1" horiz-adv-x="673" 
d="M543 1073l-99 27q115 161 88 327h156q3 -233 -145 -354zM612 0h-41q-180 0 -256.5 84t-76.5 266v484h-172v122h172v256l137 39v-295h203v-122h-203v-484q-3 -112 35 -168q38 -53 161 -53h41v-129z" />
    <glyph glyph-name="tbar.alt1" horiz-adv-x="677" 
d="M575 129h41v-129h-41q-180 0 -256.5 84t-76.5 266v133h-150v95h150v256h-172v122h172v256l137 39v-295h203v-122h-203v-256h166v-95h-166v-133q-3 -112 35 -168q38 -53 161 -53z" />
    <glyph glyph-name="tcommaaccent.alt1" horiz-adv-x="673" 
d="M571 129h41v-129h-41q-180 0 -256.5 84t-76.5 266v484h-172v122h172v256l137 39v-295h203v-122h-203v-484q-3 -112 35 -168q38 -53 161 -53zM444 -506l-98 27q118 164 88 348h156q3 -253 -146 -375z" />
    <glyph glyph-name="T.sc.alt1" horiz-adv-x="999" 
d="M438 1077h-381v123h885v-123h-381v-1077h-123v1077z" />
    <glyph glyph-name="Tcaron.sc.alt1" horiz-adv-x="1001" 
d="M252 1591h123l125 -149l123 149h122l-170 -239h-153zM561 0h-123v1077h-381v123h887v-123h-383v-1077z" />
    <glyph glyph-name="Tbar.sc.alt1" horiz-adv-x="999" 
d="M944 1077h-383v-372h232v-84h-232v-621h-123v621h-227v84h227v372h-381v123h887v-123z" />
    <glyph glyph-name="Tcommaaccent.sc.alt1" horiz-adv-x="999" 
d="M438 1077h-381v123h885v-123h-381v-1077h-123v1077zM455 -430l-88 24q99 131 75 297h138q3 -214 -125 -321z" />
    <glyph glyph-name="aringacute.alt1" horiz-adv-x="1105" 
d="M612 1769h168l-157 -249q58 -23 92.5 -74t34.5 -113q0 -82 -58 -139t-143 -57q-83 0 -140 56t-57 140q0 66 35.5 117.5t95.5 71.5zM459 1335q0 -41 26.5 -66.5t65.5 -25.5q38 0 65 27t27 65q4 56 -44 79.5t-96 0t-44 -79.5zM446 113q94 0 203 47q111 46 164 133v82
q-174 59 -319 59q-265 0 -265 -166q0 -69 57 -112t160 -43zM516 842q-163 0 -280 -123l-93 94q126 164 387 164q205 0 313.5 -101.5t108.5 -295.5v-580h-137v133q-64 -73 -170 -113q-107 -40 -203 -40q-162 0 -258 77t-96 211q0 136 101.5 214.5t304.5 78.5q126 0 321 -63
v82q0 122 -79.5 192t-219.5 70z" />
    <hkern u1="&#x20;" g2="V.sc" k="53" />
    <hkern u1="&#x20;" u2="&#x2019;" k="35" />
    <hkern u1="&#x20;" u2="v" k="51" />
    <hkern u1="&#x20;" u2="f" k="35" />
    <hkern u1="&#x20;" u2="V" k="63" />
    <hkern u1="&#x23;" g2="five.oldstyle" k="31" />
    <hkern u1="&#x23;" g2="four.oldstyle" k="111" />
    <hkern u1="&#x23;" g2="three.oldstyle" k="27" />
    <hkern u1="&#x23;" u2="&#x34;" k="31" />
    <hkern u1="&#x24;" g2="four.oldstyle" k="47" />
    <hkern u1="&#x24;" g2="three.oldstyle" k="31" />
    <hkern u1="&#x26;" u2="V" k="35" />
    <hkern u1="&#x28;" g2="Jcircumflex.sc" k="-139" />
    <hkern u1="&#x28;" g2="Hbar.sc" k="49" />
    <hkern u1="&#x28;" g2="M.sc" k="57" />
    <hkern u1="&#x28;" g2="J.sc" k="-139" />
    <hkern u1="&#x28;" g2="Lslash.sc" k="49" />
    <hkern u1="&#x28;" g2="nine.oldstyle" k="20" />
    <hkern u1="&#x28;" g2="eight.oldstyle" k="51" />
    <hkern u1="&#x28;" g2="six.oldstyle" k="47" />
    <hkern u1="&#x28;" g2="five.oldstyle" k="25" />
    <hkern u1="&#x28;" g2="four.oldstyle" k="78" />
    <hkern u1="&#x28;" g2="two.oldstyle" k="68" />
    <hkern u1="&#x28;" g2="one.oldstyle" k="61" />
    <hkern u1="&#x28;" g2="zero.oldstyle" k="88" />
    <hkern u1="&#x28;" u2="&#x135;" k="-111" />
    <hkern u1="&#x28;" u2="&#x134;" k="-193" />
    <hkern u1="&#x28;" u2="&#xef;" k="-27" />
    <hkern u1="&#x28;" u2="&#x7b;" k="37" />
    <hkern u1="&#x28;" u2="x" k="53" />
    <hkern u1="&#x28;" u2="v" k="49" />
    <hkern u1="&#x28;" u2="j" k="-111" />
    <hkern u1="&#x28;" u2="f" k="33" />
    <hkern u1="&#x28;" u2="M" k="33" />
    <hkern u1="&#x28;" u2="J" k="-193" />
    <hkern u1="&#x28;" u2="&#x39;" k="27" />
    <hkern u1="&#x28;" u2="&#x38;" k="51" />
    <hkern u1="&#x28;" u2="&#x36;" k="47" />
    <hkern u1="&#x28;" u2="&#x35;" k="33" />
    <hkern u1="&#x28;" u2="&#x34;" k="111" />
    <hkern u1="&#x28;" u2="&#x30;" k="45" />
    <hkern u1="&#x28;" u2="&#x28;" k="43" />
    <hkern u1="&#x29;" u2="&#x7d;" k="43" />
    <hkern u1="&#x29;" u2="]" k="20" />
    <hkern u1="&#x29;" u2="&#x29;" k="43" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-47" />
    <hkern u1="&#x2a;" u2="&#x12d;" k="-25" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-14" />
    <hkern u1="&#x2a;" u2="&#x127;" k="-27" />
    <hkern u1="&#x2a;" u2="&#x110;" k="-16" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-47" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-47" />
    <hkern u1="&#x2a;" u2="&#xd0;" k="-16" />
    <hkern u1="&#x2b;" g2="seven.oldstyle" k="45" />
    <hkern u1="&#x2b;" g2="four.oldstyle" k="109" />
    <hkern u1="&#x2b;" g2="three.oldstyle" k="55" />
    <hkern u1="&#x2b;" g2="two.oldstyle" k="33" />
    <hkern u1="&#x2b;" u2="&#x37;" k="37" />
    <hkern u1="&#x2b;" u2="&#x31;" k="31" />
    <hkern u1="&#x2d;" g2="seven.oldstyle" k="68" />
    <hkern u1="&#x2d;" g2="four.oldstyle" k="92" />
    <hkern u1="&#x2d;" g2="three.oldstyle" k="74" />
    <hkern u1="&#x2d;" g2="two.oldstyle" k="51" />
    <hkern u1="&#x2d;" g2="one.oldstyle" k="29" />
    <hkern u1="&#x2d;" u2="&#x37;" k="49" />
    <hkern u1="&#x2d;" u2="&#x32;" k="39" />
    <hkern u1="&#x2d;" u2="&#x31;" k="45" />
    <hkern u1="&#x2f;" g2="M.sc" k="37" />
    <hkern u1="&#x2f;" g2="nine.oldstyle" k="82" />
    <hkern u1="&#x2f;" g2="eight.oldstyle" k="29" />
    <hkern u1="&#x2f;" g2="seven.oldstyle" k="20" />
    <hkern u1="&#x2f;" g2="five.oldstyle" k="86" />
    <hkern u1="&#x2f;" g2="four.oldstyle" k="182" />
    <hkern u1="&#x2f;" g2="three.oldstyle" k="78" />
    <hkern u1="&#x2f;" g2="two.oldstyle" k="53" />
    <hkern u1="&#x2f;" g2="one.oldstyle" k="41" />
    <hkern u1="&#x2f;" g2="zero.oldstyle" k="74" />
    <hkern u1="&#x2f;" u2="&#x12d;" k="-20" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-12" />
    <hkern u1="&#x2f;" u2="x" k="33" />
    <hkern u1="&#x2f;" u2="v" k="23" />
    <hkern u1="&#x2f;" u2="&#x38;" k="29" />
    <hkern u1="&#x2f;" u2="&#x34;" k="111" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="352" />
    <hkern u1="&#x30;" u2="&#x2044;" k="-86" />
    <hkern u1="&#x30;" u2="&#xc6;" k="47" />
    <hkern u1="&#x30;" u2="&#x7d;" k="47" />
    <hkern u1="&#x30;" u2="]" k="25" />
    <hkern u1="&#x30;" u2="Y" k="57" />
    <hkern u1="&#x30;" u2="W" k="23" />
    <hkern u1="&#x30;" u2="V" k="27" />
    <hkern u1="&#x30;" u2="T" k="25" />
    <hkern u1="&#x30;" u2="A" k="20" />
    <hkern u1="&#x30;" u2="&#x29;" k="45" />
    <hkern u1="&#x31;" u2="&#x2044;" k="-205" />
    <hkern u1="&#x32;" u2="&#x2044;" k="-281" />
    <hkern u1="&#x32;" u2="&#xb7;" k="35" />
    <hkern u1="&#x32;" u2="&#x7d;" k="25" />
    <hkern u1="&#x32;" u2="Y" k="41" />
    <hkern u1="&#x32;" u2="V" k="20" />
    <hkern u1="&#x32;" u2="&#x2d;" k="23" />
    <hkern u1="&#x32;" u2="&#x29;" k="20" />
    <hkern u1="&#x33;" u2="&#x2044;" k="-119" />
    <hkern u1="&#x33;" u2="&#x7d;" k="37" />
    <hkern u1="&#x33;" u2="Y" k="49" />
    <hkern u1="&#x33;" u2="W" k="20" />
    <hkern u1="&#x33;" u2="V" k="27" />
    <hkern u1="&#x33;" u2="&#x29;" k="35" />
    <hkern u1="&#x34;" u2="&#x2044;" k="-137" />
    <hkern u1="&#x34;" u2="&#xb0;" k="27" />
    <hkern u1="&#x34;" u2="&#x7d;" k="43" />
    <hkern u1="&#x34;" u2="]" k="23" />
    <hkern u1="&#x34;" u2="\" k="25" />
    <hkern u1="&#x34;" u2="Y" k="55" />
    <hkern u1="&#x34;" u2="W" k="31" />
    <hkern u1="&#x34;" u2="V" k="37" />
    <hkern u1="&#x34;" u2="T" k="27" />
    <hkern u1="&#x34;" u2="&#x29;" k="43" />
    <hkern u1="&#x35;" u2="&#x2044;" k="-129" />
    <hkern u1="&#x35;" u2="V" k="20" />
    <hkern u1="&#x36;" u2="&#x2044;" k="-123" />
    <hkern u1="&#x36;" u2="&#x7d;" k="20" />
    <hkern u1="&#x36;" u2="Y" k="37" />
    <hkern u1="&#x36;" u2="V" k="20" />
    <hkern u1="&#x37;" u2="&#x2212;" k="90" />
    <hkern u1="&#x37;" u2="&#x2044;" k="43" />
    <hkern u1="&#x37;" u2="&#xc6;" k="176" />
    <hkern u1="&#x37;" u2="&#xb7;" k="102" />
    <hkern u1="&#x37;" u2="&#xa2;" k="82" />
    <hkern u1="&#x37;" u2="Y" k="-10" />
    <hkern u1="&#x37;" u2="A" k="102" />
    <hkern u1="&#x37;" u2="&#x3d;" k="53" />
    <hkern u1="&#x37;" u2="&#x38;" k="29" />
    <hkern u1="&#x37;" u2="&#x34;" k="121" />
    <hkern u1="&#x37;" u2="&#x2f;" k="129" />
    <hkern u1="&#x37;" u2="&#x2d;" k="104" />
    <hkern u1="&#x37;" u2="&#x2b;" k="76" />
    <hkern u1="&#x37;" u2="&#x23;" k="57" />
    <hkern u1="&#x38;" u2="&#x2044;" k="-119" />
    <hkern u1="&#x38;" u2="&#xc6;" k="20" />
    <hkern u1="&#x38;" u2="&#x7d;" k="51" />
    <hkern u1="&#x38;" u2="]" k="29" />
    <hkern u1="&#x38;" u2="\" k="27" />
    <hkern u1="&#x38;" u2="Y" k="66" />
    <hkern u1="&#x38;" u2="W" k="31" />
    <hkern u1="&#x38;" u2="V" k="33" />
    <hkern u1="&#x38;" u2="T" k="29" />
    <hkern u1="&#x38;" u2="&#x29;" k="49" />
    <hkern u1="&#x39;" u2="&#x2044;" k="-80" />
    <hkern u1="&#x39;" u2="&#xc6;" k="49" />
    <hkern u1="&#x39;" u2="&#x7d;" k="45" />
    <hkern u1="&#x39;" u2="]" k="25" />
    <hkern u1="&#x39;" u2="Y" k="53" />
    <hkern u1="&#x39;" u2="W" k="20" />
    <hkern u1="&#x39;" u2="V" k="25" />
    <hkern u1="&#x39;" u2="T" k="20" />
    <hkern u1="&#x39;" u2="A" k="23" />
    <hkern u1="&#x39;" u2="&#x29;" k="43" />
    <hkern u1="&#x3d;" g2="four.oldstyle" k="47" />
    <hkern u1="&#x40;" g2="Y.sc" k="33" />
    <hkern u1="&#x40;" u2="Y" k="88" />
    <hkern u1="&#x40;" u2="W" k="31" />
    <hkern u1="&#x40;" u2="V" k="33" />
    <hkern u1="&#x40;" u2="T" k="68" />
    <hkern u1="A" g2="at.case" k="27" />
    <hkern u1="A" u2="&#x31;" k="37" />
    <hkern u1="A" u2="&#x30;" k="20" />
    <hkern u1="B" g2="X.sc" k="10" />
    <hkern u1="B" g2="V.sc" k="12" />
    <hkern u1="B" g2="braceright.case" k="90" />
    <hkern u1="B" g2="bracketright.case" k="37" />
    <hkern u1="B" g2="parenright.case" k="80" />
    <hkern u1="B" u2="&#x7d;" k="53" />
    <hkern u1="B" u2="x" k="27" />
    <hkern u1="B" u2="v" k="23" />
    <hkern u1="B" u2="]" k="27" />
    <hkern u1="B" u2="X" k="10" />
    <hkern u1="B" u2="V" k="27" />
    <hkern u1="B" u2="&#x29;" k="49" />
    <hkern u1="C" u2="&#x135;" k="-18" />
    <hkern u1="C" u2="&#x129;" k="-20" />
    <hkern u1="C" u2="&#xef;" k="-39" />
    <hkern u1="C" u2="&#xee;" k="-18" />
    <hkern u1="D" u2="&#x141;" k="12" />
    <hkern u1="D" u2="&#x126;" k="12" />
    <hkern u1="E" u2="&#x131;" k="18" />
    <hkern u1="F" g2="Hbar.sc" k="27" />
    <hkern u1="F" g2="M.sc" k="43" />
    <hkern u1="F" g2="Lslash.sc" k="27" />
    <hkern u1="F" u2="&#x131;" k="80" />
    <hkern u1="F" u2="&#xef;" k="-41" />
    <hkern u1="F" u2="&#xed;" k="29" />
    <hkern u1="F" u2="&#xdf;" k="27" />
    <hkern u1="F" u2="x" k="90" />
    <hkern u1="F" u2="v" k="43" />
    <hkern u1="F" u2="M" k="14" />
    <hkern u1="F" u2="&#x34;" k="68" />
    <hkern u1="F" u2="&#x2f;" k="82" />
    <hkern u1="F" u2="&#x2a;" k="-18" />
    <hkern u1="F" u2="&#x20;" k="49" />
    <hkern u1="L" g2="periodcentered.case" k="522" />
    <hkern u1="L" g2="at.case" k="33" />
    <hkern u1="L" u2="&#xb7;" k="434" />
    <hkern u1="L" u2="&#x31;" k="31" />
    <hkern u1="M" g2="V.sc" k="16" />
    <hkern u1="M" g2="braceright.case" k="29" />
    <hkern u1="M" g2="parenright.case" k="23" />
    <hkern u1="M" u2="&#x2122;" k="16" />
    <hkern u1="M" u2="&#x7d;" k="33" />
    <hkern u1="M" u2="v" k="25" />
    <hkern u1="M" u2="V" k="27" />
    <hkern u1="M" u2="&#x29;" k="33" />
    <hkern u1="O" u2="&#x141;" k="12" />
    <hkern u1="O" u2="&#x126;" k="12" />
    <hkern u1="P" g2="M.sc" k="12" />
    <hkern u1="P" g2="braceright.case" k="84" />
    <hkern u1="P" g2="bracketright.case" k="27" />
    <hkern u1="P" g2="parenright.case" k="76" />
    <hkern u1="P" u2="X" k="23" />
    <hkern u1="P" u2="&#x34;" k="61" />
    <hkern u1="P" u2="&#x2f;" k="74" />
    <hkern u1="P" u2="&#x20;" k="55" />
    <hkern u1="Q" u2="&#x141;" k="12" />
    <hkern u1="Q" u2="&#x126;" k="12" />
    <hkern u1="R" u2="&#x34;" k="29" />
    <hkern u1="T" g2="Jcircumflex.sc" k="-2" />
    <hkern u1="T" g2="Hbar.sc" k="37" />
    <hkern u1="T" g2="Icircumflex.sc" k="4" />
    <hkern u1="T" g2="Lslash.sc" k="37" />
    <hkern u1="T" u2="&#x161;" k="156" />
    <hkern u1="T" u2="&#x159;" k="127" />
    <hkern u1="T" u2="&#x131;" k="211" />
    <hkern u1="T" u2="&#x12d;" k="-18" />
    <hkern u1="T" u2="&#x129;" k="-16" />
    <hkern u1="T" u2="&#xef;" k="-49" />
    <hkern u1="T" u2="&#xed;" k="41" />
    <hkern u1="T" u2="&#xdf;" k="35" />
    <hkern u1="T" u2="&#x40;" k="74" />
    <hkern u1="T" u2="&#x38;" k="25" />
    <hkern u1="T" u2="&#x36;" k="23" />
    <hkern u1="T" u2="&#x34;" k="106" />
    <hkern u1="T" u2="&#x30;" k="20" />
    <hkern u1="V" g2="M.sc" k="45" />
    <hkern u1="V" g2="braceright.case" k="29" />
    <hkern u1="V" g2="at.case" k="23" />
    <hkern u1="V" u2="&#x159;" k="55" />
    <hkern u1="V" u2="&#x131;" k="72" />
    <hkern u1="V" u2="&#xef;" k="-12" />
    <hkern u1="V" u2="&#xed;" k="27" />
    <hkern u1="V" u2="&#xdf;" k="31" />
    <hkern u1="V" u2="&#xae;" k="29" />
    <hkern u1="V" u2="x" k="51" />
    <hkern u1="V" u2="v" k="25" />
    <hkern u1="V" u2="M" k="29" />
    <hkern u1="V" u2="&#x40;" k="39" />
    <hkern u1="V" u2="&#x39;" k="20" />
    <hkern u1="V" u2="&#x38;" k="33" />
    <hkern u1="V" u2="&#x36;" k="25" />
    <hkern u1="V" u2="&#x35;" k="27" />
    <hkern u1="V" u2="&#x34;" k="70" />
    <hkern u1="V" u2="&#x30;" k="25" />
    <hkern u1="V" u2="&#x2f;" k="84" />
    <hkern u1="V" u2="&#x26;" k="31" />
    <hkern u1="V" u2="&#x20;" k="63" />
    <hkern u1="W" u2="&#x159;" k="63" />
    <hkern u1="W" u2="&#x131;" k="76" />
    <hkern u1="W" u2="&#x12d;" k="-29" />
    <hkern u1="W" u2="&#xef;" k="-33" />
    <hkern u1="W" u2="&#xed;" k="27" />
    <hkern u1="W" u2="&#xec;" k="-23" />
    <hkern u1="W" u2="&#xdf;" k="27" />
    <hkern u1="W" u2="&#x40;" k="37" />
    <hkern u1="W" u2="&#x38;" k="29" />
    <hkern u1="W" u2="&#x36;" k="23" />
    <hkern u1="W" u2="&#x35;" k="23" />
    <hkern u1="W" u2="&#x34;" k="74" />
    <hkern u1="W" u2="&#x30;" k="20" />
    <hkern u1="X" g2="V.sc" k="10" />
    <hkern u1="X" u2="&#xae;" k="23" />
    <hkern u1="X" u2="v" k="80" />
    <hkern u1="Y" g2="adieresis.alt1" k="211" />
    <hkern u1="Y" g2="Jcircumflex.sc" k="2" />
    <hkern u1="Y" g2="Itilde.sc" k="-16" />
    <hkern u1="Y" g2="Hbar.sc" k="76" />
    <hkern u1="Y" g2="Idieresis.sc" k="-10" />
    <hkern u1="Y" g2="Icircumflex.sc" k="27" />
    <hkern u1="Y" g2="Lslash.sc" k="76" />
    <hkern u1="Y" g2="at.case" k="45" />
    <hkern u1="Y" u2="&#x161;" k="123" />
    <hkern u1="Y" u2="&#x159;" k="92" />
    <hkern u1="Y" u2="&#x141;" k="12" />
    <hkern u1="Y" u2="&#x131;" k="162" />
    <hkern u1="Y" u2="&#x12d;" k="-51" />
    <hkern u1="Y" u2="&#x12b;" k="-12" />
    <hkern u1="Y" u2="&#x126;" k="12" />
    <hkern u1="Y" u2="&#xef;" k="-51" />
    <hkern u1="Y" u2="&#xed;" k="57" />
    <hkern u1="Y" u2="&#xec;" k="-41" />
    <hkern u1="Y" u2="&#xdf;" k="59" />
    <hkern u1="Y" u2="&#x40;" k="96" />
    <hkern u1="Y" u2="&#x39;" k="39" />
    <hkern u1="Y" u2="&#x38;" k="59" />
    <hkern u1="Y" u2="&#x36;" k="55" />
    <hkern u1="Y" u2="&#x35;" k="43" />
    <hkern u1="Y" u2="&#x34;" k="139" />
    <hkern u1="Y" u2="&#x33;" k="29" />
    <hkern u1="Y" u2="&#x32;" k="23" />
    <hkern u1="Y" u2="&#x30;" k="53" />
    <hkern u1="Z" u2="&#x131;" k="20" />
    <hkern u1="[" g2="Jcircumflex.sc" k="-129" />
    <hkern u1="[" g2="Hbar.sc" k="29" />
    <hkern u1="[" g2="M.sc" k="31" />
    <hkern u1="[" g2="J.sc" k="-129" />
    <hkern u1="[" g2="Lslash.sc" k="29" />
    <hkern u1="[" g2="eight.oldstyle" k="29" />
    <hkern u1="[" g2="six.oldstyle" k="27" />
    <hkern u1="[" g2="four.oldstyle" k="47" />
    <hkern u1="[" g2="two.oldstyle" k="41" />
    <hkern u1="[" g2="one.oldstyle" k="45" />
    <hkern u1="[" g2="zero.oldstyle" k="51" />
    <hkern u1="[" u2="&#x135;" k="-102" />
    <hkern u1="[" u2="&#x134;" k="-182" />
    <hkern u1="[" u2="&#xef;" k="-18" />
    <hkern u1="[" u2="&#x7b;" k="20" />
    <hkern u1="[" u2="x" k="27" />
    <hkern u1="[" u2="v" k="43" />
    <hkern u1="[" u2="j" k="-102" />
    <hkern u1="[" u2="f" k="25" />
    <hkern u1="[" u2="J" k="-182" />
    <hkern u1="[" u2="&#x38;" k="29" />
    <hkern u1="[" u2="&#x36;" k="27" />
    <hkern u1="[" u2="&#x34;" k="37" />
    <hkern u1="[" u2="&#x30;" k="25" />
    <hkern u1="[" u2="&#x28;" k="20" />
    <hkern u1="\" g2="V.sc" k="59" />
    <hkern u1="\" g2="three.oldstyle" k="25" />
    <hkern u1="\" g2="one.oldstyle" k="41" />
    <hkern u1="\" u2="&#x2019;" k="113" />
    <hkern u1="\" u2="v" k="51" />
    <hkern u1="\" u2="V" k="84" />
    <hkern u1="\" u2="&#x31;" k="31" />
    <hkern u1="c" u2="Y" k="195" />
    <hkern u1="c" u2="W" k="72" />
    <hkern u1="c" u2="V" k="66" />
    <hkern u1="c" u2="T" k="229" />
    <hkern u1="c" u2="S" k="23" />
    <hkern u1="d" u2="Z" k="12" />
    <hkern u1="f" u2="&#x12d;" k="-82" />
    <hkern u1="f" u2="&#x12b;" k="-72" />
    <hkern u1="f" u2="&#x129;" k="-43" />
    <hkern u1="f" u2="&#xef;" k="-104" />
    <hkern u1="f" u2="&#xec;" k="-45" />
    <hkern u1="f" u2="&#xc6;" k="59" />
    <hkern u1="f" u2="\" k="-10" />
    <hkern u1="f" u2="Y" k="-35" />
    <hkern u1="f" u2="W" k="-18" />
    <hkern u1="f" u2="&#x2f;" k="25" />
    <hkern u1="f" u2="&#x20;" k="43" />
    <hkern u1="k" u2="Y" k="168" />
    <hkern u1="k" u2="W" k="70" />
    <hkern u1="k" u2="V" k="61" />
    <hkern u1="k" u2="T" k="182" />
    <hkern u1="k" u2="S" k="20" />
    <hkern u1="l" u2="&#xb7;" k="131" />
    <hkern u1="l" u2="Z" k="12" />
    <hkern u1="r" u2="&#xc6;" k="119" />
    <hkern u1="r" u2="Z" k="78" />
    <hkern u1="r" u2="Y" k="133" />
    <hkern u1="r" u2="X" k="92" />
    <hkern u1="r" u2="W" k="25" />
    <hkern u1="r" u2="V" k="23" />
    <hkern u1="r" u2="T" k="170" />
    <hkern u1="r" u2="M" k="29" />
    <hkern u1="s" u2="Y" k="209" />
    <hkern u1="s" u2="W" k="90" />
    <hkern u1="s" u2="V" k="92" />
    <hkern u1="s" u2="T" k="197" />
    <hkern u1="t" u2="&#xc6;" k="43" />
    <hkern u1="t" u2="Z" k="49" />
    <hkern u1="t" u2="Y" k="90" />
    <hkern u1="t" u2="X" k="49" />
    <hkern u1="t" u2="W" k="23" />
    <hkern u1="t" u2="V" k="23" />
    <hkern u1="t" u2="T" k="76" />
    <hkern u1="t" u2="M" k="20" />
    <hkern u1="v" u2="&#x2122;" k="18" />
    <hkern u1="v" u2="&#x142;" k="14" />
    <hkern u1="v" u2="&#xc6;" k="86" />
    <hkern u1="v" u2="&#x7d;" k="51" />
    <hkern u1="v" u2="]" k="43" />
    <hkern u1="v" u2="\" k="23" />
    <hkern u1="v" u2="Z" k="57" />
    <hkern u1="v" u2="Y" k="117" />
    <hkern u1="v" u2="X" k="80" />
    <hkern u1="v" u2="W" k="27" />
    <hkern u1="v" u2="V" k="23" />
    <hkern u1="v" u2="T" k="174" />
    <hkern u1="v" u2="M" k="25" />
    <hkern u1="v" u2="&#x2f;" k="51" />
    <hkern u1="v" u2="&#x29;" k="49" />
    <hkern u1="v" u2="&#x20;" k="51" />
    <hkern u1="w" u2="&#x142;" k="12" />
    <hkern u1="w" u2="&#xc6;" k="82" />
    <hkern u1="w" u2="Z" k="55" />
    <hkern u1="w" u2="Y" k="117" />
    <hkern u1="w" u2="X" k="80" />
    <hkern u1="w" u2="W" k="31" />
    <hkern u1="w" u2="V" k="27" />
    <hkern u1="w" u2="T" k="174" />
    <hkern u1="w" u2="M" k="25" />
    <hkern u1="x" u2="&#x2122;" k="37" />
    <hkern u1="x" u2="&#x7d;" k="57" />
    <hkern u1="x" u2="]" k="27" />
    <hkern u1="x" u2="\" k="33" />
    <hkern u1="x" u2="Y" k="154" />
    <hkern u1="x" u2="W" k="57" />
    <hkern u1="x" u2="V" k="53" />
    <hkern u1="x" u2="T" k="180" />
    <hkern u1="x" u2="S" k="10" />
    <hkern u1="x" u2="&#x29;" k="53" />
    <hkern u1="y" u2="&#x142;" k="14" />
    <hkern u1="y" u2="&#xc6;" k="86" />
    <hkern u1="y" u2="Z" k="57" />
    <hkern u1="y" u2="Y" k="117" />
    <hkern u1="y" u2="X" k="80" />
    <hkern u1="y" u2="W" k="27" />
    <hkern u1="y" u2="V" k="25" />
    <hkern u1="y" u2="T" k="174" />
    <hkern u1="y" u2="M" k="25" />
    <hkern u1="z" u2="Y" k="152" />
    <hkern u1="z" u2="W" k="51" />
    <hkern u1="z" u2="V" k="47" />
    <hkern u1="z" u2="T" k="188" />
    <hkern u1="&#x7b;" g2="Jcircumflex.sc" k="-139" />
    <hkern u1="&#x7b;" g2="Hbar.sc" k="53" />
    <hkern u1="&#x7b;" g2="M.sc" k="61" />
    <hkern u1="&#x7b;" g2="J.sc" k="-139" />
    <hkern u1="&#x7b;" g2="Lslash.sc" k="53" />
    <hkern u1="&#x7b;" g2="nine.oldstyle" k="20" />
    <hkern u1="&#x7b;" g2="eight.oldstyle" k="51" />
    <hkern u1="&#x7b;" g2="six.oldstyle" k="49" />
    <hkern u1="&#x7b;" g2="five.oldstyle" k="23" />
    <hkern u1="&#x7b;" g2="four.oldstyle" k="98" />
    <hkern u1="&#x7b;" g2="two.oldstyle" k="70" />
    <hkern u1="&#x7b;" g2="one.oldstyle" k="61" />
    <hkern u1="&#x7b;" g2="zero.oldstyle" k="88" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-111" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-193" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-27" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="37" />
    <hkern u1="&#x7b;" u2="x" k="57" />
    <hkern u1="&#x7b;" u2="v" k="51" />
    <hkern u1="&#x7b;" u2="j" k="-111" />
    <hkern u1="&#x7b;" u2="f" k="35" />
    <hkern u1="&#x7b;" u2="M" k="33" />
    <hkern u1="&#x7b;" u2="J" k="-193" />
    <hkern u1="&#x7b;" u2="&#x39;" k="31" />
    <hkern u1="&#x7b;" u2="&#x38;" k="51" />
    <hkern u1="&#x7b;" u2="&#x36;" k="49" />
    <hkern u1="&#x7b;" u2="&#x35;" k="33" />
    <hkern u1="&#x7b;" u2="&#x34;" k="104" />
    <hkern u1="&#x7b;" u2="&#x33;" k="20" />
    <hkern u1="&#x7b;" u2="&#x30;" k="47" />
    <hkern u1="&#x7b;" u2="&#x28;" k="43" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="37" />
    <hkern u1="&#x7d;" u2="]" k="20" />
    <hkern u1="&#x7d;" u2="&#x29;" k="37" />
    <hkern u1="&#xa1;" g2="Jcircumflex.sc" k="-47" />
    <hkern u1="&#xa1;" g2="J.sc" k="-47" />
    <hkern u1="&#xa1;" u2="&#x135;" k="-20" />
    <hkern u1="&#xa1;" u2="&#x134;" k="-92" />
    <hkern u1="&#xa1;" u2="j" k="-20" />
    <hkern u1="&#xa1;" u2="V" k="33" />
    <hkern u1="&#xa1;" u2="J" k="-92" />
    <hkern u1="&#xa3;" g2="three.oldstyle" k="37" />
    <hkern u1="&#xae;" u2="X" k="27" />
    <hkern u1="&#xae;" u2="V" k="29" />
    <hkern u1="&#xb0;" g2="nine.oldstyle" k="61" />
    <hkern u1="&#xb0;" g2="five.oldstyle" k="49" />
    <hkern u1="&#xb0;" g2="four.oldstyle" k="184" />
    <hkern u1="&#xb0;" g2="three.oldstyle" k="57" />
    <hkern u1="&#xb0;" g2="zero.oldstyle" k="43" />
    <hkern u1="&#xb0;" u2="&#x34;" k="127" />
    <hkern u1="&#xb7;" g2="l.alt1" k="131" />
    <hkern u1="&#xb7;" g2="seven.oldstyle" k="59" />
    <hkern u1="&#xb7;" g2="four.oldstyle" k="113" />
    <hkern u1="&#xb7;" g2="three.oldstyle" k="57" />
    <hkern u1="&#xb7;" g2="two.oldstyle" k="47" />
    <hkern u1="&#xb7;" u2="l" k="131" />
    <hkern u1="&#xb7;" u2="&#x39;" k="23" />
    <hkern u1="&#xb7;" u2="&#x37;" k="63" />
    <hkern u1="&#xb7;" u2="&#x33;" k="25" />
    <hkern u1="&#xb7;" u2="&#x32;" k="49" />
    <hkern u1="&#xb7;" u2="&#x31;" k="51" />
    <hkern u1="&#xbf;" g2="Jcircumflex.sc" k="-57" />
    <hkern u1="&#xbf;" g2="V.sc" k="51" />
    <hkern u1="&#xbf;" g2="J.sc" k="-57" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-20" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-39" />
    <hkern u1="&#xbf;" u2="v" k="37" />
    <hkern u1="&#xbf;" u2="j" k="-20" />
    <hkern u1="&#xbf;" u2="V" k="74" />
    <hkern u1="&#xbf;" u2="J" k="-39" />
    <hkern u1="&#xce;" g2="braceright.case" k="-2" />
    <hkern u1="&#xce;" g2="parenright.case" k="-2" />
    <hkern u1="&#xd0;" u2="&#x141;" k="12" />
    <hkern u1="&#xd0;" u2="&#x126;" k="12" />
    <hkern u1="&#xd2;" u2="&#x141;" k="12" />
    <hkern u1="&#xd2;" u2="&#x126;" k="12" />
    <hkern u1="&#xd3;" u2="&#x141;" k="12" />
    <hkern u1="&#xd3;" u2="&#x126;" k="12" />
    <hkern u1="&#xd4;" u2="&#x141;" k="12" />
    <hkern u1="&#xd4;" u2="&#x126;" k="12" />
    <hkern u1="&#xd5;" u2="&#x141;" k="12" />
    <hkern u1="&#xd5;" u2="&#x126;" k="12" />
    <hkern u1="&#xd6;" u2="&#x141;" k="12" />
    <hkern u1="&#xd6;" u2="&#x126;" k="12" />
    <hkern u1="&#xdd;" u2="&#x141;" k="12" />
    <hkern u1="&#xdd;" u2="&#x126;" k="12" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="59" />
    <hkern u1="&#xde;" g2="AE.sc" k="84" />
    <hkern u1="&#xde;" g2="Y.sc" k="20" />
    <hkern u1="&#xde;" g2="X.sc" k="20" />
    <hkern u1="&#xde;" g2="V.sc" k="10" />
    <hkern u1="&#xde;" g2="A.sc" k="37" />
    <hkern u1="&#xde;" g2="braceright.case" k="96" />
    <hkern u1="&#xde;" g2="bracketright.case" k="33" />
    <hkern u1="&#xde;" g2="parenright.case" k="100" />
    <hkern u1="&#xde;" u2="&#x2122;" k="29" />
    <hkern u1="&#xde;" u2="&#x7d;" k="70" />
    <hkern u1="&#xde;" u2="]" k="31" />
    <hkern u1="&#xde;" u2="\" k="23" />
    <hkern u1="&#xde;" u2="X" k="53" />
    <hkern u1="&#xde;" u2="V" k="29" />
    <hkern u1="&#xde;" u2="&#x37;" k="33" />
    <hkern u1="&#xde;" u2="&#x2f;" k="31" />
    <hkern u1="&#xde;" u2="&#x29;" k="70" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="23" />
    <hkern u1="&#xdf;" u2="&#xc6;" k="23" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="43" />
    <hkern u1="&#xdf;" u2="x" k="29" />
    <hkern u1="&#xdf;" u2="v" k="29" />
    <hkern u1="&#xdf;" u2="]" k="27" />
    <hkern u1="&#xdf;" u2="\" k="23" />
    <hkern u1="&#xdf;" u2="Z" k="23" />
    <hkern u1="&#xdf;" u2="Y" k="100" />
    <hkern u1="&#xdf;" u2="X" k="33" />
    <hkern u1="&#xdf;" u2="W" k="55" />
    <hkern u1="&#xdf;" u2="V" k="59" />
    <hkern u1="&#xdf;" u2="U" k="25" />
    <hkern u1="&#xdf;" u2="T" k="68" />
    <hkern u1="&#xdf;" u2="S" k="10" />
    <hkern u1="&#xdf;" u2="M" k="12" />
    <hkern u1="&#xdf;" u2="A" k="14" />
    <hkern u1="&#xdf;" u2="&#x29;" k="43" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-53" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-47" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-33" />
    <hkern u1="&#xef;" u2="]" k="-25" />
    <hkern u1="&#xef;" u2="\" k="-20" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-49" />
    <hkern u1="&#xef;" u2="&#x29;" k="-33" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="55" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="31" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="86" />
    <hkern u1="&#xf0;" u2="x" k="29" />
    <hkern u1="&#xf0;" u2="v" k="25" />
    <hkern u1="&#xf0;" u2="]" k="49" />
    <hkern u1="&#xf0;" u2="\" k="74" />
    <hkern u1="&#xf0;" u2="Z" k="29" />
    <hkern u1="&#xf0;" u2="Y" k="184" />
    <hkern u1="&#xf0;" u2="X" k="39" />
    <hkern u1="&#xf0;" u2="W" k="98" />
    <hkern u1="&#xf0;" u2="V" k="92" />
    <hkern u1="&#xf0;" u2="U" k="23" />
    <hkern u1="&#xf0;" u2="T" k="145" />
    <hkern u1="&#xf0;" u2="S" k="10" />
    <hkern u1="&#xf0;" u2="M" k="14" />
    <hkern u1="&#xf0;" u2="A" k="16" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="27" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="39" />
    <hkern u1="&#xf0;" u2="&#x29;" k="84" />
    <hkern u1="&#x104;" g2="y.alt1" k="-104" />
    <hkern u1="&#x104;" g2="J.sc" k="-328" />
    <hkern u1="&#x104;" g2="braceright.case" k="-133" />
    <hkern u1="&#x104;" g2="bracketright.case" k="-123" />
    <hkern u1="&#x104;" g2="parenright.case" k="-133" />
    <hkern u1="&#x104;" u2="&#x134;" k="-326" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-92" />
    <hkern u1="&#x104;" u2="&#xfe;" k="-37" />
    <hkern u1="&#x104;" u2="&#x7d;" k="-86" />
    <hkern u1="&#x104;" u2="&#x7c;" k="-33" />
    <hkern u1="&#x104;" u2="p" k="-35" />
    <hkern u1="&#x104;" u2="j" k="-293" />
    <hkern u1="&#x104;" u2="g" k="-31" />
    <hkern u1="&#x104;" u2="]" k="-86" />
    <hkern u1="&#x104;" u2="J" k="-326" />
    <hkern u1="&#x104;" u2="&#x3b;" k="-115" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-156" />
    <hkern u1="&#x104;" u2="&#x29;" k="-86" />
    <hkern u1="&#x105;" u2="&#x7d;" k="31" />
    <hkern u1="&#x105;" u2="j" k="-184" />
    <hkern u1="&#x105;" u2="&#x2c;" k="-45" />
    <hkern u1="&#x105;" u2="&#x29;" k="31" />
    <hkern u1="&#x10e;" u2="&#x141;" k="12" />
    <hkern u1="&#x10e;" u2="&#x126;" k="12" />
    <hkern u1="&#x10f;" g2="l.alt1" k="-70" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-160" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-14" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-14" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-86" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-57" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-145" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-49" />
    <hkern u1="&#x10f;" u2="l" k="-57" />
    <hkern u1="&#x10f;" u2="k" k="-57" />
    <hkern u1="&#x10f;" u2="j" k="-86" />
    <hkern u1="&#x10f;" u2="i" k="-86" />
    <hkern u1="&#x10f;" u2="h" k="-57" />
    <hkern u1="&#x10f;" u2="b" k="-57" />
    <hkern u1="&#x10f;" u2="]" k="-137" />
    <hkern u1="&#x10f;" u2="\" k="-170" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-59" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-111" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-145" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-68" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-68" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-31" />
    <hkern u1="&#x110;" u2="&#x141;" k="12" />
    <hkern u1="&#x110;" u2="&#x126;" k="12" />
    <hkern u1="&#x111;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x118;" g2="y.alt1" k="-61" />
    <hkern u1="&#x118;" g2="J.sc" k="-283" />
    <hkern u1="&#x118;" g2="braceright.case" k="-88" />
    <hkern u1="&#x118;" g2="bracketright.case" k="-78" />
    <hkern u1="&#x118;" g2="parenright.case" k="-88" />
    <hkern u1="&#x118;" u2="&#x134;" k="-281" />
    <hkern u1="&#x118;" u2="&#x12e;" k="-47" />
    <hkern u1="&#x118;" u2="&#x7d;" k="-41" />
    <hkern u1="&#x118;" u2="j" k="-250" />
    <hkern u1="&#x118;" u2="g" k="14" />
    <hkern u1="&#x118;" u2="]" k="-41" />
    <hkern u1="&#x118;" u2="J" k="-281" />
    <hkern u1="&#x118;" u2="&#x3b;" k="-68" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-111" />
    <hkern u1="&#x118;" u2="&#x29;" k="-41" />
    <hkern u1="&#x126;" g2="t.alt1" k="23" />
    <hkern u1="&#x126;" g2="ae.alt1" k="29" />
    <hkern u1="&#x126;" g2="y.alt1" k="23" />
    <hkern u1="&#x126;" g2="a.alt1" k="29" />
    <hkern u1="&#x126;" g2="l.alt1" k="20" />
    <hkern u1="&#x126;" g2="Gdotaccent.sc" k="16" />
    <hkern u1="&#x126;" g2="OE.sc" k="16" />
    <hkern u1="&#x126;" g2="Q.sc" k="16" />
    <hkern u1="&#x126;" g2="O.sc" k="16" />
    <hkern u1="&#x126;" g2="G.sc" k="16" />
    <hkern u1="&#x126;" g2="C.sc" k="16" />
    <hkern u1="&#x126;" g2="braceright.case" k="39" />
    <hkern u1="&#x126;" g2="parenright.case" k="33" />
    <hkern u1="&#x126;" g2="f_j" k="23" />
    <hkern u1="&#x126;" g2="f_f_j" k="23" />
    <hkern u1="&#x126;" g2="f_f_l" k="23" />
    <hkern u1="&#x126;" g2="f_f_i" k="23" />
    <hkern u1="&#x126;" g2="fl" k="23" />
    <hkern u1="&#x126;" g2="fi" k="23" />
    <hkern u1="&#x126;" g2="f_f" k="23" />
    <hkern u1="&#x126;" u2="&#x1ef2;" k="14" />
    <hkern u1="&#x126;" u2="&#x17f;" k="23" />
    <hkern u1="&#x126;" u2="&#x17c;" k="25" />
    <hkern u1="&#x126;" u2="&#x178;" k="14" />
    <hkern u1="&#x126;" u2="&#x176;" k="14" />
    <hkern u1="&#x126;" u2="&#x153;" k="35" />
    <hkern u1="&#x126;" u2="&#x152;" k="12" />
    <hkern u1="&#x126;" u2="&#x150;" k="12" />
    <hkern u1="&#x126;" u2="&#x14e;" k="12" />
    <hkern u1="&#x126;" u2="&#x14c;" k="12" />
    <hkern u1="&#x126;" u2="&#x141;" k="14" />
    <hkern u1="&#x126;" u2="&#x126;" k="14" />
    <hkern u1="&#x126;" u2="&#x122;" k="12" />
    <hkern u1="&#x126;" u2="&#x121;" k="35" />
    <hkern u1="&#x126;" u2="&#x120;" k="12" />
    <hkern u1="&#x126;" u2="&#x11e;" k="12" />
    <hkern u1="&#x126;" u2="&#x11c;" k="12" />
    <hkern u1="&#x126;" u2="&#x10c;" k="12" />
    <hkern u1="&#x126;" u2="&#x10a;" k="12" />
    <hkern u1="&#x126;" u2="&#x108;" k="12" />
    <hkern u1="&#x126;" u2="&#x106;" k="12" />
    <hkern u1="&#x126;" u2="&#xf0;" k="35" />
    <hkern u1="&#x126;" u2="&#xe6;" k="35" />
    <hkern u1="&#x126;" u2="&#xdd;" k="14" />
    <hkern u1="&#x126;" u2="&#xd6;" k="12" />
    <hkern u1="&#x126;" u2="&#xd5;" k="12" />
    <hkern u1="&#x126;" u2="&#xd4;" k="12" />
    <hkern u1="&#x126;" u2="&#xd3;" k="12" />
    <hkern u1="&#x126;" u2="&#xd2;" k="12" />
    <hkern u1="&#x126;" u2="&#xc7;" k="12" />
    <hkern u1="&#x126;" u2="z" k="25" />
    <hkern u1="&#x126;" u2="y" k="23" />
    <hkern u1="&#x126;" u2="x" k="20" />
    <hkern u1="&#x126;" u2="w" k="23" />
    <hkern u1="&#x126;" u2="v" k="23" />
    <hkern u1="&#x126;" u2="u" k="18" />
    <hkern u1="&#x126;" u2="s" k="29" />
    <hkern u1="&#x126;" u2="q" k="35" />
    <hkern u1="&#x126;" u2="o" k="35" />
    <hkern u1="&#x126;" u2="g" k="35" />
    <hkern u1="&#x126;" u2="f" k="23" />
    <hkern u1="&#x126;" u2="e" k="35" />
    <hkern u1="&#x126;" u2="d" k="35" />
    <hkern u1="&#x126;" u2="c" k="35" />
    <hkern u1="&#x126;" u2="a" k="35" />
    <hkern u1="&#x126;" u2="Y" k="14" />
    <hkern u1="&#x126;" u2="Q" k="12" />
    <hkern u1="&#x126;" u2="O" k="12" />
    <hkern u1="&#x126;" u2="G" k="12" />
    <hkern u1="&#x126;" u2="C" k="12" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-29" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-14" />
    <hkern u1="&#x129;" u2="\" k="-39" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-27" />
    <hkern u1="&#x129;" u2="&#x29;" k="-14" />
    <hkern u1="&#x12d;" u2="&#x2122;" k="-14" />
    <hkern u1="&#x12d;" u2="\" k="-25" />
    <hkern u1="&#x12d;" u2="&#x2a;" k="-25" />
    <hkern u1="&#x12e;" g2="J.sc" k="-172" />
    <hkern u1="&#x12e;" u2="&#x134;" k="-170" />
    <hkern u1="&#x12e;" u2="j" k="-137" />
    <hkern u1="&#x12e;" u2="J" k="-170" />
    <hkern u1="&#x12f;" u2="j" k="-172" />
    <hkern u1="&#x12f;" u2="&#x2c;" k="-33" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-51" />
    <hkern u1="&#x13e;" g2="l.alt1" k="-100" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-188" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="-25" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="-41" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-25" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-41" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-35" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-98" />
    <hkern u1="&#x13e;" u2="&#x133;" k="-111" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-88" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-18" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-176" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-80" />
    <hkern u1="&#x13e;" u2="l" k="-88" />
    <hkern u1="&#x13e;" u2="k" k="-88" />
    <hkern u1="&#x13e;" u2="j" k="-111" />
    <hkern u1="&#x13e;" u2="i" k="-111" />
    <hkern u1="&#x13e;" u2="h" k="-88" />
    <hkern u1="&#x13e;" u2="b" k="-88" />
    <hkern u1="&#x13e;" u2="]" k="-166" />
    <hkern u1="&#x13e;" u2="\" k="-199" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-90" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-137" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-176" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-96" />
    <hkern u1="&#x13e;" u2="&#x22;" k="-96" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-59" />
    <hkern u1="&#x14c;" u2="&#x141;" k="12" />
    <hkern u1="&#x14c;" u2="&#x126;" k="12" />
    <hkern u1="&#x14e;" u2="&#x141;" k="12" />
    <hkern u1="&#x14e;" u2="&#x126;" k="12" />
    <hkern u1="&#x150;" u2="&#x141;" k="12" />
    <hkern u1="&#x150;" u2="&#x126;" k="12" />
    <hkern u1="&#x162;" g2="Icircumflex.sc" k="4" />
    <hkern u1="&#x162;" u2="&#xdf;" k="35" />
    <hkern u1="&#x164;" u2="&#xdf;" k="35" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-41" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-27" />
    <hkern u1="&#x165;" u2="]" k="-16" />
    <hkern u1="&#x165;" u2="\" k="-51" />
    <hkern u1="&#x165;" u2="&#x29;" k="-27" />
    <hkern u1="&#x166;" u2="&#xdf;" k="35" />
    <hkern u1="&#x173;" u2="&#x7d;" k="51" />
    <hkern u1="&#x173;" u2="j" k="-164" />
    <hkern u1="&#x173;" u2="&#x2c;" k="-25" />
    <hkern u1="&#x173;" u2="&#x29;" k="51" />
    <hkern u1="&#x174;" u2="&#xdf;" k="27" />
    <hkern u1="&#x176;" u2="&#x141;" k="12" />
    <hkern u1="&#x176;" u2="&#x126;" k="12" />
    <hkern u1="&#x176;" u2="&#xdf;" k="59" />
    <hkern u1="&#x178;" u2="&#x141;" k="12" />
    <hkern u1="&#x178;" u2="&#x126;" k="12" />
    <hkern u1="&#x178;" u2="&#xdf;" k="59" />
    <hkern u1="&#x17f;" u2="&#x159;" k="-23" />
    <hkern u1="&#x17f;" u2="&#x135;" k="-43" />
    <hkern u1="&#x17f;" u2="&#x12d;" k="-166" />
    <hkern u1="&#x17f;" u2="&#x12b;" k="-156" />
    <hkern u1="&#x17f;" u2="&#x129;" k="-127" />
    <hkern u1="&#x17f;" u2="&#xef;" k="-188" />
    <hkern u1="&#x17f;" u2="&#xee;" k="-16" />
    <hkern u1="&#x17f;" u2="&#xec;" k="-131" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="27" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="27" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="27" />
    <hkern u1="&#x1ef2;" u2="&#x141;" k="12" />
    <hkern u1="&#x1ef2;" u2="&#x126;" k="12" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="59" />
    <hkern u1="&#x2019;" u2="&#xae;" k="20" />
    <hkern u1="&#x2019;" u2="&#x40;" k="53" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="139" />
    <hkern u1="&#x2019;" u2="&#x26;" k="20" />
    <hkern u1="&#x2019;" u2="&#x20;" k="45" />
    <hkern u1="&#x2044;" g2="four.dnom" k="43" />
    <hkern u1="&#x2044;" g2="eight.oldstyle" k="-70" />
    <hkern u1="&#x2044;" g2="seven.oldstyle" k="-31" />
    <hkern u1="&#x2044;" g2="six.oldstyle" k="-74" />
    <hkern u1="&#x2044;" g2="four.oldstyle" k="92" />
    <hkern u1="&#x2044;" g2="one.oldstyle" k="-33" />
    <hkern u1="&#x2044;" u2="&#x39;" k="-111" />
    <hkern u1="&#x2044;" u2="&#x38;" k="-70" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-281" />
    <hkern u1="&#x2044;" u2="&#x36;" k="-74" />
    <hkern u1="&#x2044;" u2="&#x35;" k="-125" />
    <hkern u1="&#x2044;" u2="&#x34;" k="23" />
    <hkern u1="&#x2044;" u2="&#x33;" k="-131" />
    <hkern u1="&#x2044;" u2="&#x32;" k="-139" />
    <hkern u1="&#x2044;" u2="&#x31;" k="-201" />
    <hkern u1="&#x2044;" u2="&#x30;" k="-72" />
    <hkern u1="&#x20ac;" g2="seven.oldstyle" k="23" />
    <hkern u1="&#x20ac;" g2="four.oldstyle" k="43" />
    <hkern u1="&#x20ac;" g2="three.oldstyle" k="51" />
    <hkern u1="&#x20ac;" g2="one.oldstyle" k="20" />
    <hkern u1="&#x2122;" g2="Dcroat.sc" k="-27" />
    <hkern u1="&#x2122;" g2="Eth.sc" k="-27" />
    <hkern u1="&#x2212;" g2="seven.oldstyle" k="51" />
    <hkern u1="&#x2212;" g2="four.oldstyle" k="100" />
    <hkern u1="&#x2212;" g2="three.oldstyle" k="55" />
    <hkern u1="&#x2212;" g2="two.oldstyle" k="39" />
    <hkern u1="&#x2212;" u2="&#x37;" k="47" />
    <hkern u1="&#x2212;" u2="&#x32;" k="33" />
    <hkern u1="&#x2212;" u2="&#x31;" k="39" />
    <hkern g1="f_f" u2="&#x12d;" k="-82" />
    <hkern g1="f_f" u2="&#x12b;" k="-72" />
    <hkern g1="f_f" u2="&#x129;" k="-43" />
    <hkern g1="f_f" u2="&#xef;" k="-104" />
    <hkern g1="f_f" u2="&#xec;" k="-45" />
    <hkern g1="parenleft.case" u2="&#x141;" k="35" />
    <hkern g1="parenleft.case" u2="&#x134;" k="-18" />
    <hkern g1="parenleft.case" u2="&#x126;" k="35" />
    <hkern g1="parenleft.case" u2="&#xce;" k="2" />
    <hkern g1="parenleft.case" u2="M" k="27" />
    <hkern g1="at.case" u2="&#xc6;" k="76" />
    <hkern g1="at.case" u2="Y" k="41" />
    <hkern g1="at.case" u2="V" k="20" />
    <hkern g1="at.case" u2="A" k="29" />
    <hkern g1="bracketleft.case" u2="&#x141;" k="25" />
    <hkern g1="bracketleft.case" u2="&#x126;" k="25" />
    <hkern g1="braceleft.case" u2="&#x141;" k="39" />
    <hkern g1="braceleft.case" u2="&#x134;" k="-18" />
    <hkern g1="braceleft.case" u2="&#x126;" k="39" />
    <hkern g1="braceleft.case" u2="&#xce;" k="2" />
    <hkern g1="braceleft.case" u2="V" k="31" />
    <hkern g1="braceleft.case" u2="M" k="31" />
    <hkern g1="guillemotright.case" u2="&#x141;" k="25" />
    <hkern g1="guillemotright.case" u2="&#x126;" k="25" />
    <hkern g1="questiondown.case" u2="V" k="31" />
    <hkern g1="guilsinglright.case" u2="&#x141;" k="25" />
    <hkern g1="guilsinglright.case" u2="&#x126;" k="25" />
    <hkern g1="zero.oldstyle" g2="four.oldstyle" k="63" />
    <hkern g1="zero.oldstyle" g2="three.oldstyle" k="20" />
    <hkern g1="zero.oldstyle" u2="&#x2044;" k="-94" />
    <hkern g1="zero.oldstyle" u2="&#xb0;" k="45" />
    <hkern g1="zero.oldstyle" u2="&#x7d;" k="88" />
    <hkern g1="zero.oldstyle" u2="]" k="51" />
    <hkern g1="zero.oldstyle" u2="\" k="74" />
    <hkern g1="zero.oldstyle" u2="&#x29;" k="88" />
    <hkern g1="one.oldstyle" u2="&#x2044;" k="-223" />
    <hkern g1="one.oldstyle" u2="&#x7d;" k="51" />
    <hkern g1="one.oldstyle" u2="]" k="31" />
    <hkern g1="one.oldstyle" u2="\" k="29" />
    <hkern g1="one.oldstyle" u2="&#x29;" k="49" />
    <hkern g1="two.oldstyle" u2="&#x2044;" k="-279" />
    <hkern g1="two.oldstyle" u2="&#x7d;" k="70" />
    <hkern g1="two.oldstyle" u2="]" k="41" />
    <hkern g1="two.oldstyle" u2="\" k="53" />
    <hkern g1="two.oldstyle" u2="&#x29;" k="66" />
    <hkern g1="three.oldstyle" u2="&#x2044;" k="-319" />
    <hkern g1="three.oldstyle" u2="&#xb0;" k="68" />
    <hkern g1="three.oldstyle" u2="&#x7d;" k="31" />
    <hkern g1="three.oldstyle" u2="\" k="90" />
    <hkern g1="three.oldstyle" u2="&#x29;" k="33" />
    <hkern g1="four.oldstyle" g2="one.oldstyle" k="35" />
    <hkern g1="four.oldstyle" g2="zero.oldstyle" k="29" />
    <hkern g1="four.oldstyle" u2="&#x2212;" k="39" />
    <hkern g1="four.oldstyle" u2="&#x2044;" k="-346" />
    <hkern g1="four.oldstyle" u2="&#xb7;" k="49" />
    <hkern g1="four.oldstyle" u2="&#xb0;" k="55" />
    <hkern g1="four.oldstyle" u2="&#x7d;" k="49" />
    <hkern g1="four.oldstyle" u2="]" k="35" />
    <hkern g1="four.oldstyle" u2="\" k="96" />
    <hkern g1="four.oldstyle" u2="&#x2d;" k="41" />
    <hkern g1="four.oldstyle" u2="&#x2b;" k="43" />
    <hkern g1="four.oldstyle" u2="&#x29;" k="47" />
    <hkern g1="five.oldstyle" u2="&#x2212;" k="45" />
    <hkern g1="five.oldstyle" u2="&#x2044;" k="-326" />
    <hkern g1="five.oldstyle" u2="&#xb7;" k="55" />
    <hkern g1="five.oldstyle" u2="&#x7d;" k="29" />
    <hkern g1="five.oldstyle" u2="\" k="53" />
    <hkern g1="five.oldstyle" u2="&#x2d;" k="39" />
    <hkern g1="five.oldstyle" u2="&#x2b;" k="43" />
    <hkern g1="five.oldstyle" u2="&#x29;" k="31" />
    <hkern g1="six.oldstyle" g2="four.oldstyle" k="49" />
    <hkern g1="six.oldstyle" g2="three.oldstyle" k="29" />
    <hkern g1="six.oldstyle" u2="&#x2044;" k="-123" />
    <hkern g1="six.oldstyle" u2="&#x7d;" k="20" />
    <hkern g1="seven.oldstyle" g2="four.oldstyle" k="131" />
    <hkern g1="seven.oldstyle" u2="&#xb7;" k="25" />
    <hkern g1="seven.oldstyle" u2="&#x7d;" k="41" />
    <hkern g1="seven.oldstyle" u2="]" k="31" />
    <hkern g1="seven.oldstyle" u2="&#x2f;" k="53" />
    <hkern g1="seven.oldstyle" u2="&#x2d;" k="31" />
    <hkern g1="seven.oldstyle" u2="&#x29;" k="41" />
    <hkern g1="eight.oldstyle" g2="four.oldstyle" k="49" />
    <hkern g1="eight.oldstyle" g2="three.oldstyle" k="33" />
    <hkern g1="eight.oldstyle" u2="&#x2044;" k="-119" />
    <hkern g1="eight.oldstyle" u2="&#x7d;" k="51" />
    <hkern g1="eight.oldstyle" u2="]" k="29" />
    <hkern g1="eight.oldstyle" u2="\" k="27" />
    <hkern g1="eight.oldstyle" u2="&#x29;" k="49" />
    <hkern g1="nine.oldstyle" u2="&#x2044;" k="-281" />
    <hkern g1="nine.oldstyle" u2="&#xb0;" k="72" />
    <hkern g1="nine.oldstyle" u2="&#x7d;" k="49" />
    <hkern g1="nine.oldstyle" u2="]" k="29" />
    <hkern g1="nine.oldstyle" u2="\" k="88" />
    <hkern g1="nine.oldstyle" u2="&#x29;" k="51" />
    <hkern g1="ampersand.sc" g2="V.sc" k="27" />
    <hkern g1="B.sc" g2="X.sc" k="10" />
    <hkern g1="B.sc" g2="V.sc" k="23" />
    <hkern g1="B.sc" u2="&#x2122;" k="35" />
    <hkern g1="B.sc" u2="&#x7d;" k="76" />
    <hkern g1="B.sc" u2="]" k="41" />
    <hkern g1="B.sc" u2="\" k="41" />
    <hkern g1="B.sc" u2="&#x29;" k="74" />
    <hkern g1="F.sc" g2="M.sc" k="12" />
    <hkern g1="F.sc" u2="&#x2f;" k="61" />
    <hkern g1="F.sc" u2="&#x20;" k="41" />
    <hkern g1="L.sc" u2="&#xb7;" k="432" />
    <hkern g1="M.sc" g2="V.sc" k="23" />
    <hkern g1="M.sc" u2="&#x2122;" k="33" />
    <hkern g1="M.sc" u2="&#x7d;" k="61" />
    <hkern g1="M.sc" u2="]" k="31" />
    <hkern g1="M.sc" u2="\" k="37" />
    <hkern g1="M.sc" u2="&#x29;" k="59" />
    <hkern g1="P.sc" g2="X.sc" k="20" />
    <hkern g1="P.sc" g2="V.sc" k="8" />
    <hkern g1="P.sc" u2="&#x2122;" k="16" />
    <hkern g1="P.sc" u2="&#x7d;" k="49" />
    <hkern g1="P.sc" u2="]" k="33" />
    <hkern g1="P.sc" u2="&#x2f;" k="57" />
    <hkern g1="P.sc" u2="&#x29;" k="49" />
    <hkern g1="P.sc" u2="&#x20;" k="45" />
    <hkern g1="V.sc" g2="M.sc" k="23" />
    <hkern g1="V.sc" g2="ampersand.sc" k="25" />
    <hkern g1="V.sc" u2="&#x2f;" k="59" />
    <hkern g1="V.sc" u2="&#x20;" k="53" />
    <hkern g1="Y.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Y.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Y.sc" u2="&#x40;" k="41" />
    <hkern g1="questiondown.sc" g2="Jcircumflex.sc" k="-41" />
    <hkern g1="questiondown.sc" g2="X.sc" k="18" />
    <hkern g1="questiondown.sc" g2="V.sc" k="23" />
    <hkern g1="questiondown.sc" g2="J.sc" k="-41" />
    <hkern g1="Icircumflex.sc" u2="&#x7d;" k="27" />
    <hkern g1="Thorn.sc" g2="X.sc" k="43" />
    <hkern g1="Thorn.sc" g2="V.sc" k="25" />
    <hkern g1="Thorn.sc" u2="&#x2122;" k="39" />
    <hkern g1="Thorn.sc" u2="&#x7d;" k="80" />
    <hkern g1="Thorn.sc" u2="]" k="39" />
    <hkern g1="Thorn.sc" u2="\" k="53" />
    <hkern g1="Thorn.sc" u2="&#x29;" k="80" />
    <hkern g1="Aogonek.sc" g2="J.sc" k="-244" />
    <hkern g1="Aogonek.sc" u2="&#x7d;" k="14" />
    <hkern g1="Aogonek.sc" u2="]" k="8" />
    <hkern g1="Aogonek.sc" u2="&#x3b;" k="-57" />
    <hkern g1="Aogonek.sc" u2="&#x2c;" k="-98" />
    <hkern g1="Aogonek.sc" u2="&#x29;" k="14" />
    <hkern g1="Eogonek.sc" g2="J.sc" k="-211" />
    <hkern g1="Eogonek.sc" u2="&#x3b;" k="-25" />
    <hkern g1="Eogonek.sc" u2="&#x2c;" k="-68" />
    <hkern g1="Hbar.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Y.sc" k="12" />
    <hkern g1="Hbar.sc" u2="&#x7d;" k="53" />
    <hkern g1="Hbar.sc" u2="]" k="29" />
    <hkern g1="Hbar.sc" u2="&#x29;" k="49" />
    <hkern g1="Iogonek.sc" g2="J.sc" k="-111" />
    <hkern g1="Jcircumflex.sc" u2="&#x7d;" k="27" />
    <hkern g1="seven.numr" u2="&#x2044;" k="74" />
    <hkern g1="l.alt1" u2="&#xb7;" k="197" />
    <hkern g1="y.alt1" u2="&#x142;" k="12" />
    <hkern g1="lslash.alt1" u2="x" k="-43" />
    <hkern g1="lcaron.alt1" u2="&#x2122;" k="-98" />
    <hkern g1="lcaron.alt1" u2="&#x133;" k="-20" />
    <hkern g1="lcaron.alt1" u2="&#x7d;" k="-86" />
    <hkern g1="lcaron.alt1" u2="j" k="-20" />
    <hkern g1="lcaron.alt1" u2="i" k="-20" />
    <hkern g1="lcaron.alt1" u2="]" k="-74" />
    <hkern g1="lcaron.alt1" u2="\" k="-109" />
    <hkern g1="lcaron.alt1" u2="&#x3f;" k="-14" />
    <hkern g1="lcaron.alt1" u2="&#x2a;" k="-47" />
    <hkern g1="lcaron.alt1" u2="&#x29;" k="-88" />
    <hkern g1="aogonek.alt1" g2="y.alt1" k="-8" />
    <hkern g1="aogonek.alt1" u2="&#x7d;" k="16" />
    <hkern g1="aogonek.alt1" u2="j" k="-199" />
    <hkern g1="aogonek.alt1" u2="]" k="16" />
    <hkern g1="aogonek.alt1" u2="&#x3b;" k="-16" />
    <hkern g1="aogonek.alt1" u2="&#x2c;" k="-59" />
    <hkern g1="aogonek.alt1" u2="&#x29;" k="18" />
    <hkern g1="tcaron.alt1" u2="&#x2122;" k="-6" />
    <hkern g1="tcaron.alt1" u2="&#x7d;" k="6" />
    <hkern g1="tcaron.alt1" u2="\" k="-16" />
    <hkern g1="tcaron.alt1" u2="&#x29;" k="6" />
    <hkern g1="space"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="space"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="51" />
    <hkern g1="space"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="51" />
    <hkern g1="space"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="space"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="59" />
    <hkern g1="space"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="80" />
    <hkern g1="space"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="66" />
    <hkern g1="space"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="space"
  g2="AE,AEacute"
  k="70" />
    <hkern g1="space"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="55" />
    <hkern g1="space"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="53" />
    <hkern g1="space"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="55" />
    <hkern g1="space"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="68" />
    <hkern g1="space"
  g2="AE.sc,AEacute.sc"
  k="68" />
    <hkern g1="space"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="51" />
    <hkern g1="space"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="41" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="84" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="76" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="125" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="156" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="117" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quotedbl,quotesingle"
  k="260" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="96" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="90" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="129" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="84" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="41" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="v"
  k="84" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V"
  k="109" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="zero"
  k="23" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one"
  k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteright,quotedblright"
  k="276" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteleft,quotedblleft"
  k="270" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one.oldstyle"
  k="78" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="three.oldstyle"
  k="47" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="seven.oldstyle"
  k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="nine.oldstyle"
  k="37" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V.sc"
  k="82" />
    <hkern g1="colon,semicolon"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="104" />
    <hkern g1="colon,semicolon"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="80" />
    <hkern g1="colon,semicolon"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="colon,semicolon"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="colon,semicolon"
  g2="V"
  k="25" />
    <hkern g1="quotedbl,quotesingle"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="82" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE,AEacute"
  k="164" />
    <hkern g1="quotedbl,quotesingle"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="78" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE.sc,AEacute.sc"
  k="152" />
    <hkern g1="quotedbl,quotesingle"
  g2="three.oldstyle"
  k="27" />
    <hkern g1="quotedbl,quotesingle"
  g2="nine.oldstyle"
  k="33" />
    <hkern g1="quotedbl,quotesingle"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="31" />
    <hkern g1="quotedbl,quotesingle"
  g2="space"
  k="20" />
    <hkern g1="quotedbl,quotesingle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="260" />
    <hkern g1="quotedbl,quotesingle"
  g2="four"
  k="98" />
    <hkern g1="quotedbl,quotesingle"
  g2="slash"
  k="100" />
    <hkern g1="quotedbl,quotesingle"
  g2="guillemotleft,guilsinglleft"
  k="37" />
    <hkern g1="quotedbl,quotesingle"
  g2="four.oldstyle"
  k="164" />
    <hkern g1="quotedbl,quotesingle"
  g2="five.oldstyle"
  k="37" />
    <hkern g1="exclamdown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="121" />
    <hkern g1="exclamdown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="94" />
    <hkern g1="exclamdown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="31" />
    <hkern g1="exclamdown"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="51" />
    <hkern g1="exclamdown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="47" />
    <hkern g1="bracketleft"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="bracketleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="bracketleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="35" />
    <hkern g1="bracketleft"
  g2="AE,AEacute"
  k="49" />
    <hkern g1="bracketleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="37" />
    <hkern g1="bracketleft"
  g2="AE.sc,AEacute.sc"
  k="41" />
    <hkern g1="bracketleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="31" />
    <hkern g1="bracketleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="37" />
    <hkern g1="bracketleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="45" />
    <hkern g1="bracketleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="53" />
    <hkern g1="bracketleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="45" />
    <hkern g1="bracketleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="41" />
    <hkern g1="bracketleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="43" />
    <hkern g1="bracketleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="35" />
    <hkern g1="bracketleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="31" />
    <hkern g1="bracketleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="45" />
    <hkern g1="slash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="slash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="slash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="104" />
    <hkern g1="slash"
  g2="AE,AEacute"
  k="168" />
    <hkern g1="slash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="117" />
    <hkern g1="slash"
  g2="AE.sc,AEacute.sc"
  k="174" />
    <hkern g1="slash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="slash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="slash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="53" />
    <hkern g1="slash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="84" />
    <hkern g1="slash"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="70" />
    <hkern g1="slash"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="47" />
    <hkern g1="slash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="43" />
    <hkern g1="slash"
  g2="z,zacute,zdotaccent,zcaron"
  k="35" />
    <hkern g1="slash"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="25" />
    <hkern g1="slash"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="72" />
    <hkern g1="backslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="51" />
    <hkern g1="backslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="47" />
    <hkern g1="backslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="104" />
    <hkern g1="backslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="135" />
    <hkern g1="backslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="90" />
    <hkern g1="backslash"
  g2="quotedbl,quotesingle"
  k="100" />
    <hkern g1="backslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="72" />
    <hkern g1="backslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="66" />
    <hkern g1="backslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="98" />
    <hkern g1="backslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="51" />
    <hkern g1="backslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="29" />
    <hkern g1="backslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="backslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="hyphen,endash,emdash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="29" />
    <hkern g1="hyphen,endash,emdash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="35" />
    <hkern g1="hyphen,endash,emdash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="121" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="150" />
    <hkern g1="hyphen,endash,emdash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="63" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE,AEacute"
  k="23" />
    <hkern g1="hyphen,endash,emdash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="hyphen,endash,emdash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="94" />
    <hkern g1="hyphen,endash,emdash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="47" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="119" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE.sc,AEacute.sc"
  k="41" />
    <hkern g1="hyphen,endash,emdash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="35" />
    <hkern g1="hyphen,endash,emdash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="hyphen,endash,emdash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="35" />
    <hkern g1="hyphen,endash,emdash"
  g2="v"
  k="35" />
    <hkern g1="hyphen,endash,emdash"
  g2="V"
  k="61" />
    <hkern g1="hyphen,endash,emdash"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="hyphen,endash,emdash"
  g2="V.sc"
  k="43" />
    <hkern g1="hyphen,endash,emdash"
  g2="z,zacute,zdotaccent,zcaron"
  k="51" />
    <hkern g1="hyphen,endash,emdash"
  g2="x"
  k="53" />
    <hkern g1="hyphen,endash,emdash"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="20" />
    <hkern g1="hyphen,endash,emdash"
  g2="X"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="X.sc"
  k="43" />
    <hkern g1="hyphen,endash,emdash"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="35" />
    <hkern g1="registered"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="31" />
    <hkern g1="registered"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="45" />
    <hkern g1="registered"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="72" />
    <hkern g1="registered"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="registered"
  g2="AE,AEacute"
  k="70" />
    <hkern g1="registered"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="registered"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="20" />
    <hkern g1="registered"
  g2="AE.sc,AEacute.sc"
  k="63" />
    <hkern g1="registered"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="23" />
    <hkern g1="ampersand"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="82" />
    <hkern g1="ampersand"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="86" />
    <hkern g1="ampersand"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="33" />
    <hkern g1="ampersand"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="39" />
    <hkern g1="ampersand"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="43" />
    <hkern g1="quoteright,quotedblright"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="111" />
    <hkern g1="quoteright,quotedblright"
  g2="AE,AEacute"
  k="199" />
    <hkern g1="quoteright,quotedblright"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="106" />
    <hkern g1="quoteright,quotedblright"
  g2="AE.sc,AEacute.sc"
  k="182" />
    <hkern g1="quoteright,quotedblright"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="23" />
    <hkern g1="quoteright,quotedblright"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="45" />
    <hkern g1="quoteright,quotedblright"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="86" />
    <hkern g1="quoteright,quotedblright"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="307" />
    <hkern g1="quoteright,quotedblright"
  g2="guillemotleft,guilsinglleft"
  k="76" />
    <hkern g1="quoteright,quotedblright"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="57" />
    <hkern g1="quoteright,quotedblright"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="18" />
    <hkern g1="quoteright,quotedblright"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="57" />
    <hkern g1="quoteright,quotedblright"
  g2="M"
  k="23" />
    <hkern g1="quoteright,quotedblright"
  g2="hyphen,endash,emdash"
  k="49" />
    <hkern g1="quoteright,quotedblright"
  g2="M.sc"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="109" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE,AEacute"
  k="195" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="104" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE.sc,AEacute.sc"
  k="178" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="76" />
    <hkern g1="quoteleft,quotedblleft"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="291" />
    <hkern g1="quoteleft,quotedblleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="47" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="51" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M"
  k="23" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M.sc"
  k="20" />
    <hkern g1="trademark"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="55" />
    <hkern g1="trademark"
  g2="AE,AEacute"
  k="117" />
    <hkern g1="trademark"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="49" />
    <hkern g1="trademark"
  g2="AE.sc,AEacute.sc"
  k="106" />
    <hkern g1="braceleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="29" />
    <hkern g1="braceleft"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="31" />
    <hkern g1="braceleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="51" />
    <hkern g1="braceleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="90" />
    <hkern g1="braceleft"
  g2="AE,AEacute"
  k="109" />
    <hkern g1="braceleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="100" />
    <hkern g1="braceleft"
  g2="AE.sc,AEacute.sc"
  k="106" />
    <hkern g1="braceleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="39" />
    <hkern g1="braceleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="66" />
    <hkern g1="braceleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="86" />
    <hkern g1="braceleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="37" />
    <hkern g1="braceleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="braceleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="braceleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="70" />
    <hkern g1="braceleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="70" />
    <hkern g1="braceleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="63" />
    <hkern g1="braceleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="57" />
    <hkern g1="braceleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="84" />
    <hkern g1="braceleft"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="braceleft"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="20" />
    <hkern g1="braceleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="39" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="123" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="94" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="25" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="74" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="41" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="V"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="33" />
    <hkern g1="guillemotright,guilsinglright"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="29" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="137" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="152" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="72" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quotedbl,quotesingle"
  k="37" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE,AEacute"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="100" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="51" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="113" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE.sc,AEacute.sc"
  k="47" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="33" />
    <hkern g1="guillemotright,guilsinglright"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="v"
  k="33" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V"
  k="70" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quoteright,quotedblright"
  k="43" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V.sc"
  k="49" />
    <hkern g1="guillemotright,guilsinglright"
  g2="z,zacute,zdotaccent,zcaron"
  k="33" />
    <hkern g1="guillemotright,guilsinglright"
  g2="x"
  k="35" />
    <hkern g1="guillemotright,guilsinglright"
  g2="X.sc"
  k="37" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="25" />
    <hkern g1="questiondown"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="31" />
    <hkern g1="questiondown"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="35" />
    <hkern g1="questiondown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="139" />
    <hkern g1="questiondown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="133" />
    <hkern g1="questiondown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="74" />
    <hkern g1="questiondown"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="66" />
    <hkern g1="questiondown"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="51" />
    <hkern g1="questiondown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="84" />
    <hkern g1="questiondown"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="29" />
    <hkern g1="questiondown"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="questiondown"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="35" />
    <hkern g1="questiondown"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="questiondown"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="33" />
    <hkern g1="questiondown"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="45" />
    <hkern g1="questiondown"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="33" />
    <hkern g1="questiondown"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="33" />
    <hkern g1="questiondown"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="23" />
    <hkern g1="questiondown"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="31" />
    <hkern g1="questiondown"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="23" />
    <hkern g1="questiondown"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="33" />
    <hkern g1="asterisk"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="80" />
    <hkern g1="asterisk"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="-23" />
    <hkern g1="asterisk"
  g2="AE,AEacute"
  k="158" />
    <hkern g1="asterisk"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="78" />
    <hkern g1="asterisk"
  g2="AE.sc,AEacute.sc"
  k="150" />
    <hkern g1="asterisk"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="35" />
    <hkern g1="asterisk"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="asterisk"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="29" />
    <hkern g1="parenleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="parenleft"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="29" />
    <hkern g1="parenleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="49" />
    <hkern g1="parenleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="82" />
    <hkern g1="parenleft"
  g2="AE,AEacute"
  k="96" />
    <hkern g1="parenleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="88" />
    <hkern g1="parenleft"
  g2="AE.sc,AEacute.sc"
  k="94" />
    <hkern g1="parenleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="39" />
    <hkern g1="parenleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="63" />
    <hkern g1="parenleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="84" />
    <hkern g1="parenleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="35" />
    <hkern g1="parenleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="parenleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="80" />
    <hkern g1="parenleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="68" />
    <hkern g1="parenleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="70" />
    <hkern g1="parenleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="59" />
    <hkern g1="parenleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="55" />
    <hkern g1="parenleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="82" />
    <hkern g1="parenleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="35" />
    <hkern g1="ampersand.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="61" />
    <hkern g1="ampersand.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="29" />
    <hkern g1="ampersand.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="72" />
    <hkern g1="questiondown.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="questiondown.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="84" />
    <hkern g1="questiondown.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="questiondown.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="72" />
    <hkern g1="questiondown.sc"
  g2="AE.sc,AEacute.sc"
  k="51" />
    <hkern g1="questiondown.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="37" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE,AEacute"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE.sc,AEacute.sc"
  k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="v"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="V"
  k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="x"
  k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright"
  k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="parenright"
  k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="parenright.case"
  k="68" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="bracketright.case"
  k="27" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright.case"
  k="74" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="119" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="129" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quotedbl,quotesingle"
  k="82" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="82" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="98" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="v"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteright,quotedblright"
  k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteleft,quotedblleft"
  k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V.sc"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="space"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright"
  k="90" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright"
  k="82" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright.case"
  k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright.case"
  k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="bracketright"
  k="33" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="backslash"
  k="104" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="registered"
  k="29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="trademark"
  k="96" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="question"
  k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="asterisk"
  k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="hyphen.case,endash.case,emdash.case"
  k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="57" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="33" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="59" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="88" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="29" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE,AEacute"
  k="76" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="35" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE.sc,AEacute.sc"
  k="80" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V"
  k="31" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V.sc"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="49" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="slash"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="x"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X"
  k="39" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X.sc"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright"
  k="66" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright"
  k="63" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright.case"
  k="100" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright.case"
  k="41" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright.case"
  k="100" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright"
  k="33" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="backslash"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="trademark"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="seven"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="45" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="v"
  k="45" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="35" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="31" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="31" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="hyphen,endash,emdash"
  k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="hyphen.case,endash.case,emdash.case"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="parenright.case"
  k="20" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="braceright.case"
  k="25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="31" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="135" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="115" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="166" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="166" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="127" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quotedbl,quotesingle"
  k="160" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="156" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="143" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="172" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="137" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="88" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="v"
  k="135" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="61" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="33" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V"
  k="109" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteright,quotedblright"
  k="160" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteleft,quotedblleft"
  k="160" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V.sc"
  k="133" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="space"
  k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="23" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen,endash,emdash"
  k="129" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="braceright"
  k="86" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="parenright"
  k="76" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="braceright.case"
  k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="bracketright"
  k="23" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="backslash"
  k="139" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="registered"
  k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="trademark"
  k="162" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="asterisk"
  k="162" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen.case,endash.case,emdash.case"
  k="158" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft.case,guilsinglleft.case"
  k="115" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotright.case,guilsinglright.case"
  k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="braceright.case"
  k="25" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="27" />
    <hkern g1="F"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="F"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="43" />
    <hkern g1="F"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="41" />
    <hkern g1="F"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="96" />
    <hkern g1="F"
  g2="AE,AEacute"
  k="199" />
    <hkern g1="F"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="131" />
    <hkern g1="F"
  g2="AE.sc,AEacute.sc"
  k="244" />
    <hkern g1="F"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="41" />
    <hkern g1="F"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="31" />
    <hkern g1="F"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="F"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="20" />
    <hkern g1="F"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="F"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="10" />
    <hkern g1="F"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="86" />
    <hkern g1="F"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="160" />
    <hkern g1="F"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="F"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="80" />
    <hkern g1="F"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="80" />
    <hkern g1="F"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="78" />
    <hkern g1="F"
  g2="z,zacute,zdotaccent,zcaron"
  k="76" />
    <hkern g1="F"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="F"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="113" />
    <hkern g1="F"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="F"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="16" />
    <hkern g1="F"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="14" />
    <hkern g1="F"
  g2="guillemotright.case,guilsinglright.case"
  k="23" />
    <hkern g1="F"
  g2="colon,semicolon"
  k="25" />
    <hkern g1="F"
  g2="guillemotright,guilsinglright"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="31" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="45" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="78" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE,AEacute"
  k="66" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="33" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE.sc,AEacute.sc"
  k="72" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V.sc"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="43" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="x"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X"
  k="33" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X.sc"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright"
  k="57" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright"
  k="55" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright.case"
  k="41" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="trademark"
  k="18" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="B"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="B"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="B"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="B"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="18" />
    <hkern g1="B"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="41" />
    <hkern g1="B"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="59" />
    <hkern g1="B"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="25" />
    <hkern g1="B"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="B"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="B"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="14" />
    <hkern g1="B"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="B"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="25" />
    <hkern g1="B"
  g2="AE.sc,AEacute.sc"
  k="27" />
    <hkern g1="B"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="B"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="B"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="23" />
    <hkern g1="B"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="B"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="P"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="86" />
    <hkern g1="P"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="16" />
    <hkern g1="P"
  g2="AE,AEacute"
  k="168" />
    <hkern g1="P"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="80" />
    <hkern g1="P"
  g2="AE.sc,AEacute.sc"
  k="211" />
    <hkern g1="P"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="39" />
    <hkern g1="P"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="164" />
    <hkern g1="P"
  g2="guillemotleft,guilsinglleft"
  k="29" />
    <hkern g1="P"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="P"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="P"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="27" />
    <hkern g1="P"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="P"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="33" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="V"
  k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="43" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="guillemotleft,guilsinglleft"
  k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="braceright"
  k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="parenright"
  k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="parenright.case"
  k="25" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="braceright.case"
  k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t,tcommaaccent,tcaron,tbar"
  k="47" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="168" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="119" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE,AEacute"
  k="168" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="135" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="14" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="20" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE.sc,AEacute.sc"
  k="184" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="84" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="78" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="v"
  k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="121" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="61" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="V.sc"
  k="27" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="209" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="space"
  k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="125" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="slash"
  k="104" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft,guilsinglleft"
  k="137" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="195" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="211" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="213" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="z,zacute,zdotaccent,zcaron"
  k="186" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="197" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="x"
  k="180" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="X.sc"
  k="27" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="31" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M"
  k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen,endash,emdash"
  k="121" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M.sc"
  k="68" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="registered"
  k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen.case,endash.case,emdash.case"
  k="119" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft.case,guilsinglleft.case"
  k="115" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotright.case,guilsinglright.case"
  k="72" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="colon,semicolon"
  k="104" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotright,guilsinglright"
  k="123" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="ampersand"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="53" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="49" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="51" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="v"
  k="51" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen,endash,emdash"
  k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="registered"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen.case,endash.case,emdash.case"
  k="45" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="70" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="49" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="78" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE,AEacute"
  k="31" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="63" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="59" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE.sc,AEacute.sc"
  k="41" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="v"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V"
  k="31" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="quoteright,quotedblright"
  k="41" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="quoteleft,quotedblleft"
  k="39" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V.sc"
  k="18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="x"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X"
  k="18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X.sc"
  k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="27" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright"
  k="53" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright"
  k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright.case"
  k="80" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright.case"
  k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright.case"
  k="84" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright"
  k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="backslash"
  k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="question"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE,AEacute"
  k="49" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="35" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="31" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="z,zacute,zdotaccent,zcaron"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="x"
  k="27" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="parenright.case"
  k="29" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="braceright.case"
  k="37" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="K,Kcommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="74" />
    <hkern g1="K,Kcommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="72" />
    <hkern g1="K,Kcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="12" />
    <hkern g1="K,Kcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="72" />
    <hkern g1="K,Kcommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="63" />
    <hkern g1="K,Kcommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="35" />
    <hkern g1="K,Kcommaaccent"
  g2="v"
  k="74" />
    <hkern g1="K,Kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="76" />
    <hkern g1="K,Kcommaaccent"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="74" />
    <hkern g1="K,Kcommaaccent"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="16" />
    <hkern g1="K,Kcommaaccent"
  g2="V.sc"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="37" />
    <hkern g1="K,Kcommaaccent"
  g2="space"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="slash"
  k="-20" />
    <hkern g1="K,Kcommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="25" />
    <hkern g1="K,Kcommaaccent"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen,endash,emdash"
  k="33" />
    <hkern g1="K,Kcommaaccent"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="registered"
  k="27" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen.case,endash.case,emdash.case"
  k="74" />
    <hkern g1="K,Kcommaaccent"
  g2="guillemotleft.case,guilsinglleft.case"
  k="86" />
    <hkern g1="M"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="M"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="M"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="M"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="25" />
    <hkern g1="M"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="47" />
    <hkern g1="M"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="M"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="12" />
    <hkern g1="M"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="M"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="25" />
    <hkern g1="M"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="M"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="M"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="18" />
    <hkern g1="M"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="V"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="V"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="V"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="25" />
    <hkern g1="V"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="V"
  g2="AE,AEacute"
  k="145" />
    <hkern g1="V"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="74" />
    <hkern g1="V"
  g2="AE.sc,AEacute.sc"
  k="141" />
    <hkern g1="V"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="V"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="V"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="V"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="V"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="51" />
    <hkern g1="V"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="94" />
    <hkern g1="V"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="109" />
    <hkern g1="V"
  g2="guillemotleft,guilsinglleft"
  k="68" />
    <hkern g1="V"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="92" />
    <hkern g1="V"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="72" />
    <hkern g1="V"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="76" />
    <hkern g1="V"
  g2="z,zacute,zdotaccent,zcaron"
  k="53" />
    <hkern g1="V"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="31" />
    <hkern g1="V"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="92" />
    <hkern g1="V"
  g2="hyphen,endash,emdash"
  k="61" />
    <hkern g1="V"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="20" />
    <hkern g1="V"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="12" />
    <hkern g1="V"
  g2="hyphen.case,endash.case,emdash.case"
  k="35" />
    <hkern g1="V"
  g2="guillemotleft.case,guilsinglleft.case"
  k="51" />
    <hkern g1="V"
  g2="colon,semicolon"
  k="25" />
    <hkern g1="V"
  g2="guillemotright,guilsinglright"
  k="27" />
    <hkern g1="X"
  g2="t,tcommaaccent,tcaron,tbar"
  k="31" />
    <hkern g1="X"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="80" />
    <hkern g1="X"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="78" />
    <hkern g1="X"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="80" />
    <hkern g1="X"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="59" />
    <hkern g1="X"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="45" />
    <hkern g1="X"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="41" />
    <hkern g1="X"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="47" />
    <hkern g1="X"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="18" />
    <hkern g1="X"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="39" />
    <hkern g1="X"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="39" />
    <hkern g1="X"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="12" />
    <hkern g1="X"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="X"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="16" />
    <hkern g1="X"
  g2="hyphen.case,endash.case,emdash.case"
  k="63" />
    <hkern g1="X"
  g2="guillemotleft.case,guilsinglleft.case"
  k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t,tcommaaccent,tcaron,tbar"
  k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="129" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE,AEacute"
  k="213" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="156" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE.sc,AEacute.sc"
  k="227" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="v"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="139" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="227" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="space"
  k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="158" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="slash"
  k="135" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft,guilsinglleft"
  k="152" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="209" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="162" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="168" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="z,zacute,zdotaccent,zcaron"
  k="162" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="223" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="x"
  k="156" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="X.sc"
  k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M"
  k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen,endash,emdash"
  k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M.sc"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="57" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="braceright.case"
  k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="registered"
  k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen.case,endash.case,emdash.case"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft.case,guilsinglleft.case"
  k="121" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright.case,guilsinglright.case"
  k="47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="colon,semicolon"
  k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright,guilsinglright"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="ampersand"
  k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE,AEacute"
  k="147" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="78" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE.sc,AEacute.sc"
  k="147" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="v"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="53" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="100" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="space"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="117" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="slash"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="76" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="76" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="z,zacute,zdotaccent,zcaron"
  k="59" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="29" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="x"
  k="57" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen,endash,emdash"
  k="63" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M.sc"
  k="47" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="braceright.case"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="registered"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen.case,endash.case,emdash.case"
  k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft.case,guilsinglleft.case"
  k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="colon,semicolon"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotright,guilsinglright"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="ampersand"
  k="27" />
    <hkern g1="Thorn"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="39" />
    <hkern g1="Thorn"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="90" />
    <hkern g1="Thorn"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="94" />
    <hkern g1="Thorn"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="Thorn"
  g2="AE,AEacute"
  k="96" />
    <hkern g1="Thorn"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="82" />
    <hkern g1="Thorn"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="35" />
    <hkern g1="d,dcaron,dslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="d,dcaron,dslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="12" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="10" />
    <hkern g1="f,f_f"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="53" />
    <hkern g1="f,f_f"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="18" />
    <hkern g1="f,f_f"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="47" />
    <hkern g1="f,f_f"
  g2="guillemotleft,guilsinglleft"
  k="35" />
    <hkern g1="f,f_f"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="8" />
    <hkern g1="f,f_f"
  g2="hyphen,endash,emdash"
  k="51" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="209" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="227" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="100" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quotedbl,quotesingle"
  k="31" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="AE,AEacute"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="v"
  k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="V"
  k="94" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteright,quotedblright"
  k="51" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteleft,quotedblleft"
  k="51" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="x"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="X"
  k="39" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="M"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="braceright"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="parenright"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="bracketright"
  k="53" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="backslash"
  k="84" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="trademark"
  k="53" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="question"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="asterisk"
  k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="20" />
    <hkern g1="l,lacute,lcommaaccent,lcaron,lslash,fl,f_f_l"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="l,lacute,lcommaaccent,lcaron,lslash,fl,f_f_l"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="31" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="hyphen,endash,emdash"
  k="59" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="braceright"
  k="66" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="parenright"
  k="63" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="bracketright"
  k="39" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="backslash"
  k="45" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="trademark"
  k="39" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="195" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="225" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="90" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="V"
  k="92" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteright,quotedblright"
  k="39" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteleft,quotedblleft"
  k="41" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="x"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="X"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="braceright"
  k="90" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="parenright"
  k="88" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="bracketright"
  k="47" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="backslash"
  k="76" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="trademark"
  k="51" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="asterisk"
  k="29" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="k,kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="49" />
    <hkern g1="k,kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="33" />
    <hkern g1="k,kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="57" />
    <hkern g1="k,kcommaaccent"
  g2="guillemotleft,guilsinglleft"
  k="39" />
    <hkern g1="k,kcommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="k,kcommaaccent"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="k,kcommaaccent"
  g2="hyphen,endash,emdash"
  k="74" />
    <hkern g1="k,kcommaaccent"
  g2="braceright"
  k="55" />
    <hkern g1="k,kcommaaccent"
  g2="parenright"
  k="51" />
    <hkern g1="k,kcommaaccent"
  g2="backslash"
  k="35" />
    <hkern g1="k,kcommaaccent"
  g2="trademark"
  k="47" />
    <hkern g1="k,kcommaaccent"
  g2="asterisk"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="v"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="quoteright,quotedblright"
  k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="quoteleft,quotedblleft"
  k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="braceright"
  k="84" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="parenright"
  k="82" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="bracketright"
  k="45" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="backslash"
  k="70" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="trademark"
  k="53" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="asterisk"
  k="25" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="217" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="227" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quotedbl,quotesingle"
  k="23" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="v"
  k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="V"
  k="76" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteright,quotedblright"
  k="37" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteleft,quotedblleft"
  k="37" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="braceright"
  k="90" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="parenright"
  k="86" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="bracketright"
  k="47" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="backslash"
  k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="trademark"
  k="53" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="question"
  k="20" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="asterisk"
  k="31" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="41" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="18" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="space"
  k="41" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="hyphen,endash,emdash"
  k="43" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="braceright"
  k="37" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="parenright"
  k="37" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="bracketright"
  k="27" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="86" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="29" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="space"
  k="49" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="115" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="slash"
  k="70" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="hyphen,endash,emdash"
  k="111" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="braceright"
  k="49" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="parenright"
  k="49" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="bracketright"
  k="43" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="trademark"
  k="16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="v"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="v"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="v"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="84" />
    <hkern g1="v"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="v"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="v"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="v"
  g2="hyphen,endash,emdash"
  k="35" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="hyphen,endash,emdash"
  k="55" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="braceright"
  k="59" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="parenright"
  k="55" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="bracketright"
  k="35" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="backslash"
  k="31" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="trademark"
  k="35" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="space"
  k="51" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="84" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="slash"
  k="51" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="hyphen,endash,emdash"
  k="35" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="braceright"
  k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="parenright"
  k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="bracketright"
  k="41" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="backslash"
  k="23" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="trademark"
  k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="43" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="space"
  k="49" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="74" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="slash"
  k="45" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="29" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="braceright"
  k="51" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="parenright"
  k="51" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="bracketright"
  k="43" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="backslash"
  k="23" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="trademark"
  k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="x"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="x"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="27" />
    <hkern g1="x"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="29" />
    <hkern g1="x"
  g2="guillemotleft,guilsinglleft"
  k="35" />
    <hkern g1="x"
  g2="hyphen,endash,emdash"
  k="53" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="211" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="162" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="76" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="V"
  k="72" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="X"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="braceright"
  k="70" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="parenright"
  k="68" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="bracketright"
  k="41" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="backslash"
  k="47" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="trademark"
  k="41" />
    <hkern g1="germandbls"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="germandbls"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="31" />
    <hkern g1="germandbls"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="27" />
    <hkern g1="germandbls"
  g2="quotedbl,quotesingle"
  k="18" />
    <hkern g1="germandbls"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="29" />
    <hkern g1="germandbls"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="germandbls"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="20" />
    <hkern g1="germandbls"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="germandbls"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="germandbls"
  g2="quoteleft,quotedblleft"
  k="16" />
    <hkern g1="germandbls"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="germandbls"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="18" />
    <hkern g1="eth"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="eth"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="eth"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="eth"
  g2="quotedbl,quotesingle"
  k="41" />
    <hkern g1="eth"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="eth"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="14" />
    <hkern g1="eth"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="14" />
    <hkern g1="eth"
  g2="quoteright,quotedblright"
  k="63" />
    <hkern g1="eth"
  g2="quoteleft,quotedblleft"
  k="63" />
    <hkern g1="eth"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="eth"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quotedbl,quotesingle"
  k="78" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="92" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="63" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="113" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteright,quotedblright"
  k="82" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteleft,quotedblleft"
  k="84" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="V.sc"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="space"
  k="55" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="guillemotleft,guilsinglleft"
  k="23" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="hyphen,endash,emdash"
  k="23" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="braceright"
  k="100" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="parenright"
  k="90" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="bracketright"
  k="37" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="backslash"
  k="117" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="registered"
  k="27" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="trademark"
  k="100" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="question"
  k="23" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="asterisk"
  k="78" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="question.sc"
  k="23" />
    <hkern g1="B.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="B.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="29" />
    <hkern g1="B.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="B.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="51" />
    <hkern g1="B.sc"
  g2="AE.sc,AEacute.sc"
  k="18" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="asterisk"
  k="-18" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="29" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="35" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="66" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="AE.sc,AEacute.sc"
  k="59" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="V.sc"
  k="25" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="25" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="X.sc"
  k="29" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="16" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="braceright"
  k="82" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="parenright"
  k="82" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="bracketright"
  k="45" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="backslash"
  k="51" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="trademark"
  k="39" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="29" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="23" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="braceright"
  k="27" />
    <hkern g1="F.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="84" />
    <hkern g1="F.sc"
  g2="AE.sc,AEacute.sc"
  k="168" />
    <hkern g1="F.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="16" />
    <hkern g1="F.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="129" />
    <hkern g1="F.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="quotedbl,quotesingle"
  k="18" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="18" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="39" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="27" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="68" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="27" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="25" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="23" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="X.sc"
  k="16" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="braceright"
  k="78" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="parenright"
  k="78" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="bracketright"
  k="35" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="backslash"
  k="47" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="trademark"
  k="39" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="asterisk"
  k="16" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="braceright"
  k="39" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="parenright"
  k="35" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="66" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="25" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="45" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quotedbl,quotesingle"
  k="127" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="135" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="119" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="150" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="125" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="125" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="51" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="27" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="V.sc"
  k="117" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="space"
  k="53" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="37" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="131" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="braceright"
  k="92" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="parenright"
  k="82" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="bracketright"
  k="27" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="backslash"
  k="139" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="registered"
  k="51" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="trademark"
  k="150" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="asterisk"
  k="131" />
    <hkern g1="M.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="18" />
    <hkern g1="M.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="M.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="41" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="31" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="47" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="74" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="AE.sc,AEacute.sc"
  k="68" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="33" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="X.sc"
  k="35" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="23" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="braceright"
  k="86" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="parenright"
  k="86" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="bracketright"
  k="47" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="backslash"
  k="53" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="trademark"
  k="41" />
    <hkern g1="P.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="P.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="P.sc"
  g2="AE.sc,AEacute.sc"
  k="143" />
    <hkern g1="P.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="133" />
    <hkern g1="P.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="14" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="27" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="V.sc"
  k="16" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="braceright"
  k="59" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="parenright"
  k="57" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="bracketright"
  k="29" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="backslash"
  k="27" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="trademark"
  k="27" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="33" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="V.sc"
  k="18" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="braceright"
  k="61" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="parenright"
  k="61" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="bracketright"
  k="33" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="backslash"
  k="33" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="trademark"
  k="37" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="92" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="AE.sc,AEacute.sc"
  k="141" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="43" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="space"
  k="53" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="96" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="slash"
  k="72" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotleft,guilsinglleft"
  k="100" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="hyphen,endash,emdash"
  k="94" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="M.sc"
  k="18" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotright,guilsinglright"
  k="74" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="at"
  k="23" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="20" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="AE.sc,AEacute.sc"
  k="45" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="18" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="braceright"
  k="37" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="parenright"
  k="35" />
    <hkern g1="V.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="V.sc"
  g2="AE.sc,AEacute.sc"
  k="127" />
    <hkern g1="V.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="V.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="82" />
    <hkern g1="V.sc"
  g2="guillemotleft,guilsinglleft"
  k="49" />
    <hkern g1="V.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="V.sc"
  g2="hyphen,endash,emdash"
  k="43" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="63" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="AE.sc,AEacute.sc"
  k="131" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="space"
  k="55" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="90" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="slash"
  k="66" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="guillemotleft,guilsinglleft"
  k="51" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="hyphen,endash,emdash"
  k="47" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="M.sc"
  k="23" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="ampersand.sc"
  k="25" />
    <hkern g1="X.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="X.sc"
  g2="guillemotleft,guilsinglleft"
  k="37" />
    <hkern g1="X.sc"
  g2="hyphen,endash,emdash"
  k="43" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="111" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="AE.sc,AEacute.sc"
  k="184" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="72" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="space"
  k="68" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="129" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="slash"
  k="98" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotleft,guilsinglleft"
  k="113" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="25" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="hyphen,endash,emdash"
  k="119" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="M.sc"
  k="39" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="registered"
  k="20" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="colon,semicolon"
  k="37" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotright,guilsinglright"
  k="39" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="ampersand.sc"
  k="47" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="20" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="hyphen,endash,emdash"
  k="37" />
    <hkern g1="Thorn.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="35" />
    <hkern g1="Thorn.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="68" />
    <hkern g1="Thorn.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="25" />
    <hkern g1="Thorn.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="82" />
    <hkern g1="Thorn.sc"
  g2="AE.sc,AEacute.sc"
  k="84" />
    <hkern g1="Thorn.sc"
  g2="quoteright,quotedblright"
  k="23" />
    <hkern g1="Thorn.sc"
  g2="quoteleft,quotedblleft"
  k="20" />
    <hkern g1="Thorn.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="70" />
    <hkern g1="Thorn.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="29" />
    <hkern g1="zero"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="20" />
    <hkern g1="seven"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="seven"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="170" />
    <hkern g1="four"
  g2="quotedbl,quotesingle"
  k="25" />
    <hkern g1="nine"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="25" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="37" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="119" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="117" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="35" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="AE,AEacute"
  k="66" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="V"
  k="35" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="45" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="X"
  k="63" />
    <hkern g1="questiondown.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="20" />
    <hkern g1="questiondown.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="41" />
    <hkern g1="questiondown.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="25" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="72" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="51" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="AE,AEacute"
  k="49" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="57" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="115" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="121" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="53" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="AE,AEacute"
  k="104" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="V"
  k="55" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="68" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="X"
  k="72" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="20" />
    <hkern g1="three.oldstyle"
  g2="quotedbl,quotesingle"
  k="37" />
    <hkern g1="four.oldstyle"
  g2="quotedbl,quotesingle"
  k="43" />
    <hkern g1="seven.oldstyle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="113" />
    <hkern g1="nine.oldstyle"
  g2="quotedbl,quotesingle"
  k="41" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="14" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="v"
  k="14" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="slash"
  k="-18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quotedbl,quotesingle"
  k="23" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteright,quotedblright"
  k="39" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteleft,quotedblleft"
  k="37" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="braceright"
  k="90" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="parenright"
  k="86" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="bracketright"
  k="45" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="backslash"
  k="80" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="trademark"
  k="53" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="asterisk"
  k="31" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="space"
  k="51" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="84" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="slash"
  k="51" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="hyphen,endash,emdash"
  k="35" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="braceright"
  k="47" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="parenright"
  k="47" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="bracketright"
  k="41" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="backslash"
  k="23" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="trademark"
  k="18" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="8" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="braceright"
  k="47" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="parenright"
  k="45" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="bracketright"
  k="20" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="backslash"
  k="23" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="trademark"
  k="23" />
    <hkern g1="parenleft.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="33" />
    <hkern g1="parenleft.case"
  g2="AE,AEacute"
  k="47" />
    <hkern g1="parenleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="100" />
    <hkern g1="parenleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="29" />
    <hkern g1="parenleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="68" />
    <hkern g1="parenleft.case"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="20" />
    <hkern g1="bracketleft.case"
  g2="AE,AEacute"
  k="20" />
    <hkern g1="bracketleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="41" />
    <hkern g1="bracketleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="33" />
    <hkern g1="braceleft.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="49" />
    <hkern g1="braceleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="39" />
    <hkern g1="braceleft.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="25" />
    <hkern g1="braceleft.case"
  g2="AE,AEacute"
  k="61" />
    <hkern g1="braceleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="100" />
    <hkern g1="braceleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="37" />
    <hkern g1="braceleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="74" />
    <hkern g1="braceleft.case"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="25" />
  </font>
</defs></svg>
