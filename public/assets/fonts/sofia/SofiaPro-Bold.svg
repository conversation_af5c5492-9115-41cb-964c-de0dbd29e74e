<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Mon Dec 14 13:22:26 2020
 By <PERSON><PERSON><PERSON>,,,
Copyright (c) Olivier <PERSON> - Mostardesign Studio, 2012. All rights reserved.
</metadata>
<defs>
<font id="SofiaPro-Bold" horiz-adv-x="1163" >
  <font-face 
    font-family="Sofia Pro"
    font-weight="700"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 0 0 0 0 0 0 0 0"
    ascent="1544"
    descent="-504"
    x-height="956"
    cap-height="1411"
    bbox="-369 -588 2370 2279"
    underline-thickness="102"
    underline-position="-307"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="1558" 
d="M1466 1477h105v-273h-105q-145 0 -145 -182v-66h192v-225h-192v-731h-287v731h-520v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66h520v66q0 219 112.5 337t319.5 118z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1423" 
d="M514 956h778v-956h-282v731h-496v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66zM1151 1477q72 0 123 -48.5t51 -117.5t-51 -118.5t-123 -49.5q-73 0 -125.5 49.5t-52.5 118.5t52 117.5t126 48.5z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1388" 
d="M659 1477h598v-1477h-284v1204h-314q-145 0 -145 -182v-66h193v-225h-193v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="2230" 
d="M1321 956h778v-956h-282v731h-496v-731h-287v731h-520v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66h520v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66zM1958 1477q72 0 123 -48.5t51 -117.5
t-51 -118.5t-123 -49.5q-73 0 -125.5 49.5t-52.5 118.5t52 117.5t126 48.5z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="2195" 
d="M1466 1477h598v-1477h-284v1204h-314q-145 0 -145 -182v-66h192v-225h-192v-731h-287v731h-520v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66h520v66q0 219 112.5 337t319.5 118z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="2181" 
d="M1688 -481h-175v258h125q128 0 125 151v803h-442v-731h-287v731h-520v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66h520v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66h729v-1022q0 -207 -86 -311
t-276 -104zM1780 1192q-51 50 -51 119t51 116q54 49 127.5 49t124.5 -49q51 -47 51 -116t-51 -119t-124.5 -50t-127.5 50z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="1374" 
d="M881 -481h-174v258h124q128 0 125 151v803h-442v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118h105v-273h-105q-145 0 -145 -182v-66h729v-1022q0 -207 -86 -311t-276 -104zM973 1192q-51 50 -51 119t51 116q54 49 127.5 49t124.5 -49q51 -47 51 -116
t-51 -119t-124.5 -50t-127.5 50z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="499" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="579" 
d="M174 426l-41 985h316l-41 -985h-234zM467 152q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5t125 48.5t126 -49t52 -121z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="747" 
d="M307 803h-155l-23 608h201zM596 803h-156l-22 608h200z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1480" 
d="M1284 1411l-108 -356h235l-53 -189h-236l-106 -344h235l-55 -188h-233l-109 -334h-223l104 334h-209l-106 -334h-221l106 334h-233l53 188h231l105 344h-230l54 189h231l107 356h227l-107 -356h207l109 356h225zM895 866h-209l-104 -344h206z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1177" 
d="M518 1423v219h150v-221q177 -25 284 -138.5t107 -287.5h-299q0 73 -48 116.5t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53q396 -114 387 -426q-3 -181 -127 -287q-125 -104 -307 -122v-236h-150v236q-198 21 -317 137q-119 119 -119 295h301
q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 171 119 284q121 115 303 133z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1417" 
d="M1104 1341l-660 -1341h-141l660 1341h141zM346 1356q113 0 193 -80q82 -79 82 -195q0 -114 -80 -194q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 194q0 114 78 195q80 80 194 80zM346 975q45 0 77 31t32 75t-32.5 76.5t-76.5 32.5t-75 -32t-31 -77t29.5 -75.5
t76.5 -30.5zM1071 526q114 0 193 -82q82 -79 82 -192q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195q0 113 78 194q80 80 194 80zM965 252q0 -47 29.5 -77t76.5 -30q34 0 61.5 19.5t39.5 47t6.5 60t-31.5 56.5q-24 26 -56 31.5t-59.5 -6.5t-47 -39.5
t-19.5 -61.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1421" 
d="M586 -20q-206 0 -344 114q-138 117 -138 297q0 279 234 361q-79 25 -128.5 101t-49.5 179q0 155 116 271q115 115 295 124q254 12 404 -182l-242 -147q-53 53 -115 53q-68 0 -106.5 -40.5t-38.5 -98.5q0 -8 1 -19.5t10 -38.5t25 -48t49.5 -37.5t78.5 -16.5h233v160h265
v-160h163v-229h-163v-209q0 -120 34 -154q33 -33 156 -33v-247q-195 3 -274 34q-89 35 -123 142q-38 -80 -142 -129q-103 -47 -200 -47zM870 510v113h-241q-47 0 -85 -11.5t-61 -29.5t-40 -41t-22 -46.5t-8 -44.5t3 -36q34 -156 213 -156q82 0 162 59q79 61 79 193z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="458" 
d="M307 803h-155l-23 608h201z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="794" 
d="M729 1427v-223q-103 0 -197 -104q-95 -105 -153 -277q-60 -177 -60 -356q0 -278 127 -500q128 -223 283 -223v-221q-278 0 -465 266q-184 263 -184 678q0 414 184 686q188 274 465 274z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="794" 
d="M66 -477v221q155 0 280 223q129 221 129 500q0 182 -59 356q-60 173 -155.5 277t-194.5 104v223q277 0 462 -274q187 -272 187 -686q0 -415 -187 -678q-184 -266 -462 -266z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="974" 
d="M492 913l-156 -225l-156 119l168 207l-258 73l64 191l247 -98l-16 268h205l-19 -266l252 96l60 -191l-256 -69l170 -213l-160 -113z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1042" 
d="M942 430h-317v-317h-209v317h-316v209h316v315h209v-315h317v-209z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="579" 
d="M186 -276l-184 69q73 100 117 243.5t43 287.5h289q-3 -218 -84 -365q-82 -155 -181 -235z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="972" 
d="M848 391h-723v225h723v-225z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="571" 
d="M463 152q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5t125 48.5t126 -49t52 -121z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="1024" 
d="M59 0l678 1411h228l-678 -1411h-228z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1236" 
d="M119 672q0 696 502 696q282 0 395 -205q102 -181 102 -491q0 -307 -102 -488q-54 -99 -154.5 -151.5t-240.5 -52.5q-502 0 -502 692zM406 672q0 -414 215 -414q210 0 210 414q0 418 -210 418q-215 0 -215 -418z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="772" 
d="M80 1114l516 246v-1360h-287v961l-229 -99v252z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1122" 
d="M977 270v-270h-873v156l551 653q72 84 72 152q0 57 -46 95t-122 38q-74 0 -126 -52t-64 -135l-263 45q31 193 156 304.5t313 111.5q183 0 310 -115q129 -114 129 -292q0 -180 -142 -340l-309 -351h414z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1130" 
d="M528 582h-141v245h141q131 0 131 146q0 47 -36.5 88t-98.5 41q-94 0 -141 -74l-231 141q62 98 173.5 152t243.5 47q175 -9 287 -121q114 -111 111 -256q0 -79 -46.5 -149t-125.5 -113q111 -42 174 -147q64 -107 55 -217q-15 -170 -172 -285q-156 -111 -356 -96
q-138 7 -255 84.5t-173 195.5l251 125q21 -45 82 -88q55 -38 117 -41q71 -4 129 41t64 102q2 11 2 23.5t-2.5 35.5t-14 43t-30.5 39t-54.5 29t-83.5 9z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1232" 
d="M680 961l-250 -418h250v418zM1139 543v-242h-170v-301h-289v301h-641l627 1040h303v-798h170z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1212" 
d="M829 473q0 106 -67 162.5t-180 56.5q-86 0 -166.5 -36.5t-128.5 -110.5l-158 82l111 714h786v-262h-559l-35 -217q76 70 246 70q186 0 313 -121q125 -119 125 -338q0 -217 -151 -356q-149 -137 -402 -137q-139 0 -267.5 74t-207.5 196l242 156q38 -61 104 -100.5
t129 -39.5q114 0 190 60.5t76 146.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1200" 
d="M686 864q160 0 285 -119q127 -121 127 -333q0 -194 -135 -314q-130 -118 -353 -118q-192 0 -327 126q-136 130 -154 340q-6 72 -7 141.5t4 144t17 142t33 133.5t51 120.5t73 101t96 78t123.5 48t152.5 13.5q71 -3 129 -17.5t111 -44.5t96.5 -79.5t76.5 -118.5l-268 -115
q-52 107 -155 107q-68 -1 -120 -35.5t-80 -90.5t-39.5 -123t-6.5 -138q13 45 88 100q68 51 182 51zM748 303q54 54 54 125.5t-54 122.5q-51 54 -122.5 54t-125.5 -54q-51 -51 -51 -122.5t51 -125.5q54 -51 125.5 -51t122.5 51z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1126" 
d="M88 1077v264h1012l-592 -1341h-326l473 1077h-567z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1220" 
d="M197 993q0 156 120 264q123 111 293 111t293 -111q123 -108 123 -264q0 -137 -98 -225q89 -52 140.5 -141.5t51.5 -196.5q0 -190 -149 -321q-147 -129 -361 -129q-212 0 -362 129q-148 133 -148 321q0 109 51 198t142 140q-96 93 -96 225zM387 444q0 -84 65.5 -141
t157.5 -57t158 57t66 141q0 85 -61.5 143t-164.5 58q-101 0 -161 -58t-60 -143zM473 985q0 -57 36.5 -93t100.5 -36t101 36t37 93q0 52 -38.5 88.5t-99.5 36.5t-99 -36t-38 -89z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1198" 
d="M518 483q-162 0 -287 119t-125 334q0 191 134 313q134 119 354 119q193 0 328 -129q135 -126 153 -338q7 -82 7 -159.5t-7 -161t-25 -157.5t-45 -144t-71 -125t-100 -95.5t-133 -61t-169 -17.5q-71 3 -129 17.5t-111 44.5t-96.5 79.5t-76.5 118.5l268 114
q52 -106 156 -106q93 0 151 61q104 107 94 326q-53 -152 -270 -152zM455 1042q-51 -51 -51 -122t51 -125q54 -51 125 -51t122 51q54 54 54 125t-54 122q-51 54 -122 54t-125 -54z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="630" 
d="M190 29q-51 48 -51 123t51 120q51 50 125 50t125 -50q52 -46 52 -120t-52 -123q-51 -49 -125 -49t-125 49zM190 682q-51 50 -51 123t51 123t125 50t125 -50t51 -123t-51 -123q-51 -47 -125 -47t-125 47z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="626" 
d="M205 -276l-185 69q73 100 117 243.5t43 287.5h289q-3 -218 -84 -365q-83 -156 -180 -235zM207 682q-51 50 -51 123t51 123t125 50t125 -50t51 -123t-51 -123q-51 -47 -125 -47t-125 47z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1277" 
d="M1122 106l-1028 394v229l1028 393l2 -225l-741 -283l741 -282z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1220" 
d="M1081 918v-201h-942v201h942zM139 305v201h942v-201h-942z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1277" 
d="M156 1122l1028 -393v-229l-1028 -394l-2 226l741 282l-741 283z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1015" 
d="M561 471l-4 -45h-278l-5 45q0 138 15 211q18 69 60.5 108t148.5 93l82 35q47 25 61 48t14 64q0 47 -41 79t-104 32q-66 0 -105.5 -39.5t-39.5 -102.5h-287q0 196 119 312t313 116q193 0 313 -108q119 -107 119 -291q0 -144 -74.5 -246t-199.5 -141q-61 -19 -84 -56.5
t-23 -113.5zM600 152q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5t125 48.5t126 -49t52 -121z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1974" 
d="M1194 279v258q0 100 -70 170q-69 69 -168 69t-168 -69t-69 -170q0 -97 69 -166q70 -70 168 -70q94 0 162 51v-192q-85 -39 -162 -39q-173 0 -297 121q-122 119 -122 295q0 178 122 297q122 122 297 122q171 0 291 -114q125 -113 131 -285v-291q0 -63 34 -93t89 -30
q62 0 107.5 41.5t66.5 110.5q41 122 41 268q0 293 -217 486q-217 196 -522 196q-295 0 -500 -211q-203 -209 -203 -508q0 -297 203 -503q205 -205 500 -205h67v-183h-67q-373 0 -629 259t-256 632q0 375 256 637q258 264 629 264q144 4 284 -38t255.5 -122.5t203 -187
t135.5 -240.5t46 -276q0 -77 -12.5 -156t-43 -160t-75 -143t-114.5 -101.5t-155 -39.5q-138 0 -222.5 79.5t-84.5 236.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1304" 
d="M1149 1018q0 -84 -49 -155.5t-144 -108.5q130 -35 197 -129q70 -94 70 -193q0 -181 -123 -307q-122 -125 -328 -125h-606v1411h565q191 0 304.5 -106.5t113.5 -286.5zM467 1147v-293h235q57 0 94.5 41t37.5 104q0 69 -34.5 108.5t-99.5 39.5h-233zM750 606h-283v-342h289
q69 0 110 51t41 123q0 69 -43 118.5t-114 49.5z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1431" 
d="M1145 457l241 -168q-95 -144 -250.5 -226.5t-342.5 -82.5q-302 0 -506 210q-203 212 -203 512q0 303 203 515q204 210 506 210q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-174 0 -291 -129q-117 -126 -117 -314q0 -184 117 -313q118 -127 291 -127
q106 0 198 52.5t154 142.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1447" 
d="M719 1139h-254v-867h254q161 0 250 123q88 122 88 307q0 187 -88 312t-250 125zM719 0h-553v1411h553q296 0 467 -201q170 -200 170 -508t-170 -505t-467 -197z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1179" 
d="M1077 0h-911v1411h893v-264h-594v-322h520v-260h-520v-301h612v-264z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1101" 
d="M467 0h-301v1411h883v-264h-582v-322h461v-262h-461v-563z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1548" 
d="M1337 1196l-221 -190q-125 143 -309 143q-179 0 -301 -131q-121 -133 -121 -316q0 -185 123 -317q122 -131 299 -131q122 0 231 68q108 71 111 176h-379v243h725q0 -384 -186 -573q-185 -188 -502 -188q-304 0 -514 210q-209 212 -209 512t209 512q213 213 514 213
q159 0 297.5 -61t232.5 -170z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1435" 
d="M967 836v575h303v-1411h-303v567h-502v-567h-299v1411h299v-575h502z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="630" 
d="M166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="632" 
d="M-203 -449l27 271q71 -23 180 -23q79 0 120.5 53t41.5 142v1417h301v-1417q0 -213 -113 -344q-115 -133 -350 -133q-65 0 -207 34z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1247" 
d="M1155 1411l-534 -698l661 -713h-389l-424 479v-479h-303v1411h303v-463l334 463h352z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1046" 
d="M995 0h-829v1411h301v-1147h528v-264z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1869" 
d="M510 1411l426 -909l422 909h252l172 -1411h-301l-99 846l-329 -684h-232l-332 684l-100 -846h-301l174 1411h248z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1390" 
d="M924 1411h301v-1411h-269l-491 854v-854h-299v1411h274l484 -844v844z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1257" 
d="M739 1147h-272v-397h264q79 0 128.5 59.5t49.5 138.5q0 78 -47.5 138.5t-122.5 60.5zM467 0h-301v1411h579q228 0 347 -137q118 -133 118 -326q0 -192 -118 -325q-121 -136 -347 -136h-278v-487z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1613" 
d="M1225 111l135 -226l-211 -127l-152 246q-91 -24 -190 -24q-304 0 -514 210q-209 212 -209 512t209 512q213 213 514 213q307 0 514 -210q211 -211 211 -515q0 -176 -82 -331.5t-225 -259.5zM725 457l211 125l135 -220q160 127 160 340q0 186 -125 314q-123 129 -299 129
q-178 0 -301 -129q-121 -130 -121 -314q0 -153 92 -274q94 -117 240 -147q50 -13 121 -13z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1304" 
d="M729 1147h-262v-397h254q79 0 128.5 59.5t49.5 138.5q0 78 -47.5 138.5t-122.5 60.5zM860 500l350 -500h-360l-315 487h-68v-487h-301v1411h577q228 0 347 -137q122 -134 122 -326q0 -166 -92.5 -289t-259.5 -159z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1191" 
d="M90 414h301q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 184 140 303q136 118 346 118q211 0 342 -116q135 -117 135 -316h-299q0 73 -48 116.5t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53
q396 -114 387 -426q-3 -134 -78 -231q-142 -183 -430 -183q-229 0 -371 121q-141 123 -141 316z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1179" 
d="M438 1141h-381v270h1065v-270h-381v-1141h-303v1141z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1396" 
d="M944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1191" 
d="M463 0l-430 1411h311l252 -926l250 926h313l-430 -1411h-266z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1814" 
d="M1030 1212l187 -690l276 889h326l-480 -1411h-235l-199 741l-194 -741h-238l-475 1411h324l274 -889l188 690h246z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1347" 
d="M938 1411h358l-444 -659l489 -752h-354l-313 512l-314 -512h-352l488 752l-445 659h361l262 -422z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1333" 
d="M819 635v-635h-305v639l-545 772h357l340 -528l344 528h356z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1218" 
d="M86 0v227l645 920h-618v264h1007v-227l-651 -920h651v-264h-1034z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="692" 
d="M623 -469h-473v1880h473v-207h-232v-1462h232v-211z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="1024" 
d="M737 0l-678 1411h228l678 -1411h-228z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="692" 
d="M70 1411h473v-1880h-473v211h231v1462h-231v207z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1521" 
d="M1399 -270h-1276v204h1276v-204z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="1134" 
d="M567 1104l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1232" 
d="M420 481q0 -99 64 -165t157 -66q94 0 157.5 64.5t63.5 166.5q0 99 -64 163.5t-157 64.5q-94 0 -157.5 -63t-63.5 -165zM414 119v-119h-283v1477h283v-637q35 63 111 100t169 37q190 0 326 -139q137 -137 137 -363q0 -221 -137 -358q-134 -137 -326 -137q-94 0 -169.5 37
t-110.5 102z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1075" 
d="M594 977q128 0 237.5 -53.5t178.5 -149.5l-232 -166q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-67 -97 -177 -150.5t-239 -53.5q-214 0 -367 141t-153 360t153 357q154 139 367 139z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1232" 
d="M813 481q0 102 -63.5 165t-157.5 63q-93 0 -157 -64.5t-64 -163.5q0 -102 63.5 -166.5t157.5 -64.5q93 0 157 66t64 165zM1102 1477v-1477h-283v119q-35 -65 -110.5 -102t-169.5 -37q-191 0 -328 137q-135 138 -135 358q0 225 135 363q139 139 328 139q93 0 169 -37
t111 -100v637h283z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="751" 
d="M659 1477h105v-273h-105q-145 0 -145 -182v-66h193v-225h-193v-731h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1245" 
d="M831 481q0 100 -67.5 163t-163.5 63q-95 0 -162 -64.5t-67 -161.5q0 -102 66.5 -167.5t162.5 -65.5q91 0 162 65q69 66 69 168zM102 -180l230 112q24 -65 95 -109t146 -44q286 0 265 340q-32 -62 -112.5 -100.5t-174.5 -38.5q-191 0 -336 139q-141 138 -141 356
q0 223 141 361q144 141 336 141q93 0 172.5 -38t114.5 -101v118h276v-823q0 -300 -139.5 -455t-401.5 -155q-157 0 -283 80.5t-188 216.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1185" 
d="M416 506v-506h-285v1477h285v-670q27 82 108 125q80 45 160 45q186 0 284.5 -118.5t98.5 -338.5v-520h-285v510q0 85 -52 137.5t-124 52.5q-83 0 -136.5 -49.5t-53.5 -144.5z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="544" 
d="M131 956h283v-956h-283v956zM272 1477q72 0 123 -48.5t51 -117.5t-51 -118.5t-123 -49.5q-73 0 -125.5 49.5t-52.5 118.5t52 117.5t126 48.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="548" 
d="M55 -481h-174v258h125q128 0 125 151v1028h287v-1022q0 -207 -86.5 -311t-276.5 -104zM147 1192q-51 50 -51 119t51 116q54 49 127.5 49t124.5 -49q51 -47 51 -116t-51 -119t-124.5 -50t-127.5 50z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1077" 
d="M733 0l-319 379v-379h-283v1477h283v-783l227 262h363l-398 -421l477 -535h-350z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="546" 
d="M131 0v1477h285v-1477h-285z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1748" 
d="M1188 700q-76 0 -120 -49t-44 -145v-506h-285v516q-1 85 -45.5 134.5t-111.5 49.5q-76 0 -121 -48t-45 -146v-506h-285v956h285v-139q32 73 100.5 115.5t149.5 42.5q228 0 307 -168q105 170 287 170q175 0 271.5 -111.5t96.5 -322.5v-543h-282v510q0 89 -44.5 139.5
t-113.5 50.5z" />
    <glyph glyph-name="n" unicode="n" 
d="M416 506v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5v-571h-284v510q0 91 -45 140.5t-121 49.5q-176 0 -176 -194z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -63 155t-154 62t-153 -62t-62 -155q0 -97 61.5 -158t153.5 -61zM922 121q-143 -137 -353 -137q-209 0 -352 137t-143 360q0 222 143 359t352 137q210 0 353 -137q145 -139 145 -359q0 -221 -145 -360z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1232" 
d="M420 475q0 -102 63.5 -164.5t157.5 -62.5q93 0 157 64t64 163q0 101 -63.5 166.5t-157.5 65.5q-93 0 -157 -66.5t-64 -165.5zM131 -469v1425h283v-118q35 65 110.5 102t169.5 37q190 0 326 -139q137 -137 137 -357q0 -225 -137 -362q-136 -139 -326 -139q-93 0 -169 37
t-111 100v-586h-283z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1232" 
d="M817 838v118h285v-1425h-285v586q-32 -63 -109 -100t-171 -37q-190 0 -326 139q-135 138 -135 362q0 219 135 357q139 139 326 139q94 0 171 -37t109 -102zM811 475q0 99 -64 165.5t-157 66.5t-156 -65t-63 -167q0 -99 63 -163t156 -64q94 0 157.5 62.5t63.5 164.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="866" 
d="M416 334v-334h-285v956h285v-231q34 120 109 186t161 66q92 0 164 -35l-51 -256q-63 29 -146 29q-237 0 -237 -381z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="956" 
d="M504 360l-125 33q-201 53 -254 154q-22 47 -28.5 79t-4.5 79q3 106 113 188q113 84 260 84q160 0 276 -78q114 -77 117 -240h-268q-3 84 -109 84q-47 0 -74.5 -19.5t-25.5 -51.5q3 -51 84 -72l162 -43q102 -26 168 -88q57 -55 74.5 -97t17.5 -93q0 -146 -125 -222
q-124 -77 -289 -77q-159 0 -278 88q-118 87 -121 241h266q2 -43 43.5 -76.5t97.5 -33.5q51 2 84 23.5t33 56.5q0 31 -21.5 49t-72.5 32z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="747" 
d="M504 1323v-367h201v-225h-201v-731h-283v731h-174v225h174v367h283z" />
    <glyph glyph-name="u" unicode="u" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1064" 
d="M653 0h-243l-404 956h303l223 -594l222 594h305z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1562" 
d="M1239 956h303l-336 -956h-237l-189 608l-180 -608h-238l-346 956h303l170 -569l168 569h244l168 -569z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1085" 
d="M8 0l377 518l-315 438h331l144 -211l141 211h330l-316 -438l375 -518h-330l-202 293l-205 -293h-330z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1044" 
d="M733 956h303l-590 -1425h-301l230 567l-369 858h303l211 -542z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1044" 
d="M113 694v262h833v-213l-459 -481h459v-262h-862v211l451 483h-422z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="843" 
d="M63 385v172q51 0 88 33.5t37 85.5v325q-3 181 117 295q121 115 297 115h174v-207h-180q-69 0 -118.5 -51.5t-49.5 -124.5v-336q0 -81 -48.5 -141.5t-113.5 -79.5q65 -19 113.5 -79.5t48.5 -141.5v-313q0 -75 50.5 -133t117.5 -58h180v-215h-174q-176 0 -297 115
q-120 114 -117 295v325q0 52 -37 85.5t-88 33.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="448" 
d="M150 -254v1896h149v-1896h-149z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="843" 
d="M780 557v-172q-51 0 -88 -33.5t-37 -85.5v-325q3 -180 -118 -295t-295 -115h-174v215h180q67 0 117.5 58t50.5 133v313q0 81 48.5 141.5t113.5 79.5q-65 19 -113.5 79.5t-48.5 141.5v336q0 73 -49.5 124.5t-118.5 51.5h-180v207h174q174 0 295 -115t118 -295v-325
q0 -52 37 -85.5t88 -33.5z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="546" 
d="M391 530l41 -999h-315l41 999h233zM451 805q0 -71 -52.5 -120.5t-126.5 -49.5t-125 48.5t-51 121.5t51 122.5t125 49.5t126.5 -49.5t52.5 -122.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1101" 
d="M504 1190h149v-215q236 -21 363 -201l-232 -166q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-126 -181 -363 -202v-236h-149v242q-184 35 -303 166q-121 133 -121 327q0 191 121 324q117 129 303 164v221z
" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1392" 
d="M823 571h-235v-317h377q26 0 43.5 21.5t17.5 52.5v59h281v-115q0 -118 -76 -194q-75 -78 -182 -78h-965v254h217v317h-170v129h170v269q0 204 121 331q124 127 315 127q202 0 334 -120q131 -119 131 -351h-297q0 168 -162 168q-72 0 -113.5 -41.5t-41.5 -113.5v-269h235
v-129z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1271" 
d="M635 524q72 0 122 49t50 121t-50 122t-122 50t-121 -50t-49 -122q0 -71 50 -120.5t120 -49.5zM416 336l-146 -148l-141 142l145 147q-59 95 -59 217q0 121 61 219l-147 148l141 141l148 -147q101 61 217 61q121 0 219 -61l145 147l142 -141l-148 -150q64 -107 64 -217
q0 -122 -62 -217l146 -147l-142 -142l-145 148q-98 -62 -219 -62t-219 62z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1519" 
d="M1102 1411h356l-526 -747h299v-140h-320v-190h320v-139h-320v-195h-305v195h-317v139h317v190h-317v140h299l-527 747h357l340 -528z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="452" 
d="M301 1642v-782h-149v782h149zM152 532h149v-786h-149v786z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1107" 
d="M659 467v-250q-53 -12 -102 -12q-184 0 -309 119q-127 121 -127 311v360q0 191 127 312q124 118 309 118q152 0 274 -88q121 -84 156 -250l-239 -71q-22 78 -74 118t-117 40q-76 0 -129 -50.5t-53 -128.5v-360q0 -78 52.5 -127t129.5 -49h12q51 0 90 8zM449 616v250
q57 13 102 13q184 0 309 -119q127 -121 127 -311v-361q0 -190 -127 -311q-125 -119 -309 -119q-153 0 -275 88q-120 83 -155 250l239 72q22 -78 74 -118t117 -40q76 0 129 50t53 128v361q0 78 -52.5 127t-129.5 49h-12q-47 0 -90 -9z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="1148" 
d="M246 1307q0 57 44 98t105 41q60 0 103 -41t43 -98q0 -60 -43 -101t-103 -41q-62 0 -105.5 41t-43.5 101zM608 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1632" 
d="M1116 502q-50 -76 -131.5 -122t-171.5 -46q-152 0 -252 106q-102 108 -102 256q0 151 102 256q101 107 252 107q192 0 299 -162l-135 -94q-70 94 -164 94q-79 0 -130.5 -57t-51.5 -144q0 -83 51.5 -139.5t130.5 -56.5q96 0 166 94zM307 190q-207 213 -207 512t207 512
q210 213 508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210zM422 1100q-162 -162 -162 -398q0 -235 162 -397q164 -164 393 -164t393 164q162 162 162 397q0 236 -162 398q-163 166 -393 166t-393 -166z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="958" 
d="M805 662h-156v92q-35 -49 -92.5 -78t-118.5 -29q-142 0 -243 98t-101 256t101 256q102 99 243 99q134 0 211 -103v88h156v-679zM649 1004q0 88 -59 142t-141 54q-85 0 -140 -54.5t-55 -141.5t56 -144t139 -57q86 0 143 55t57 146zM803 434h-645v109h645v-109z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1034" 
d="M362 53l-282 424l282 424h222l-283 -424l283 -424h-222zM725 53l-283 424l283 424h221l-282 -424l282 -424h-221z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1286" 
d="M139 518v246h996v-551h-248v305h-748z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1632" 
d="M737 897v-166h109q31 0 52.5 26.5t21.5 59.5q0 28 -20 54t-48 26h-115zM1108 356h-207l-149 224h-15v-224h-172v695h291q112 0 176 -66.5t64 -167.5q0 -80 -43 -142t-117 -87zM307 190q-207 213 -207 512t207 512q210 213 508 213t508 -213q209 -212 209 -512t-209 -512
q-207 -210 -508 -210t-508 210zM422 1100q-162 -162 -162 -398q0 -235 162 -397q164 -164 393 -164t393 164q162 162 162 397q0 236 -162 398q-163 166 -393 166t-393 -166z" />
    <glyph glyph-name="overscore" unicode="&#xaf;" horiz-adv-x="1060" 
d="M815 1202h-569v174h569v-174z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="770" 
d="M90 1083q0 121 86 207q85 88 207 88q124 0 209 -88q88 -85 88 -207q0 -123 -88 -211q-86 -86 -209 -86q-121 0 -207 86q-86 89 -86 211zM238 1083q0 -63 42.5 -106t102.5 -43q63 0 106 43t43 106q0 62 -43 105t-106 43q-60 0 -102.5 -43.5t-42.5 -104.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1120" 
d="M981 598h-317v-293h-209v293h-316v209h316v289h209v-289h317v-209zM981 207v-209h-842v209h842z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="649" 
d="M59 889v88l320 379q41 47 41 90q0 33 -27.5 55.5t-70.5 22.5t-74 -30t-37 -79l-152 27q19 111 91 175t182 64q105 0 178 -67q76 -64 76 -168q0 -106 -82 -197l-180 -205h241v-155h-506z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="649" 
d="M213 1485l-131 82q36 56 100.5 87t141.5 27q100 -6 166.5 -69t62.5 -148q0 -99 -100 -153q62 -23 100.5 -85t32.5 -126q-9 -100 -99 -166q-88 -64 -206 -55q-80 6 -148 51t-100 112l143 72q39 -67 117 -76q42 -1 75 24t38 58q0 42 -7 60q-21 50 -100 47h-82v141h82
q78 0 78 86q0 28 -22.5 52t-57.5 24q-55 0 -84 -45z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="1134" 
d="M264 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1175" 
d="M408 -391h-261v1347h285v-510q0 -96 49.5 -143t120.5 -47q77 0 124.5 49.5t47.5 145.5v505h285v-956h-285v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-83 0 -147 55l29 -271v-155z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1460" 
d="M512 1411h190v-1440q0 -49 -49 -49h-161v600q-174 9 -302 140q-122 128 -122 305q0 184 129 313q128 131 315 131zM1104 1204h-70v-1304q0 -107 -86 -193q-85 -88 -194 -88h-265v186h248q40 0 66 28t26 67v1511h279q115 0 199 -84q82 -82 82 -197v-174h-191v156
q0 40 -27.5 66t-66.5 26z" />
    <glyph glyph-name="middot" unicode="&#xb7;" horiz-adv-x="575" 
d="M465 711q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5t125 48.5t126 -49t52 -121z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="1087" 
d="M492 -254q21 -54 79 -39q55 24 31 86l-96 262h133l102 -190q21 -34 21 -101q0 -84 -66 -147q-63 -63 -153 -63q-62 0 -115.5 36t-79.5 97q-19 46 -16 120l157 -8q-3 -26 3 -53z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="440" 
d="M176 889v559l-133 -55v143l297 141v-788h-164z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="929" 
d="M457 647q-148 0 -252 98q-103 103 -103 261q0 156 103 251q102 99 252 99t252 -99q104 -94 104 -251q0 -160 -104 -261q-101 -98 -252 -98zM655 1006q0 86 -56 140t-142 54t-142.5 -54.5t-56.5 -139.5q0 -90 56.5 -146.5t142.5 -56.5t142 56.5t56 146.5zM793 434h-646
v109h646v-109z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1034" 
d="M672 901l282 -424l-282 -424h-221l282 424l-282 424h221zM309 901l283 -424l-283 -424h-221l283 424l-283 424h221z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1603" 
d="M1528 174h-98v-174h-166v174h-373l366 604h173v-465h98v-139zM1264 557l-148 -244h148v244zM213 563v559l-133 -55v143l297 142v-789h-164zM1116 1368h168l-924 -1388h-170z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1619" 
d="M1010 0v88l319 379q41 47 41 90q0 33 -27.5 55.5t-70.5 22.5t-74 -30t-37 -79l-151 27q19 111 91 175.5t181 64.5q104 0 178 -68q76 -64 76 -168q0 -106 -82 -197l-180 -204h242v-156h-506zM213 563v559l-133 -55v143l297 142v-789h-164zM1104 1368h168l-924 -1388h-170z
" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1699" 
d="M254 1159l-131 82q36 56 100.5 87.5t141.5 27.5q100 -6 166.5 -69t62.5 -148q0 -100 -100 -154q62 -23 100.5 -85t32.5 -126q-9 -100 -99 -166q-88 -64 -206 -55q-79 6 -147.5 51t-100.5 113l143 71q38 -66 117 -75q42 -1 74.5 24t37.5 58q0 44 -6 59q-21 50 -100 47h-82
v141h82q78 0 78 86q0 28 -22.5 52t-57.5 24q-55 0 -84 -45zM1622 174h-98v-174h-166v174h-373l367 604h172v-465h98v-139zM1358 557l-148 -244h148v244zM1247 1368h168l-923 -1388h-170z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="997" 
d="M446 485l5 45h278l4 -45q0 -93 -2 -120q-3 -44 -14 -91q-14 -68 -56.5 -106t-150.5 -94l-82 -35q-47 -25 -61.5 -48.5t-14.5 -64.5q0 -47 41.5 -78.5t104.5 -31.5q66 0 105.5 39t39.5 102h287q0 -195 -119 -311q-120 -117 -313 -117t-314 109q-118 106 -118 290
q0 144 74.5 246t199.5 141q61 18 83.5 56t22.5 114zM408 805q0 73 52 122.5t126 49.5t125 -49.5t51 -122.5t-51 -121.5t-125 -48.5t-126 49t-52 121z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM621 1559l-304 344h283l225 -344h-204z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM535 1559l225 344h282l-303 -344h-204z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM307 1567l223 344h283l225 -344h-204l-162 182l-160 -182h-205z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM584 1696q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41
q-58 43 -75 43z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM319 1761q0 57 44.5 98.5t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100zM682 1761q0 57 44 98.5t105 41.5
q60 0 103 -41.5t43 -98.5q0 -60 -43 -100.5t-103 -40.5q-62 0 -105.5 40.5t-43.5 100.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1349" 
d="M426 1804q0 106 72 178q71 74 174 74q105 0 176 -74q74 -71 74 -178q0 -103 -74 -174q-71 -71 -176 -71q-103 0 -174 71q-72 72 -72 174zM559 1804q0 -47 33 -79.5t80 -32.5q49 0 82.5 32.5t33.5 79.5q0 51 -33 85t-83 34q-48 0 -80.5 -34t-32.5 -85zM674 1085l-144 -491
h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1912" 
d="M-43 0l891 1411h944v-264h-594v-322h520v-260h-520v-301h612v-264h-911v2l-2 -2v324h-377l-205 -324h-358zM897 612v398l-207 -398h207z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1435" 
d="M678 -254q22 -54 80 -39q54 24 30 86l-69 191q-275 27 -455 231q-178 205 -178 487q0 303 203 515q204 210 506 210q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-174 0 -291 -129q-117 -126 -117 -314q0 -184 117 -313q118 -127 291 -127q106 0 198 52.5
t154 142.5l242 -168q-87 -131 -224 -211.5t-301 -93.5l64 -119q20 -39 20 -101q0 -85 -65 -147q-63 -63 -154 -63q-62 0 -115 36t-79 97q-20 48 -17 120l158 -8q-3 -29 2 -53z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1179" 
d="M1077 0h-911v1411h893v-264h-594v-322h520v-260h-520v-301h612v-264zM578 1559l-304 344h283l225 -344h-204z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1179" 
d="M166 1411h893v-264h-594v-322h520v-260h-520v-301h612v-264h-911v1411zM436 1559l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1179" 
d="M1077 0h-911v1411h893v-264h-594v-322h520v-260h-520v-301h612v-264zM215 1567l223 344h283l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1179" 
d="M166 1411h893v-264h-594v-322h520v-260h-520v-301h612v-264h-911v1411zM252 1761q0 57 44 98.5t105 41.5q60 0 103 -41.5t43 -98.5q0 -60 -43 -100.5t-103 -40.5q-62 0 -105.5 40.5t-43.5 100.5zM614 1761q0 57 44.5 98.5t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5
q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="630" 
d="M258 1559l-303 344h283l225 -344h-205zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="630" 
d="M166 0v1411h299v-1411h-299zM164 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="630" 
d="M-49 1567l223 344h283l225 -344h-205l-162 182l-159 -182h-205zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="630" 
d="M-12 1761q0 57 44 98.5t105 41.5q60 0 103 -41.5t43 -98.5q0 -60 -43 -100.5t-103 -40.5q-62 0 -105.5 40.5t-43.5 100.5zM350 1761q0 57 44.5 98.5t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100zM166 0v1411h299
v-1411h-299z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1478" 
d="M197 582h-193v200h193v629h553q296 0 467 -201q169 -199 169 -508t-169 -505q-170 -197 -467 -197h-553v582zM748 582h-252v-310h254q160 0 249 123q88 122 88 307q0 187 -88 312t-249 125h-254v-357h252v-200z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1390" 
d="M606 1696q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 75 -41t36 22t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-41 0 -100 41q-58 43 -78 43zM924 1411h301v-1411h-269l-491 854v-854h-299v1411h274l484 -844v844z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM739 1559l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM666 1559l225 344h283l-304 -344h-204z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM434 1567l223 344h283l225 -344h-204l-162 182l-160 -182h-205z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM711 1696q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41q-58 43 -75 43z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM471 1761q0 57 44.5 98.5t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100zM834 1761q0 57 44 98.5t105 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -105.5 40.5
t-43.5 100.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1005" 
d="M887 770l-240 -240l240 -237l-148 -150l-239 240l-236 -233l-147 147l235 233l-237 238l147 147l238 -237l239 240z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1595" 
d="M350 137l-2 -2l-164 -168l-96 96l164 168q-170 203 -170 471q0 299 207 512q210 213 508 213q264 0 456 -165l162 165l96 -94l-163 -168q165 -201 165 -463q0 -301 -208 -512q-207 -210 -508 -210q-252 0 -447 157zM381 702q0 -141 80 -256l583 601q-109 83 -247 83
q-177 0 -295 -124q-121 -127 -121 -304zM797 276q176 0 295 125q122 125 122 301q0 134 -77 246l-580 -596q104 -76 240 -76z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1396" 
d="M944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174zM655 1559l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1396" 
d="M944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174zM535 1559l225 344h282l-303 -344h-204z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1396" 
d="M944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174zM332 1567l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1396" 
d="M369 1761q0 57 44 98.5t105 41.5q60 0 103 -41.5t43 -98.5q0 -60 -43 -100.5t-103 -40.5q-62 0 -105.5 40.5t-43.5 100.5zM731 1761q0 57 44.5 98.5t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100zM944 504v907h301
v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1333" 
d="M819 635v-635h-305v639l-545 772h357l340 -528l344 528h356zM512 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1171" 
d="M823 715q0 94 -51.5 153.5t-122.5 59.5h-184v-416h184q71 0 122.5 56.5t51.5 146.5zM664 258h-199v-258h-299v1411h299v-229h199q192 0 325 -127q131 -125 131 -332q0 -208 -131 -336q-132 -129 -325 -129z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1159" 
d="M473 2l55 268q44 -8 97 -8q84 0 137 60.5t53 144.5q0 73 -49 137q-48 66 -156 66h-55v248h35q50 0 78 34.5t28 91.5q0 56 -39 88.5t-106 32.5q-57 0 -97 -39t-40 -100v-1026h-283v1004q0 196 115 311q115 112 299 112q190 0 309 -98t119 -242q0 -170 -129 -253
q112 -56 175.5 -155.5t63.5 -232.5q0 -205 -118 -335q-116 -131 -340 -131q-72 0 -152 22z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM580 1104l-304 344h283l225 -344h-204z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM432 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM262 1112l223 344h283l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM539 1241q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41q-58 43 -75 43z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM299 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101zM662 1307q0 57 44 98t105 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -105.5 41t-43.5 101z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM381 1350q0 106 72 178q71 74 174 74q105 0 176 -74q74 -71 74 -178q0 -103 -74 -174q-72 -72 -176 -72q-102 0 -174 72t-72 174zM514 1350q0 -47 33 -80t80 -33q49 0 82.5 33t33.5 80q0 51 -33 84.5t-83 33.5q-48 0 -80.5 -33.5t-32.5 -84.5
z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1986" 
d="M1663 315l207 -141q-132 -194 -436 -194q-177 0 -312 102v-82h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-79q132 100 312 100q210 0 348 -141q141 -138 141 -363
q0 -22 -4 -88h-700q10 -75 77.5 -117.5t157.5 -42.5q137 0 209 90zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162q0 -99 71 -164q72 -63 168 -63zM1223 563h413q-7 52 -37.5 90t-72.5 55.5t-90.5 17.5t-91.5 -17t-76.5 -55
t-44.5 -91z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1075" 
d="M659 -16l64 -119q20 -36 20 -101q0 -85 -65 -147q-63 -63 -154 -63q-62 0 -115 36t-79 97q-20 48 -17 120l158 -8q-3 -29 2 -53q22 -54 80 -39q52 21 31 86l-72 193q-188 27 -313 161t-125 334q0 219 153 357q154 139 367 139q128 0 237.5 -53.5t178.5 -149.5l-232 -166
q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-126 -176 -351 -200z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM508 1104l-303 344h282l226 -344h-205z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM436 1104l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM203 1112l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM240 1307q0 57 44 98t105 41q60 0 103 -41t43 -98q0 -60 -43 -101t-103 -41q-62 0 -105.5 41t-43.5 101zM602 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="542" 
d="M131 0v956h281v-956h-281zM211 1104l-303 344h282l226 -344h-205z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="542" 
d="M412 956v-956h-281v956h281zM131 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="542" 
d="M131 0v956h281v-956h-281zM-94 1112l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="542" 
d="M412 956v-956h-281v956h281zM-61 1307q0 57 44 98t105 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -105.5 41t-43.5 101zM301 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1144" 
d="M850 1280l-180 -82l305 -420q94 -125 94 -297q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137t-143 360q0 220 147 355t359 120l-119 150l-174 -80l-60 100l166 74l-151 211h272l86 -119l176 80zM571 262q93 0 155 61t62 158q0 93 -62 153t-155 60
q-92 0 -153.5 -60t-61.5 -153q0 -97 61.5 -158t153.5 -61z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M416 506v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5v-571h-284v510q0 91 -45 140.5t-121 49.5q-176 0 -176 -194zM492 1241q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62
h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41q-58 43 -75 43z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -62 153t-155 60q-92 0 -153.5 -60t-61.5 -153q0 -97 61.5 -158t153.5 -61zM74 481q0 221 143 355q143 137 352 137q210 0 353 -137q145 -136 145 -355q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137t-143 360zM463 1104
l-303 344h282l226 -344h-205z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -62 153t-155 60q-92 0 -153.5 -60t-61.5 -153q0 -97 61.5 -158t153.5 -61zM1067 481q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137t-143 360q0 221 143 355q143 137 352 137q210 0 353 -137q145 -136 145 -355zM449 1104
l225 344h282l-303 -344h-204z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -62 153t-155 60q-92 0 -153.5 -60t-61.5 -153q0 -97 61.5 -158t153.5 -61zM74 481q0 221 143 355q143 137 352 137q210 0 353 -137q145 -136 145 -355q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137t-143 360zM205 1112
l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -62 153t-155 60q-92 0 -153.5 -60t-61.5 -153q0 -97 61.5 -158t153.5 -61zM74 481q0 221 143 355q143 137 352 137q210 0 353 -137q145 -136 145 -355q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137t-143 360zM481 1241
q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 75 -41t36 22t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41q-58 43 -76 43z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -62 153t-155 60q-92 0 -153.5 -60t-61.5 -153q0 -97 61.5 -158t153.5 -61zM1067 481q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137t-143 360q0 221 143 355q143 137 352 137q210 0 353 -137q145 -136 145 -355zM242 1307
q0 57 44 98t105 41q60 0 103 -41t43 -98q0 -60 -43 -101t-103 -41q-62 0 -105.5 41t-43.5 101zM604 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1193" 
d="M760 133q0 -67 -47 -111.5t-115 -44.5t-114 44.5t-46 111.5t46 110.5t114 43.5t115 -44t47 -110zM438 868q0 66 46 110t114 44t115 -44t47 -110q0 -67 -47 -111t-115 -44t-114 44t-46 111zM106 399v197h981v-197h-981z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1140" 
d="M569 -16q-161 0 -288 86l-119 -121l-96 96l114 119q-106 127 -106 317q0 222 143 359t352 137q169 0 308 -103l106 111l96 -94l-108 -111q96 -131 96 -299q0 -221 -145 -360q-143 -137 -353 -137zM569 698q-91 0 -153 -62t-62 -155q0 -63 27 -110l293 299q-54 28 -105 28
zM569 262q93 0 155 61t62 158q0 43 -18 90l-285 -292q39 -17 86 -17z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM571 1104l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM397 1104l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM209 1112l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM236 1307q0 57 44 98t105 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41
q-62 0 -105.5 41t-43.5 101zM598 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1044" 
d="M446 -469h-301l230 567l-369 858h303l211 -542l213 542h303zM342 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1232" 
d="M420 481q0 -99 64 -165t157 -66q94 0 157.5 64.5t63.5 166.5q0 99 -64 163.5t-157 64.5q-94 0 -157.5 -63t-63.5 -165zM414 119v-588h-283v1946h283v-637q35 63 111 100t169 37q190 0 326 -139q137 -137 137 -363q0 -221 -137 -358q-134 -137 -326 -137q-94 0 -169.5 37
t-110.5 102z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1044" 
d="M446 -469h-301l230 567l-369 858h303l211 -542l213 542h303zM199 1307q0 57 44 98t105 41q60 0 103 -41t43 -98q0 -60 -43 -101t-103 -41q-62 0 -105.5 41t-43.5 101zM561 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41
t-44 101z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM958 1657h-569v174h569v-174z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM913 1202h-569v174h569v-174z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM397 1866h174q0 -109 101 -109q98 0 98 109h178q0 -121 -76.5 -195t-199.5 -74q-129 0 -202 77t-73 192z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162
q0 -99 71 -164q72 -63 168 -63zM352 1411h174q0 -108 101 -108q98 0 98 108h178q0 -121 -76 -194.5t-200 -73.5q-129 0 -202 76.5t-73 191.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1349" 
d="M838 1411l522 -1411h-172l-76 -207q-21 -65 31 -86q58 -15 80 39l2 53l157 8q3 -74 -16 -120q-26 -61 -79.5 -97t-115.5 -36q-92 0 -155 63q-64 61 -64 147q0 60 21 101l71 135l-124 334h-488l-125 -334h-319l524 1411h326zM674 1085l-144 -491h293z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1253" 
d="M608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162q0 -99 71 -164q72 -63 168 -63zM1122 956v-956h-139l-76 -207q-21 -65 31 -86q58 -15 80 39l2 53l158 8q3 -72 -17 -120q-26 -61 -79 -97t-115 -36q-93 0 -156 63
q-63 60 -63 147q0 62 20 101l72 135v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1431" 
d="M1145 457l241 -168q-95 -144 -250.5 -226.5t-342.5 -82.5q-302 0 -506 210q-203 212 -203 512q0 303 203 515q204 210 506 210q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-174 0 -291 -129q-117 -126 -117 -314q0 -184 117 -313q118 -127 291 -127
q106 0 198 52.5t154 142.5zM678 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1075" 
d="M594 977q128 0 237.5 -53.5t178.5 -149.5l-232 -166q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-67 -97 -177 -150.5t-239 -53.5q-214 0 -367 141t-153 360t153 357q154 139 367 139zM434 1104l225 344h283
l-303 -344h-205z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1431" 
d="M1145 457l241 -168q-95 -144 -250.5 -226.5t-342.5 -82.5q-302 0 -506 210q-203 212 -203 512q0 303 203 515q204 210 506 210q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-174 0 -291 -129q-117 -126 -117 -314q0 -184 117 -313q118 -127 291 -127
q106 0 198 52.5t154 142.5zM420 1567l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1075" 
d="M594 977q128 0 237.5 -53.5t178.5 -149.5l-232 -166q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-67 -97 -177 -150.5t-239 -53.5q-214 0 -367 141t-153 360t153 357q154 139 367 139zM213 1112l223 344h283
l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1431" 
d="M1145 457l241 -168q-95 -144 -250.5 -226.5t-342.5 -82.5q-302 0 -506 210q-203 212 -203 512q0 303 203 515q204 210 506 210q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-174 0 -291 -129q-117 -126 -117 -314q0 -184 117 -313q118 -127 291 -127
q106 0 198 52.5t154 142.5zM627 1776q0 63 45.5 107t111.5 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1075" 
d="M594 977q128 0 237.5 -53.5t178.5 -149.5l-232 -166q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-67 -97 -177 -150.5t-239 -53.5q-214 0 -367 141t-153 360t153 357q154 139 367 139zM414 1321
q0 63 45.5 107.5t111.5 44.5t112 -44.5t46 -107.5q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1431" 
d="M1145 457l241 -168q-95 -144 -250.5 -226.5t-342.5 -82.5q-302 0 -506 210q-203 212 -203 512q0 303 203 515q204 210 506 210q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-174 0 -291 -129q-117 -126 -117 -314q0 -184 117 -313q118 -127 291 -127
q106 0 198 52.5t154 142.5zM1135 1901l-226 -345h-282l-224 345h205l160 -183l162 183h205z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1075" 
d="M594 977q128 0 237.5 -53.5t178.5 -149.5l-232 -166q-63 97 -184 97q-96 0 -166 -64q-72 -63 -72 -160q0 -100 72 -166q69 -63 166 -63q122 0 184 96l232 -164q-67 -97 -177 -150.5t-239 -53.5q-214 0 -367 141t-153 360t153 357q154 139 367 139zM930 1446l-225 -344
h-283l-223 344h204l160 -182l162 182h205z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1447" 
d="M719 1139h-254v-867h254q161 0 250 123q88 122 88 307q0 187 -88 312t-250 125zM719 0h-553v1411h553q296 0 467 -201q170 -200 170 -508t-170 -505t-467 -197zM1065 1901l-225 -345h-283l-223 345h205l159 -183l162 183h205z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1300" 
d="M813 481q0 102 -63.5 165t-157.5 63q-93 0 -157 -64.5t-64 -163.5q0 -102 63.5 -166.5t157.5 -64.5q93 0 157 66t64 165zM1102 1477v-1477h-283v119q-35 -65 -110.5 -102t-169.5 -37q-191 0 -328 137q-135 138 -135 358q0 225 135 363q139 139 328 139q93 0 169 -37
t111 -100v637h283zM1303 938l-140 72q67 93 94 180q28 88 25 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1478" 
d="M197 582h-193v200h193v629h553q296 0 467 -201q169 -199 169 -508t-169 -505q-170 -197 -467 -197h-553v582zM748 582h-252v-310h254q160 0 249 123q88 122 88 307q0 187 -88 312t-249 125h-254v-357h252v-200z" />
    <glyph glyph-name="dslash" unicode="&#x111;" horiz-adv-x="1232" 
d="M819 1141h-305v176h305v160h283v-160h155v-176h-155v-1141h-283v119q-35 -65 -110.5 -102t-169.5 -37q-191 0 -328 137q-135 138 -135 358q0 225 135 363q139 139 328 139q93 0 169 -37t111 -100v301zM813 481q0 102 -63.5 165t-157.5 63q-93 0 -157 -64.5t-64 -163.5
q0 -102 63.5 -166.5t157.5 -64.5q93 0 157 66t64 165z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1179" 
d="M1077 0h-911v1411h893v-264h-594v-322h520v-260h-520v-301h612v-264zM864 1657h-569v174h569v-174z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM852 1202h-569v174h569v-174z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1179" 
d="M166 1411h893v-264h-594v-322h520v-260h-520v-301h612v-264h-911v1411zM305 1866h174q0 -109 101 -109q98 0 98 109h178q0 -121 -76.5 -195t-199.5 -74q-129 0 -202 77t-73 192z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM293 1411h174q0 -108 100 -108q99 0 99 108h178q0 -121 -76.5 -194.5t-200.5 -73.5q-129 0 -201.5 76.5t-72.5 191.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1179" 
d="M166 1411h893v-264h-594v-322h520v-260h-520v-301h612v-264h-911v1411zM442 1776q0 63 46 107t112 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-113 43.5t-45 110.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM410 1321q0 63 45.5 107.5t111.5 44.5t112 -44.5t46 -107.5q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1179" 
d="M1110 -254l2 53l158 8q3 -72 -17 -120q-26 -61 -79 -97t-115 -36q-93 0 -156 63q-63 60 -63 147q0 60 20 101l72 135h-766v1411h893v-264h-594v-322h520v-260h-520v-301h612v-264l-78 -207q-24 -62 31 -86q58 -15 80 39z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1128" 
d="M803 315l207 -141q-4 -6 -21 -25t-41.5 -46.5t-40.5 -47.5q-30 -40 -61.5 -111t-48.5 -151q-7 -38 -0.5 -57t30.5 -29q58 -15 80 39l2 53l158 8q3 -74 -16 -120q-26 -61 -79.5 -97t-115.5 -36q-93 0 -156 63q-63 60 -63 147q0 62 20 101l70 131q-82 -16 -154 -16
q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55t-44.5 -91z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1179" 
d="M1077 0h-911v1411h893v-264h-594v-322h520v-260h-520v-301h612v-264zM946 1901l-225 -345h-283l-223 345h205l160 -183l161 183h205z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1126" 
d="M803 315l207 -141q-132 -194 -437 -194q-211 0 -356 139q-143 140 -143 362t143 359t356 137q211 0 349 -141q141 -138 141 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90zM362 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55
t-44.5 -91zM934 1446l-225 -344h-283l-223 344h205l159 -182l162 182h205z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1548" 
d="M1337 1196l-221 -190q-125 143 -309 143q-179 0 -301 -131q-121 -133 -121 -316q0 -185 123 -317q122 -131 299 -131q122 0 231 68q108 71 111 176h-379v243h725q0 -384 -186 -573q-185 -188 -502 -188q-304 0 -514 210q-209 212 -209 512t209 512q213 213 514 213
q159 0 297.5 -61t232.5 -170zM416 1567l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1245" 
d="M831 481q0 100 -67.5 163t-163.5 63q-95 0 -162 -64.5t-67 -161.5q0 -102 66.5 -167.5t162.5 -65.5q91 0 162 65q69 66 69 168zM102 -180l230 112q24 -65 95 -109t146 -44q286 0 265 340q-32 -62 -112.5 -100.5t-174.5 -38.5q-191 0 -336 139q-141 138 -141 356
q0 223 141 361q144 141 336 141q93 0 172.5 -38t114.5 -101v118h276v-823q0 -300 -139.5 -455t-401.5 -155q-157 0 -283 80.5t-188 216.5zM258 1112l223 344h283l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1548" 
d="M1337 1196l-221 -190q-125 143 -309 143q-179 0 -301 -131q-121 -133 -121 -316q0 -185 123 -317q122 -131 299 -131q122 0 231 68q108 71 111 176h-379v243h725q0 -384 -186 -573q-185 -188 -502 -188q-304 0 -514 210q-209 212 -209 512t209 512q213 213 514 213
q159 0 297.5 -61t232.5 -170zM506 1866h174q0 -109 100 -109q99 0 99 109h178q0 -121 -76.5 -195t-200.5 -74q-129 0 -201.5 77t-72.5 192z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1245" 
d="M831 481q0 100 -67.5 163t-163.5 63q-95 0 -162 -64.5t-67 -161.5q0 -102 66.5 -167.5t162.5 -65.5q91 0 162 65q69 66 69 168zM332 -68q24 -65 95 -109t146 -44q286 0 265 340q-32 -62 -112.5 -100.5t-174.5 -38.5q-191 0 -336 139q-141 138 -141 356q0 223 141 361
q144 141 336 141q93 0 172.5 -38t114.5 -101v118h276v-823q0 -300 -139.5 -455t-401.5 -155q-157 0 -283 80.5t-188 216.5zM348 1411h174q0 -108 101 -108q98 0 98 108h178q0 -121 -76 -194.5t-200 -73.5q-129 0 -202 76.5t-73 191.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1548" 
d="M1337 1196l-221 -190q-125 143 -309 143q-179 0 -301 -131q-121 -133 -121 -316q0 -185 123 -317q122 -131 299 -131q122 0 231 68q108 71 111 176h-379v243h725q0 -384 -186 -573q-185 -188 -502 -188q-304 0 -514 210q-209 212 -209 512t209 512q213 213 514 213
q159 0 297.5 -61t232.5 -170zM623 1776q0 63 45.5 107t111.5 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1245" 
d="M831 481q0 100 -67.5 163t-163.5 63q-95 0 -162 -64.5t-67 -161.5q0 -102 66.5 -167.5t162.5 -65.5q91 0 162 65q69 66 69 168zM332 -68q24 -65 95 -109t146 -44q286 0 265 340q-32 -62 -112.5 -100.5t-174.5 -38.5q-191 0 -336 139q-141 138 -141 356q0 223 141 361
q144 141 336 141q93 0 172.5 -38t114.5 -101v118h276v-823q0 -300 -139.5 -455t-401.5 -155q-157 0 -283 80.5t-188 216.5zM467 1321q0 63 46 107.5t112 44.5t111.5 -44.5t45.5 -107.5q0 -67 -44.5 -110.5t-112.5 -43.5t-113 43.5t-45 110.5z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1548" 
d="M1337 1196l-221 -190q-125 143 -309 143q-179 0 -301 -131q-121 -133 -121 -316q0 -185 123 -317q122 -131 299 -131q122 0 231 68q108 71 111 176h-379v243h725q0 -384 -186 -573q-185 -188 -502 -188q-304 0 -514 210q-209 212 -209 512t209 512q213 213 514 213
q159 0 297.5 -61t232.5 -170zM741 -588l-139 72q67 93 94 180q28 88 25 221h240q-24 -307 -220 -473z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1245" 
d="M831 481q0 100 -67.5 163t-163.5 63q-95 0 -162 -64.5t-67 -161.5q0 -102 66.5 -167.5t162.5 -65.5q91 0 162 65q69 66 69 168zM332 -68q24 -65 95 -109t146 -44q286 0 265 340q-32 -62 -112.5 -100.5t-174.5 -38.5q-191 0 -336 139q-141 138 -141 356q0 223 141 361
q144 141 336 141q93 0 172.5 -38t114.5 -101v118h276v-823q0 -300 -139.5 -455t-401.5 -155q-157 0 -283 80.5t-188 216.5zM657 1608l140 -72q-68 -96 -95 -180q-27 -86 -24 -221h-240q24 308 219 473z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1435" 
d="M967 836v575h303v-1411h-303v567h-502v-567h-299v1411h299v-575h502zM352 1567l223 344h283l225 -344h-204l-162 182l-160 -182h-205z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1185" 
d="M416 506v-506h-285v1477h285v-670q27 82 108 125q80 45 160 45q186 0 284.5 -118.5t98.5 -338.5v-520h-285v510q0 85 -52 137.5t-124 52.5q-83 0 -136.5 -49.5t-53.5 -144.5zM-94 1581l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1464" 
d="M180 0v1049h-147v165h147v197h299v-197h502v197h303v-197h148v-165h-148v-1049h-303v567h-502v-567h-299zM479 1049v-213h502v213h-502z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1185" 
d="M416 506v-506h-285v1141h-156v176h156v160h285v-160h303v-176h-303v-334q27 82 108 125q80 45 160 45q186 0 284.5 -118.5t98.5 -338.5v-520h-285v510q0 85 -52 137.5t-124 52.5q-83 0 -136.5 -49.5t-53.5 -144.5z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="630" 
d="M227 1696q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h153q-6 -127 -65.5 -194.5t-140.5 -67.5q-44 0 -103 41q-58 43 -76 43zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="542" 
d="M131 0v956h281v-956h-281zM205 1241q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-41 0 -100 41q-58 43 -78 43z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="630" 
d="M600 1657h-569v174h569v-174zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="542" 
d="M131 0v956h281v-956h-281zM553 1202h-569v174h569v-174z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="630" 
d="M41 1866h174q0 -109 100 -109q99 0 99 109h178q0 -121 -76.5 -195t-200.5 -74q-129 0 -201.5 77t-72.5 192zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="542" 
d="M412 956v-956h-281v956h281zM-4 1411h174q0 -108 100 -108q99 0 99 108h178q0 -121 -76.5 -194.5t-200.5 -73.5q-129 0 -201.5 76.5t-72.5 191.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="630" 
d="M463 1411v-1411h-156l-76 -207q-21 -65 31 -86q58 -15 80 39l2 53l158 8q3 -72 -17 -120q-26 -61 -79 -97t-115 -36q-93 0 -156 63q-63 60 -63 147q0 62 20 101l72 135v1411h299z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="544" 
d="M274 0l-75 -207q-21 -65 30 -86q58 -15 80 39q5 24 2 53l158 8q3 -74 -16 -120q-26 -61 -79.5 -97t-115.5 -36q-93 0 -156 63q-63 60 -63 147q0 65 20 101l72 135v956h283v-956h-140zM272 1477q72 0 123 -48.5t51 -117.5t-51 -118.5t-123 -49.5q-73 0 -125.5 49.5
t-52.5 118.5t52 117.5t126 48.5z" />
    <glyph glyph-name="Idot" unicode="&#x130;" horiz-adv-x="630" 
d="M158 1776q0 63 45.5 107t111.5 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="544" 
d="M131 956h283v-956h-283v956z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1263" 
d="M428 -449l27 271q71 -23 180 -23q79 0 120.5 53t41.5 142v1417h301v-1417q0 -213 -113 -344q-115 -133 -350 -133q-65 0 -207 34zM166 0v1411h299v-1411h-299z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1093" 
d="M600 -481h-174v258h125q128 0 125 151v1028h287v-1022q0 -207 -86.5 -311t-276.5 -104zM692 1192q-51 50 -51 119t51 116q54 49 127.5 49t124.5 -49q51 -47 51 -116t-51 -119t-124.5 -50t-127.5 50zM131 956h283v-956h-283v956zM272 1477q72 0 123 -48.5t51 -117.5
t-51 -118.5t-123 -49.5q-73 0 -125.5 49.5t-52.5 118.5t52 117.5t126 48.5z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="632" 
d="M-203 -449l27 271q71 -23 180 -23q79 0 120.5 53t41.5 142v1417h301v-1417q0 -213 -113 -344q-115 -133 -350 -133q-65 0 -207 34zM-49 1567l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="548" 
d="M55 -481h-174v258h125q128 0 125 151v1028h287v-1022q0 -207 -86.5 -311t-276.5 -104zM-66 1112l224 344h282l226 -344h-205l-162 182l-160 -182h-205z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1247" 
d="M1155 1411l-534 -698l661 -713h-389l-424 479v-479h-303v1411h303v-463l334 463h352zM590 -588l-139 72q67 93 94 180q27 86 24 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1077" 
d="M733 0l-319 379v-379h-283v1477h283v-783l227 262h363l-398 -421l477 -535h-350zM473 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1046" 
d="M1042 0h-370l-260 295v-295h-281v956h281v-278l233 278h354l-419 -471z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1046" 
d="M166 1411h301v-1147h528v-264h-829v1411zM170 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="546" 
d="M131 0v1477h285v-1477h-285zM133 1579l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1046" 
d="M995 0h-829v1411h301v-1147h528v-264zM479 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="546" 
d="M182 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473zM131 0v1477h285v-1477h-285z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1056" 
d="M995 0h-829v1411h301v-1147h528v-264zM778 877l-172 59q155 220 152 475h239q-13 -179 -75 -323.5t-144 -210.5z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="636" 
d="M131 0v1477h285v-1477h-285zM649 942l-172 59q155 220 152 476h239q-13 -179 -75 -324t-144 -211z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1046" 
d="M612 629q0 63 46 107t112 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-113 43.5t-45 110.5zM995 0h-829v1411h301v-1147h528v-264z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="778" 
d="M504 629q0 63 46 107t112 44t111.5 -44t45.5 -107q0 -67 -44.5 -110.5t-112.5 -43.5t-113 43.5t-45 110.5zM131 0v1477h285v-1477h-285z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1062" 
d="M182 0v596l-147 -92v194l147 93v620h301v-432l191 119v-195l-191 -119v-520h529v-264h-830z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="647" 
d="M207 1477h285v-523l176 111v-195l-176 -110v-760h-285v582l-178 -111v195l178 110v701z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1390" 
d="M569 1559l226 344h282l-303 -344h-205zM1225 0h-269l-491 854v-854h-299v1411h274l484 -844v844h301v-1411z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M416 506v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5v-571h-284v510q0 91 -45 140.5t-121 49.5q-176 0 -176 -194zM422 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1390" 
d="M924 1411h301v-1411h-269l-491 842v-842h-299v1411h274l484 -831v831zM623 -588l-140 72q68 96 95 180q27 86 24 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" 
d="M416 506v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5v-571h-284v510q0 91 -45 140.5t-121 49.5q-176 0 -176 -194zM520 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1390" 
d="M924 1411h301v-1411h-269l-491 854v-854h-299v1411h274l484 -844v844zM1061 1901l-225 -345h-283l-223 345h205l159 -183l162 183h205z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M416 506v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5v-571h-284v510q0 91 -45 140.5t-121 49.5q-176 0 -176 -194zM946 1446l-225 -344h-283l-223 344h205l160 -182l161 182h205z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" 
d="M416 506v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5v-571h-284v510q0 91 -45 140.5t-121 49.5q-176 0 -176 -194zM-33 961l-153 45q58 77 74.5 163.5t-3.5 184.5h242q3 -261 -160 -393z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1390" 
d="M940 33l-475 821v-854h-299v1411h274l484 -844v844h301v-1384q0 -216 -111 -351q-110 -137 -348 -137q-72 0 -209 35l31 295q89 -29 194 -29q71 4 114.5 60.5t43.5 132.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" 
d="M1042 571v-505q0 -213 -107.5 -336t-322.5 -123q-50 0 -129 20l27 262q56 -16 102 -16q72 0 109 52t37 136v449q0 91 -45 140.5t-121 49.5q-176 0 -176 -194v-506h-285v956h285v-139q38 72 114.5 116t161.5 44q168 0 259 -103.5t91 -302.5z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM1083 1657h-569v174h569v-174z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -63 155t-154 62t-153 -62t-62 -155q0 -97 61.5 -158t153.5 -61zM922 121q-143 -137 -353 -137q-209 0 -352 137t-143 360q0 222 143 359t352 137q210 0 353 -137q145 -139 145 -359q0 -221 -145 -360zM854 1202h-569v174h569v-174z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1599" 
d="M1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM524 1866h174q0 -109 101 -109q98 0 98 109h178q0 -121 -76.5 -195t-199.5 -74q-129 0 -202 77t-73 192z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1140" 
d="M569 262q93 0 155 61t62 158q0 93 -63 155t-154 62t-153 -62t-62 -155q0 -97 61.5 -158t153.5 -61zM217 121q-143 137 -143 360q0 222 143 359t352 137q210 0 353 -137q145 -139 145 -359q0 -221 -145 -360q-143 -137 -353 -137q-209 0 -352 137zM295 1411h174
q0 -108 100 -108q99 0 99 108h178q0 -121 -76.5 -194.5t-200.5 -73.5q-129 0 -201.5 76.5t-72.5 191.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1599" 
d="M549 1556l225 345h227l-247 -345h-205zM850 1556l252 345h233l-270 -345h-215zM1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301zM84 702q0 299 207 512q210 213 508 213
q301 0 508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210q-207 213 -207 512z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1140" 
d="M315 1102l226 344h227l-248 -344h-205zM616 1102l252 344h234l-271 -344h-215zM569 262q93 0 155 61t62 158q0 93 -63 155t-154 62t-153 -62t-62 -155q0 -97 61.5 -158t153.5 -61zM922 121q-143 -137 -353 -137q-209 0 -352 137t-143 360q0 222 143 359t352 137
q210 0 353 -137q145 -139 145 -359q0 -221 -145 -360z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2234" 
d="M1221 1270v141h893v-264h-594v-322h520v-260h-520v-301h612v-264h-911v137q-177 -157 -422 -157q-301 0 -508 210q-207 213 -207 512t207 512q210 213 508 213q254 0 422 -157zM1217 702q0 178 -123 304q-118 124 -295 124t-295 -124q-121 -127 -121 -304
q0 -176 121 -303q120 -123 295 -123q176 0 295 125q123 126 123 301z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1845" 
d="M928 129q-142 -145 -359 -145q-209 0 -352 137t-143 360q0 222 143 359t352 137q215 0 361 -146q143 146 362 146q207 0 348 -141q142 -139 142 -363q0 -22 -4 -88h-701q10 -75 78 -117.5t158 -42.5q137 0 209 90l207 -141q-132 -194 -437 -194q-221 0 -364 149zM569 262
q93 0 155 61t62 158q0 93 -63 155t-154 62t-153 -62t-62 -155q0 -97 61.5 -158t153.5 -61zM1081 563h414q-7 52 -37.5 90t-72.5 55.5t-91 17.5t-92 -17t-76.5 -55t-44.5 -91z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1304" 
d="M729 1147h-262v-397h254q79 0 128.5 59.5t49.5 138.5q0 78 -47.5 138.5t-122.5 60.5zM860 500l350 -500h-360l-315 487h-68v-487h-301v1411h577q228 0 347 -137q122 -134 122 -326q0 -166 -92.5 -289t-259.5 -159zM473 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="866" 
d="M416 334v-334h-285v956h285v-231q34 120 109 186t161 66q92 0 164 -35l-51 -256q-63 29 -146 29q-237 0 -237 -381zM147 1104l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1304" 
d="M729 1147h-262v-397h254q79 0 128.5 59.5t49.5 138.5q0 78 -47.5 138.5t-122.5 60.5zM860 500l350 -500h-360l-315 487h-68v-487h-301v1411h577q228 0 347 -137q122 -134 122 -326q0 -166 -92.5 -289t-259.5 -159zM586 -588l-140 72q68 96 95 180q27 86 24 221h240
q-24 -308 -219 -473z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="866" 
d="M416 334v-334h-285v956h285v-231q34 120 109 186t161 66q92 0 164 -35l-51 -256q-63 29 -146 29q-237 0 -237 -381zM178 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1304" 
d="M729 1147h-262v-397h254q79 0 128.5 59.5t49.5 138.5q0 78 -47.5 138.5t-122.5 60.5zM860 500l350 -500h-360l-315 487h-68v-487h-301v1411h577q228 0 347 -137q122 -134 122 -326q0 -166 -92.5 -289t-259.5 -159zM1010 1901l-226 -345h-282l-223 345h204l160 -183
l162 183h205z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="866" 
d="M416 334v-334h-285v956h285v-231q34 120 109 186t161 66q92 0 164 -35l-51 -256q-63 29 -146 29q-237 0 -237 -381zM815 1446l-225 -344h-283l-223 344h205l160 -182l161 182h205z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1191" 
d="M391 414q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 184 140 303q136 118 346 118q211 0 342 -116q135 -117 135 -316h-299q0 73 -48 116.5t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53
q396 -114 387 -426q-1 -129 -75 -228t-191 -143q-110 -43 -242 -43q-229 0 -371 121q-141 123 -141 316h301zM465 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="956" 
d="M379 393q-201 53 -254 154q-22 47 -28.5 79t-4.5 79q3 106 113 188q113 84 260 84q160 0 276 -78q114 -77 117 -240h-268q-3 84 -109 84q-47 0 -74.5 -19.5t-25.5 -51.5q3 -51 84 -72l162 -43q97 -27 163 -84t82 -108q15 -44 15 -86q0 -146 -125 -222q-124 -77 -289 -77
q-159 0 -278 88q-118 87 -121 241h266q2 -43 43.5 -76.5t97.5 -33.5q51 2 84 23.5t33 56.5q0 31 -21.5 49t-72.5 32zM346 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1191" 
d="M90 414h301q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 184 140 303q136 118 346 118q211 0 342 -116q135 -117 135 -316h-299q0 73 -48 116.5t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53
q396 -114 387 -426q-3 -134 -78 -231q-142 -183 -430 -183q-229 0 -371 121q-141 123 -141 316zM236 1567l223 344h282l226 -344h-205l-162 182l-160 -182h-204z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="956" 
d="M504 360l-125 33q-201 53 -254 154q-22 47 -28.5 79t-4.5 79q3 106 113 188q113 84 260 84q160 0 276 -78q114 -77 117 -240h-268q-3 84 -109 84q-47 0 -74.5 -19.5t-25.5 -51.5q3 -51 84 -72l162 -43q102 -26 168 -88q57 -55 74.5 -97t17.5 -93q0 -146 -125 -222
q-124 -77 -289 -77q-159 0 -278 88q-118 87 -121 241h266q2 -43 43.5 -76.5t97.5 -33.5q51 2 84 23.5t33 56.5q0 31 -21.5 49t-72.5 32zM115 1112l223 344h283l225 -344h-205l-162 182l-160 -182h-204z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1189" 
d="M545 -254q22 -54 80 -39q54 24 30 86l-67 184q-223 3 -363 125q-137 119 -137 312h301q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 184 140 303q136 118 346 118q211 0 342 -116q135 -117 135 -316h-299q0 73 -48 116.5
t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53q396 -114 387 -426q-3 -166 -111 -268q-110 -104 -270 -133l68 -125q20 -33 20 -101q0 -85 -65 -147q-63 -63 -154 -63q-62 0 -115.5 36t-79.5 97q-19 46 -16 120l158 -8q-3 -29 2 -53z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="958" 
d="M426 -254q22 -54 80 -39q55 24 31 86l-70 187q-156 3 -274 90q-116 88 -119 239h266q2 -43 43.5 -76.5t97.5 -33.5q51 2 84 23.5t33 56.5q0 31 -21.5 49t-72.5 32l-125 33q-201 53 -254 154q-22 47 -28.5 79t-4.5 79q3 106 113 188q113 84 260 84q160 0 276 -78
q114 -77 117 -240h-268q-3 84 -109 84q-47 0 -74.5 -19.5t-25.5 -51.5q3 -51 84 -72l162 -43q102 -26 168 -88q57 -55 74.5 -97t17.5 -93q0 -109 -77 -183.5t-204 -101.5l70 -129q20 -33 20 -101q0 -85 -65 -147q-63 -63 -154 -63q-62 0 -115 36t-79 97q-20 48 -17 120
l158 -8q-3 -29 2 -53z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1191" 
d="M90 414h301q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 184 140 303q136 118 346 118q211 0 342 -116q135 -117 135 -316h-299q0 73 -48 116.5t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53
q396 -114 387 -426q-3 -134 -78 -231q-142 -183 -430 -183q-229 0 -371 121q-141 123 -141 316zM967 1901l-226 -345h-282l-223 345h204l160 -183l162 183h205z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="956" 
d="M504 360l-125 33q-201 53 -254 154q-22 47 -28.5 79t-4.5 79q3 106 113 188q113 84 260 84q160 0 276 -78q114 -77 117 -240h-268q-3 84 -109 84q-47 0 -74.5 -19.5t-25.5 -51.5q3 -51 84 -72l162 -43q102 -26 168 -88q57 -55 74.5 -97t17.5 -93q0 -146 -125 -222
q-124 -77 -289 -77q-159 0 -278 88q-118 87 -121 241h266q2 -43 43.5 -76.5t97.5 -33.5q51 2 84 23.5t33 56.5q0 31 -21.5 49t-72.5 32zM846 1446l-225 -344h-283l-223 344h204l160 -182l162 182h205z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1179" 
d="M438 1141h-381v270h1065v-270h-381v-1141h-303v1141zM496 -588l-140 72q68 96 95 180q27 86 24 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="747" 
d="M504 1323v-367h201v-225h-201v-731h-283v731h-174v225h174v367h283zM266 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1179" 
d="M438 1141h-381v270h1065v-270h-381v-1141h-303v1141zM956 1901l-225 -345h-282l-224 345h205l160 -183l162 183h204z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="747" 
d="M504 1323v-367h201v-225h-201v-731h-283v731h-174v225h174v367h283zM731 1069l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1179" 
d="M438 1141h-381v270h1065v-270h-381v-1141h-303v1141zM874 1657h-569v174h569v-174z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="749" 
d="M504 0h-283v375h-108v157h108v199h-174v225h174v367h283v-367h201v-225h-201v-199h143v-157h-143v-375z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1396" 
d="M608 1696q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q48 0 103 -43q56 -41 75 -41t36 22t18 62h153q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41q-58 43 -76 43zM944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145
q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM508 1241q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41
q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-41 0 -100 41q-58 43 -78 43z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1396" 
d="M944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174zM983 1657h-569v174h569v-174z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM881 1202h-570v174h570v-174z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1396" 
d="M944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174zM422 1866h174q0 -109 100 -109q99 0 99 109h178q0 -121 -76.5 -195t-200.5 -74q-129 0 -201.5 77
t-72.5 192z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5zM322 1411h174q0 -108 100 -108q98 0 98 108h178q0 -121 -76 -194.5t-200 -73.5q-129 0 -201.5 76.5
t-72.5 191.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1396" 
d="M451 1804q0 107 71 178q71 74 174 74q105 0 176 -74q74 -71 74 -178q0 -103 -74 -174q-71 -71 -176 -71q-103 0 -174 71t-71 174zM584 1804q0 -47 32.5 -79.5t79.5 -32.5q49 0 83 32.5t34 79.5q0 51 -33.5 85t-83.5 34q-47 0 -79.5 -34t-32.5 -85zM944 504v907h301v-907
q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M348 1350q0 106 72 178q71 74 174 74q105 0 176 -74q74 -71 74 -178q0 -103 -74 -174q-72 -72 -176 -72q-102 0 -174 72t-72 174zM481 1350q0 -47 33 -80t80 -33q49 0 83 33t34 80q0 51 -33.5 84.5t-83.5 33.5q-48 0 -80.5 -33.5t-32.5 -84.5zM748 451v505h284v-956h-284
v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1396" 
d="M379 1556l225 345h227l-247 -345h-205zM680 1556l252 345h233l-270 -345h-215zM944 504v907h301v-907q0 -234 -160 -379t-385 -145q-222 0 -387 145q-161 142 -161 379v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M281 1102l225 344h227l-248 -344h-204zM582 1102l252 344h233l-270 -344h-215zM748 451v505h284v-956h-284v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1400" 
d="M756 -18l-70 -189q-21 -65 31 -86q58 -15 80 39l2 53l157 8q3 -74 -16 -120q-26 -61 -79.5 -97t-115.5 -36q-92 0 -155 63q-64 61 -64 147q0 58 21 101l65 123q-191 26 -327 168q-131 137 -131 348v907h305v-907q0 -106 71 -174q74 -68 172 -68q99 0 170 68q74 71 74 174
v907h301v-907q0 -220 -143 -363q-141 -141 -348 -159z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M674 -135l74 135v131q-36 -68 -105.5 -109.5t-142.5 -41.5q-171 0 -275 106.5t-104 298.5v571h285v-510q0 -96 49.5 -143t119.5 -47q77 0 125 49.5t48 145.5v505h284v-956h-143l-76 -207q-21 -65 31 -86q58 -15 80 39l2 53l157 8q3 -74 -16 -120q-26 -61 -79.5 -97
t-115.5 -36q-92 0 -155 63q-64 61 -64 147q0 58 21 101z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1814" 
d="M1030 1212l187 -690l276 889h326l-480 -1411h-235l-199 741l-194 -741h-238l-475 1411h324l274 -889l188 690h246zM543 1567l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1562" 
d="M1239 956h303l-336 -956h-237l-189 608l-180 -608h-238l-346 956h303l170 -569l168 569h244l168 -569zM414 1112l223 344h283l225 -344h-205l-162 182l-160 -182h-204z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1333" 
d="M819 635v-635h-305v639l-545 772h357l340 -528l344 528h356zM301 1567l223 344h283l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1044" 
d="M733 956h303l-590 -1425h-301l230 567l-369 858h303l211 -542zM162 1112l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1333" 
d="M819 635v-635h-305v639l-545 772h357l340 -528l344 528h356zM338 1761q0 57 44 98.5t105 41.5q60 0 103 -41.5t43 -98.5q0 -60 -43 -100.5t-103 -40.5q-62 0 -105.5 40.5t-43.5 100.5zM700 1761q0 57 44.5 98.5t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5
q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1218" 
d="M86 0v227l645 920h-618v264h1007v-227l-651 -920h651v-264h-1034zM461 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1044" 
d="M946 956v-213l-459 -481h459v-262h-862v211l451 483h-422v262h833zM383 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1218" 
d="M86 0v227l645 920h-618v264h1007v-227l-651 -920h651v-264h-1034zM444 1776q0 63 46 107t112 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-113 43.5t-45 110.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1044" 
d="M946 956v-213l-459 -481h459v-262h-862v211l451 483h-422v262h833zM356 1321q0 63 46 107.5t112 44.5t112 -44.5t46 -107.5q0 -67 -45 -110.5t-113 -43.5t-113 43.5t-45 110.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1218" 
d="M86 0v227l645 920h-618v264h1007v-227l-651 -920h651v-264h-1034zM969 1901l-226 -345h-282l-223 345h204l160 -183l162 183h205z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1044" 
d="M113 694v262h833v-213l-459 -481h459v-262h-862v211l451 483h-422zM881 1446l-226 -344h-282l-223 344h204l160 -182l162 182h205z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="686" 
d="M659 1477h105v-273h-105q-145 0 -145 -182v-1022h-287v731h-180v225h180v66q0 219 112.5 337t319.5 118z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="1284" 
d="M166 -393h-105l56 272h104q142 0 180 182l136 670h-181l45 225h181l14 66q43 219 178.5 337t343.5 118h105l-54 -273h-104q-144 0 -182 -182l-15 -66h193l-45 -225h-193l-149 -747q-37 -175 -178 -277q-135 -100 -330 -100z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1349" 
d="M674 1085l-144 -491h293zM1044 0l-124 334h-488l-125 -334h-319l524 1411h326l522 -1411h-316zM604 2028l166 251h283l-246 -278q119 -73 119 -215q0 -103 -74 -174q-72 -72 -176 -72q-102 0 -174 72t-72 174q0 84 49 151t125 91zM563 1786q0 -47 33 -80t80 -33
q49 0 83 33t34 80q0 51 -33.5 85t-83.5 34q-48 0 -80.5 -34t-32.5 -85z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1253" 
d="M1122 0h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-956zM555 1591l166 252h283l-246 -278q119 -73 119 -215q0 -103 -74 -174q-72 -72 -176 -72q-102 0 -174 72
t-72 174q0 84 49 150.5t125 90.5zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162q0 -99 71 -164q72 -63 168 -63zM514 1350q0 -47 33 -80t80 -33q49 0 82.5 33t33.5 80q0 51 -33 84.5t-83 33.5q-48 0 -80.5 -33.5t-32.5 -84.5z
" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1912" 
d="M1792 1411v-264h-594v-322h520v-260h-520v-301h612v-264h-911v2l-2 -2v324h-377l-205 -324h-358l891 1411h944zM690 612h207v398zM913 1559l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1986" 
d="M1663 315l207 -141q-132 -194 -436 -194q-177 0 -312 102v-82h-282v109q-39 -60 -127 -95q-77 -32 -152 -32q-194 0 -340 137q-147 138 -147 360q0 221 147 359q148 139 340 139q77 0 155 -31.5t124 -95.5v106h282v-79q132 100 312 100q210 0 348 -141q141 -138 141 -363
q0 -22 -4 -88h-700q10 -75 77.5 -117.5t157.5 -42.5q137 0 209 90zM608 252q95 0 163.5 63.5t68.5 163.5t-68.5 164t-163.5 64q-93 0 -168 -66q-71 -65 -71 -162q0 -99 71 -164q72 -63 168 -63zM1223 563h413q-7 52 -37.5 90t-72.5 55.5t-90.5 17.5t-91.5 -17t-76.5 -55
t-44.5 -91zM883 1104l225 344h283l-304 -344h-204z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1595" 
d="M700 1559l226 344h282l-303 -344h-205zM348 135l-164 -168l-96 96l164 168q-170 203 -170 471q0 299 207 512q210 213 508 213q264 0 456 -165l162 165l96 -94l-163 -168q165 -201 165 -463q0 -301 -208 -512q-207 -210 -508 -210q-252 0 -447 157zM381 702
q0 -141 80 -256l583 601q-109 83 -247 83q-177 0 -295 -124q-121 -127 -121 -304zM797 276q176 0 295 125q122 125 122 301q0 134 -77 246l-580 -596q104 -76 240 -76z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1140" 
d="M418 1104l225 344h283l-303 -344h-205zM569 -16q-161 0 -288 86l-119 -121l-96 96l114 119q-106 127 -106 317q0 222 143 359t352 137q169 0 308 -103l106 111l96 -94l-108 -111q96 -131 96 -299q0 -221 -145 -360q-143 -137 -353 -137zM569 698q-91 0 -153 -62t-62 -155
q0 -63 27 -110l293 299q-54 28 -105 28zM569 262q93 0 155 61t62 158q0 43 -18 90l-285 -292q39 -17 86 -17z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1191" 
d="M90 414h301q5 -74 61 -122t150 -48q92 0 149 41t58 108q2 56 -36 94t-134 66l-170 47q-365 114 -365 406q0 184 140 303q136 118 346 118q211 0 342 -116q135 -117 135 -316h-299q0 73 -48 116.5t-134 43.5q-80 0 -131.5 -40t-51.5 -101q0 -100 152 -144l168 -53
q396 -114 387 -426q-3 -134 -78 -231q-142 -183 -430 -183q-229 0 -371 121q-141 123 -141 316zM498 -588l-140 72q68 96 95 180q27 86 24 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="956" 
d="M504 360l-125 33q-201 53 -254 154q-22 47 -28.5 79t-4.5 79q3 106 113 188q113 84 260 84q160 0 276 -78q114 -77 117 -240h-268q-3 84 -109 84q-47 0 -74.5 -19.5t-25.5 -51.5q3 -51 84 -72l162 -43q102 -26 168 -88q57 -55 74.5 -97t17.5 -93q0 -146 -125 -222
q-124 -77 -289 -77q-159 0 -278 88q-118 87 -121 241h266q2 -43 43.5 -76.5t97.5 -33.5q51 2 84 23.5t33 56.5q0 31 -21.5 49t-72.5 32zM399 -588l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1161" 
d="M215 1112l223 344h283l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1161" 
d="M946 1446l-225 -344h-283l-223 344h205l160 -182l161 182h205z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1042" 
d="M246 1411h174q0 -108 100 -108q98 0 98 108h179q0 -121 -76.5 -194.5t-200.5 -73.5q-129 0 -201.5 76.5t-72.5 191.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="790" 
d="M238 1321q0 63 45.5 107.5t111.5 44.5t112 -44.5t46 -107.5q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="1150" 
d="M328 1350q0 107 71 178q71 74 174 74q106 0 177 -74q73 -70 73 -178q0 -104 -73 -174q-72 -72 -177 -72q-102 0 -174 72q-71 71 -71 174zM461 1350q0 -47 32.5 -80t79.5 -33q49 0 83 33t34 80q0 51 -33.5 84.5t-83.5 33.5q-48 0 -80 -33.5t-32 -84.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1087" 
d="M598 -254l2 53l158 8q3 -72 -17 -120q-26 -61 -79 -97t-115 -36q-93 0 -156 63q-63 60 -63 147q0 60 20 101l103 190h133l-97 -262q-24 -62 31 -86q58 -15 80 39z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="919" 
d="M367 1241q-19 0 -36 -24t-18 -64h-153q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-44 0 -103 41q-58 43 -75 43z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1114" 
d="M164 1102l225 344h227l-247 -344h-205zM465 1102l252 344h233l-270 -344h-215z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1814" 
d="M1030 1212l187 -690l276 889h326l-480 -1411h-235l-199 741l-194 -741h-238l-475 1411h324l274 -889l188 690h246zM834 1559l-304 344h283l225 -344h-204z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1562" 
d="M1239 956h303l-336 -956h-237l-189 608l-180 -608h-238l-346 956h303l170 -569l168 569h244l168 -569zM688 1104l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1814" 
d="M1493 1411h326l-480 -1411h-235l-199 741l-194 -741h-238l-475 1411h324l274 -889l188 690h246l187 -690zM774 1559l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1562" 
d="M1206 0h-237l-189 608l-180 -608h-238l-346 956h303l170 -569l168 569h244l168 -569l170 569h303zM653 1104l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1814" 
d="M1493 1411h326l-480 -1411h-235l-199 741l-194 -741h-238l-475 1411h324l274 -889l188 690h246l187 -690zM580 1761q0 57 44 98.5t105 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -105.5 40.5t-43.5 100.5zM942 1761q0 57 44.5 98.5
t105.5 41.5q60 0 102.5 -41.5t42.5 -98.5q0 -60 -42.5 -100.5t-102.5 -40.5q-62 0 -106 41t-44 100z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1562" 
d="M1206 0h-237l-189 608l-180 -608h-238l-346 956h303l170 -569l168 569h244l168 -569l170 569h303zM451 1307q0 57 44 98t105 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -105.5 41t-43.5 101zM813 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98
q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1333" 
d="M819 635v-635h-305v639l-545 772h357l340 -528l344 528h356zM614 1559l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1044" 
d="M733 956h303l-590 -1425h-301l230 567l-369 858h303l211 -542zM512 1104l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="28" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1298" 
d="M1176 354h-1053v254h1053v-254z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1734" 
d="M1612 354h-1489v254h1489v-254z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="575" 
d="M414 1427l110 -63q-22 -30 -69 -100q-15 -22 -56 -103q-67 -134 -67 -330h-234q0 174 107 345q108 172 209 251z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="557" 
d="M180 831l-110 64q22 30 69 100q15 22 56 103q67 134 67 329h234q0 -170 -109 -344q-111 -177 -207 -252z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="524" 
d="M86 -254l-111 64q32 43 70 100q13 19 55 102q68 136 68 330h233q0 -172 -108 -344q-111 -177 -207 -252z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="925" 
d="M764 1427l110 -63q-22 -30 -69 -100q-10 -16 -55 -103q-68 -136 -68 -330h-233q0 176 106 345q108 172 209 251zM414 1427l110 -63q-22 -30 -69 -100q-15 -22 -56 -103q-67 -134 -67 -330h-234q0 174 107 345q108 172 209 251z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="907" 
d="M180 831l-110 64q22 30 69 100q15 22 56 103q67 134 67 329h234q0 -170 -109 -344q-111 -177 -207 -252zM530 831l-110 64q22 30 69 100q15 22 56 103q67 134 67 329h234q0 -170 -109 -344q-111 -177 -207 -252z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="874" 
d="M86 -254l-111 64q32 43 70 100q13 19 55 102q68 136 68 330h233q0 -172 -108 -344q-111 -177 -207 -252zM436 -254l-110 64q22 30 69 100q17 25 56 102q67 134 67 330h234q0 -170 -109 -344q-111 -177 -207 -252z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="839" 
d="M293 1411h254v-334h215v-200h-215v-1067h-254v1067h-215v200h215v334z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="905" 
d="M326 1411h254v-334h215v-200h-215v-410h215v-199h-215v-458h-254v458h-215v199h215v410h-215v200h215v334z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="999" 
d="M268 262q-96 90 -96 225q0 136 96 226t232 90q135 0 231 -90t96 -226q0 -135 -96 -225t-231 -90q-136 0 -232 90z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1771" 
d="M463 152q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5t125 48.5t126 -49t52 -121zM1063 152q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5t125 48.5t126 -49t52 -121zM1663 152q0 -73 -52 -122.5t-126 -49.5t-125 49.5t-51 122.5t51 121.5
t125 48.5t126 -49t52 -121z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2074" 
d="M1106 1341l-660 -1341h-141l660 1341h141zM154 887q-78 81 -78 194q0 114 78 195q80 80 194 80q115 0 195 -80t80 -195q0 -114 -80 -194q-81 -78 -195 -78q-113 0 -194 78zM272 1157q-31 -31 -31 -75.5t31 -75.5t76 -31t76 31q32 31 32 75.5t-32 75.5q-31 32 -76 32
t-76 -32zM1348 252q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q80 -80 80 -192zM967 252q0 -47 30.5 -77t75.5 -30q44 0 76.5 31.5t32.5 75.5t-32.5 76t-76.5 32t-75 -32t-31 -76zM1731 526q113 0 192 -82
q82 -79 82 -192q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195q0 113 78 194q80 80 195 80zM1624 252q0 -47 30 -77t77 -30q34 0 61 19.5t39 47t6.5 60t-31.5 56.5q-24 26 -56 31.5t-59.5 -6.5t-47 -39.5t-19.5 -61.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="671" 
d="M362 53l-282 424l282 424h222l-283 -424l283 -424h-222z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="671" 
d="M309 901l283 -424l-283 -424h-221l283 424l-283 424h221z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="368" 
d="M557 1368h168l-924 -1388h-170z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="718" 
d="M70 1278q0 403 290 403q289 0 289 -403q0 -401 -289 -401q-290 0 -290 401zM233 1278q0 -242 127 -242q125 0 125 242q0 244 -125 244q-127 0 -127 -244z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="700" 
d="M649 1063h-98v-174h-166v174h-373l367 604h172v-465h98v-139zM385 1446l-147 -244h147v244z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="700" 
d="M119 1077l69 45q52 -82 136 -82q67 0 111 35.5t44 87.5q0 60 -39 93.5t-104 33.5q-115 0 -172 -84l-90 47l63 414h455v-151h-324l-22 -130q47 41 143 41q113 0 183.5 -71t70.5 -193q0 -128 -87 -207t-232 -79q-79 0 -154.5 43.5t-120.5 113.5q3 0 70 43z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="694" 
d="M299 1210q-29 -31 -29 -73.5t29 -73.5q31 -29 73.5 -29t73.5 29q31 31 31 73.5t-31 73.5t-73.5 31t-73.5 -31zM639 1532l-154 -66q-27 62 -90 62q-76 0 -112 -63.5t-31 -161.5q11 32 56.5 60t99.5 28q98 0 168.5 -71.5t70.5 -191.5q0 -112 -78.5 -181.5t-203.5 -69.5
q-113 0 -190 74t-87 196q-15 162 12 278q59 265 301 256q174 -9 238 -149z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="663" 
d="M55 1667h586l-346 -778h-186l276 627h-330v151z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="708" 
d="M295 1511q-20 -20 -20 -50.5t20 -53.5q23 -23 60 -23t57 23q23 23 23 53.5t-23 50.5q-20 23 -57 23t-60 -23zM223 1149q0 -49 38.5 -84t92.5 -35t92.5 35t38.5 84q0 45 -35.5 80t-82.5 35h-27q-48 0 -82.5 -35t-34.5 -80zM115 1464q0 91 69 154t170 63q99 0 168 -63
q72 -66 72 -154q0 -84 -57 -129q112 -66 112 -190q0 -112 -88 -191q-86 -77 -207 -77q-120 0 -209 77q-86 80 -86 191q0 123 113 190q-57 51 -57 129z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="694" 
d="M408 1348q31 31 31 73.5t-31 73.5q-29 31 -72 31t-74 -31t-31 -73.5t31 -73.5q31 -29 74 -29t72 29zM70 1028l153 66q27 -64 90 -64q76 0 112.5 64.5t31.5 162.5q-12 -32 -56 -60t-100 -28q-99 0 -169.5 71.5t-70.5 191.5q0 112 77.5 180.5t205.5 68.5q113 0 191 -74
t88 -196q15 -205 -41 -360q-32 -84 -103 -131t-172 -43q-170 9 -237 151z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="716" 
d="M70 82q0 403 290 403q289 0 289 -403q0 -401 -289 -401q-290 0 -290 401zM233 82q0 -242 127 -242q125 0 125 242q0 244 -125 244q-127 0 -127 -244z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="440" 
d="M176 -307v559l-133 -55v143l297 141v-788h-164z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="655" 
d="M63 -307v88l320 379q41 47 41 90q0 33 -27.5 55.5t-70.5 22.5t-74 -30t-37 -79l-152 27q19 111 91 175t182 64q105 0 178 -67q76 -64 76 -168q0 -106 -82 -197l-180 -205h241v-155h-506z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="649" 
d="M213 289l-131 82q36 56 100.5 87t141.5 27q100 -6 166.5 -69t62.5 -148q0 -99 -100 -153q62 -23 100.5 -85t32.5 -126q-9 -98 -99 -164t-206 -57q-80 6 -148 51t-100 112l143 72q39 -67 117 -76q42 -1 75 24t38 58q0 42 -7 60q-21 50 -100 47h-82v141h82q78 0 78 86
q0 28 -22.5 52t-57.5 24q-55 0 -84 -45z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="704" 
d="M653 -133h-98v-174h-166v174h-373l367 604h172v-465h98v-139zM389 250l-147 -244h147v244z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="694" 
d="M186 -74q52 -82 136 -82q67 0 111 35.5t44 87.5q0 60 -39 93.5t-104 33.5q-115 0 -172 -84l-90 47l63 414h455v-152h-324l-22 -129q47 41 143 41q113 0 183.5 -71t70.5 -193q0 -128 -87 -207t-232 -79q-79 0 -154.5 43.5t-120.5 113.5q5 0 70 45q67 43 69 43z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="694" 
d="M299 14q-29 -31 -29 -73.5t29 -71.5q31 -31 73.5 -31t73.5 31q31 29 31 71.5t-31 73.5t-73.5 31t-73.5 -31zM639 336l-154 -66q-27 62 -90 62q-76 0 -112 -64t-31 -162q12 32 57 60.5t99 28.5q98 0 168.5 -71.5t70.5 -191.5q0 -112 -78.5 -181.5t-203.5 -69.5
q-113 0 -190 74t-87 196q-15 162 12 278q59 265 301 256q174 -9 238 -149z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="636" 
d="M47 471h586l-346 -778h-187l277 626h-330v152z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="708" 
d="M295 315q-20 -20 -20 -50.5t20 -53.5q23 -23 60 -23t57 23q23 23 23 53.5t-23 50.5q-20 23 -57 23t-60 -23zM223 -47q0 -49 38.5 -84t92.5 -35t92.5 35t38.5 84q0 45 -35.5 80t-82.5 35h-27q-48 0 -82.5 -35t-34.5 -80zM115 268q0 91 69 154t170 63q99 0 168 -63
q72 -66 72 -154q0 -84 -57 -129q112 -66 112 -190q0 -113 -88 -189q-85 -79 -207 -79q-121 0 -209 79q-86 77 -86 189q0 123 113 190q-57 51 -57 129z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="694" 
d="M408 152q31 31 31 73.5t-31 73.5q-29 31 -72 31t-74 -31t-31 -73.5t31 -73.5q31 -29 74 -29t72 29zM70 -168l153 66q27 -64 90 -64q76 0 112.5 64.5t31.5 162.5q-12 -32 -56 -60t-100 -28q-99 0 -169.5 71.5t-70.5 191.5q0 112 77.5 180.5t205.5 68.5q113 0 191 -74
t88 -196q16 -208 -41 -358q-31 -87 -102 -133.5t-173 -42.5q-170 9 -237 151z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="1454" 
d="M682 -12l-76 -217l-119 43l72 202q-65 19 -127 56l-82 -230l-119 43l95 265q-118 100 -183 244.5t-65 307.5q-4 193 89 360.5t257 267t356 97.5l80 222l119 -45l-68 -187q67 -11 136 -37l69 197l119 -45l-74 -205q132 -78 213 -199l-244 -167q-35 50 -71 79l-279 -778h6
q106 0 199 52.5t154 142.5l241 -168q-95 -144 -251 -226.5t-343 -82.5q-63 0 -104 8zM815 1143l-283 -789q52 -47 121 -69l297 825q-70 30 -135 33zM379 702q0 -132 59 -235l236 661q-133 -38 -214 -155t-81 -271z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="1398" 
d="M829 467h-235v-213h377q26 0 43.5 21.5t17.5 52.5v59h281v-115q0 -118 -76 -194q-75 -78 -182 -78h-965v254h217v213h-170v129h170v96h-170v129h170v148q0 204 121 331q124 127 315 127q202 0 334 -120q131 -119 131 -351h-297q0 168 -161 168q-70 0 -113 -42t-43 -113
v-148h235v-129h-235v-96h235v-129z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="1601" 
d="M268 0v434h-155v139h155v322h-155v139h155v377h275l215 -377h268v377h301v-377h166v-139h-166v-322h166v-139h-166v-434h-268l-250 434h-242v-434h-299zM1026 895h-188l184 -322h4v322zM729 573l-162 281v-281h162z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="1546" 
d="M989 897h-436v-147h264q66 0 112 41t60 106zM553 1147v-129h432q-20 58 -61 93.5t-99 35.5h-272zM1475 1018v-121h-181q-15 -176 -135 -293q-117 -117 -328 -117h-278v-487h-301v897h-160v121h160v393h579q206 0 322 -113q119 -116 139 -280h183z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1468" 
d="M770 911h-645v121h633q-47 115 -154 115h-479l-2 264h1255v-121h-430q110 -104 131 -258h299v-121h-293q-11 -157 -102 -268.5t-248 -142.5l350 -500h-360l-315 487h-285v263h471q69 0 115 45t59 116z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="2062" 
d="M598 0l-106 317h-365v140h317l-104 309h-213v139h166l-170 506h323l156 -506h223l84 307h246l82 -307h223l158 506h326l-172 -506h161v-139h-209l-106 -309h315v-140h-362l-107 -317h-235l-86 317h-225l-82 -317h-238zM1106 457l-76 284l-76 -284h152zM786 766h-141
l76 -244zM1417 766h-141l65 -244z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="1951" 
d="M1348 680v-244h-287v191q0 81 -58 134.5t-151 53.5h-389v-815h-287v1102h739q191 0 312 -121t121 -301zM604 422v244h287v-191q0 -81 58 -134.5t151 -53.5h389v815h287v-1102h-740q-192 0 -313 121q-119 119 -119 301z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1697" 
d="M633 891h471v-129h-1010v129h223q57 233 248 385q189 151 439 151q182 0 337 -80.5t250 -218.5l-243 -167q-126 184 -344 184q-126 0 -224.5 -68.5t-146.5 -185.5zM1104 653v-129h-475q45 -119 146 -190.5t229 -71.5q106 0 198 52.5t154 142.5l241 -168
q-95 -144 -250.5 -226.5t-342.5 -82.5q-251 0 -443 151q-187 153 -246 393h-221v129h1010z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="821" 
d="M375 1067v-492l55 7q47 0 47 77v410q0 119 -47 119q-20 -4 -31.5 -12.5t-16.5 -27.5t-6 -34t-1 -47zM479 231l201 -14v-217h-252q-305 0 -305 348v731q0 348 305 348q301 0 301 -362v-381q0 -169 -74 -258q-73 -88 -280 -88q0 -22 3 -37.5t12.5 -32.5t32 -26.5
t56.5 -10.5z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="2015" 
d="M1235 0h-268l-492 854v-854h-299v1411h275l483 -844v844h301v-1411zM1649 1427q114 0 192 -81q82 -79 82 -193q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195t78 195q79 79 195 79zM1530 1153q0 -51 34 -85t85 -34q38 0 67.5 21.5t43 52t7.5 66.5
t-34 63q-27 28 -63 34t-66.5 -7.5t-52 -43t-21.5 -67.5zM1909 651h-520v137h520v-137z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1742" 
d="M858 666h-170l74 591h-221v-591h-172v591h-242v154h795l227 -463l221 463h148l90 -745h-172l-52 454l-174 -370h-123l-174 370z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="1323" 
d="M1237 571h-879v-395q135 -129 312 -129q148 0 245.5 63.5t184.5 196.5l63 -39q-64 -98 -129.5 -158.5t-156.5 -95t-207 -34.5q-244 0 -400 172q-155 174 -155 421q0 253 153 422q153 172 410 172q256 0 405 -174q154 -174 154 -422zM358 639h633v334q-139 127 -313 127
q-196 0 -320 -127v-334z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1034" 
d="M895 639v-209h-756v209h756z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1232" 
d="M1085 305h-542l-105 -215h-143l104 215h-256v201h355l102 211h-457v201h555l97 196h145l-96 -196h241v-201h-340l-104 -211h444v-201z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1308" 
d="M1155 285l-1028 393v229l1028 393l2 -225l-741 -282l741 -283zM137 176h1018v-176h-1018v176z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1308" 
d="M154 1300l1028 -393v-229l-1028 -393l-2 225l741 283l-741 282zM1171 0h-1017v176h1017v-176z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="645" 
d="M283 -588l-140 72q68 96 95 180q27 86 24 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="2439" 
d="M705 1147h-263v-397h254q79 0 128.5 59.5t49.5 138.5q0 78 -47 138.5t-122 60.5zM836 500l350 -500h-361l-315 487h-68v-487h-301v1411h578q227 0 346 -137q123 -135 123 -326q0 -166 -92.5 -289t-259.5 -159zM1632 475q0 -102 63.5 -164.5t157.5 -62.5q93 0 157.5 64
t64.5 163q0 101 -64 166.5t-158 65.5q-93 0 -157 -66.5t-64 -165.5zM1343 -469v1425h283v-118q35 65 111 102t170 37q189 0 325 -139q138 -138 138 -357q0 -224 -138 -362q-136 -139 -325 -139q-93 0 -169.5 37t-111.5 100v-586h-283z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1734" 
d="M164 1407h1407v-1407h-1407v1407zM872 680l-536 539v-773h166l-152 154v365l522 -519l525 519v-365l-152 -154h172v773zM395 197l-28 73h-31v-108h18v76l27 -76h20l23 76v-76h18v108h-28zM508 270q-51 0 -51 -55q0 -24 13.5 -38.5t37.5 -14.5q22 0 34.5 15.5t12.5 37.5
q0 24 -12.5 39.5t-34.5 15.5zM508 182q-13 0 -24 11t-11 22q0 12 11 23.5t24 11.5q24 0 24 -35q0 -33 -24 -33zM582 203h-27q0 -41 45 -41t45 35q0 22 -31 28q-32 6 -32 13q0 12 18 12q21 0 21 -12h18q0 32 -39 32t-39 -32q0 -17 33 -31q27 0 27 -10q0 -15 -21 -15
q-18 0 -18 21zM645 270v-20h35v-88h16v88h31v20h-82zM815 162l-43 108h-18l-45 -108h24l8 26h45l7 -26h22zM760 244h6l14 -37h-32zM872 270h-57v-108h19v45h26q19 0 19 -19v-26h26q-6 6 -6 26q0 19 -14 27q14 6 14 23q0 32 -27 32zM860 221h-26v29h26q19 0 19 -12
q0 -17 -19 -17zM954 270h-41v-108h41q52 0 52 59q0 49 -52 49zM954 182h-16v68h8q35 0 35 -35q0 -12 -8.5 -22.5t-18.5 -10.5zM1087 250v20h-75v-108h82v20h-58v25h51v18h-51v25h51zM1112 203h-18q0 -41 45 -41t45 35q0 22 -31 28q-35 6 -35 13q0 12 15 12q24 0 24 -12h21
q0 32 -39 32q-11 0 -23 -5.5t-19.5 -13.5t1 -20t35.5 -24q24 0 24 -10q0 -15 -18 -15q-27 0 -27 21zM1206 162v108h-14v-108h14zM1296 176l9 -14h12v59h-45v-18h24q-9 -21 -24 -21q-13 0 -23 11t-10 22q0 13 10 24t23 11q18 0 18 -21h27q-9 41 -45 41q-23 0 -37 -15.5
t-14 -39.5q0 -21 14.5 -37t36.5 -16q18 0 24 14zM1391 203l-45 67h-17v-108h17v67l45 -67h26v108h-26v-67z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="CR" horiz-adv-x="681" 
 />
    <glyph glyph-name="published" horiz-adv-x="1632" 
d="M754 877v-166h112q28 0 47.5 17t24 41.5t0.5 49t-22.5 41.5t-45.5 17h-116zM582 1030h290q113 0 176.5 -66.5t63.5 -168.5q0 -101 -63.5 -168.5t-176.5 -67.5h-118v-217h-172v688zM307 190q-207 213 -207 512t207 512q210 213 508 213t508 -213q209 -212 209 -512
t-209 -512q-207 -210 -508 -210t-508 210zM422 1100q-162 -162 -162 -398q0 -235 162 -397q164 -164 393 -164t393 164q162 162 162 397q0 236 -162 398q-163 166 -393 166t-393 -166z" />
    <glyph glyph-name="frenchfranc" horiz-adv-x="1527" 
d="M1022 0h-270v612h-267v-612h-301v1411h883v-264h-582v-281h537v-221q34 124 101 188t149 64q112 0 184 -35l-51 -260q-62 33 -145 33q-238 0 -238 -381v-254z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="794" 
d="M729 1657v-223q-103 0 -197 -105q-95 -106 -153 -276q-60 -177 -60 -357q0 -274 127 -501q130 -222 283 -222v-221q-277 0 -465 264q-184 266 -184 680t184 686q189 275 465 275z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="794" 
d="M66 -248v221q153 0 280 222q129 225 129 501q0 183 -59 357q-60 173 -155.5 277t-194.5 104v223q276 0 462 -275q187 -272 187 -686t-187 -680q-184 -264 -462 -264z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="972" 
d="M848 575h-723v271h723v-271z" />
    <glyph glyph-name="at.case" horiz-adv-x="1974" 
d="M1194 444v258q0 100 -70 170t-168 70t-168 -70q-69 -69 -69 -170q0 -96 69 -165q70 -70 168 -70q94 0 162 51v-192q-85 -39 -162 -39q-173 0 -297 121q-122 119 -122 294q0 178 122 297q123 123 297 123q171 0 291 -114q125 -113 131 -285v-291q0 -63 34 -93t89 -30
q62 0 107.5 41.5t66.5 110.5q41 122 41 268q0 292 -217 485q-218 197 -522 197q-295 0 -500 -211q-203 -209 -203 -508q0 -298 203 -504q204 -204 500 -204h67v-183h-67q-374 0 -629 258q-256 259 -256 633q0 375 256 637q258 264 629 264q144 4 284 -38t255.5 -122.5
t203 -187t135.5 -240.5t46 -276q0 -77 -12.5 -156t-43 -160t-75 -143t-114.5 -101.5t-155 -39.5q-138 0 -222.5 79t-84.5 236z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="692" 
d="M623 -238h-473v1880h473v-206h-232v-1463h232v-211z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="692" 
d="M70 1642h473v-1880h-473v211h231v1463h-231v206z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="843" 
d="M63 614v172q51 0 88 33.5t37 85.5v326q-3 181 117 295t297 114h174v-206h-180q-69 0 -118.5 -52t-49.5 -125v-335q0 -82 -48.5 -142.5t-113.5 -79.5q65 -19 113.5 -79.5t48.5 -141.5v-313q0 -75 50.5 -133t117.5 -58h180v-215h-174q-176 0 -297 115q-120 114 -117 295
v326q0 52 -37 85t-88 33z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="843" 
d="M780 786v-172q-51 0 -88 -33t-37 -85v-326q3 -180 -118 -295t-295 -115h-174v215h180q67 0 117.5 58t50.5 133v313q0 81 48.5 141.5t113.5 79.5q-65 19 -113.5 79.5t-48.5 142.5v335q0 73 -49.5 125t-118.5 52h-180v206h174q175 0 295 -114q121 -115 118 -295v-326
q0 -52 37 -85.5t88 -33.5z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="581" 
d="M408 977l41 -997h-316l41 997h234zM467 1255q0 -72 -52 -121t-126 -49t-125 48.5t-51 121.5t51 122.5t125 49.5t126 -49.5t52 -122.5z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="1079" 
d="M381 283l-283 424l283 423h221l-283 -423l283 -424h-221zM743 283l-282 424l282 423h222l-283 -423l283 -424h-222z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="571" 
d="M463 754q0 -72 -52 -121t-126 -49t-125 48.5t-51 121.5t51 122.5t125 49.5t126 -49.5t52 -122.5z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="1079" 
d="M698 1130l283 -423l-283 -424h-221l283 424l-283 423h221zM336 1130l282 -423l-282 -424h-221l282 424l-282 423h221z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="1015" 
d="M455 936l4 45h278l4 -45q0 -94 -2 -121q-3 -43 -14 -90q-14 -68 -57 -106.5t-150 -94.5l-82 -35q-47 -25 -61.5 -48t-14.5 -64q0 -47 41.5 -79t104.5 -32q66 0 105.5 39.5t39.5 102.5h287q0 -196 -119 -312t-313 -116q-193 0 -313 108q-119 107 -119 291q0 144 74.5 246
t199.5 141q61 19 84 56.5t23 113.5zM416 1255q0 73 52 122.5t126 49.5t125 -49.5t51 -122.5t-51 -121.5t-125 -48.5t-126 49t-52 121z" />
    <glyph glyph-name="endash.case" horiz-adv-x="1314" 
d="M1184 584h-1053v254h1053v-254z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1751" 
d="M1620 584h-1489v254h1489v-254z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="962" 
d="M250 492q-96 90 -96 225t96 225t231 90q136 0 232 -90t96 -225t-96 -225q-97 -91 -232 -91q-134 0 -231 91z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="716" 
d="M381 283l-283 424l283 423h221l-283 -423l283 -424h-221z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="716" 
d="M336 1130l282 -423l-282 -424h-221l282 424l-282 423h221z" />
    <glyph glyph-name="cent.case" horiz-adv-x="1103" 
d="M504 1427h149v-217q237 -21 363 -200l-232 -166q-62 96 -184 96q-97 0 -166 -63q-72 -63 -72 -160q0 -100 72 -166q70 -64 166 -64q121 0 184 97l232 -164q-126 -182 -363 -203v-235h-149v241q-184 35 -303 166q-121 133 -121 328q0 190 121 323q117 129 303 164v223z
" />
    <glyph glyph-name="zero.tnum" horiz-adv-x="1187" 
d="M94 672q0 696 502 696q282 0 395 -205q103 -182 103 -491q0 -306 -103 -488q-54 -99 -154.5 -151.5t-240.5 -52.5q-502 0 -502 692zM381 672q0 -414 215 -414q211 0 211 414q0 418 -211 418q-215 0 -215 -418z" />
    <glyph glyph-name="one.tnum" horiz-adv-x="1187" 
d="M287 1114l516 246v-1360h-287v961l-229 -99v252z" />
    <glyph glyph-name="two.tnum" horiz-adv-x="1187" 
d="M1010 270v-270h-873v156l551 653q72 84 72 152q0 57 -46 95t-122 38q-74 0 -126.5 -52t-64.5 -135l-262 45q31 193 156 304.5t313 111.5q183 0 310 -115q129 -114 129 -292q0 -180 -142 -340l-309 -351h414z" />
    <glyph glyph-name="three.tnum" horiz-adv-x="1187" 
d="M557 582h-141v245h141q131 0 131 146q0 47 -36.5 88t-98.5 41q-94 0 -141 -74l-232 141q62 98 174 152t244 47q175 -9 287 -121q113 -110 110 -256q0 -79 -46.5 -149t-125.5 -113q111 -42 174 -147q65 -108 56 -217q-15 -170 -172 -285q-156 -111 -357 -96
q-138 7 -255 84.5t-173 195.5l252 125q21 -45 82 -88q55 -38 117 -41q71 -4 128.5 40.5t63.5 102.5q2 11 2 23.5t-2.5 36t-14 43t-30 38.5t-54 29t-83.5 9z" />
    <glyph glyph-name="four.tnum" horiz-adv-x="1187" 
d="M657 961l-249 -418h249v418zM1116 543v-242h-170v-301h-289v301h-641l627 1040h303v-798h170z" />
    <glyph glyph-name="five.tnum" horiz-adv-x="1187" 
d="M817 473q0 106 -67.5 162.5t-180.5 56.5q-86 0 -166.5 -36.5t-128.5 -110.5l-157 82l110 714h787v-262h-559l-35 -217q76 70 246 70q186 0 313 -121q125 -119 125 -338q0 -216 -152 -356q-149 -137 -401 -137q-139 0 -267.5 74t-207.5 196l241 156q38 -61 104.5 -100.5
t129.5 -39.5q114 0 190 60.5t76 146.5z" />
    <glyph glyph-name="six.tnum" horiz-adv-x="1187" 
d="M680 864q160 0 285 -119q127 -121 127 -333q0 -193 -136 -314q-130 -118 -352 -118q-193 0 -328 126q-135 129 -153 340q-6 72 -7 141.5t4 144t17 142t33 133.5t51 120.5t73 101t96 78t123.5 48t152.5 13.5q71 -3 129 -17.5t111 -44.5t96.5 -79.5t76.5 -118.5l-268 -115
q-52 107 -156 107q-90 -2 -150 -61.5t-81 -144t-14 -181.5q13 45 88 100q68 51 182 51zM741 303q54 54 54 125.5t-54 122.5q-51 54 -122 54t-125 -54q-51 -51 -51 -122.5t51 -125.5q54 -51 125 -51t122 51z" />
    <glyph glyph-name="seven.tnum" horiz-adv-x="1187" 
d="M119 1077v264h1011l-591 -1341h-326l473 1077h-567z" />
    <glyph glyph-name="eight.tnum" horiz-adv-x="1187" 
d="M180 993q0 155 121 264q123 111 293 111t293 -111q123 -108 123 -264q0 -136 -99 -225q89 -52 141 -141.5t52 -196.5q0 -189 -150 -321q-147 -129 -360 -129t-363 129q-147 132 -147 321q0 109 50.5 198t141.5 140q-96 93 -96 225zM371 444q0 -84 65.5 -141t157.5 -57
t157.5 57t65.5 141q0 85 -61.5 143t-163.5 58q-101 0 -161 -58t-60 -143zM457 985q0 -57 36.5 -93t100.5 -36t100.5 36t36.5 93q0 53 -38 89t-99 36t-99 -36t-38 -89z" />
    <glyph glyph-name="nine.tnum" horiz-adv-x="1187" 
d="M512 483q-162 0 -287 119t-125 334q0 192 133 313q134 119 355 119q192 0 327 -129q136 -127 154 -338q7 -82 7 -159.5t-7 -161t-25 -157.5t-45 -144t-71 -125t-100 -95.5t-133 -61t-169 -17.5q-71 3 -129 17.5t-111 44.5t-96.5 79.5t-76.5 118.5l268 114
q52 -106 156 -106q93 0 151 61q104 107 94 326q-53 -152 -270 -152zM449 1042q-51 -51 -51 -122t51 -125q54 -51 125 -51t122 51q54 54 54 125t-54 122q-51 54 -122 54t-125 -54z" />
    <glyph glyph-name="zero.taboldstyle" horiz-adv-x="1187" 
d="M592 -16q-212 0 -369 151q-155 149 -155 389q0 239 153 389q158 152 371 152q218 0 371 -150q155 -149 155 -391q0 -240 -155 -389q-154 -151 -371 -151zM592 250q104 0 180 76q78 78 78 198q0 119 -78 197q-76 76 -180 76q-102 0 -178 -76t-76 -197q0 -122 76 -198
t178 -76z" />
    <glyph glyph-name="one.taboldstyle" horiz-adv-x="1187" 
d="M281 809l553 250v-1059h-287v643l-266 -104v270z" />
    <glyph glyph-name="two.taboldstyle" horiz-adv-x="1187" 
d="M1016 250v-250h-858v164l481 475q66 63 66 96q0 38 -31 63t-84 25q-133 0 -148 -121l-278 33q21 163 131.5 256.5t296.5 93.5q187 0 305 -100q121 -100 121 -233q0 -139 -127 -248l-279 -254h404z" />
    <glyph glyph-name="six.taboldstyle" horiz-adv-x="1187" 
d="M680 864q160 0 285 -119q127 -121 127 -333q0 -193 -136 -314q-130 -118 -352 -118q-193 0 -328 126q-135 129 -153 340q-6 72 -7 141.5t4 144t17 142t33 133.5t51 120.5t73 101t96 78t123.5 48t152.5 13.5q71 -3 129 -17.5t111 -44.5t96.5 -79.5t76.5 -118.5l-268 -115
q-52 107 -156 107q-90 -2 -150 -61.5t-81 -144t-14 -181.5q13 45 88 100q68 51 182 51zM741 303q54 54 54 125.5t-54 122.5q-51 54 -122 54t-125 -54q-51 -51 -51 -122.5t51 -125.5q54 -51 125 -51t122 51z" />
    <glyph glyph-name="eight.taboldstyle" horiz-adv-x="1187" 
d="M180 993q0 155 121 264q123 111 293 111t293 -111q123 -108 123 -264q0 -136 -99 -225q89 -52 141 -141.5t52 -196.5q0 -189 -150 -321q-147 -129 -360 -129t-363 129q-147 132 -147 321q0 109 50.5 198t141.5 140q-96 93 -96 225zM371 444q0 -84 65.5 -141t157.5 -57
t157.5 57t65.5 141q0 85 -61.5 143t-163.5 58q-101 0 -161 -58t-60 -143zM457 985q0 -57 36.5 -93t100.5 -36t100.5 36t36.5 93q0 53 -38 89t-99 36t-99 -36t-38 -89z" />
    <glyph glyph-name="three.taboldstyle" horiz-adv-x="1187" 
d="M561 190h-141v246h141q131 0 131 146q0 47 -36.5 88t-98.5 41q-94 0 -141 -74l-232 141q62 98 174 152t244 47q175 -9 287 -121q113 -110 110 -256q0 -79 -46.5 -149t-125.5 -113q110 -41 174 -148q65 -108 56 -217q-15 -170 -172 -282q-156 -114 -357 -99
q-138 7 -255 85t-173 196l252 125q19 -43 82 -86q58 -40 117 -43q71 -4 128.5 40.5t63.5 102.5q1 9 1.5 19t-1 29t-7 35.5t-18.5 35t-32 31.5t-51.5 21t-73.5 7z" />
    <glyph glyph-name="four.taboldstyle" horiz-adv-x="1187" 
d="M659 573l-249 -417h249v417zM1118 156v-242h-170v-301h-289v301h-641l627 1040h303v-798h170z" />
    <glyph glyph-name="five.taboldstyle" horiz-adv-x="1187" 
d="M817 88q0 106 -67.5 162.5t-180.5 56.5q-86 0 -166.5 -36.5t-128.5 -110.5l-157 82l110 714h787v-262h-559l-35 -217q76 70 246 70q186 0 313 -121q125 -119 125 -338q0 -214 -152 -354t-401 -140q-138 0 -267 74.5t-208 196.5l241 155q38 -61 104 -100t130 -39
q114 0 190 60.5t76 146.5z" />
    <glyph glyph-name="seven.taboldstyle" horiz-adv-x="1187" 
d="M121 692v264h1012l-592 -1341h-326l473 1077h-567z" />
    <glyph glyph-name="nine.taboldstyle" horiz-adv-x="1187" 
d="M518 92q-162 0 -287 119t-125 334q0 191 134 313q134 119 354 119q193 0 328 -129q135 -126 153 -338q6 -72 7 -141.5t-4 -144t-17 -142t-33 -133.5t-51 -120.5t-73 -101t-96 -78t-123.5 -48t-152.5 -13.5q-71 3 -129 17.5t-111 44.5t-96.5 79.5t-76.5 118.5l268 115
q52 -106 156 -106q93 0 151 61q104 107 94 326q-53 -152 -270 -152zM455 651q-51 -51 -51 -122.5t51 -125.5q54 -51 125 -51t122 51q54 54 54 125.5t-54 122.5q-51 54 -122 54t-125 -54z" />
    <glyph glyph-name="zero.oldstyle" horiz-adv-x="1292" 
d="M645 -16q-212 0 -369 151q-155 149 -155 389q0 239 153 389q158 152 371 152q218 0 371 -150q155 -149 155 -391q0 -240 -155 -389q-154 -151 -371 -151zM645 250q104 0 180 76q78 78 78 198q0 119 -78 197q-76 76 -180 76q-102 0 -178 -76t-76 -197q0 -122 76 -198
t178 -76z" />
    <glyph glyph-name="one.oldstyle" horiz-adv-x="792" 
d="M84 809l553 250v-1059h-287v643l-266 -104v270z" />
    <glyph glyph-name="two.oldstyle" horiz-adv-x="1110" 
d="M977 250v-250h-858v164l481 475q66 63 66 96q0 38 -31 63t-84 25q-133 0 -148 -121l-278 33q21 163 131.5 256.5t296.5 93.5q187 0 305 -100q121 -100 121 -233q0 -139 -127 -248l-279 -254h404z" />
    <glyph glyph-name="three.oldstyle" horiz-adv-x="1087" 
d="M512 190h-141v246h141q131 0 131 146q0 47 -36.5 88t-98.5 41q-94 0 -141 -74l-232 141q62 98 174 152t244 47q175 -9 287 -121q113 -110 110 -256q0 -79 -46.5 -149t-125.5 -113q110 -41 174 -148q65 -108 56 -217q-15 -170 -172 -282q-156 -114 -357 -99
q-138 7 -255 85t-173 196l252 125q19 -43 82 -86q58 -40 117 -43q71 -4 128.5 40.5t63.5 102.5q1 9 1.5 19t-1 29t-7 35.5t-18.5 35t-32 31.5t-51.5 21t-73.5 7z" />
    <glyph glyph-name="four.oldstyle" horiz-adv-x="1202" 
d="M668 573l-250 -417h250v417zM1126 156v-242h-170v-301h-288v301h-641l626 1040h303v-798h170z" />
    <glyph glyph-name="five.oldstyle" horiz-adv-x="1181" 
d="M815 86q0 106 -67.5 162.5t-180.5 56.5q-86 0 -166.5 -36.5t-128.5 -110.5l-157 82l110 714h787v-262h-559l-35 -217q76 70 246 70q186 0 313 -121q125 -119 125 -338q0 -214 -152 -354t-401 -140q-138 0 -267 74.5t-208 196.5l241 155q38 -61 104 -100t130 -39
q114 0 190 60.5t76 146.5z" />
    <glyph glyph-name="six.oldstyle" horiz-adv-x="1200" 
d="M686 864q160 0 285 -119q127 -121 127 -333q0 -194 -135 -314q-130 -118 -353 -118q-192 0 -327 126q-136 130 -154 340q-6 72 -7 141.5t4 144t17 142t33 133.5t51 120.5t73 101t96 78t123.5 48t152.5 13.5q71 -3 129 -17.5t111 -44.5t96.5 -79.5t76.5 -118.5l-268 -115
q-52 107 -155 107q-68 -1 -120 -35.5t-80 -90.5t-39.5 -123t-6.5 -138q13 45 88 100q68 51 182 51zM748 303q54 54 54 125.5t-54 122.5q-51 54 -122.5 54t-125.5 -54q-51 -51 -51 -122.5t51 -125.5q54 -51 125.5 -51t122.5 51z" />
    <glyph glyph-name="seven.oldstyle" horiz-adv-x="1081" 
d="M68 692v264h1011l-592 -1341h-325l473 1077h-567z" />
    <glyph glyph-name="eight.oldstyle" horiz-adv-x="1220" 
d="M197 993q0 156 120 264q123 111 293 111t293 -111q123 -108 123 -264q0 -137 -98 -225q89 -52 140.5 -141.5t51.5 -196.5q0 -190 -149 -321q-147 -129 -361 -129q-212 0 -362 129q-148 133 -148 321q0 109 51 198t142 140q-96 93 -96 225zM387 444q0 -84 65.5 -141
t157.5 -57t158 57t66 141q0 85 -61.5 143t-164.5 58q-101 0 -161 -58t-60 -143zM473 985q0 -57 36.5 -93t100.5 -36t101 36t37 93q0 52 -38.5 88.5t-99.5 36.5t-99 -36t-38 -89z" />
    <glyph glyph-name="nine.oldstyle" horiz-adv-x="1159" 
d="M504 92q-162 0 -287 119t-125 334q0 192 133 313q134 119 355 119q192 0 327 -129q136 -127 154 -338q6 -72 7 -141.5t-4 -144t-17 -142t-33 -133.5t-51 -120.5t-73 -101t-96 -78t-123.5 -48t-152.5 -13.5q-145 6 -247.5 66t-166.5 194l269 115q52 -106 155 -106
q94 0 152 61q104 107 94 326q-53 -152 -270 -152zM440 651q-51 -51 -51 -122.5t51 -125.5q54 -51 125.5 -51t122.5 51q54 54 54 125.5t-54 122.5q-51 54 -122.5 54t-125.5 -54z" />
    <glyph glyph-name="Lslash.sc" horiz-adv-x="890" 
d="M432 1159v-344l154 96v-159l-154 -97v-426h428v-229h-688v492l-121 -74v160l121 75v506h260z" />
    <glyph glyph-name="Zcaron.sc" horiz-adv-x="1056" 
d="M225 1556h181l129 -143l131 143h180l-191 -290h-241zM96 0v195l520 737h-497v227h831v-192l-522 -738h522v-229h-854z" />
    <glyph glyph-name="exclam.sc" horiz-adv-x="591" 
d="M197 342l-35 817h266l-33 -817h-198zM188 20q-43 43 -43 104.5t43 102.5q44 41 106 41t105 -41q44 -41 44 -102.5t-44 -104.5q-43 -41 -105 -41t-106 41z" />
    <glyph glyph-name="ampersand.sc" horiz-adv-x="1208" 
d="M727 420v86h-139q-199 0 -217 -125q-6 -68 43.5 -114t124.5 -46q52 0 104 34.5t74 96.5q10 38 10 68zM545 705h182v131h229v-131h134v-199h-134v-164q0 -90 29 -119q30 -24 127 -24v-215q-155 3 -229 24q-74 24 -105 109q-35 -65 -114 -101t-160 -36q-171 0 -287 96
q-113 98 -113 248q0 221 177 292q-131 52 -131 230q0 125 100 225q103 103 242 103q214 0 337 -156l-206 -129q-45 45 -93 45q-54 0 -83 -30.5t-29 -76.5v-8t2.5 -17t6.5 -24t13.5 -25.5t22.5 -24t34.5 -16.5t47.5 -7z" />
    <glyph glyph-name="question.sc" horiz-adv-x="874" 
d="M315 821h-233l-6 -6q0 161 98 257.5t260 96.5q161 0 259 -90t98 -241q0 -117 -62 -203t-166 -117q-46 -13 -60 -35t-20 -82l-6 -57h-239l-2 41q0 142 44.5 220t155.5 122q68 29 87.5 50t19.5 63q0 33 -31.5 57.5t-77.5 24.5q-49 0 -81.5 -28.5t-37.5 -72.5zM211 125
q0 62 43.5 103.5t105.5 41.5q64 0 109 -41.5t45 -103.5q0 -61 -45 -103t-109 -42q-62 0 -105.5 42t-43.5 103z" />
    <glyph glyph-name="A.sc" 
d="M877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="B.sc" horiz-adv-x="1126" 
d="M973 834q0 -151 -146 -213q95 -29 150 -104.5t55 -158.5q0 -151 -102 -256q-102 -102 -271 -102h-499v1159h467q158 0 252 -88t94 -237zM418 930v-221h184q44 0 72 30.5t28 79.5q0 52 -26.5 81.5t-75.5 29.5h-182zM641 492h-223v-263h227q53 0 85 39.5t32 96.5
q0 52 -33.5 89.5t-87.5 37.5z" />
    <glyph glyph-name="C.sc" horiz-adv-x="1226" 
d="M956 385l209 -145q-78 -122 -207.5 -191t-283.5 -69q-247 0 -416 172q-168 174 -168 419q0 250 168 424t416 174q150 0 277 -67.5t210 -186.5l-211 -143q-106 154 -276 154q-140 0 -232 -103t-92 -252q0 -144 92 -247q94 -101 232 -101q86 0 160 43t122 119z" />
    <glyph glyph-name="D.sc" horiz-adv-x="1236" 
d="M614 0h-454v1159h454q247 0 385 -164q142 -165 142 -420q0 -250 -142 -415q-139 -160 -385 -160zM418 924v-688h196q127 0 197 98q72 101 72 241q0 152 -72.5 250.5t-196.5 98.5h-196z" />
    <glyph glyph-name="E.sc" horiz-adv-x="1021" 
d="M160 0v1159h739v-229h-483v-246h424v-225h-424v-230h497v-229h-753z" />
    <glyph glyph-name="F.sc" horiz-adv-x="954" 
d="M160 0v1159h731v-229h-473v-246h375v-227h-375v-457h-258z" />
    <glyph glyph-name="G.sc" horiz-adv-x="1312" 
d="M1124 975l-186 -162h-8q-100 115 -246 115q-141 0 -240 -105q-96 -107 -96 -252q0 -142 98 -249q99 -105 238 -105q100 0 180 51q85 52 88 131h-307v211h604q0 -310 -148.5 -470t-416.5 -160q-250 0 -422 172q-172 175 -172 419q0 249 172 424q171 174 422 174
q130 0 246 -51.5t194 -142.5z" />
    <glyph glyph-name="H.sc" horiz-adv-x="1228" 
d="M809 1159h260v-1159h-260v461h-393v-461h-256v1159h256v-467h393v467z" />
    <glyph glyph-name="I.sc" horiz-adv-x="575" 
d="M160 1159h256v-1159h-256v1159z" />
    <glyph glyph-name="J.sc" horiz-adv-x="577" 
d="M-141 -362l22 233q62 -21 154 -21q61 0 93 41t32 111v1157h258v-1157q0 -184 -95.5 -289.5t-287.5 -105.5q-58 0 -176 31z" />
    <glyph glyph-name="K.sc" horiz-adv-x="1081" 
d="M985 1159l-438 -573l543 -586h-336l-334 379v-379h-260v1159h260v-358l262 358h303z" />
    <glyph glyph-name="L.sc" horiz-adv-x="876" 
d="M160 0v1159h258v-930h428v-229h-686z" />
    <glyph glyph-name="M.sc" horiz-adv-x="1581" 
d="M451 1159l340 -729l337 729h218l139 -1159h-258l-78 668l-258 -537h-197l-260 537l-80 -668h-258l142 1159h213z" />
    <glyph glyph-name="N.sc" horiz-adv-x="1191" 
d="M1032 1159v-1159h-227l-389 674v-674h-256v1159h233l381 -663v663h258z" />
    <glyph glyph-name="O.sc" horiz-adv-x="1355" 
d="M90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242q-93 100 -235 100
q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="P.sc" horiz-adv-x="1083" 
d="M160 0v1159h477q190 0 287 -112q100 -112 100 -271t-100 -268q-99 -111 -287 -111h-219v-397h-258zM633 930h-215v-307h207q61 0 100 45.5t39 107.5q0 60 -37 107t-94 47z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="1366" 
d="M1032 92l111 -184l-185 -113l-124 203q-70 -18 -150 -18q-250 0 -422 172q-172 175 -172 419q0 249 172 424q171 174 422 174t422 -174q172 -175 172 -424q0 -142 -65 -268t-181 -211zM604 377l187 110l110 -180q119 110 119 264q0 149 -98 250q-99 105 -238 105
q-141 0 -240 -105q-96 -104 -96 -250q0 -123 73 -214.5t189 -116.5q46 -11 86 -11l-57 90z" />
    <glyph glyph-name="R.sc" horiz-adv-x="1126" 
d="M741 408l289 -408h-313l-256 397h-43v-397h-258v1159h477q184 0 287 -115q102 -114 102 -268q0 -132 -75.5 -232.5t-209.5 -135.5zM625 930h-207v-307h198q86 -2 122 75.5t2 155.5t-115 76z" />
    <glyph glyph-name="S.sc" horiz-adv-x="1024" 
d="M94 346h258q5 -61 48 -98t116 -37t117 30t45 83q0 46 -28.5 74.5t-104.5 47.5l-139 41q-300 89 -300 334q0 157 115 252q113 98 285 98q175 0 285 -99.5t110 -264.5h-256q0 58 -37 93.5t-102 35.5q-61 0 -102.5 -31t-41.5 -76q0 -75 119 -112l137 -41q76 -21 134.5 -54
t92.5 -68t56 -77t29.5 -78.5t7.5 -74.5q0 -167 -127 -256q-125 -88 -295 -88q-188 0 -305 102t-117 264z" />
    <glyph glyph-name="T.sc" horiz-adv-x="964" 
d="M43 926v233h879v-233h-308v-926h-262v926h-309z" />
    <glyph glyph-name="U.sc" horiz-adv-x="1196" 
d="M791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="V.sc" horiz-adv-x="1034" 
d="M49 1159h268l199 -727l199 727h270l-354 -1159h-228z" />
    <glyph glyph-name="W.sc" horiz-adv-x="1554" 
d="M1028 459l221 700h279l-395 -1159h-201l-156 582l-153 -582h-203l-391 1159h278l217 -702l148 540h211z" />
    <glyph glyph-name="X.sc" horiz-adv-x="1173" 
d="M1104 1159l-367 -541l406 -618h-305l-252 406l-248 -406h-305l399 618l-364 541h311l207 -336l211 336h307z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="1177" 
d="M719 522v-522h-262v524l-449 635h307l273 -422l272 422h311z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="1056" 
d="M96 0v195l520 737h-497v227h831v-192l-522 -738h522v-229h-854z" />
    <glyph glyph-name="Scaron.sc" horiz-adv-x="1024" 
d="M205 1556h182l127 -145l133 145h178l-188 -290h-242zM94 346h258q5 -61 48 -98t116 -37t117 30t45 83q0 46 -28.5 74.5t-104.5 47.5l-139 41q-300 89 -300 334q0 157 115 252q113 98 285 98q175 0 285 -99.5t110 -264.5h-256q0 58 -37 93.5t-102 35.5q-61 0 -102.5 -31
t-41.5 -76q0 -75 119 -112l137 -41q76 -21 134.5 -54t92.5 -68t56 -77t29.5 -78.5t7.5 -74.5q0 -167 -127 -256q-125 -88 -295 -88q-188 0 -305 102t-117 264z" />
    <glyph glyph-name="OE.sc" horiz-adv-x="1875" 
d="M1014 1049v110h739v-227h-481v-248h420v-225h-420v-230h495v-229h-753v98q-145 -118 -334 -118q-246 0 -418 172q-172 175 -172 419q0 248 170 424q171 174 420 174q202 0 334 -120zM348 571q0 -136 96 -237q94 -98 236 -98q139 0 233 98q99 99 99 237q0 140 -99 242
q-93 100 -233 100q-143 0 -236 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Ydieresis.sc" horiz-adv-x="1177" 
d="M719 522v-522h-262v524l-449 635h307l273 -422l272 422h311zM315 1440q0 51 36.5 85t90.5 34t88.5 -34t34.5 -85q0 -54 -34.5 -87.5t-88.5 -33.5q-55 0 -91 34t-36 87zM610 1440q0 51 36.5 85t90.5 34t88.5 -34t34.5 -85q0 -54 -34.5 -87.5t-88.5 -33.5q-55 0 -91 34
t-36 87z" />
    <glyph glyph-name="exclamdown.sc" horiz-adv-x="587" 
d="M393 817l33 -817h-266l35 817h198zM186 932q-43 41 -43 102.5t43 102.5q44 43 106 43t105 -43q44 -41 44 -102.5t-44 -102.5q-43 -41 -105 -41t-106 41z" />
    <glyph glyph-name="questiondown.sc" horiz-adv-x="880" 
d="M563 328h234l6 6q0 -161 -98.5 -257.5t-260.5 -96.5q-161 0 -258.5 90t-97.5 241q0 117 61.5 203t165.5 117q46 13 60 35t20 82l6 57h240l2 -41q0 -142 -45 -220t-156 -122q-68 -29 -87 -50t-19 -63q0 -33 31 -57.5t77 -24.5q49 0 81.5 28.5t37.5 72.5zM668 1024
q0 -62 -44 -103.5t-106 -41.5q-64 0 -108.5 41.5t-44.5 103.5q0 61 45 103t108 42q62 0 106 -42t44 -103z" />
    <glyph glyph-name="Agrave.sc" 
d="M717 1268h-182l-256 291h247zM877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="Aacute.sc" 
d="M647 1559h246l-256 -291h-182zM877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="Acircumflex.sc" 
d="M889 1274h-180l-129 143l-129 -143h-181l189 293h239zM877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="Atilde.sc" 
d="M877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372zM508 1378q-6 0 -13.5 -6.5t-14.5 -23.5t-7 -39h-139q6 110 56 168.5t118 58.5q32 0 76 -35q42 -33 69 -33q5 0 13 5.5t16 22t8 40.5h137q-4 -111 -54.5 -168t-119.5 -57
q-30 0 -73 32q-47 35 -72 35z" />
    <glyph glyph-name="Adieresis.sc" 
d="M285 1440q0 50 37.5 84.5t91.5 34.5t88.5 -34t34.5 -85q0 -54 -34.5 -87.5t-88.5 -33.5q-55 0 -92 34t-37 87zM580 1440q0 50 37.5 84.5t91.5 34.5t89.5 -34t35.5 -85q0 -54 -35.5 -87.5t-89.5 -33.5q-55 0 -92 34t-37 87zM877 0l-101 270h-387l-102 -270h-273l430 1159
h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="Aring.sc" 
d="M373 1475q0 90 59.5 150.5t147.5 60.5q87 0 147.5 -61.5t60.5 -149.5t-60 -147.5t-148 -59.5t-147.5 59.5t-59.5 147.5zM580 1389q37 0 63.5 25t26.5 61q0 37 -26.5 63.5t-63.5 26.5q-35 0 -60.5 -26.5t-25.5 -63.5q0 -36 25 -61t61 -25zM877 0l-101 270h-387l-102 -270
h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="AE.sc" horiz-adv-x="1630" 
d="M766 262h-297l-166 -262h-309l733 1159h778v-227h-481v-248h422v-225h-422v-230h498v-229h-756v262zM766 799l-150 -287h150v287z" />
    <glyph glyph-name="Ccedilla.sc" horiz-adv-x="1230" 
d="M586 -199q4 -20 21.5 -28t35.5 0q38 15 23 63l-56 150q-226 24 -372 192q-146 172 -146 397q0 250 168 424q169 175 416 175q150 0 277 -67.5t210 -186.5l-211 -144q-106 154 -276 154q-140 0 -232 -103t-92 -252q0 -144 92 -247q94 -101 232 -101q86 0 160 43t122 119
l209 -147q-151 -229 -426 -256q5 -13 26 -47.5t24 -40.5q16 -37 16 -84q0 -73 -54.5 -126t-129.5 -53q-77 0 -128.5 60t-50.5 160l138 -7q0 -27 4 -47z" />
    <glyph glyph-name="Egrave.sc" horiz-adv-x="1021" 
d="M160 0v1159h739v-229h-483v-246h424v-225h-424v-230h497v-229h-753zM682 1268h-182l-258 291h247z" />
    <glyph glyph-name="Eacute.sc" horiz-adv-x="1021" 
d="M565 1559h248l-258 -291h-182zM899 1159v-229h-483v-246h424v-225h-424v-230h497v-229h-753v1159h739z" />
    <glyph glyph-name="Ecircumflex.sc" horiz-adv-x="1021" 
d="M160 0v1159h739v-229h-483v-246h424v-225h-424v-230h497v-229h-753zM813 1274h-180l-129 143l-131 -143h-178l186 293h242z" />
    <glyph glyph-name="Edieresis.sc" horiz-adv-x="1021" 
d="M229 1440q0 51 37.5 85t91.5 34q51 0 87 -34t36 -85q0 -53 -35.5 -87t-87.5 -34q-56 0 -92.5 33.5t-36.5 87.5zM524 1440q0 51 37.5 85t91.5 34q51 0 87 -34t36 -85q0 -53 -35.5 -87t-87.5 -34q-56 0 -92.5 33.5t-36.5 87.5zM899 1159v-229h-483v-246h424v-225h-424v-230
h497v-229h-753v1159h739z" />
    <glyph glyph-name="Igrave.sc" horiz-adv-x="575" 
d="M420 1268h-182l-256 291h245zM160 1159h256v-1159h-256v1159z" />
    <glyph glyph-name="Iacute.sc" horiz-adv-x="575" 
d="M147 1298l199 271h256l-266 -271h-189zM160 1159h256v-1159h-256v1159z" />
    <glyph glyph-name="Icircumflex.sc" horiz-adv-x="575" 
d="M600 1274h-182l-129 143l-127 -143h-182l190 293h238zM160 1159h256v-1159h-256v1159z" />
    <glyph glyph-name="Idieresis.sc" horiz-adv-x="575" 
d="M160 1159h256v-1159h-256v1159zM10 1440q0 51 37.5 85t91.5 34q51 0 87 -34t36 -85q0 -53 -35.5 -87t-87.5 -34q-56 0 -92.5 33.5t-36.5 87.5zM305 1440q0 51 37.5 85t91.5 34q51 0 87 -34t36 -85q0 -53 -35.5 -87t-87.5 -34q-56 0 -92.5 33.5t-36.5 87.5z" />
    <glyph glyph-name="Eth.sc" horiz-adv-x="1255" 
d="M635 0h-457v479h-155v162h155v518h457q245 0 383 -164q141 -163 141 -420q0 -249 -141 -413q-137 -162 -383 -162zM436 924v-283h203v-162h-203v-243h199q126 0 196 98t70 241q0 154 -71 251.5t-195 97.5h-199z" />
    <glyph glyph-name="Ntilde.sc" horiz-adv-x="1191" 
d="M1032 1159v-1159h-227l-389 674v-674h-256v1159h233l381 -663v663h258zM522 1378q-6 0 -13.5 -6.5t-14.5 -23.5t-7 -39h-139q6 110 56 168.5t118 58.5q29 0 76 -35q44 -33 70 -33q5 0 13 5.5t16 22t8 40.5h137q-4 -111 -54.5 -168t-119.5 -57q-31 0 -74 32q-47 35 -72 35
z" />
    <glyph glyph-name="Ograve.sc" horiz-adv-x="1355" 
d="M807 1268h-180l-258 291h245zM90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242
q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Oacute.sc" horiz-adv-x="1355" 
d="M748 1559h247l-258 -291h-180zM90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242
q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Ocircumflex.sc" horiz-adv-x="1355" 
d="M987 1274h-180l-131 145l-127 -145h-180l188 293h240zM90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98
q99 99 99 237q0 140 -99 242q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Otilde.sc" horiz-adv-x="1355" 
d="M90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242q-93 100 -235 100
q-141 0 -234 -100q-96 -104 -96 -242zM604 1378q-6 0 -13.5 -6.5t-14.5 -23.5t-7 -39h-139q6 110 56 168.5t118 58.5q29 0 76 -35q44 -33 70 -33q5 0 13 5.5t15.5 22t7.5 40.5h138q-4 -111 -54.5 -168t-119.5 -57q-31 0 -74 32q-47 35 -72 35z" />
    <glyph glyph-name="Odieresis.sc" horiz-adv-x="1355" 
d="M403 1440q0 51 37.5 85t91.5 34q51 0 87 -34t36 -85q0 -53 -35.5 -87t-87.5 -34q-56 0 -92.5 33.5t-36.5 87.5zM698 1440q0 51 37.5 85t91.5 34t88.5 -34t34.5 -85q0 -54 -34.5 -87.5t-88.5 -33.5q-56 0 -92.5 33.5t-36.5 87.5zM90 571q0 248 170 424q168 174 416 174
q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Oslash.sc" horiz-adv-x="1353" 
d="M311 104l-135 -137l-86 86l133 137q-135 170 -135 381q0 248 170 424q168 174 416 174q210 0 373 -135l131 137l86 -86l-134 -135q134 -169 134 -379q0 -244 -172 -419q-169 -172 -418 -172q-199 0 -363 124zM344 571q0 -110 59 -196l463 477q-88 61 -192 61
q-141 0 -234 -100q-96 -104 -96 -242zM674 236q141 0 235 98q99 99 99 237q0 108 -60 193l-459 -475q77 -53 185 -53z" />
    <glyph glyph-name="Ugrave.sc" horiz-adv-x="1196" 
d="M741 1268h-180l-256 291h248zM791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="Uacute.sc" horiz-adv-x="1196" 
d="M645 1559h246l-256 -291h-182zM791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="Ucircumflex.sc" horiz-adv-x="1196" 
d="M907 1274h-180l-131 145l-127 -145h-182l190 293h240zM791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="Udieresis.sc" horiz-adv-x="1196" 
d="M324 1440q0 50 37.5 84.5t91.5 34.5t88 -34t34 -85q0 -54 -34 -87.5t-88 -33.5q-55 0 -92 34t-37 87zM618 1440q0 49 38 84t92 35t89 -34t35 -85q0 -54 -35 -87.5t-89 -33.5q-55 0 -92.5 34t-37.5 87zM791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118
q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="Yacute.sc" horiz-adv-x="1177" 
d="M719 522v-522h-262v524l-449 635h307l273 -422l272 422h311zM641 1559h248l-258 -291h-180z" />
    <glyph glyph-name="Thorn.sc" horiz-adv-x="1007" 
d="M569 211h-153v-211h-256v1159h256v-186h153q163 0 271 -105q108 -102 108 -274q0 -169 -108 -277q-106 -106 -271 -106zM416 754v-326h143q55 0 95 45t40 113q0 73 -39 120.5t-94 47.5h-145z" />
    <glyph glyph-name="Amacron.sc" 
d="M344 1350v153h475v-153h-475zM877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="Abreve.sc" 
d="M350 1528h154q0 -86 76 -86q73 0 73 86h158q0 -102 -64 -165t-167 -63q-107 0 -168.5 65t-61.5 163zM877 0l-101 270h-387l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="Cacute.sc" horiz-adv-x="1226" 
d="M760 1559h248l-258 -291h-183zM1165 240q-78 -122 -207.5 -191t-283.5 -69q-247 0 -416 172q-168 174 -168 419q0 250 168 424t416 174q150 0 277 -67.5t210 -186.5l-211 -143q-106 154 -276 154q-140 0 -232 -103t-92 -252q0 -144 92 -247q94 -101 232 -101q86 0 160 43
t122 119z" />
    <glyph glyph-name="Ccircumflex.sc" horiz-adv-x="1226" 
d="M977 1274h-180l-129 143l-129 -143h-181l189 293h239zM956 385l209 -145q-78 -122 -207.5 -191t-283.5 -69q-247 0 -416 172q-168 174 -168 419q0 250 168 424t416 174q150 0 277 -67.5t210 -186.5l-211 -143q-106 154 -276 154q-140 0 -232 -103t-92 -252
q0 -144 92 -247q94 -101 232 -101q86 0 160 43t122 119z" />
    <glyph glyph-name="Cdotaccent.sc" horiz-adv-x="1226" 
d="M532 1450q0 55 39.5 92t96.5 37q56 0 95.5 -37t39.5 -92q0 -57 -39 -94t-96 -37q-60 0 -98 37t-38 94zM1165 240q-78 -122 -207.5 -191t-283.5 -69q-247 0 -416 172q-168 174 -168 419q0 250 168 424t416 174q150 0 277 -67.5t210 -186.5l-211 -143q-106 154 -276 154
q-140 0 -232 -103t-92 -252q0 -144 92 -247q94 -101 232 -101q86 0 160 43t122 119z" />
    <glyph glyph-name="Ccaron.sc" horiz-adv-x="1226" 
d="M344 1556h182l127 -143l129 143h183l-193 -290h-237zM956 385l209 -145q-78 -122 -207.5 -191t-283.5 -69q-247 0 -416 172q-168 174 -168 419q0 250 168 424t416 174q150 0 277 -67.5t210 -186.5l-211 -143q-106 154 -276 154q-140 0 -232 -103t-92 -252q0 -144 92 -247
q94 -101 232 -101q86 0 160 43t122 119z" />
    <glyph glyph-name="Dcaron.sc" horiz-adv-x="1245" 
d="M621 0h-461v1167h461q246 0 387 -166q141 -163 141 -421q0 -254 -141 -418q-140 -162 -387 -162zM621 924h-195v-680h195q121 0 192.5 95.5t71.5 240.5t-70 243q-70 101 -194 101zM293 1556h180l129 -143l131 143h182l-192 -290h-242z" />
    <glyph glyph-name="Aogonek.sc" 
d="M1149 0h-158l-51 -164q-15 -48 23 -63q18 -8 35.5 0t21.5 28l4 47l137 7q1 -100 -50 -160t-128 -60q-75 0 -129.5 53t-54.5 126q0 47 16 84l27 49l32 59l-98 264h-387l-102 -270h-273l430 1159h275zM694 496l-112 370l-109 -370h221z" />
    <glyph glyph-name="Emacron.sc" horiz-adv-x="1021" 
d="M160 0v1159h739v-229h-483v-246h424v-225h-424v-230h497v-229h-753zM264 1350v153h475v-153h-475z" />
    <glyph glyph-name="Ebreve.sc" horiz-adv-x="1021" 
d="M272 1528h154q0 -86 78 -86q71 0 71 86h158q0 -104 -63 -166t-166 -62q-107 0 -169.5 65.5t-62.5 162.5zM899 1159v-229h-483v-246h424v-225h-424v-230h497v-229h-753v1159h739z" />
    <glyph glyph-name="Edotaccent.sc" horiz-adv-x="1021" 
d="M383 1450q0 56 38.5 92.5t98.5 36.5q56 0 94.5 -37t38.5 -92q0 -56 -38.5 -93.5t-94.5 -37.5q-60 0 -98.5 37t-38.5 94zM899 1159v-229h-483v-246h424v-225h-424v-230h497v-229h-753v1159h739z" />
    <glyph glyph-name="Ecaron.sc" horiz-adv-x="1021" 
d="M195 1556h178l131 -143l129 143h180l-190 -290h-242zM160 0v1159h739v-229h-483v-246h424v-225h-424v-230h497v-229h-753z" />
    <glyph glyph-name="Gcircumflex.sc" horiz-adv-x="1312" 
d="M973 1274h-182l-129 143l-127 -143h-183l191 293h237zM1124 975l-186 -162q-112 115 -254 115q-141 0 -240 -105q-96 -107 -96 -252q0 -142 98 -249q99 -105 238 -105q100 0 180 51q85 52 88 131h-307v211h604q0 -310 -148.5 -470t-416.5 -160q-250 0 -422 172
q-172 175 -172 419q0 249 172 424q171 174 422 174q130 0 246 -51.5t194 -142.5z" />
    <glyph glyph-name="Gbreve.sc" horiz-adv-x="1312" 
d="M1124 975l-186 -162q-112 115 -254 115q-141 0 -240 -105q-96 -107 -96 -252q0 -142 98 -249q99 -105 238 -105q100 0 180 51q85 52 88 131h-307v211h604q0 -310 -148.5 -470t-416.5 -160q-250 0 -422 172q-172 175 -172 419q0 249 172 424q171 174 422 174
q130 0 246 -51.5t194 -142.5zM432 1528h154q0 -86 76 -86q73 0 73 86h158q0 -102 -64 -165t-167 -63q-107 0 -168.5 65t-61.5 163z" />
    <glyph glyph-name="Eogonek.sc" horiz-adv-x="1021" 
d="M723 -102l61 102h-624v1159h739v-229h-483v-246h424v-225h-424v-230h497v-225l-65 -168q-15 -48 22 -63q18 -8 36 0t22 28l4 47l137 7q1 -100 -50 -160t-128 -60q-75 0 -129.5 53t-54.5 126q0 47 16 84z" />
    <glyph glyph-name="Dcroat.sc" horiz-adv-x="1255" 
d="M635 0h-457v479h-155v162h155v518h457q245 0 383 -164q141 -163 141 -420q0 -249 -141 -413q-137 -162 -383 -162zM436 924v-283h203v-162h-203v-243h199q126 0 196 98t70 241q0 154 -71 251.5t-195 97.5h-199z" />
    <glyph glyph-name="Gdotaccent.sc" horiz-adv-x="1312" 
d="M1124 975l-186 -162h-8q-100 115 -246 115q-141 0 -240 -105q-96 -107 -96 -252q0 -142 98 -249q99 -105 238 -105q100 0 180 51q85 52 88 131h-307v211h604q0 -310 -148.5 -470t-416.5 -160q-250 0 -422 172q-172 175 -172 419q0 249 172 424q171 174 422 174
q130 0 246 -51.5t194 -142.5zM545 1450q0 55 39 92t96 37q56 0 95.5 -37t39.5 -92q0 -57 -39 -94t-96 -37q-60 0 -97.5 37t-37.5 94z" />
    <glyph glyph-name="Hcircumflex.sc" horiz-adv-x="1228" 
d="M809 1159h260v-1159h-260v461h-393v-461h-256v1159h256v-467h393v467zM924 1274h-181l-129 143l-129 -143h-180l189 293h239z" />
    <glyph glyph-name="Hbar.sc" horiz-adv-x="1251" 
d="M1081 1159v-164h121v-133h-121v-862h-260v461h-393v-461h-258v862h-119v133h119v164h258v-164h393v164h260zM428 862v-170h393v170h-393z" />
    <glyph glyph-name="Itilde.sc" horiz-adv-x="550" 
d="M391 1176v-1192h-231v1192h231zM203 1378q-6 0 -13.5 -6.5t-14.5 -23.5t-7 -39h-139q6 110 56 168.5t118 58.5q29 0 76 -35q44 -33 69 -33q5 0 13 5.5t16 22t8 40.5h137q-4 -111 -54.5 -168t-119.5 -57q-31 0 -74 32q-47 35 -71 35z" />
    <glyph glyph-name="Imacron.sc" horiz-adv-x="575" 
d="M51 1350v153h475v-153h-475zM160 1159h256v-1159h-256v1159z" />
    <glyph glyph-name="Iogonek.sc" horiz-adv-x="575" 
d="M100 -102l60 110v1151h256v-1159h-129l-62 -164q-15 -48 23 -63q18 -8 35.5 0t21.5 28l4 47l137 7q1 -100 -50 -160t-128 -60q-75 0 -129.5 53t-54.5 126q0 47 16 84z" />
    <glyph glyph-name="IJ.sc" horiz-adv-x="1153" 
d="M434 -362l23 233q61 -21 153 -21q61 0 93 41t32 111v1157h258v-1157q0 -184 -95.5 -289.5t-287.5 -105.5q-58 0 -176 31zM160 1159h256v-1159h-256v1159z" />
    <glyph glyph-name="Jcircumflex.sc" horiz-adv-x="552" 
d="M588 1274h-182l-130 143l-129 -143h-180l189 293h239zM-139 -354l22 207q50 -15 142 -15q66 0 100.5 44t34.5 120v1145h233v-1145q0 -177 -92 -280t-276 -103q-53 0 -164 27z" />
    <glyph glyph-name="Lacute.sc" horiz-adv-x="876" 
d="M346 1559h250l-258 -291h-180zM418 1159v-930h428v-229h-686v1159h258z" />
    <glyph glyph-name="Lcaron.sc" horiz-adv-x="905" 
d="M668 711l-154 49q93 136 119 270q12 58 12 131h205q-12 -150 -62.5 -271t-119.5 -179zM418 1159v-930h428v-229h-686v1159h258z" />
    <glyph glyph-name="Ldot.sc" horiz-adv-x="878" 
d="M160 0v1159h258v-930h428v-229h-686zM535 520q0 50 35 83.5t89 33.5q51 0 87 -34t36 -83q0 -53 -35.5 -87t-87.5 -34q-54 0 -89 33.5t-35 87.5z" />
    <glyph glyph-name="Nacute.sc" horiz-adv-x="1191" 
d="M676 1559h246l-258 -291h-181zM805 0l-389 674v-674h-256v1159h233l381 -663v663h258v-1159h-227z" />
    <glyph glyph-name="Ncaron.sc" horiz-adv-x="1191" 
d="M1032 1159v-1159h-227l-389 674v-674h-256v1159h233l381 -663v663h258zM274 1556h179l131 -143l129 143h180l-191 -290h-241z" />
    <glyph glyph-name="Omacron.sc" horiz-adv-x="1355" 
d="M438 1350v153h475v-153h-475zM90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242
q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Obreve.sc" horiz-adv-x="1355" 
d="M446 1528h154q0 -86 76 -86q74 0 74 86h157q0 -104 -63 -166t-168 -62q-107 0 -168.5 65t-61.5 163zM90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571
q0 -136 96 -237q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Ohungarumlaut.sc" horiz-adv-x="1355" 
d="M707 1266l215 290h204l-229 -290h-190zM461 1266l192 290h203l-213 -290h-182zM90 571q0 248 170 424q168 174 416 174q250 0 418 -174q172 -175 172 -424q0 -244 -172 -419q-169 -172 -418 -172q-247 0 -416 172q-170 176 -170 419zM346 571q0 -136 96 -237
q94 -98 234 -98q141 0 235 98q99 99 99 237q0 140 -99 242q-93 100 -235 100q-141 0 -234 -100q-96 -104 -96 -242z" />
    <glyph glyph-name="Racute.sc" horiz-adv-x="1126" 
d="M596 1559h248l-258 -291h-183zM1030 0h-313l-256 397h-43v-397h-258v1159h477q184 0 287 -115q102 -114 102 -268q0 -132 -75.5 -232.5t-209.5 -135.5zM418 930v-307h198q86 -2 122 75.5t2 155.5t-115 76h-207z" />
    <glyph glyph-name="Rcaron.sc" horiz-adv-x="1126" 
d="M244 1556h182l127 -143l131 143h182l-190 -290h-242zM741 408l289 -408h-313l-256 397h-43v-397h-258v1159h477q184 0 287 -115q102 -114 102 -268q0 -132 -75.5 -232.5t-209.5 -135.5zM625 930h-207v-307h198q86 -2 122 75.5t2 155.5t-115 76z" />
    <glyph glyph-name="Sacute.sc" horiz-adv-x="1024" 
d="M586 1559h245l-256 -291h-182zM352 346q5 -61 48 -98t116 -37t117 30t45 83q0 46 -28.5 74.5t-104.5 47.5l-139 41q-300 89 -300 334q0 157 115 252q113 98 285 98q175 0 285 -99.5t110 -264.5h-256q0 58 -37 93.5t-102 35.5q-61 0 -102.5 -31t-41.5 -76q0 -75 119 -112
l137 -41q76 -21 134.5 -54t92.5 -68t56 -77t29.5 -78.5t7.5 -74.5q0 -167 -127 -256q-125 -88 -295 -88q-188 0 -305 102t-117 264h258z" />
    <glyph glyph-name="Scircumflex.sc" horiz-adv-x="1024" 
d="M825 1274h-178l-131 143l-129 -143h-180l188 293h242zM94 346h258q5 -61 48 -98t116 -37t117 30t45 83q0 46 -28.5 74.5t-104.5 47.5l-139 41q-300 89 -300 334q0 157 115 252q113 98 285 98q175 0 285 -99.5t110 -264.5h-256q0 58 -37 93.5t-102 35.5q-61 0 -102.5 -31
t-41.5 -76q0 -75 119 -112l137 -41q76 -21 134.5 -54t92.5 -68t56 -77t29.5 -78.5t7.5 -74.5q0 -167 -127 -256q-125 -88 -295 -88q-188 0 -305 102t-117 264z" />
    <glyph glyph-name="Scedilla.sc" horiz-adv-x="1021" 
d="M592 -14q5 -13 25.5 -47.5t23.5 -40.5q16 -37 16 -84q0 -73 -54.5 -126t-129.5 -53q-77 0 -128 60t-50 160l137 -7q0 -27 4 -47t22 -28t36 0q37 15 22 63l-55 148q-165 13 -265 113.5t-100 250.5h256q2 -59 46.5 -98t117.5 -39t117 30t45 83q2 44 -28 73.5t-105 48.5
l-137 39q-299 89 -299 336q0 155 114 250q113 98 283 98q172 0 282 -98q111 -95 111 -264h-254q0 58 -37.5 92.5t-103.5 34.5q-62 0 -103 -30t-41 -77q0 -75 121 -110l135 -43q122 -40 199 -105q70 -62 95.5 -119.5t23.5 -125.5q-1 -142 -97 -231t-245 -107z" />
    <glyph glyph-name="Utilde.sc" horiz-adv-x="1196" 
d="M791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5zM526 1378q-6 0 -13.5 -6.5t-14 -23t-6.5 -39.5h-140q6 110 56 168.5t118 58.5q29 0 76 -35
q44 -33 70 -33q5 0 13 5.5t16 22t8 40.5h137q-4 -111 -54.5 -168t-119.5 -57q-31 0 -74 32q-47 35 -72 35z" />
    <glyph glyph-name="Umacron.sc" horiz-adv-x="1196" 
d="M360 1350v153h478v-153h-478zM791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="Ubreve.sc" horiz-adv-x="1196" 
d="M369 1528h153q0 -86 74 -86q76 0 76 86h157q0 -102 -65 -165t-168 -63q-106 0 -166.5 65t-60.5 163zM791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z
" />
    <glyph glyph-name="Uring.sc" horiz-adv-x="1196" 
d="M596 1686q88 0 149.5 -60.5t61.5 -150.5q0 -88 -60.5 -147.5t-150.5 -59.5q-86 0 -145.5 59.5t-59.5 147.5t59.5 149.5t145.5 61.5zM596 1389q55 -4 78 41t0 91t-78 44q-35 0 -59.5 -26.5t-24.5 -63.5t24.5 -61.5t59.5 -24.5zM791 412v747h258v-747q0 -195 -131 -314
q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5z" />
    <glyph glyph-name="Uhungarumlaut.sc" horiz-adv-x="1196" 
d="M791 412v747h258v-747q0 -195 -131 -314q-130 -118 -318 -118q-189 0 -319 118q-134 122 -134 314v747h263v-747q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5zM584 1266l215 290h205l-230 -290h-190zM338 1266l192 290h203l-213 -290h-182z" />
    <glyph glyph-name="Uogonek.sc" horiz-adv-x="1196" 
d="M469 -102l47 94q-156 27 -264 141q-105 114 -105 283v743h263v-743q0 -80 56.5 -134.5t133.5 -54.5t134 54.5t57 134.5v743h258v-743q0 -176 -115 -297q-115 -118 -285 -133l-55 -150q-15 -48 22 -63q18 -8 36 0t22 28l4 47l137 7q1 -100 -50 -160t-128 -60
q-75 0 -129.5 53t-54.5 126q0 47 16 84z" />
    <glyph glyph-name="Wcircumflex.sc" horiz-adv-x="1554" 
d="M1090 1274h-183l-129 143l-131 -143h-178l188 293h240zM1028 459l221 700h279l-395 -1159h-201l-156 582l-153 -582h-203l-391 1159h278l217 -702l148 540h211z" />
    <glyph glyph-name="Ycircumflex.sc" horiz-adv-x="1177" 
d="M897 1274h-180l-129 143l-129 -143h-180l188 293h240zM719 522v-522h-262v524l-449 635h307l273 -422l272 422h311z" />
    <glyph glyph-name="Tcaron.sc" horiz-adv-x="1003" 
d="M367 926h-326v233h922v-233h-326v-926h-270v926zM195 1556h178l131 -143l129 143h180l-190 -290h-242z" />
    <glyph glyph-name="Tbar.sc" horiz-adv-x="964" 
d="M354 500h-159v153h159v271h-309v231h875v-231h-308v-271h158v-153h-158v-500h-258v500z" />
    <glyph glyph-name="Zacute.sc" horiz-adv-x="1056" 
d="M96 0v195l520 737h-497v227h831v-192l-522 -738h522v-229h-854zM588 1559h248l-261 -291h-180z" />
    <glyph glyph-name="Zdotaccent.sc" horiz-adv-x="1056" 
d="M96 0v195l520 737h-497v227h831v-192l-522 -738h522v-229h-854zM416 1450q0 55 39 92t96 37t96 -37t39 -92q0 -57 -39 -94t-96 -37t-96 37t-39 94z" />
    <glyph glyph-name="Wgrave.sc" horiz-adv-x="1554" 
d="M897 1268h-182l-256 291h246zM1028 459l221 700h279l-395 -1159h-201l-156 582l-153 -582h-203l-391 1159h278l217 -702l148 540h211z" />
    <glyph glyph-name="Wacute.sc" horiz-adv-x="1554" 
d="M848 1559h248l-258 -291h-183zM1528 1159l-395 -1159h-201l-156 582l-153 -582h-203l-391 1159h278l217 -702l148 540h211l145 -538l221 700h279z" />
    <glyph glyph-name="Wdieresis.sc" horiz-adv-x="1554" 
d="M504 1440q0 51 37.5 85t91.5 34q51 0 88 -35t37 -84q0 -53 -36.5 -87t-88.5 -34q-56 0 -92.5 33.5t-36.5 87.5zM799 1440q0 51 37.5 85t91.5 34q51 0 88 -35t37 -84q0 -53 -36.5 -87t-88.5 -34q-56 0 -92.5 33.5t-36.5 87.5zM1528 1159l-395 -1159h-201l-156 582
l-153 -582h-203l-391 1159h278l217 -702l148 540h211l145 -538l221 700h279z" />
    <glyph glyph-name="Aringacute.sc" 
d="M657 1866h244l-203 -230q95 -65 95 -178q0 -86 -62.5 -146.5t-150.5 -60.5q-105 0 -166 88q-39 51 -39 119t42.5 124.5t106.5 80.5zM498 1458q0 -35 24.5 -60.5t59.5 -25.5q38 0 64 25t26 61q0 38 -26 64t-64 26q-35 0 -59.5 -26t-24.5 -64zM877 0l-101 270h-387
l-102 -270h-273l430 1159h275l430 -1159h-272zM473 496h219l-112 372z" />
    <glyph glyph-name="AEacute.sc" horiz-adv-x="1630" 
d="M965 1559h247l-260 -291h-178zM303 0h-309l733 1159h778v-227h-481v-248h422v-225h-422v-230h498v-229h-756v262h-297zM766 512v287l-150 -287h150z" />
    <glyph glyph-name="Oslashacute.sc" horiz-adv-x="1353" 
d="M776 1559h248l-260 -291h-178zM176 -33l-86 86l133 137q-135 170 -135 381q0 248 170 424q168 174 416 174q210 0 373 -135l131 137l86 -86l-134 -135q134 -169 134 -379q0 -244 -172 -419q-169 -172 -418 -172q-199 0 -363 124zM344 571q0 -110 59 -196l463 477
q-88 61 -192 61q-141 0 -234 -100q-96 -104 -96 -242zM674 236q141 0 235 98q99 99 99 237q0 108 -60 193l-459 -475q77 -53 185 -53z" />
    <glyph glyph-name="Ibreve.sc" horiz-adv-x="575" 
d="M160 1159h256v-1159h-256v1159zM51 1528h154q0 -86 76 -86q73 0 73 86h158q0 -102 -64 -165t-167 -63q-107 0 -168.5 65t-61.5 163z" />
    <glyph glyph-name="Eng.sc" horiz-adv-x="1193" 
d="M776 31l-358 643v-674h-258v1159h235l379 -682v682h260v-1130q0 -186 -95.5 -295t-283.5 -109q-54 0 -178 31l27 250q80 -25 155 -25q53 1 82.5 43.5t34.5 106.5z" />
    <glyph glyph-name="Ygrave.sc" horiz-adv-x="1177" 
d="M725 1268h-182l-256 291h248zM719 522v-522h-262v524l-449 635h307l273 -422l272 422h311z" />
    <glyph glyph-name="Gcommaaccent.sc" horiz-adv-x="1312" 
d="M1124 975l-186 -162q-112 115 -254 115q-141 0 -240 -105q-96 -107 -96 -252q0 -142 98 -249q99 -105 238 -105q100 0 180 51q85 52 88 131h-307v211h604q0 -310 -148.5 -470t-416.5 -160q-250 0 -422 172q-172 175 -172 419q0 249 172 424q171 174 422 174
q130 0 246 -51.5t194 -142.5zM635 -455l-105 58q43 58 56.5 124t13.5 193h184q0 -247 -149 -375z" />
    <glyph glyph-name="Kcommaaccent.sc" horiz-adv-x="1081" 
d="M985 1159l-438 -573l543 -586h-336l-334 379v-379h-260v1159h260v-358l262 358h303zM532 -455l-104 58q43 58 56.5 124t13.5 193h184q0 -246 -150 -375z" />
    <glyph glyph-name="Lcommaaccent.sc" horiz-adv-x="876" 
d="M160 0v1159h258v-930h428v-229h-686zM428 -455l-104 58q42 58 55.5 123.5t13.5 193.5h185q0 -246 -150 -375z" />
    <glyph glyph-name="Ncommaaccent.sc" horiz-adv-x="1191" 
d="M1032 1159v-1159h-227l-389 664v-664h-256v1159h233l381 -653v653h258zM555 -455l-104 58q42 58 55.5 123.5t13.5 193.5h185q0 -246 -150 -375z" />
    <glyph glyph-name="Rcommaaccent.sc" horiz-adv-x="1126" 
d="M741 408l289 -408h-313l-256 397h-43v-397h-258v1159h477q184 0 287 -115q102 -114 102 -268q0 -132 -75.5 -232.5t-209.5 -135.5zM625 930h-207v-307h198q86 -2 122 75.5t2 155.5t-115 76zM532 -455l-104 58q43 58 56.5 124t13.5 193h184q0 -246 -150 -375z" />
    <glyph glyph-name="Tcommaaccent.sc" horiz-adv-x="964" 
d="M43 926v233h879v-233h-308v-926h-262v926h-309zM420 -455l-105 58q43 58 56.5 124t13.5 193h184q0 -247 -149 -375z" />
    <glyph glyph-name="Scommaaccent.sc" horiz-adv-x="1024" 
d="M94 346h258q5 -61 48 -98t116 -37t117 30t45 83q0 46 -28.5 74.5t-104.5 47.5l-139 41q-300 89 -300 334q0 157 115 252q113 98 285 98q175 0 285 -99.5t110 -264.5h-256q0 58 -37 93.5t-102 35.5q-61 0 -102.5 -31t-41.5 -76q0 -75 119 -112l137 -41q76 -21 134.5 -54
t92.5 -68t56 -77t29.5 -78.5t7.5 -74.5q0 -167 -127 -256q-125 -88 -295 -88q-188 0 -305 102t-117 264zM463 -455l-105 58q43 58 56.5 124t13.5 193h184q0 -247 -149 -375z" />
    <glyph glyph-name="Idotaccent.sc" horiz-adv-x="575" 
d="M160 1159h256v-1159h-256v1159zM178 1327q-43 43 -43 104.5t43 102.5q44 41 106 41t105 -41q44 -41 44 -102.5t-44 -104.5q-43 -41 -105 -41t-106 41z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="718" 
d="M70 952q0 404 290 404q289 0 289 -404q0 -401 -289 -401q-290 0 -290 401zM233 952q0 -241 127 -241q125 0 125 241q0 244 -125 244q-127 0 -127 -244z" />
    <glyph glyph-name="one.numr" horiz-adv-x="442" 
d="M176 563v559l-133 -55v143l297 142v-789h-164z" />
    <glyph glyph-name="two.numr" horiz-adv-x="651" 
d="M61 563v88l320 379q41 47 41 90q0 33 -27.5 55.5t-70.5 22.5q-44 0 -74.5 -29.5t-36.5 -78.5l-152 26q19 111 91.5 175.5t181.5 64.5q104 0 178 -68q76 -64 76 -168q0 -105 -82 -196l-180 -205h241v-156h-506z" />
    <glyph glyph-name="three.numr" horiz-adv-x="649" 
d="M213 1159l-131 82q36 56 100.5 87.5t141.5 27.5q100 -6 166.5 -69t62.5 -148q0 -100 -100 -154q62 -23 100.5 -85t32.5 -126q-9 -100 -99 -166q-88 -64 -206 -55q-79 6 -147.5 51t-100.5 113l143 71q38 -66 117 -75q42 -1 75 24t38 58q0 41 -7 59q-21 50 -100 47h-82v141
h82q78 0 78 86q0 28 -22.5 52t-57.5 24q-55 0 -84 -45z" />
    <glyph glyph-name="four.numr" horiz-adv-x="706" 
d="M655 737h-98v-174h-166v174h-373l367 604h172v-464h98v-140zM391 1120l-147 -243h147v243z" />
    <glyph glyph-name="five.numr" horiz-adv-x="698" 
d="M119 754l69 43q52 -82 136 -82q67 0 111 35.5t44 87.5q0 60 -39 93.5t-104 33.5q-115 0 -172 -84l-90 47l63 413h455v-151h-324l-22 -129q47 41 143 41q113 0 183.5 -71t70.5 -193q0 -128 -87 -207.5t-232 -79.5q-79 0 -154.5 44t-120.5 114q5 0 70 45z" />
    <glyph glyph-name="six.numr" horiz-adv-x="694" 
d="M299 885q-29 -31 -29 -74t29 -74q31 -29 73.5 -29t73.5 29q31 31 31 74t-31 74t-73.5 31t-73.5 -31zM639 1206l-154 -65q-27 61 -90 61q-76 0 -112 -63.5t-31 -161.5q11 32 56.5 60t99.5 28q99 0 169 -71t70 -191q0 -112 -78.5 -182t-203.5 -70q-113 0 -190 74t-87 196
q-15 162 12 279q59 265 301 256q174 -9 238 -150z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="643" 
d="M47 1341h586l-346 -778h-187l277 627h-330v151z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="708" 
d="M295 1186q-20 -20 -20 -51t20 -54q23 -23 60 -23t57 23q23 23 23 54t-23 51q-20 23 -57 23t-60 -23zM223 823q0 -49 38.5 -83.5t92.5 -34.5t92.5 34.5t38.5 83.5q0 45 -35.5 80t-82.5 35h-27q-48 0 -82.5 -35t-34.5 -80zM115 1139q0 90 69 153q70 64 170 64q98 0 168 -64
q72 -66 72 -153q0 -84 -57 -129q112 -66 112 -191q0 -111 -88 -190q-87 -78 -207 -78q-119 0 -209 78q-86 80 -86 190q0 124 113 191q-57 51 -57 129z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="694" 
d="M408 1022q31 31 31 73.5t-31 73.5q-29 31 -72 31t-74 -31t-31 -73.5t31 -73.5q31 -29 74 -29t72 29zM70 702l153 66q26 -63 90 -63q76 0 112.5 64.5t31.5 162.5q-12 -32 -56 -60t-100 -28q-99 0 -169.5 71t-70.5 191q0 113 78 181.5t205 68.5q113 0 191 -74.5t88 -196.5
q15 -205 -41 -360q-32 -84 -103 -131t-172 -43q-170 9 -237 151z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="718" 
d="M70 389q0 404 290 404q289 0 289 -404q0 -401 -289 -401q-290 0 -290 401zM233 389q0 -242 127 -242q125 0 125 242q0 244 -125 244q-127 0 -127 -244z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="442" 
d="M176 0v559l-133 -55v143l297 141v-788h-164z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="651" 
d="M61 0v88l320 379q41 47 41 90q0 33 -27.5 55.5t-70.5 22.5q-44 0 -74.5 -30t-36.5 -79l-152 27q19 111 91.5 175.5t181.5 64.5q104 0 178 -68q76 -64 76 -168q0 -106 -82 -197l-180 -204h241v-156h-506z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="651" 
d="M215 596l-131 82q36 56 100.5 87.5t141.5 27.5q100 -6 166.5 -69.5t62.5 -148.5q0 -99 -100 -153q62 -23 100.5 -85t32.5 -126q-9 -100 -99 -166q-88 -64 -206 -55q-79 6 -147.5 51t-100.5 113l143 71q38 -66 117 -75q42 -1 75 23.5t38 57.5q0 43 -7 60q-21 50 -100 47
h-82v141h82q78 0 78 86q0 28 -22.5 52t-57.5 24q-55 0 -84 -45z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="704" 
d="M655 174h-98v-174h-166v174h-373l367 604h172v-465h98v-139zM391 557l-147 -244h147v244z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="696" 
d="M186 233q52 -81 136 -81q67 0 111 35t44 87q0 60 -39 93.5t-104 33.5q-115 0 -172 -84l-90 48l63 413h455v-151h-324l-22 -129q47 41 143 41q112 0 183 -71.5t71 -193.5q0 -128 -87 -207t-232 -79q-79 0 -154.5 43.5t-120.5 113.5q3 0 70 43q65 45 69 45z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="694" 
d="M299 322q-29 -31 -29 -74t29 -74q31 -29 73.5 -29t73.5 29q31 31 31 74t-31 74t-73.5 31t-73.5 -31zM639 643l-154 -65q-27 61 -90 61q-76 0 -112 -63.5t-31 -161.5q11 32 56.5 60t99.5 28q99 0 169 -71t70 -191q0 -112 -78.5 -182t-203.5 -70q-113 0 -190 74t-87 196
q-15 162 12 279q59 265 301 256q174 -9 238 -150z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="645" 
d="M47 778h586l-346 -778h-187l277 627h-330v151z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="708" 
d="M295 623q-20 -20 -20 -51t20 -54q23 -23 60 -23t57 23q23 23 23 54t-23 51q-20 23 -57 23t-60 -23zM223 260q0 -49 38.5 -84t92.5 -35t92.5 35t38.5 84q0 45 -35.5 80t-82.5 35h-27q-48 0 -82.5 -35t-34.5 -80zM115 575q0 91 69 154q70 64 170 64q98 0 168 -64
q72 -66 72 -154q0 -84 -57 -129q112 -66 112 -190q0 -111 -88 -190q-87 -78 -207 -78q-119 0 -209 78q-86 80 -86 190q0 123 113 190q-57 51 -57 129z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="696" 
d="M408 459q31 31 31 73.5t-31 73.5q-29 31 -72 31t-74 -31t-31 -73.5t31 -73.5q31 -29 74 -29t72 29zM70 139l153 66q27 -64 90 -64q76 0 112.5 65t31.5 163q-12 -32 -56 -60t-100 -28q-99 0 -169.5 71t-70.5 191q0 113 78 181.5t205 68.5q113 0 191 -74.5t88 -196.5
q15 -205 -41 -360q-32 -84 -103 -131t-172 -43q-170 9 -237 151z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="647" 
d="M532 258h105v-258h-154q-190 0 -276 104.5t-86 311.5v1061h287v-1067q-3 -152 124 -152z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="1042" 
d="M311 -61l62 143l-361 874h301l211 -538l211 538h303l-471 -1126q-72 -165 -157 -238q-81 -69 -236 -69q-64 0 -135 24v248q77 -14 111 -14q68 0 102 47q25 30 59 111z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="706" 
d="M588 258h104v-258h-153q-190 0 -276.5 104.5t-86.5 311.5v153l-158 -98v195l158 98v713h287v-535l194 123v-195l-194 -122v-338q-3 -152 125 -152z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="647" 
d="M123 1579l225 344h283l-303 -344h-205zM637 0h-154q-190 0 -276 104.5t-86 311.5v1061h287v-1067q-3 -152 124 -152h105v-258z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="647" 
d="M532 258h105v-258h-154q-190 0 -276 104.5t-86 311.5v1061h287v-1067q-3 -152 124 -152zM373 -588l-140 72q68 96 95 180q27 86 24 221h240q-24 -308 -219 -473z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="657" 
d="M641 942l-172 59q155 220 152 476h239q-13 -179 -75 -324t-144 -211zM637 0h-154q-190 0 -276 104.5t-86 311.5v1061h287v-1067q-3 -152 124 -152h105v-258z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM383 1104l225 344h283l-303 -344h-205z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM446 1104l-303 344h283l225 -344h-205z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM170 1112l223 344h283l225 -344h-205l-161 182l-160 -182h-205z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM211 1307q0 57 44 98t105 41q60 0 103 -41t43 -98q0 -60 -43 -101t-103 -41q-62 0 -105.5 41t-43.5 101zM573 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM424 1241q-19 0 -35.5 -24t-17.5 -64h-154q6 129 66 197.5t141 68.5q47 0 102 -43q56 -41 76 -41q19 0 35.5 22t17.5 62h154q-6 -127 -65.5 -194.5t-141.5 -67.5q-43 0 -102 41q-58 43 -76 43z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM289 1350q0 107 71 178q71 74 175 74q105 0 176 -74q73 -70 73 -178q0 -104 -73 -174q-72 -72 -176 -72q-103 0 -175 72q-71 71 -71 174zM422 1350q0 -47 33 -80t80 -33q49 0 82.5 33t33.5 80q0 51 -33 84.5t-83 33.5
q-48 0 -80.5 -33.5t-32.5 -84.5z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="1103" 
d="M475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM829 1202h-569v174h569v-174z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="1101" 
d="M473 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM268 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5
q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM270 1411h174q0 -108 101 -108q98 0 98 108h178q0 -121 -76 -194.5t-200 -73.5q-129 0 -202 76.5t-73 191.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="1103" 
d="M270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-139l-76 -207q-21 -65 31 -86q58 -15 80 39l2 53l157 8q3 -74 -16 -120q-26 -61 -79.5 -97t-115.5 -36q-92 0 -155 63q-64 61 -64 147q0 60 21 101l71 135v96q-101 -116 -276 -116q-163 0 -263 86
t-100 223q0 129 103 204.5t307 75.5q146 0 229 -51q0 213 -196 213q-142 0 -236 -94zM475 195q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="1042" 
d="M399 1104l226 344h282l-303 -344h-205zM373 82l-361 874h301l211 -538l211 538h303l-471 -1126q-72 -165 -157 -238q-81 -69 -236 -69q-64 0 -135 24v248q77 -14 111 -14q68 0 102 47q25 30 59 111z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="1042" 
d="M205 1307q0 57 44 98t105 41q60 0 103 -41t43 -98q0 -60 -43 -101t-103 -41q-62 0 -105.5 41t-43.5 101zM567 1307q0 57 44 98t106 41q60 0 102.5 -41t42.5 -98q0 -60 -42.5 -101t-102.5 -41q-62 0 -106 41t-44 101zM373 82l-361 874h301l211 -538l211 538h303
l-471 -1126q-72 -165 -157 -238q-81 -69 -236 -69q-64 0 -135 24v248q77 -14 111 -14q68 0 102 47q25 30 59 111z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="1779" 
d="M291 635l-174 162q128 180 420 180q223 0 342 -125q140 125 344 125q211 0 348 -137q141 -138 141 -359q0 -22 -4 -88h-700q10 -81 72 -124.5t163 -43.5q71 0 132 29t89 82l209 -137q-61 -108 -181.5 -163.5t-268.5 -55.5q-289 0 -377 153q-102 -153 -422 -153
q-163 0 -263 86t-100 223q0 133 109 213t324 80q114 0 206 -56q0 91 -47 148t-135 57q-146 0 -227 -96zM1012 578h413q-10 75 -65.5 112t-134.5 37q-183 0 -213 -149zM473 217q28 0 55.5 4t66 15.5t66.5 39.5t37 70q-83 51 -204 51q-142 0 -142 -96q0 -34 33 -59t88 -25z
" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="1777" 
d="M291 635l-174 162q128 180 420 180q223 0 342 -125q140 125 344 125q211 0 348 -137q141 -138 141 -359q0 -22 -4 -88h-700q10 -81 72 -124.5t163 -43.5q71 0 132 29t89 82l209 -137q-61 -108 -181.5 -163.5t-268.5 -55.5q-289 0 -377 153q-102 -153 -422 -153
q-163 0 -263 86t-100 223q0 133 109 213t324 80q114 0 206 -56q0 91 -47 148t-135 57q-146 0 -227 -96zM1012 578h413q-10 75 -65.5 112t-134.5 37q-183 0 -213 -149zM473 217q28 0 55.5 4t66 15.5t66.5 39.5t37 70q-83 51 -204 51q-142 0 -142 -96q0 -34 33 -59t88 -25z
M782 1104l226 344h282l-303 -344h-205z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="1042" 
d="M311 -61l62 143l-361 874h301l211 -538l211 538h303l-471 -1126q-72 -165 -157 -238q-81 -69 -236 -69q-64 0 -135 24v248q77 -14 111 -14q68 0 102 47q25 30 59 111zM162 1112l223 344h283l225 -344h-205l-162 182l-159 -182h-205z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="827" 
d="M532 258h105v-258h-154q-190 0 -276 104.5t-86 311.5v1061h287v-1067q-3 -152 124 -152zM494 629q0 63 45.5 107t111.5 44t112 -44t46 -107q0 -67 -45 -110.5t-113 -43.5t-112.5 43.5t-44.5 110.5z" />
    <glyph glyph-name="t.alt1" horiz-adv-x="800" 
d="M219 395v336h-174v225h174v201l287 92v-293h196v-225h-196v-342q-3 -131 125 -131h84v-258h-133q-193 0 -278 96.5t-85 298.5z" />
    <glyph glyph-name="tcaron.alt1" horiz-adv-x="800" 
d="M219 395v336h-174v225h174v201l287 92v-293h196v-225h-196v-342q-3 -131 125 -131h84v-258h-133q-193 0 -278 96.5t-85 298.5zM729 1069l-139 72q67 93 94 180q28 88 25 221h239q-24 -308 -219 -473z" />
    <glyph glyph-name="tbar.alt1" horiz-adv-x="802" 
d="M647 553v-158h-139v-6q0 -131 125 -131h84v-258h-133q-193 0 -278 96.5t-85 298.5h-108v158h108v178h-174v225h174v201l287 92v-293h197v-225h-197v-178h139z" />
    <glyph glyph-name="tcommaaccent.alt1" horiz-adv-x="800" 
d="M219 395v336h-174v225h174v201l287 92v-293h196v-225h-196v-342q-3 -131 125 -131h84v-258h-133q-193 0 -278 96.5t-85 298.5zM442 -588l-139 72q67 93 94 180q28 88 25 221h240q-24 -307 -220 -473z" />
    <glyph glyph-name="T.sc.alt1" horiz-adv-x="964" 
d="M43 926v233h879v-233h-308v-926h-262v926h-309z" />
    <glyph glyph-name="Tcaron.sc.alt1" horiz-adv-x="1003" 
d="M367 926h-326v233h922v-233h-326v-926h-270v926zM195 1556h178l131 -143l129 143h180l-190 -290h-242z" />
    <glyph glyph-name="Tbar.sc.alt1" horiz-adv-x="964" 
d="M354 500h-159v153h159v271h-309v231h875v-231h-308v-271h158v-153h-158v-500h-258v500z" />
    <glyph glyph-name="Tcommaaccent.sc.alt1" horiz-adv-x="964" 
d="M43 926v233h879v-233h-308v-926h-262v926h-309zM420 -455l-105 58q43 58 56.5 124t13.5 193h184q0 -247 -149 -375z" />
    <glyph glyph-name="aringacute.alt1" horiz-adv-x="1103" 
d="M496 1591l166 252h282l-246 -278q119 -73 119 -215q0 -103 -74 -174q-72 -72 -176 -72q-102 0 -174 72q-71 71 -71 174q0 84 49 150.5t125 90.5zM455 1350q0 -47 32.5 -80t79.5 -33q49 0 83 33t34 80q0 51 -33.5 84.5t-83.5 33.5q-48 0 -80 -33.5t-32 -84.5zM475 195
q76 0 139 34.5t86 106.5q-58 55 -184 55q-81 0 -121.5 -27.5t-40.5 -72.5q0 -42 32.5 -69t88.5 -27zM270 637l-151 160q128 180 420 180q192 0 319 -103t127 -307v-567h-283v96q-101 -116 -276 -116q-163 0 -263 86t-100 223q0 129 103 204.5t307 75.5q146 0 229 -51
q0 213 -196 213q-142 0 -236 -94z" />
    <hkern u1="&#x20;" g2="Tcaron.sc.alt1" k="63" />
    <hkern u1="&#x20;" g2="lslash.alt1" k="55" />
    <hkern u1="&#x20;" g2="Tcaron.sc" k="63" />
    <hkern u1="&#x20;" g2="V.sc" k="51" />
    <hkern u1="&#x20;" u2="&#x2019;" k="29" />
    <hkern u1="&#x20;" u2="&#x142;" k="76" />
    <hkern u1="&#x20;" u2="&#x110;" k="31" />
    <hkern u1="&#x20;" u2="&#xd0;" k="31" />
    <hkern u1="&#x20;" u2="v" k="49" />
    <hkern u1="&#x20;" u2="f" k="37" />
    <hkern u1="&#x20;" u2="V" k="63" />
    <hkern u1="&#x22;" u2="&#xee;" k="-14" />
    <hkern u1="&#x23;" g2="five.oldstyle" k="25" />
    <hkern u1="&#x23;" g2="four.oldstyle" k="115" />
    <hkern u1="&#x23;" g2="three.oldstyle" k="27" />
    <hkern u1="&#x23;" u2="&#x34;" k="31" />
    <hkern u1="&#x24;" g2="four.oldstyle" k="37" />
    <hkern u1="&#x24;" g2="three.oldstyle" k="31" />
    <hkern u1="&#x26;" u2="V" k="35" />
    <hkern u1="&#x27;" u2="&#xee;" k="-14" />
    <hkern u1="&#x28;" g2="Jcircumflex.sc" k="-141" />
    <hkern u1="&#x28;" g2="Idieresis.sc" k="8" />
    <hkern u1="&#x28;" g2="Icircumflex.sc" k="-20" />
    <hkern u1="&#x28;" g2="M.sc" k="57" />
    <hkern u1="&#x28;" g2="J.sc" k="-143" />
    <hkern u1="&#x28;" g2="eight.oldstyle" k="51" />
    <hkern u1="&#x28;" g2="six.oldstyle" k="49" />
    <hkern u1="&#x28;" g2="four.oldstyle" k="74" />
    <hkern u1="&#x28;" g2="two.oldstyle" k="82" />
    <hkern u1="&#x28;" g2="one.oldstyle" k="63" />
    <hkern u1="&#x28;" g2="zero.oldstyle" k="125" />
    <hkern u1="&#x28;" u2="&#x135;" k="-115" />
    <hkern u1="&#x28;" u2="&#x134;" k="-209" />
    <hkern u1="&#x28;" u2="&#x12b;" k="-12" />
    <hkern u1="&#x28;" u2="&#x127;" k="-23" />
    <hkern u1="&#x28;" u2="&#xef;" k="-57" />
    <hkern u1="&#x28;" u2="&#xee;" k="-39" />
    <hkern u1="&#x28;" u2="&#xec;" k="-68" />
    <hkern u1="&#x28;" u2="&#x7b;" k="29" />
    <hkern u1="&#x28;" u2="x" k="43" />
    <hkern u1="&#x28;" u2="v" k="31" />
    <hkern u1="&#x28;" u2="j" k="-115" />
    <hkern u1="&#x28;" u2="f" k="25" />
    <hkern u1="&#x28;" u2="M" k="35" />
    <hkern u1="&#x28;" u2="J" k="-209" />
    <hkern u1="&#x28;" u2="&#x38;" k="51" />
    <hkern u1="&#x28;" u2="&#x36;" k="49" />
    <hkern u1="&#x28;" u2="&#x35;" k="23" />
    <hkern u1="&#x28;" u2="&#x34;" k="129" />
    <hkern u1="&#x28;" u2="&#x30;" k="37" />
    <hkern u1="&#x28;" u2="&#x28;" k="53" />
    <hkern u1="&#x29;" u2="&#x7d;" k="49" />
    <hkern u1="&#x29;" u2="]" k="33" />
    <hkern u1="&#x29;" u2="&#x29;" k="53" />
    <hkern u1="&#x2a;" g2="Jcircumflex.sc" k="-25" />
    <hkern u1="&#x2a;" g2="Hbar.sc" k="-18" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-96" />
    <hkern u1="&#x2a;" u2="&#x12b;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-23" />
    <hkern u1="&#x2a;" u2="&#x127;" k="-47" />
    <hkern u1="&#x2a;" u2="&#x126;" k="-27" />
    <hkern u1="&#x2a;" u2="&#x110;" k="-53" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-68" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-125" />
    <hkern u1="&#x2a;" u2="&#xd0;" k="-53" />
    <hkern u1="&#x2b;" g2="four.oldstyle" k="90" />
    <hkern u1="&#x2b;" g2="three.oldstyle" k="53" />
    <hkern u1="&#x2b;" u2="&#x37;" k="29" />
    <hkern u1="&#x2d;" g2="four.oldstyle" k="61" />
    <hkern u1="&#x2d;" g2="three.oldstyle" k="57" />
    <hkern u1="&#x2d;" u2="&#x37;" k="49" />
    <hkern u1="&#x2f;" g2="M.sc" k="49" />
    <hkern u1="&#x2f;" g2="nine.oldstyle" k="96" />
    <hkern u1="&#x2f;" g2="eight.oldstyle" k="35" />
    <hkern u1="&#x2f;" g2="seven.oldstyle" k="23" />
    <hkern u1="&#x2f;" g2="six.oldstyle" k="27" />
    <hkern u1="&#x2f;" g2="five.oldstyle" k="96" />
    <hkern u1="&#x2f;" g2="four.oldstyle" k="197" />
    <hkern u1="&#x2f;" g2="three.oldstyle" k="98" />
    <hkern u1="&#x2f;" g2="two.oldstyle" k="63" />
    <hkern u1="&#x2f;" g2="one.oldstyle" k="61" />
    <hkern u1="&#x2f;" g2="zero.oldstyle" k="86" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-33" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-63" />
    <hkern u1="&#x2f;" u2="x" k="35" />
    <hkern u1="&#x2f;" u2="&#x38;" k="35" />
    <hkern u1="&#x2f;" u2="&#x36;" k="27" />
    <hkern u1="&#x2f;" u2="&#x34;" k="119" />
    <hkern u1="&#x2f;" u2="&#x30;" k="23" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="397" />
    <hkern u1="&#x30;" u2="&#x2044;" k="-115" />
    <hkern u1="&#x30;" u2="&#xc6;" k="23" />
    <hkern u1="&#x30;" u2="&#x7d;" k="31" />
    <hkern u1="&#x30;" u2="]" k="25" />
    <hkern u1="&#x30;" u2="\" k="20" />
    <hkern u1="&#x30;" u2="Y" k="43" />
    <hkern u1="&#x30;" u2="V" k="20" />
    <hkern u1="&#x30;" u2="&#x29;" k="37" />
    <hkern u1="&#x31;" u2="&#x2044;" k="-254" />
    <hkern u1="&#x32;" u2="&#x2044;" k="-287" />
    <hkern u1="&#x32;" u2="Y" k="29" />
    <hkern u1="&#x33;" u2="&#x2044;" k="-145" />
    <hkern u1="&#x33;" u2="&#x7d;" k="27" />
    <hkern u1="&#x33;" u2="]" k="20" />
    <hkern u1="&#x33;" u2="\" k="20" />
    <hkern u1="&#x33;" u2="Y" k="41" />
    <hkern u1="&#x33;" u2="V" k="23" />
    <hkern u1="&#x33;" u2="&#x29;" k="31" />
    <hkern u1="&#x34;" u2="&#x2044;" k="-166" />
    <hkern u1="&#x34;" u2="&#xb0;" k="20" />
    <hkern u1="&#x34;" u2="&#x7d;" k="41" />
    <hkern u1="&#x34;" u2="]" k="31" />
    <hkern u1="&#x34;" u2="\" k="27" />
    <hkern u1="&#x34;" u2="Y" k="41" />
    <hkern u1="&#x34;" u2="V" k="29" />
    <hkern u1="&#x34;" u2="T" k="25" />
    <hkern u1="&#x34;" u2="&#x29;" k="45" />
    <hkern u1="&#x35;" u2="&#x2044;" k="-135" />
    <hkern u1="&#x36;" u2="&#x2044;" k="-147" />
    <hkern u1="&#x36;" u2="Y" k="25" />
    <hkern u1="&#x37;" u2="&#x2212;" k="57" />
    <hkern u1="&#x37;" u2="&#xc6;" k="141" />
    <hkern u1="&#x37;" u2="&#xb7;" k="43" />
    <hkern u1="&#x37;" u2="&#xa2;" k="70" />
    <hkern u1="&#x37;" u2="Y" k="-53" />
    <hkern u1="&#x37;" u2="W" k="-27" />
    <hkern u1="&#x37;" u2="A" k="84" />
    <hkern u1="&#x37;" u2="&#x38;" k="23" />
    <hkern u1="&#x37;" u2="&#x34;" k="96" />
    <hkern u1="&#x37;" u2="&#x2f;" k="123" />
    <hkern u1="&#x37;" u2="&#x2d;" k="66" />
    <hkern u1="&#x37;" u2="&#x2b;" k="66" />
    <hkern u1="&#x37;" u2="&#x23;" k="55" />
    <hkern u1="&#x38;" u2="&#x2044;" k="-139" />
    <hkern u1="&#x38;" u2="&#x7d;" k="45" />
    <hkern u1="&#x38;" u2="]" k="33" />
    <hkern u1="&#x38;" u2="\" k="35" />
    <hkern u1="&#x38;" u2="Y" k="57" />
    <hkern u1="&#x38;" u2="V" k="29" />
    <hkern u1="&#x38;" u2="&#x29;" k="49" />
    <hkern u1="&#x39;" u2="&#x2044;" k="-100" />
    <hkern u1="&#x39;" u2="&#xc6;" k="31" />
    <hkern u1="&#x39;" u2="&#x7d;" k="29" />
    <hkern u1="&#x39;" u2="]" k="23" />
    <hkern u1="&#x39;" u2="Y" k="41" />
    <hkern u1="&#x39;" u2="&#x29;" k="35" />
    <hkern u1="&#x3d;" g2="four.oldstyle" k="45" />
    <hkern u1="&#x40;" g2="Y.sc" k="23" />
    <hkern u1="&#x40;" u2="Y" k="78" />
    <hkern u1="&#x40;" u2="V" k="27" />
    <hkern u1="&#x40;" u2="T" k="45" />
    <hkern u1="A" u2="&#x31;" k="20" />
    <hkern u1="B" g2="X.sc" k="10" />
    <hkern u1="B" g2="V.sc" k="12" />
    <hkern u1="B" g2="braceright.case" k="82" />
    <hkern u1="B" g2="bracketright.case" k="53" />
    <hkern u1="B" g2="parenright.case" k="74" />
    <hkern u1="B" u2="&#x7d;" k="51" />
    <hkern u1="B" u2="x" k="33" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="]" k="31" />
    <hkern u1="B" u2="X" k="25" />
    <hkern u1="B" u2="V" k="29" />
    <hkern u1="B" u2="&#x29;" k="49" />
    <hkern u1="C" u2="&#x135;" k="-74" />
    <hkern u1="C" u2="&#x127;" k="-41" />
    <hkern u1="C" u2="&#xef;" k="-23" />
    <hkern u1="C" u2="&#xee;" k="-94" />
    <hkern u1="C" u2="&#x34;" k="-31" />
    <hkern u1="D" u2="&#x141;" k="14" />
    <hkern u1="D" u2="&#x126;" k="14" />
    <hkern u1="E" u2="&#x135;" k="-14" />
    <hkern u1="E" u2="&#xee;" k="-37" />
    <hkern u1="F" g2="Jcircumflex.sc" k="-47" />
    <hkern u1="F" g2="Icircumflex.sc" k="-35" />
    <hkern u1="F" g2="M.sc" k="29" />
    <hkern u1="F" u2="&#x135;" k="-63" />
    <hkern u1="F" u2="&#x131;" k="57" />
    <hkern u1="F" u2="&#x12b;" k="-23" />
    <hkern u1="F" u2="&#x127;" k="-31" />
    <hkern u1="F" u2="&#xef;" k="-66" />
    <hkern u1="F" u2="&#xee;" k="-84" />
    <hkern u1="F" u2="&#xed;" k="25" />
    <hkern u1="F" u2="&#xec;" k="-63" />
    <hkern u1="F" u2="&#xdf;" k="20" />
    <hkern u1="F" u2="x" k="47" />
    <hkern u1="F" u2="v" k="14" />
    <hkern u1="F" u2="M" k="18" />
    <hkern u1="F" u2="&#x34;" k="53" />
    <hkern u1="F" u2="&#x2f;" k="88" />
    <hkern u1="F" u2="&#x2a;" k="-43" />
    <hkern u1="F" u2="&#x20;" k="49" />
    <hkern u1="K" u2="&#xef;" k="-23" />
    <hkern u1="K" u2="&#xec;" k="-25" />
    <hkern u1="K" u2="&#x32;" k="-10" />
    <hkern u1="L" g2="periodcentered.case" k="438" />
    <hkern u1="L" u2="&#xb7;" k="418" />
    <hkern u1="L" u2="&#x34;" k="-12" />
    <hkern u1="L" u2="&#x31;" k="41" />
    <hkern u1="M" g2="V.sc" k="14" />
    <hkern u1="M" u2="&#x2122;" k="16" />
    <hkern u1="M" u2="&#x7d;" k="29" />
    <hkern u1="M" u2="v" k="23" />
    <hkern u1="M" u2="V" k="29" />
    <hkern u1="M" u2="&#x29;" k="27" />
    <hkern u1="O" u2="&#x141;" k="14" />
    <hkern u1="O" u2="&#x126;" k="14" />
    <hkern u1="P" g2="M.sc" k="12" />
    <hkern u1="P" g2="braceright.case" k="76" />
    <hkern u1="P" g2="bracketright.case" k="49" />
    <hkern u1="P" g2="parenright.case" k="76" />
    <hkern u1="P" u2="&#x135;" k="-49" />
    <hkern u1="P" u2="&#x127;" k="-23" />
    <hkern u1="P" u2="&#xee;" k="-78" />
    <hkern u1="P" u2="X" k="41" />
    <hkern u1="P" u2="M" k="10" />
    <hkern u1="P" u2="&#x34;" k="47" />
    <hkern u1="P" u2="&#x2f;" k="82" />
    <hkern u1="P" u2="&#x20;" k="55" />
    <hkern u1="Q" u2="&#x141;" k="14" />
    <hkern u1="Q" u2="&#x126;" k="14" />
    <hkern u1="R" u2="&#x135;" k="-2" />
    <hkern u1="R" u2="&#xee;" k="-33" />
    <hkern u1="R" u2="&#x34;" k="25" />
    <hkern u1="S" u2="&#xee;" k="-12" />
    <hkern u1="T" g2="ycircumflex.alt1" k="125" />
    <hkern u1="T" g2="Jcircumflex.sc" k="-43" />
    <hkern u1="T" g2="Icircumflex.sc" k="-31" />
    <hkern u1="T" u2="&#x177;" k="129" />
    <hkern u1="T" u2="&#x15d;" k="92" />
    <hkern u1="T" u2="&#x159;" k="106" />
    <hkern u1="T" u2="&#x155;" k="115" />
    <hkern u1="T" u2="&#x135;" k="-61" />
    <hkern u1="T" u2="&#x131;" k="131" />
    <hkern u1="T" u2="&#x12b;" k="-18" />
    <hkern u1="T" u2="&#x127;" k="-27" />
    <hkern u1="T" u2="&#xef;" k="-63" />
    <hkern u1="T" u2="&#xee;" k="-82" />
    <hkern u1="T" u2="&#xed;" k="35" />
    <hkern u1="T" u2="&#xec;" k="-59" />
    <hkern u1="T" u2="&#xdf;" k="23" />
    <hkern u1="T" u2="&#x40;" k="53" />
    <hkern u1="T" u2="&#x34;" k="100" />
    <hkern u1="U" u2="&#xdf;" k="18" />
    <hkern u1="V" g2="Jcircumflex.sc" k="-29" />
    <hkern u1="V" g2="Idieresis.sc" k="-23" />
    <hkern u1="V" g2="Icircumflex.sc" k="-14" />
    <hkern u1="V" g2="M.sc" k="49" />
    <hkern u1="V" g2="at.case" k="20" />
    <hkern u1="V" u2="&#x131;" k="76" />
    <hkern u1="V" u2="&#x12d;" k="-31" />
    <hkern u1="V" u2="&#x12b;" k="-33" />
    <hkern u1="V" u2="&#x127;" k="-20" />
    <hkern u1="V" u2="&#xef;" k="-63" />
    <hkern u1="V" u2="&#xee;" k="-31" />
    <hkern u1="V" u2="&#xed;" k="35" />
    <hkern u1="V" u2="&#xec;" k="-84" />
    <hkern u1="V" u2="&#xdf;" k="31" />
    <hkern u1="V" u2="&#xae;" k="23" />
    <hkern u1="V" u2="x" k="51" />
    <hkern u1="V" u2="v" k="16" />
    <hkern u1="V" u2="M" k="31" />
    <hkern u1="V" u2="&#x40;" k="33" />
    <hkern u1="V" u2="&#x38;" k="27" />
    <hkern u1="V" u2="&#x36;" k="23" />
    <hkern u1="V" u2="&#x34;" k="70" />
    <hkern u1="V" u2="&#x30;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="92" />
    <hkern u1="V" u2="&#x20;" k="63" />
    <hkern u1="W" g2="Jcircumflex.sc" k="-59" />
    <hkern u1="W" g2="Imacron.sc" k="-23" />
    <hkern u1="W" g2="Itilde.sc" k="-29" />
    <hkern u1="W" g2="Idieresis.sc" k="-59" />
    <hkern u1="W" g2="Icircumflex.sc" k="-47" />
    <hkern u1="W" u2="&#x161;" k="76" />
    <hkern u1="W" u2="&#x159;" k="47" />
    <hkern u1="W" u2="&#x135;" k="-27" />
    <hkern u1="W" u2="&#x131;" k="72" />
    <hkern u1="W" u2="&#x12d;" k="-66" />
    <hkern u1="W" u2="&#x12b;" k="-66" />
    <hkern u1="W" u2="&#x127;" k="-63" />
    <hkern u1="W" u2="&#xef;" k="-100" />
    <hkern u1="W" u2="&#xee;" k="-53" />
    <hkern u1="W" u2="&#xed;" k="31" />
    <hkern u1="W" u2="&#xec;" k="-121" />
    <hkern u1="W" u2="&#xdf;" k="25" />
    <hkern u1="W" u2="&#x40;" k="23" />
    <hkern u1="W" u2="&#x34;" k="63" />
    <hkern u1="X" g2="V.sc" k="20" />
    <hkern u1="X" u2="&#xef;" k="-43" />
    <hkern u1="X" u2="&#xec;" k="-61" />
    <hkern u1="X" u2="&#xae;" k="27" />
    <hkern u1="X" u2="v" k="92" />
    <hkern u1="X" u2="&#x2f;" k="-10" />
    <hkern u1="Y" g2="ydieresis.alt1" k="98" />
    <hkern u1="Y" g2="atilde.alt1" k="178" />
    <hkern u1="Y" g2="adieresis.alt1" k="129" />
    <hkern u1="Y" g2="agrave.alt1" k="88" />
    <hkern u1="Y" g2="Idotaccent.sc" k="37" />
    <hkern u1="Y" g2="Ibreve.sc" k="-20" />
    <hkern u1="Y" g2="Lacute.sc" k="55" />
    <hkern u1="Y" g2="Jcircumflex.sc" k="-55" />
    <hkern u1="Y" g2="Imacron.sc" k="-49" />
    <hkern u1="Y" g2="Itilde.sc" k="-55" />
    <hkern u1="Y" g2="Hbar.sc" k="82" />
    <hkern u1="Y" g2="Idieresis.sc" k="-86" />
    <hkern u1="Y" g2="Icircumflex.sc" k="-47" />
    <hkern u1="Y" g2="Iacute.sc" k="53" />
    <hkern u1="Y" g2="Igrave.sc" k="14" />
    <hkern u1="Y" g2="Lslash.sc" k="82" />
    <hkern u1="Y" g2="at.case" k="41" />
    <hkern u1="Y" u2="&#x17e;" k="84" />
    <hkern u1="Y" u2="&#x161;" k="49" />
    <hkern u1="Y" u2="&#x15d;" k="156" />
    <hkern u1="Y" u2="&#x159;" k="18" />
    <hkern u1="Y" u2="&#x131;" k="154" />
    <hkern u1="Y" u2="&#x12d;" k="-92" />
    <hkern u1="Y" u2="&#x12b;" k="-82" />
    <hkern u1="Y" u2="&#x129;" k="-18" />
    <hkern u1="Y" u2="&#x127;" k="-57" />
    <hkern u1="Y" u2="&#x11b;" k="139" />
    <hkern u1="Y" u2="&#x10d;" k="133" />
    <hkern u1="Y" u2="&#xff;" k="102" />
    <hkern u1="Y" u2="&#xfc;" k="137" />
    <hkern u1="Y" u2="&#xf6;" k="156" />
    <hkern u1="Y" u2="&#xf2;" k="106" />
    <hkern u1="Y" u2="&#xf0;" k="156" />
    <hkern u1="Y" u2="&#xef;" k="-109" />
    <hkern u1="Y" u2="&#xee;" k="-31" />
    <hkern u1="Y" u2="&#xed;" k="74" />
    <hkern u1="Y" u2="&#xec;" k="-147" />
    <hkern u1="Y" u2="&#xeb;" k="152" />
    <hkern u1="Y" u2="&#xe8;" k="150" />
    <hkern u1="Y" u2="&#xe4;" k="193" />
    <hkern u1="Y" u2="&#xdf;" k="61" />
    <hkern u1="Y" u2="&#x40;" k="90" />
    <hkern u1="Y" u2="&#x39;" k="27" />
    <hkern u1="Y" u2="&#x38;" k="51" />
    <hkern u1="Y" u2="&#x37;" k="-14" />
    <hkern u1="Y" u2="&#x36;" k="49" />
    <hkern u1="Y" u2="&#x35;" k="25" />
    <hkern u1="Y" u2="&#x34;" k="141" />
    <hkern u1="Y" u2="&#x33;" k="23" />
    <hkern u1="Y" u2="&#x32;" k="29" />
    <hkern u1="Y" u2="&#x30;" k="41" />
    <hkern u1="Z" u2="&#x131;" k="20" />
    <hkern u1="Z" u2="&#xef;" k="-23" />
    <hkern u1="Z" u2="&#xee;" k="-31" />
    <hkern u1="Z" u2="&#xec;" k="-18" />
    <hkern u1="[" g2="Jcircumflex.sc" k="-137" />
    <hkern u1="[" g2="Icircumflex.sc" k="-16" />
    <hkern u1="[" g2="M.sc" k="43" />
    <hkern u1="[" g2="J.sc" k="-139" />
    <hkern u1="[" g2="eight.oldstyle" k="33" />
    <hkern u1="[" g2="six.oldstyle" k="31" />
    <hkern u1="[" g2="four.oldstyle" k="72" />
    <hkern u1="[" g2="two.oldstyle" k="55" />
    <hkern u1="[" g2="one.oldstyle" k="55" />
    <hkern u1="[" g2="zero.oldstyle" k="76" />
    <hkern u1="[" u2="&#x135;" k="-111" />
    <hkern u1="[" u2="&#x134;" k="-207" />
    <hkern u1="[" u2="&#x127;" k="-16" />
    <hkern u1="[" u2="&#xef;" k="-53" />
    <hkern u1="[" u2="&#xee;" k="-37" />
    <hkern u1="[" u2="&#xec;" k="-49" />
    <hkern u1="[" u2="&#x7b;" k="20" />
    <hkern u1="[" u2="x" k="33" />
    <hkern u1="[" u2="v" k="31" />
    <hkern u1="[" u2="j" k="-111" />
    <hkern u1="[" u2="f" k="25" />
    <hkern u1="[" u2="M" k="23" />
    <hkern u1="[" u2="J" k="-207" />
    <hkern u1="[" u2="&#x38;" k="33" />
    <hkern u1="[" u2="&#x36;" k="31" />
    <hkern u1="[" u2="&#x34;" k="61" />
    <hkern u1="[" u2="&#x30;" k="25" />
    <hkern u1="[" u2="&#x28;" k="33" />
    <hkern u1="\" g2="V.sc" k="63" />
    <hkern u1="\" g2="seven.oldstyle" k="20" />
    <hkern u1="\" g2="one.oldstyle" k="20" />
    <hkern u1="\" u2="&#x2019;" k="84" />
    <hkern u1="\" u2="v" k="53" />
    <hkern u1="\" u2="X" k="-10" />
    <hkern u1="\" u2="V" k="92" />
    <hkern u1="\" u2="&#x31;" k="35" />
    <hkern u1="c" u2="Z" k="12" />
    <hkern u1="c" u2="Y" k="190" />
    <hkern u1="c" u2="X" k="10" />
    <hkern u1="c" u2="W" k="76" />
    <hkern u1="c" u2="V" k="82" />
    <hkern u1="c" u2="T" k="188" />
    <hkern u1="c" u2="S" k="14" />
    <hkern u1="d" u2="Z" k="14" />
    <hkern u1="f" u2="&#x2122;" k="-20" />
    <hkern u1="f" u2="&#x135;" k="-76" />
    <hkern u1="f" u2="&#x12d;" k="-74" />
    <hkern u1="f" u2="&#x12b;" k="-86" />
    <hkern u1="f" u2="&#x129;" k="-68" />
    <hkern u1="f" u2="&#xef;" k="-131" />
    <hkern u1="f" u2="&#xee;" k="-76" />
    <hkern u1="f" u2="&#xec;" k="-127" />
    <hkern u1="f" u2="&#xc6;" k="49" />
    <hkern u1="f" u2="\" k="-20" />
    <hkern u1="f" u2="Y" k="-102" />
    <hkern u1="f" u2="X" k="-18" />
    <hkern u1="f" u2="W" k="-74" />
    <hkern u1="f" u2="V" k="-41" />
    <hkern u1="f" u2="T" k="-14" />
    <hkern u1="f" u2="&#x2a;" k="-16" />
    <hkern u1="f" u2="&#x29;" k="-10" />
    <hkern u1="f" u2="&#x20;" k="41" />
    <hkern u1="i" u2="&#xef;" k="-18" />
    <hkern u1="i" u2="&#xec;" k="-18" />
    <hkern u1="j" u2="&#xef;" k="-18" />
    <hkern u1="j" u2="&#xec;" k="-18" />
    <hkern u1="k" u2="Y" k="141" />
    <hkern u1="k" u2="W" k="53" />
    <hkern u1="k" u2="V" k="53" />
    <hkern u1="k" u2="T" k="164" />
    <hkern u1="k" u2="S" k="23" />
    <hkern u1="l" u2="&#xb7;" k="72" />
    <hkern u1="l" u2="Z" k="14" />
    <hkern u1="r" u2="&#xc6;" k="121" />
    <hkern u1="r" u2="Z" k="53" />
    <hkern u1="r" u2="Y" k="127" />
    <hkern u1="r" u2="X" k="98" />
    <hkern u1="r" u2="W" k="25" />
    <hkern u1="r" u2="V" k="27" />
    <hkern u1="r" u2="T" k="158" />
    <hkern u1="r" u2="S" k="10" />
    <hkern u1="r" u2="M" k="29" />
    <hkern u1="s" u2="Z" k="12" />
    <hkern u1="s" u2="Y" k="186" />
    <hkern u1="s" u2="X" k="12" />
    <hkern u1="s" u2="W" k="94" />
    <hkern u1="s" u2="V" k="102" />
    <hkern u1="s" u2="T" k="143" />
    <hkern u1="s" u2="S" k="10" />
    <hkern u1="t" u2="&#xc6;" k="53" />
    <hkern u1="t" u2="Z" k="35" />
    <hkern u1="t" u2="Y" k="84" />
    <hkern u1="t" u2="X" k="61" />
    <hkern u1="t" u2="W" k="20" />
    <hkern u1="t" u2="V" k="25" />
    <hkern u1="t" u2="T" k="72" />
    <hkern u1="t" u2="M" k="18" />
    <hkern u1="v" u2="&#x2122;" k="16" />
    <hkern u1="v" u2="&#xc6;" k="88" />
    <hkern u1="v" u2="&#x7d;" k="31" />
    <hkern u1="v" u2="]" k="29" />
    <hkern u1="v" u2="Z" k="33" />
    <hkern u1="v" u2="Y" k="117" />
    <hkern u1="v" u2="X" k="92" />
    <hkern u1="v" u2="W" k="16" />
    <hkern u1="v" u2="V" k="16" />
    <hkern u1="v" u2="T" k="143" />
    <hkern u1="v" u2="M" k="23" />
    <hkern u1="v" u2="&#x2f;" k="53" />
    <hkern u1="v" u2="&#x29;" k="31" />
    <hkern u1="v" u2="&#x20;" k="49" />
    <hkern u1="w" u2="&#xc6;" k="78" />
    <hkern u1="w" u2="Z" k="33" />
    <hkern u1="w" u2="Y" k="115" />
    <hkern u1="w" u2="X" k="82" />
    <hkern u1="w" u2="W" k="20" />
    <hkern u1="w" u2="V" k="23" />
    <hkern u1="w" u2="T" k="143" />
    <hkern u1="w" u2="M" k="20" />
    <hkern u1="x" u2="&#x2122;" k="37" />
    <hkern u1="x" u2="&#x7d;" k="51" />
    <hkern u1="x" u2="]" k="33" />
    <hkern u1="x" u2="\" k="35" />
    <hkern u1="x" u2="Y" k="139" />
    <hkern u1="x" u2="W" k="51" />
    <hkern u1="x" u2="V" k="51" />
    <hkern u1="x" u2="T" k="152" />
    <hkern u1="x" u2="S" k="12" />
    <hkern u1="x" u2="&#x29;" k="45" />
    <hkern u1="y" u2="&#xc6;" k="88" />
    <hkern u1="y" u2="Z" k="35" />
    <hkern u1="y" u2="Y" k="117" />
    <hkern u1="y" u2="X" k="92" />
    <hkern u1="y" u2="W" k="16" />
    <hkern u1="y" u2="V" k="18" />
    <hkern u1="y" u2="T" k="143" />
    <hkern u1="y" u2="M" k="20" />
    <hkern u1="z" u2="Z" k="10" />
    <hkern u1="z" u2="Y" k="160" />
    <hkern u1="z" u2="W" k="59" />
    <hkern u1="z" u2="V" k="61" />
    <hkern u1="z" u2="T" k="152" />
    <hkern u1="z" u2="S" k="10" />
    <hkern u1="&#x7b;" g2="Jcircumflex.sc" k="-139" />
    <hkern u1="&#x7b;" g2="Idieresis.sc" k="14" />
    <hkern u1="&#x7b;" g2="Icircumflex.sc" k="-18" />
    <hkern u1="&#x7b;" g2="M.sc" k="59" />
    <hkern u1="&#x7b;" g2="J.sc" k="-141" />
    <hkern u1="&#x7b;" g2="eight.oldstyle" k="45" />
    <hkern u1="&#x7b;" g2="six.oldstyle" k="43" />
    <hkern u1="&#x7b;" g2="four.oldstyle" k="102" />
    <hkern u1="&#x7b;" g2="two.oldstyle" k="88" />
    <hkern u1="&#x7b;" g2="one.oldstyle" k="63" />
    <hkern u1="&#x7b;" g2="zero.oldstyle" k="121" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-115" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-207" />
    <hkern u1="&#x7b;" u2="&#x12b;" k="-12" />
    <hkern u1="&#x7b;" u2="&#x127;" k="-20" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-57" />
    <hkern u1="&#x7b;" u2="&#xee;" k="-37" />
    <hkern u1="&#x7b;" u2="&#xec;" k="-53" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="27" />
    <hkern u1="&#x7b;" u2="x" k="51" />
    <hkern u1="&#x7b;" u2="v" k="31" />
    <hkern u1="&#x7b;" u2="j" k="-115" />
    <hkern u1="&#x7b;" u2="f" k="27" />
    <hkern u1="&#x7b;" u2="M" k="35" />
    <hkern u1="&#x7b;" u2="J" k="-207" />
    <hkern u1="&#x7b;" u2="&#x38;" k="45" />
    <hkern u1="&#x7b;" u2="&#x36;" k="43" />
    <hkern u1="&#x7b;" u2="&#x35;" k="20" />
    <hkern u1="&#x7b;" u2="&#x34;" k="117" />
    <hkern u1="&#x7b;" u2="&#x30;" k="33" />
    <hkern u1="&#x7b;" u2="&#x28;" k="49" />
    <hkern u1="&#x7c;" g2="Jcircumflex.sc" k="-49" />
    <hkern u1="&#x7c;" g2="J.sc" k="-51" />
    <hkern u1="&#x7c;" u2="&#x135;" k="-33" />
    <hkern u1="&#x7c;" u2="&#x134;" k="-109" />
    <hkern u1="&#x7c;" u2="j" k="-33" />
    <hkern u1="&#x7c;" u2="J" k="-109" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="27" />
    <hkern u1="&#x7d;" u2="]" k="20" />
    <hkern u1="&#x7d;" u2="&#x29;" k="29" />
    <hkern u1="&#xa1;" g2="Jcircumflex.sc" k="-86" />
    <hkern u1="&#xa1;" g2="J.sc" k="-88" />
    <hkern u1="&#xa1;" u2="&#x135;" k="-63" />
    <hkern u1="&#xa1;" u2="&#x134;" k="-147" />
    <hkern u1="&#xa1;" u2="j" k="-63" />
    <hkern u1="&#xa1;" u2="V" k="37" />
    <hkern u1="&#xa1;" u2="J" k="-147" />
    <hkern u1="&#xa3;" g2="three.oldstyle" k="27" />
    <hkern u1="&#xae;" u2="X" k="33" />
    <hkern u1="&#xae;" u2="V" k="25" />
    <hkern u1="&#xb0;" g2="nine.oldstyle" k="37" />
    <hkern u1="&#xb0;" g2="five.oldstyle" k="29" />
    <hkern u1="&#xb0;" g2="four.oldstyle" k="145" />
    <hkern u1="&#xb0;" g2="three.oldstyle" k="31" />
    <hkern u1="&#xb0;" u2="&#x34;" k="82" />
    <hkern u1="&#xb7;" g2="l.alt1" k="72" />
    <hkern u1="&#xb7;" g2="four.oldstyle" k="113" />
    <hkern u1="&#xb7;" u2="l" k="72" />
    <hkern u1="&#xb7;" u2="&#x37;" k="31" />
    <hkern u1="&#xb7;" u2="&#x34;" k="20" />
    <hkern u1="&#xb7;" u2="&#x33;" k="33" />
    <hkern u1="&#xbf;" g2="Jcircumflex.sc" k="-109" />
    <hkern u1="&#xbf;" g2="V.sc" k="57" />
    <hkern u1="&#xbf;" g2="J.sc" k="-115" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-80" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-158" />
    <hkern u1="&#xbf;" u2="v" k="33" />
    <hkern u1="&#xbf;" u2="j" k="-80" />
    <hkern u1="&#xbf;" u2="f" k="20" />
    <hkern u1="&#xbf;" u2="V" k="88" />
    <hkern u1="&#xbf;" u2="J" k="-158" />
    <hkern u1="&#xce;" g2="braceright.case" k="-53" />
    <hkern u1="&#xce;" g2="bracketright.case" k="-51" />
    <hkern u1="&#xce;" g2="parenright.case" k="-53" />
    <hkern u1="&#xd0;" u2="&#x141;" k="14" />
    <hkern u1="&#xd0;" u2="&#x126;" k="14" />
    <hkern u1="&#xd2;" u2="&#x141;" k="14" />
    <hkern u1="&#xd2;" u2="&#x126;" k="14" />
    <hkern u1="&#xd3;" u2="&#x141;" k="14" />
    <hkern u1="&#xd3;" u2="&#x126;" k="14" />
    <hkern u1="&#xd4;" u2="&#x141;" k="14" />
    <hkern u1="&#xd4;" u2="&#x126;" k="14" />
    <hkern u1="&#xd5;" u2="&#x141;" k="14" />
    <hkern u1="&#xd5;" u2="&#x126;" k="14" />
    <hkern u1="&#xd6;" u2="&#x141;" k="14" />
    <hkern u1="&#xd6;" u2="&#x126;" k="14" />
    <hkern u1="&#xd9;" u2="&#xdf;" k="18" />
    <hkern u1="&#xda;" u2="&#xdf;" k="18" />
    <hkern u1="&#xdb;" u2="&#xdf;" k="18" />
    <hkern u1="&#xdc;" u2="&#xdf;" k="18" />
    <hkern u1="&#xdd;" g2="Idotaccent.sc" k="37" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="156" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="61" />
    <hkern u1="&#xde;" g2="AE.sc" k="70" />
    <hkern u1="&#xde;" g2="Y.sc" k="12" />
    <hkern u1="&#xde;" g2="X.sc" k="14" />
    <hkern u1="&#xde;" g2="A.sc" k="27" />
    <hkern u1="&#xde;" g2="braceright.case" k="96" />
    <hkern u1="&#xde;" g2="bracketright.case" k="53" />
    <hkern u1="&#xde;" g2="parenright.case" k="102" />
    <hkern u1="&#xde;" u2="&#x2122;" k="27" />
    <hkern u1="&#xde;" u2="&#x7d;" k="55" />
    <hkern u1="&#xde;" u2="]" k="35" />
    <hkern u1="&#xde;" u2="\" k="23" />
    <hkern u1="&#xde;" u2="X" k="61" />
    <hkern u1="&#xde;" u2="V" k="29" />
    <hkern u1="&#xde;" u2="&#x2f;" k="31" />
    <hkern u1="&#xde;" u2="&#x29;" k="57" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="25" />
    <hkern u1="&#xdf;" u2="&#xc6;" k="10" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="45" />
    <hkern u1="&#xdf;" u2="x" k="29" />
    <hkern u1="&#xdf;" u2="v" k="23" />
    <hkern u1="&#xdf;" u2="]" k="33" />
    <hkern u1="&#xdf;" u2="\" k="31" />
    <hkern u1="&#xdf;" u2="Z" k="23" />
    <hkern u1="&#xdf;" u2="Y" k="104" />
    <hkern u1="&#xdf;" u2="X" k="37" />
    <hkern u1="&#xdf;" u2="W" k="55" />
    <hkern u1="&#xdf;" u2="V" k="63" />
    <hkern u1="&#xdf;" u2="U" k="25" />
    <hkern u1="&#xdf;" u2="T" k="53" />
    <hkern u1="&#xdf;" u2="S" k="10" />
    <hkern u1="&#xdf;" u2="M" k="12" />
    <hkern u1="&#xdf;" u2="&#x29;" k="45" />
    <hkern u1="&#xed;" g2="lcaron.alt1" k="-25" />
    <hkern u1="&#xed;" g2="l.alt1" k="-25" />
    <hkern u1="&#xed;" u2="&#x2122;" k="-68" />
    <hkern u1="&#xed;" u2="&#x161;" k="-29" />
    <hkern u1="&#xed;" u2="&#x159;" k="-59" />
    <hkern u1="&#xed;" u2="&#x13e;" k="-14" />
    <hkern u1="&#xed;" u2="&#x133;" k="-20" />
    <hkern u1="&#xed;" u2="&#xfe;" k="-14" />
    <hkern u1="&#xed;" u2="&#x7d;" k="-57" />
    <hkern u1="&#xed;" u2="l" k="-14" />
    <hkern u1="&#xed;" u2="k" k="-14" />
    <hkern u1="&#xed;" u2="j" k="-20" />
    <hkern u1="&#xed;" u2="i" k="-20" />
    <hkern u1="&#xed;" u2="h" k="-14" />
    <hkern u1="&#xed;" u2="b" k="-14" />
    <hkern u1="&#xed;" u2="]" k="-53" />
    <hkern u1="&#xed;" u2="\" k="-68" />
    <hkern u1="&#xed;" u2="&#x29;" k="-74" />
    <hkern u1="&#xee;" g2="l.alt1" k="-27" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-49" />
    <hkern u1="&#xee;" u2="&#xfe;" k="-16" />
    <hkern u1="&#xee;" u2="&#x7d;" k="-37" />
    <hkern u1="&#xee;" u2="l" k="-16" />
    <hkern u1="&#xee;" u2="k" k="-16" />
    <hkern u1="&#xee;" u2="h" k="-16" />
    <hkern u1="&#xee;" u2="b" k="-16" />
    <hkern u1="&#xee;" u2="]" k="-37" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-70" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-127" />
    <hkern u1="&#xee;" u2="&#x29;" k="-39" />
    <hkern u1="&#xee;" u2="&#x27;" k="-16" />
    <hkern u1="&#xee;" u2="&#x22;" k="-16" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-59" />
    <hkern u1="&#xef;" u2="&#x133;" k="-14" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-49" />
    <hkern u1="&#xef;" u2="j" k="-12" />
    <hkern u1="&#xef;" u2="i" k="-14" />
    <hkern u1="&#xef;" u2="]" k="-45" />
    <hkern u1="&#xef;" u2="\" k="-33" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-33" />
    <hkern u1="&#xef;" u2="&#x29;" k="-51" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="45" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="20" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="74" />
    <hkern u1="&#xf0;" u2="x" k="35" />
    <hkern u1="&#xf0;" u2="v" k="20" />
    <hkern u1="&#xf0;" u2="]" k="59" />
    <hkern u1="&#xf0;" u2="\" k="61" />
    <hkern u1="&#xf0;" u2="Z" k="31" />
    <hkern u1="&#xf0;" u2="Y" k="137" />
    <hkern u1="&#xf0;" u2="X" k="49" />
    <hkern u1="&#xf0;" u2="W" k="94" />
    <hkern u1="&#xf0;" u2="V" k="98" />
    <hkern u1="&#xf0;" u2="U" k="25" />
    <hkern u1="&#xf0;" u2="T" k="117" />
    <hkern u1="&#xf0;" u2="S" k="12" />
    <hkern u1="&#xf0;" u2="M" k="16" />
    <hkern u1="&#xf0;" u2="A" k="12" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="20" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="35" />
    <hkern u1="&#xf0;" u2="&#x29;" k="72" />
    <hkern u1="&#x104;" g2="y.alt1" k="-55" />
    <hkern u1="&#x104;" g2="J.sc" k="-233" />
    <hkern u1="&#x104;" g2="braceright.case" k="-39" />
    <hkern u1="&#x104;" g2="bracketright.case" k="-37" />
    <hkern u1="&#x104;" g2="parenright.case" k="-41" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-111" />
    <hkern u1="&#x104;" u2="&#x201a;" k="-111" />
    <hkern u1="&#x104;" u2="&#x134;" k="-295" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-37" />
    <hkern u1="&#x104;" u2="&#x7d;" k="-39" />
    <hkern u1="&#x104;" u2="j" k="-211" />
    <hkern u1="&#x104;" u2="g" k="-2" />
    <hkern u1="&#x104;" u2="]" k="-35" />
    <hkern u1="&#x104;" u2="J" k="-295" />
    <hkern u1="&#x104;" u2="&#x3b;" k="-74" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-92" />
    <hkern u1="&#x104;" u2="&#x29;" k="-39" />
    <hkern u1="&#x105;" u2="&#x201e;" k="-18" />
    <hkern u1="&#x105;" u2="&#x201a;" k="-18" />
    <hkern u1="&#x105;" u2="j" k="-98" />
    <hkern u1="&#x10e;" u2="&#x141;" k="14" />
    <hkern u1="&#x10e;" u2="&#x126;" k="14" />
    <hkern u1="&#x10f;" g2="t.alt1" k="-31" />
    <hkern u1="&#x10f;" g2="y.alt1" k="-61" />
    <hkern u1="&#x10f;" g2="l.alt1" k="-158" />
    <hkern u1="&#x10f;" g2="f_j" k="-29" />
    <hkern u1="&#x10f;" g2="f_f_j" k="-29" />
    <hkern u1="&#x10f;" g2="f_f_l" k="-29" />
    <hkern u1="&#x10f;" g2="f_f_i" k="-29" />
    <hkern u1="&#x10f;" g2="fl" k="-29" />
    <hkern u1="&#x10f;" g2="fi" k="-29" />
    <hkern u1="&#x10f;" g2="f_f" k="-29" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-227" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-23" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-31" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-23" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-31" />
    <hkern u1="&#x10f;" u2="&#x17f;" k="-29" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-102" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-174" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-147" />
    <hkern u1="&#x10f;" u2="&#xf0;" k="-33" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-70" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-217" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-135" />
    <hkern u1="&#x10f;" u2="y" k="-68" />
    <hkern u1="&#x10f;" u2="w" k="-57" />
    <hkern u1="&#x10f;" u2="v" k="-66" />
    <hkern u1="&#x10f;" u2="t" k="-45" />
    <hkern u1="&#x10f;" u2="l" k="-147" />
    <hkern u1="&#x10f;" u2="k" k="-147" />
    <hkern u1="&#x10f;" u2="j" k="-172" />
    <hkern u1="&#x10f;" u2="i" k="-174" />
    <hkern u1="&#x10f;" u2="h" k="-147" />
    <hkern u1="&#x10f;" u2="f" k="-29" />
    <hkern u1="&#x10f;" u2="b" k="-147" />
    <hkern u1="&#x10f;" u2="]" k="-213" />
    <hkern u1="&#x10f;" u2="\" k="-227" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-129" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-186" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-219" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-156" />
    <hkern u1="&#x10f;" u2="&#x26;" k="-47" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-156" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-152" />
    <hkern u1="&#x110;" u2="&#x141;" k="14" />
    <hkern u1="&#x110;" u2="&#x126;" k="14" />
    <hkern u1="&#x111;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x111;" u2="&#x7d;" k="-20" />
    <hkern u1="&#x111;" u2="]" k="-16" />
    <hkern u1="&#x111;" u2="&#x2a;" k="-72" />
    <hkern u1="&#x111;" u2="&#x29;" k="-23" />
    <hkern u1="&#x118;" g2="y.alt1" k="-111" />
    <hkern u1="&#x118;" g2="J.sc" k="-291" />
    <hkern u1="&#x118;" g2="braceright.case" k="-94" />
    <hkern u1="&#x118;" g2="bracketright.case" k="-94" />
    <hkern u1="&#x118;" g2="parenright.case" k="-96" />
    <hkern u1="&#x118;" u2="&#x201e;" k="-168" />
    <hkern u1="&#x118;" u2="&#x201a;" k="-168" />
    <hkern u1="&#x118;" u2="&#x134;" k="-350" />
    <hkern u1="&#x118;" u2="&#x12e;" k="-92" />
    <hkern u1="&#x118;" u2="&#xfe;" k="-16" />
    <hkern u1="&#x118;" u2="&#x7d;" k="-96" />
    <hkern u1="&#x118;" u2="&#x7c;" k="-14" />
    <hkern u1="&#x118;" u2="p" k="-18" />
    <hkern u1="&#x118;" u2="j" k="-266" />
    <hkern u1="&#x118;" u2="g" k="-39" />
    <hkern u1="&#x118;" u2="]" k="-90" />
    <hkern u1="&#x118;" u2="J" k="-350" />
    <hkern u1="&#x118;" u2="&#x3b;" k="-129" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-150" />
    <hkern u1="&#x118;" u2="&#x29;" k="-96" />
    <hkern u1="&#x119;" u2="&#x201e;" k="-27" />
    <hkern u1="&#x119;" u2="&#x201a;" k="-27" />
    <hkern u1="&#x119;" u2="&#x7d;" k="66" />
    <hkern u1="&#x119;" u2="&#x29;" k="66" />
    <hkern u1="&#x126;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x126;" u2="&#x152;" k="14" />
    <hkern u1="&#x126;" u2="&#x150;" k="14" />
    <hkern u1="&#x126;" u2="&#x14e;" k="14" />
    <hkern u1="&#x126;" u2="&#x14c;" k="14" />
    <hkern u1="&#x126;" u2="&#x141;" k="14" />
    <hkern u1="&#x126;" u2="&#x126;" k="14" />
    <hkern u1="&#x126;" u2="&#x122;" k="14" />
    <hkern u1="&#x126;" u2="&#x120;" k="14" />
    <hkern u1="&#x126;" u2="&#x11e;" k="14" />
    <hkern u1="&#x126;" u2="&#x11c;" k="14" />
    <hkern u1="&#x126;" u2="&#x10c;" k="14" />
    <hkern u1="&#x126;" u2="&#x10a;" k="14" />
    <hkern u1="&#x126;" u2="&#x108;" k="14" />
    <hkern u1="&#x126;" u2="&#x106;" k="14" />
    <hkern u1="&#x126;" u2="&#xd6;" k="14" />
    <hkern u1="&#x126;" u2="&#xd5;" k="14" />
    <hkern u1="&#x126;" u2="&#xd4;" k="14" />
    <hkern u1="&#x126;" u2="&#xd3;" k="14" />
    <hkern u1="&#x126;" u2="&#xd2;" k="14" />
    <hkern u1="&#x126;" u2="&#xc7;" k="14" />
    <hkern u1="&#x126;" u2="u" k="20" />
    <hkern u1="&#x126;" u2="Q" k="14" />
    <hkern u1="&#x126;" u2="O" k="14" />
    <hkern u1="&#x126;" u2="G" k="14" />
    <hkern u1="&#x126;" u2="C" k="14" />
    <hkern u1="&#x126;" u2="&#x2a;" k="-29" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-53" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-43" />
    <hkern u1="&#x129;" u2="]" k="-39" />
    <hkern u1="&#x129;" u2="\" k="-53" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-35" />
    <hkern u1="&#x129;" u2="&#x29;" k="-45" />
    <hkern u1="&#x12b;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-14" />
    <hkern u1="&#x12e;" g2="J.sc" k="-74" />
    <hkern u1="&#x12e;" u2="&#x134;" k="-135" />
    <hkern u1="&#x12e;" u2="j" k="-49" />
    <hkern u1="&#x12e;" u2="J" k="-135" />
    <hkern u1="&#x12f;" u2="&#x201e;" k="-18" />
    <hkern u1="&#x12f;" u2="&#x201a;" k="-18" />
    <hkern u1="&#x12f;" u2="j" k="-98" />
    <hkern u1="&#x133;" u2="&#xef;" k="-18" />
    <hkern u1="&#x133;" u2="&#xec;" k="-18" />
    <hkern u1="&#x134;" g2="braceright.case" k="-51" />
    <hkern u1="&#x134;" g2="bracketright.case" k="-49" />
    <hkern u1="&#x134;" g2="parenright.case" k="-51" />
    <hkern u1="&#x135;" g2="l.alt1" k="-49" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-61" />
    <hkern u1="&#x135;" u2="&#xfe;" k="-39" />
    <hkern u1="&#x135;" u2="&#xdf;" k="-27" />
    <hkern u1="&#x135;" u2="&#x7d;" k="-53" />
    <hkern u1="&#x135;" u2="&#x7c;" k="-29" />
    <hkern u1="&#x135;" u2="l" k="-39" />
    <hkern u1="&#x135;" u2="k" k="-39" />
    <hkern u1="&#x135;" u2="h" k="-39" />
    <hkern u1="&#x135;" u2="b" k="-39" />
    <hkern u1="&#x135;" u2="]" k="-51" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-92" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-150" />
    <hkern u1="&#x135;" u2="&#x29;" k="-55" />
    <hkern u1="&#x135;" u2="&#x27;" k="-39" />
    <hkern u1="&#x135;" u2="&#x22;" k="-39" />
    <hkern u1="&#x135;" u2="&#x21;" k="-31" />
    <hkern u1="&#x13d;" g2="Y.sc" k="49" />
    <hkern u1="&#x13d;" g2="W.sc" k="68" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="31" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="-47" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x178;" k="-47" />
    <hkern u1="&#x13d;" u2="&#x176;" k="-47" />
    <hkern u1="&#x13d;" u2="&#x174;" k="-20" />
    <hkern u1="&#x13d;" u2="&#x166;" k="39" />
    <hkern u1="&#x13d;" u2="&#x164;" k="39" />
    <hkern u1="&#x13d;" u2="&#x162;" k="39" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="-47" />
    <hkern u1="&#x13d;" u2="&#x7d;" k="53" />
    <hkern u1="&#x13d;" u2="\" k="41" />
    <hkern u1="&#x13d;" u2="Y" k="-47" />
    <hkern u1="&#x13d;" u2="W" k="-20" />
    <hkern u1="&#x13d;" u2="V" k="14" />
    <hkern u1="&#x13d;" u2="T" k="39" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="66" />
    <hkern u1="&#x13d;" u2="&#x29;" k="51" />
    <hkern u1="&#x13d;" u2="&#x27;" k="115" />
    <hkern u1="&#x13d;" u2="&#x22;" k="115" />
    <hkern u1="&#x13e;" g2="t.alt1" k="-35" />
    <hkern u1="&#x13e;" g2="y.alt1" k="-66" />
    <hkern u1="&#x13e;" g2="l.alt1" k="-168" />
    <hkern u1="&#x13e;" g2="f_j" k="-33" />
    <hkern u1="&#x13e;" g2="f_f_j" k="-33" />
    <hkern u1="&#x13e;" g2="f_f_l" k="-33" />
    <hkern u1="&#x13e;" g2="f_f_i" k="-33" />
    <hkern u1="&#x13e;" g2="fl" k="-33" />
    <hkern u1="&#x13e;" g2="fi" k="-33" />
    <hkern u1="&#x13e;" g2="f_f" k="-33" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-229" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="-27" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="-25" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-27" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-25" />
    <hkern u1="&#x13e;" u2="&#x17f;" k="-33" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-131" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-166" />
    <hkern u1="&#x13e;" u2="&#x148;" k="-68" />
    <hkern u1="&#x13e;" u2="&#x133;" k="-174" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-84" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-158" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="-35" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-63" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-219" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-145" />
    <hkern u1="&#x13e;" u2="y" k="-72" />
    <hkern u1="&#x13e;" u2="w" k="-61" />
    <hkern u1="&#x13e;" u2="v" k="-70" />
    <hkern u1="&#x13e;" u2="t" k="-45" />
    <hkern u1="&#x13e;" u2="l" k="-158" />
    <hkern u1="&#x13e;" u2="k" k="-158" />
    <hkern u1="&#x13e;" u2="j" k="-172" />
    <hkern u1="&#x13e;" u2="i" k="-174" />
    <hkern u1="&#x13e;" u2="h" k="-158" />
    <hkern u1="&#x13e;" u2="f" k="-33" />
    <hkern u1="&#x13e;" u2="b" k="-158" />
    <hkern u1="&#x13e;" u2="]" k="-215" />
    <hkern u1="&#x13e;" u2="\" k="-229" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-123" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-180" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-223" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-158" />
    <hkern u1="&#x13e;" u2="&#x26;" k="-43" />
    <hkern u1="&#x13e;" u2="&#x22;" k="-158" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-154" />
    <hkern u1="&#x142;" g2="t.alt1" k="-33" />
    <hkern u1="&#x142;" g2="y.alt1" k="-63" />
    <hkern u1="&#x142;" g2="f_j" k="-31" />
    <hkern u1="&#x142;" g2="f_f_j" k="-31" />
    <hkern u1="&#x142;" g2="f_f_l" k="-31" />
    <hkern u1="&#x142;" g2="f_f_i" k="-31" />
    <hkern u1="&#x142;" g2="fl" k="-31" />
    <hkern u1="&#x142;" g2="fi" k="-31" />
    <hkern u1="&#x142;" g2="f_f" k="-31" />
    <hkern u1="&#x142;" u2="&#x17f;" k="-31" />
    <hkern u1="&#x142;" u2="y" k="-70" />
    <hkern u1="&#x142;" u2="w" k="-59" />
    <hkern u1="&#x142;" u2="v" k="-68" />
    <hkern u1="&#x142;" u2="t" k="-31" />
    <hkern u1="&#x142;" u2="f" k="-31" />
    <hkern u1="&#x142;" u2="&#x2a;" k="-76" />
    <hkern u1="&#x142;" u2="&#x20;" k="25" />
    <hkern u1="&#x14c;" u2="&#x141;" k="14" />
    <hkern u1="&#x14c;" u2="&#x126;" k="14" />
    <hkern u1="&#x14e;" u2="&#x141;" k="14" />
    <hkern u1="&#x14e;" u2="&#x126;" k="14" />
    <hkern u1="&#x150;" u2="&#x141;" k="14" />
    <hkern u1="&#x150;" u2="&#x126;" k="14" />
    <hkern u1="&#x151;" u2="&#x7d;" k="74" />
    <hkern u1="&#x151;" u2="\" k="61" />
    <hkern u1="&#x151;" u2="&#x29;" k="59" />
    <hkern u1="&#x162;" g2="Icircumflex.sc" k="-31" />
    <hkern u1="&#x162;" u2="&#xee;" k="-82" />
    <hkern u1="&#x162;" u2="&#xdf;" k="23" />
    <hkern u1="&#x164;" u2="&#xdf;" k="23" />
    <hkern u1="&#x165;" g2="adieresis.alt1" k="-8" />
    <hkern u1="&#x165;" g2="l.alt1" k="-131" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-190" />
    <hkern u1="&#x165;" u2="&#x161;" k="-131" />
    <hkern u1="&#x165;" u2="&#x148;" k="-31" />
    <hkern u1="&#x165;" u2="&#x133;" k="-125" />
    <hkern u1="&#x165;" u2="&#x11b;" k="-41" />
    <hkern u1="&#x165;" u2="&#x10d;" k="-47" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-121" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-178" />
    <hkern u1="&#x165;" u2="&#x7c;" k="-117" />
    <hkern u1="&#x165;" u2="l" k="-121" />
    <hkern u1="&#x165;" u2="k" k="-121" />
    <hkern u1="&#x165;" u2="j" k="-123" />
    <hkern u1="&#x165;" u2="i" k="-125" />
    <hkern u1="&#x165;" u2="h" k="-121" />
    <hkern u1="&#x165;" u2="b" k="-121" />
    <hkern u1="&#x165;" u2="]" k="-176" />
    <hkern u1="&#x165;" u2="\" k="-190" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-55" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-121" />
    <hkern u1="&#x165;" u2="&#x29;" k="-182" />
    <hkern u1="&#x165;" u2="&#x27;" k="-117" />
    <hkern u1="&#x165;" u2="&#x22;" k="-117" />
    <hkern u1="&#x165;" u2="&#x21;" k="-113" />
    <hkern u1="&#x165;" u2="&#x20;" k="16" />
    <hkern u1="&#x166;" u2="&#xdf;" k="23" />
    <hkern u1="&#x168;" u2="&#xdf;" k="18" />
    <hkern u1="&#x16a;" u2="&#xdf;" k="18" />
    <hkern u1="&#x16c;" u2="&#xdf;" k="18" />
    <hkern u1="&#x16e;" u2="&#xdf;" k="18" />
    <hkern u1="&#x170;" u2="&#xdf;" k="18" />
    <hkern u1="&#x172;" u2="&#xdf;" k="18" />
    <hkern u1="&#x173;" u2="&#x201e;" k="-16" />
    <hkern u1="&#x173;" u2="&#x201a;" k="-16" />
    <hkern u1="&#x173;" u2="j" k="-94" />
    <hkern u1="&#x174;" u2="&#xdf;" k="25" />
    <hkern u1="&#x176;" g2="Idotaccent.sc" k="37" />
    <hkern u1="&#x176;" u2="&#xf0;" k="156" />
    <hkern u1="&#x176;" u2="&#xdf;" k="61" />
    <hkern u1="&#x178;" g2="Idotaccent.sc" k="37" />
    <hkern u1="&#x178;" u2="&#xf0;" k="156" />
    <hkern u1="&#x178;" u2="&#xdf;" k="61" />
    <hkern u1="&#x17f;" u2="&#x159;" k="-47" />
    <hkern u1="&#x17f;" u2="&#x149;" k="-248" />
    <hkern u1="&#x17f;" u2="&#x135;" k="-139" />
    <hkern u1="&#x17f;" u2="&#x131;" k="-2" />
    <hkern u1="&#x17f;" u2="&#x12d;" k="-137" />
    <hkern u1="&#x17f;" u2="&#x12b;" k="-150" />
    <hkern u1="&#x17f;" u2="&#x129;" k="-131" />
    <hkern u1="&#x17f;" u2="&#xef;" k="-195" />
    <hkern u1="&#x17f;" u2="&#xee;" k="-141" />
    <hkern u1="&#x17f;" u2="&#xed;" k="-2" />
    <hkern u1="&#x17f;" u2="&#xec;" k="-193" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="25" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="25" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="25" />
    <hkern u1="&#x1ef2;" g2="Idotaccent.sc" k="37" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="156" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="61" />
    <hkern u1="&#x2018;" g2="Jcircumflex.sc" k="-18" />
    <hkern u1="&#x2018;" u2="&#x12b;" k="-27" />
    <hkern u1="&#x2018;" u2="&#x127;" k="-16" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-63" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-43" />
    <hkern u1="&#x2019;" g2="Jcircumflex.sc" k="-14" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="-14" />
    <hkern u1="&#x2019;" u2="&#x127;" k="-14" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-51" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-23" />
    <hkern u1="&#x2019;" u2="&#xec;" k="-72" />
    <hkern u1="&#x2019;" u2="&#xae;" k="20" />
    <hkern u1="&#x2019;" u2="&#x40;" k="47" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="129" />
    <hkern u1="&#x2019;" u2="&#x20;" k="37" />
    <hkern u1="&#x201c;" g2="Jcircumflex.sc" k="-18" />
    <hkern u1="&#x201c;" u2="&#x12b;" k="-27" />
    <hkern u1="&#x201c;" u2="&#x127;" k="-20" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-63" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-41" />
    <hkern u1="&#x201d;" g2="Jcircumflex.sc" k="-16" />
    <hkern u1="&#x201d;" u2="&#x12b;" k="-14" />
    <hkern u1="&#x201d;" u2="&#x127;" k="-14" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-51" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-23" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-72" />
    <hkern u1="&#x2044;" g2="seven.dnom" k="-18" />
    <hkern u1="&#x2044;" g2="four.dnom" k="59" />
    <hkern u1="&#x2044;" g2="eight.oldstyle" k="-92" />
    <hkern u1="&#x2044;" g2="seven.oldstyle" k="-84" />
    <hkern u1="&#x2044;" g2="six.oldstyle" k="-92" />
    <hkern u1="&#x2044;" g2="four.oldstyle" k="88" />
    <hkern u1="&#x2044;" g2="two.oldstyle" k="-16" />
    <hkern u1="&#x2044;" g2="one.oldstyle" k="-37" />
    <hkern u1="&#x2044;" u2="&#x39;" k="-139" />
    <hkern u1="&#x2044;" u2="&#x38;" k="-92" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-324" />
    <hkern u1="&#x2044;" u2="&#x36;" k="-92" />
    <hkern u1="&#x2044;" u2="&#x35;" k="-172" />
    <hkern u1="&#x2044;" u2="&#x33;" k="-154" />
    <hkern u1="&#x2044;" u2="&#x32;" k="-131" />
    <hkern u1="&#x2044;" u2="&#x31;" k="-182" />
    <hkern u1="&#x2044;" u2="&#x30;" k="-100" />
    <hkern u1="&#x20ac;" g2="four.oldstyle" k="41" />
    <hkern u1="&#x20ac;" g2="three.oldstyle" k="43" />
    <hkern u1="&#x2122;" g2="Dcroat.sc" k="-43" />
    <hkern u1="&#x2122;" g2="Eth.sc" k="-43" />
    <hkern u1="&#x2122;" u2="&#x110;" k="-12" />
    <hkern u1="&#x2122;" u2="&#xee;" k="-37" />
    <hkern u1="&#x2122;" u2="&#xd0;" k="-12" />
    <hkern u1="&#x2212;" g2="four.oldstyle" k="70" />
    <hkern u1="&#x2212;" g2="three.oldstyle" k="41" />
    <hkern u1="&#x2212;" u2="&#x37;" k="43" />
    <hkern g1="f_f" u2="&#x149;" k="-184" />
    <hkern g1="f_f" u2="&#x135;" k="-76" />
    <hkern g1="f_f" u2="&#x12d;" k="-74" />
    <hkern g1="f_f" u2="&#x12b;" k="-86" />
    <hkern g1="f_f" u2="&#x129;" k="-68" />
    <hkern g1="f_f" u2="&#xef;" k="-131" />
    <hkern g1="f_f" u2="&#xee;" k="-76" />
    <hkern g1="f_f" u2="&#xec;" k="-127" />
    <hkern g1="fi" u2="&#x149;" k="-70" />
    <hkern g1="fi" u2="&#xef;" k="-20" />
    <hkern g1="fi" u2="&#xec;" k="-18" />
    <hkern g1="fl" u2="&#x149;" k="-109" />
    <hkern g1="f_f_i" u2="&#x149;" k="-70" />
    <hkern g1="f_f_i" u2="&#xef;" k="-20" />
    <hkern g1="f_f_i" u2="&#xec;" k="-18" />
    <hkern g1="f_f_l" u2="&#x149;" k="-109" />
    <hkern g1="f_f_j" u2="&#x149;" k="-70" />
    <hkern g1="f_f_j" u2="&#xef;" k="-20" />
    <hkern g1="f_f_j" u2="&#xec;" k="-18" />
    <hkern g1="f_j" u2="&#x149;" k="-70" />
    <hkern g1="f_j" u2="&#xef;" k="-20" />
    <hkern g1="f_j" u2="&#xec;" k="-18" />
    <hkern g1="parenleft.case" u2="&#x134;" k="-188" />
    <hkern g1="parenleft.case" u2="&#xce;" k="-51" />
    <hkern g1="parenleft.case" u2="J" k="-188" />
    <hkern g1="at.case" u2="&#xc6;" k="39" />
    <hkern g1="at.case" u2="Y" k="35" />
    <hkern g1="bracketleft.case" u2="&#x134;" k="-186" />
    <hkern g1="bracketleft.case" u2="&#xce;" k="-49" />
    <hkern g1="bracketleft.case" u2="J" k="-186" />
    <hkern g1="braceleft.case" u2="&#x134;" k="-188" />
    <hkern g1="braceleft.case" u2="&#xce;" k="-51" />
    <hkern g1="braceleft.case" u2="J" k="-188" />
    <hkern g1="questiondown.case" u2="V" k="25" />
    <hkern g1="zero.oldstyle" g2="four.oldstyle" k="57" />
    <hkern g1="zero.oldstyle" g2="three.oldstyle" k="25" />
    <hkern g1="zero.oldstyle" u2="&#x2044;" k="-106" />
    <hkern g1="zero.oldstyle" u2="&#x7d;" k="121" />
    <hkern g1="zero.oldstyle" u2="]" k="76" />
    <hkern g1="zero.oldstyle" u2="\" k="86" />
    <hkern g1="zero.oldstyle" u2="&#x29;" k="125" />
    <hkern g1="one.oldstyle" u2="&#x2044;" k="-272" />
    <hkern g1="one.oldstyle" u2="&#x7d;" k="43" />
    <hkern g1="one.oldstyle" u2="]" k="35" />
    <hkern g1="one.oldstyle" u2="\" k="33" />
    <hkern g1="one.oldstyle" u2="&#x29;" k="41" />
    <hkern g1="two.oldstyle" u2="&#x2044;" k="-297" />
    <hkern g1="two.oldstyle" u2="&#x7d;" k="88" />
    <hkern g1="two.oldstyle" u2="]" k="55" />
    <hkern g1="two.oldstyle" u2="\" k="63" />
    <hkern g1="two.oldstyle" u2="&#x29;" k="82" />
    <hkern g1="three.oldstyle" u2="&#x2044;" k="-358" />
    <hkern g1="three.oldstyle" u2="&#xb0;" k="47" />
    <hkern g1="three.oldstyle" u2="\" k="111" />
    <hkern g1="three.oldstyle" u2="&#x29;" k="23" />
    <hkern g1="four.oldstyle" g2="one.oldstyle" k="27" />
    <hkern g1="four.oldstyle" u2="&#x2212;" k="33" />
    <hkern g1="four.oldstyle" u2="&#x2044;" k="-365" />
    <hkern g1="four.oldstyle" u2="&#xb7;" k="39" />
    <hkern g1="four.oldstyle" u2="&#xb0;" k="37" />
    <hkern g1="four.oldstyle" u2="&#x7d;" k="49" />
    <hkern g1="four.oldstyle" u2="]" k="41" />
    <hkern g1="four.oldstyle" u2="\" k="109" />
    <hkern g1="four.oldstyle" u2="&#x2d;" k="33" />
    <hkern g1="four.oldstyle" u2="&#x2b;" k="35" />
    <hkern g1="four.oldstyle" u2="&#x29;" k="47" />
    <hkern g1="five.oldstyle" u2="&#x2044;" k="-350" />
    <hkern g1="five.oldstyle" u2="&#x7d;" k="27" />
    <hkern g1="five.oldstyle" u2="]" k="25" />
    <hkern g1="five.oldstyle" u2="\" k="74" />
    <hkern g1="five.oldstyle" u2="&#x29;" k="31" />
    <hkern g1="six.oldstyle" g2="four.oldstyle" k="45" />
    <hkern g1="six.oldstyle" g2="three.oldstyle" k="27" />
    <hkern g1="six.oldstyle" u2="&#x2044;" k="-147" />
    <hkern g1="seven.oldstyle" g2="four.oldstyle" k="104" />
    <hkern g1="seven.oldstyle" u2="&#x2044;" k="-45" />
    <hkern g1="seven.oldstyle" u2="&#x7d;" k="27" />
    <hkern g1="seven.oldstyle" u2="]" k="25" />
    <hkern g1="seven.oldstyle" u2="&#x2f;" k="43" />
    <hkern g1="seven.oldstyle" u2="&#x29;" k="27" />
    <hkern g1="eight.oldstyle" g2="four.oldstyle" k="49" />
    <hkern g1="eight.oldstyle" g2="three.oldstyle" k="29" />
    <hkern g1="eight.oldstyle" u2="&#x2044;" k="-139" />
    <hkern g1="eight.oldstyle" u2="&#x7d;" k="45" />
    <hkern g1="eight.oldstyle" u2="]" k="33" />
    <hkern g1="eight.oldstyle" u2="\" k="35" />
    <hkern g1="eight.oldstyle" u2="&#x29;" k="49" />
    <hkern g1="nine.oldstyle" u2="&#x2044;" k="-305" />
    <hkern g1="nine.oldstyle" u2="&#xb0;" k="47" />
    <hkern g1="nine.oldstyle" u2="&#x7d;" k="51" />
    <hkern g1="nine.oldstyle" u2="]" k="43" />
    <hkern g1="nine.oldstyle" u2="\" k="104" />
    <hkern g1="nine.oldstyle" u2="&#x29;" k="55" />
    <hkern g1="ampersand.sc" g2="V.sc" k="27" />
    <hkern g1="B.sc" g2="X.sc" k="16" />
    <hkern g1="B.sc" g2="V.sc" k="25" />
    <hkern g1="B.sc" u2="&#x2122;" k="37" />
    <hkern g1="B.sc" u2="&#x7d;" k="80" />
    <hkern g1="B.sc" u2="]" k="59" />
    <hkern g1="B.sc" u2="\" k="59" />
    <hkern g1="B.sc" u2="&#x29;" k="80" />
    <hkern g1="D.sc" g2="Hbar.sc" k="12" />
    <hkern g1="D.sc" g2="Lslash.sc" k="12" />
    <hkern g1="F.sc" g2="M.sc" k="14" />
    <hkern g1="F.sc" u2="&#x2f;" k="55" />
    <hkern g1="F.sc" u2="&#x20;" k="41" />
    <hkern g1="L.sc" u2="&#xb7;" k="412" />
    <hkern g1="M.sc" g2="V.sc" k="27" />
    <hkern g1="M.sc" u2="&#x2122;" k="31" />
    <hkern g1="M.sc" u2="&#x7d;" k="57" />
    <hkern g1="M.sc" u2="]" k="41" />
    <hkern g1="M.sc" u2="\" k="49" />
    <hkern g1="M.sc" u2="&#x29;" k="55" />
    <hkern g1="O.sc" g2="Hbar.sc" k="12" />
    <hkern g1="O.sc" g2="Lslash.sc" k="12" />
    <hkern g1="P.sc" g2="X.sc" k="29" />
    <hkern g1="P.sc" g2="M.sc" k="8" />
    <hkern g1="P.sc" u2="&#x2122;" k="18" />
    <hkern g1="P.sc" u2="&#x7d;" k="53" />
    <hkern g1="P.sc" u2="]" k="43" />
    <hkern g1="P.sc" u2="\" k="23" />
    <hkern g1="P.sc" u2="&#x2f;" k="57" />
    <hkern g1="P.sc" u2="&#x29;" k="51" />
    <hkern g1="P.sc" u2="&#x20;" k="47" />
    <hkern g1="Q.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Q.sc" g2="Lslash.sc" k="12" />
    <hkern g1="V.sc" g2="M.sc" k="27" />
    <hkern g1="V.sc" u2="&#x2f;" k="63" />
    <hkern g1="V.sc" u2="&#x20;" k="51" />
    <hkern g1="Y.sc" u2="&#x40;" k="29" />
    <hkern g1="questiondown.sc" g2="V.sc" k="20" />
    <hkern g1="Icircumflex.sc" u2="&#x2122;" k="-39" />
    <hkern g1="Icircumflex.sc" u2="&#x7d;" k="-23" />
    <hkern g1="Icircumflex.sc" u2="]" k="-20" />
    <hkern g1="Icircumflex.sc" u2="&#x2a;" k="-12" />
    <hkern g1="Icircumflex.sc" u2="&#x29;" k="-25" />
    <hkern g1="Idieresis.sc" u2="&#x29;" k="16" />
    <hkern g1="Eth.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Eth.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Oacute.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Thorn.sc" g2="X.sc" k="41" />
    <hkern g1="Thorn.sc" g2="V.sc" k="23" />
    <hkern g1="Thorn.sc" u2="&#x2122;" k="41" />
    <hkern g1="Thorn.sc" u2="&#x7d;" k="78" />
    <hkern g1="Thorn.sc" u2="]" k="53" />
    <hkern g1="Thorn.sc" u2="\" k="63" />
    <hkern g1="Thorn.sc" u2="&#x29;" k="78" />
    <hkern g1="Aogonek.sc" g2="J.sc" k="-188" />
    <hkern g1="Aogonek.sc" u2="&#x201e;" k="-84" />
    <hkern g1="Aogonek.sc" u2="&#x201a;" k="-84" />
    <hkern g1="Aogonek.sc" u2="&#x7d;" k="23" />
    <hkern g1="Aogonek.sc" u2="]" k="16" />
    <hkern g1="Aogonek.sc" u2="&#x3b;" k="-37" />
    <hkern g1="Aogonek.sc" u2="&#x2c;" k="-55" />
    <hkern g1="Aogonek.sc" u2="&#x29;" k="23" />
    <hkern g1="Eogonek.sc" g2="J.sc" k="-240" />
    <hkern g1="Eogonek.sc" u2="&#x201e;" k="-133" />
    <hkern g1="Eogonek.sc" u2="&#x201a;" k="-133" />
    <hkern g1="Eogonek.sc" u2="&#x7d;" k="-31" />
    <hkern g1="Eogonek.sc" u2="]" k="-31" />
    <hkern g1="Eogonek.sc" u2="&#x3b;" k="-86" />
    <hkern g1="Eogonek.sc" u2="&#x2c;" k="-104" />
    <hkern g1="Eogonek.sc" u2="&#x29;" k="-31" />
    <hkern g1="Hbar.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Gdotaccent.sc" k="12" />
    <hkern g1="Hbar.sc" g2="OE.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Y.sc" k="16" />
    <hkern g1="Hbar.sc" g2="Q.sc" k="12" />
    <hkern g1="Hbar.sc" g2="O.sc" k="12" />
    <hkern g1="Hbar.sc" g2="G.sc" k="12" />
    <hkern g1="Hbar.sc" g2="C.sc" k="12" />
    <hkern g1="Hbar.sc" u2="&#x2a;" k="-20" />
    <hkern g1="Iogonek.sc" g2="J.sc" k="-61" />
    <hkern g1="Jcircumflex.sc" u2="&#x2122;" k="-49" />
    <hkern g1="Jcircumflex.sc" u2="&#x7d;" k="-33" />
    <hkern g1="Jcircumflex.sc" u2="]" k="-31" />
    <hkern g1="Jcircumflex.sc" u2="&#x2a;" k="-25" />
    <hkern g1="Jcircumflex.sc" u2="&#x29;" k="-35" />
    <hkern g1="Lcaron.sc" g2="Tcommaaccent.sc.alt1" k="33" />
    <hkern g1="Lcaron.sc" g2="Tbar.sc.alt1" k="33" />
    <hkern g1="Lcaron.sc" g2="Tcaron.sc.alt1" k="31" />
    <hkern g1="Lcaron.sc" g2="T.sc.alt1" k="33" />
    <hkern g1="Lcaron.sc" g2="Y.sc" k="-2" />
    <hkern g1="Lcaron.sc" g2="W.sc" k="16" />
    <hkern g1="Lcaron.sc" g2="V.sc" k="39" />
    <hkern g1="Lcaron.sc" g2="T.sc" k="33" />
    <hkern g1="Lcaron.sc" u2="&#x2a;" k="8" />
    <hkern g1="Lcaron.sc" u2="&#x20;" k="84" />
    <hkern g1="Tcaron.sc" u2="&#x20;" k="66" />
    <hkern g1="seven.numr" u2="&#x2044;" k="68" />
    <hkern g1="l.alt1" u2="&#xb7;" k="143" />
    <hkern g1="lslash.alt1" g2="y.alt1" k="4" />
    <hkern g1="lslash.alt1" u2="y" k="-2" />
    <hkern g1="lslash.alt1" u2="x" k="-35" />
    <hkern g1="lslash.alt1" u2="v" k="-2" />
    <hkern g1="lslash.alt1" u2="&#x2a;" k="-41" />
    <hkern g1="lcommaaccent.alt1" u2="&#x201e;" k="-18" />
    <hkern g1="lcommaaccent.alt1" u2="&#x201a;" k="-18" />
    <hkern g1="lcaron.alt1" g2="y.alt1" k="-37" />
    <hkern g1="lcaron.alt1" g2="l.alt1" k="-137" />
    <hkern g1="lcaron.alt1" u2="&#x2122;" k="-201" />
    <hkern g1="lcaron.alt1" u2="&#x17e;" k="-102" />
    <hkern g1="lcaron.alt1" u2="&#x161;" k="-139" />
    <hkern g1="lcaron.alt1" u2="&#x148;" k="-37" />
    <hkern g1="lcaron.alt1" u2="&#x133;" k="-143" />
    <hkern g1="lcaron.alt1" u2="&#x10d;" k="-55" />
    <hkern g1="lcaron.alt1" u2="&#xfe;" k="-127" />
    <hkern g1="lcaron.alt1" u2="&#xdf;" k="-33" />
    <hkern g1="lcaron.alt1" u2="&#x7d;" k="-190" />
    <hkern g1="lcaron.alt1" u2="&#x7c;" k="-117" />
    <hkern g1="lcaron.alt1" u2="y" k="-41" />
    <hkern g1="lcaron.alt1" u2="w" k="-31" />
    <hkern g1="lcaron.alt1" u2="v" k="-41" />
    <hkern g1="lcaron.alt1" u2="t" k="-14" />
    <hkern g1="lcaron.alt1" u2="l" k="-127" />
    <hkern g1="lcaron.alt1" u2="k" k="-127" />
    <hkern g1="lcaron.alt1" u2="j" k="-141" />
    <hkern g1="lcaron.alt1" u2="i" k="-143" />
    <hkern g1="lcaron.alt1" u2="h" k="-127" />
    <hkern g1="lcaron.alt1" u2="b" k="-127" />
    <hkern g1="lcaron.alt1" u2="]" k="-188" />
    <hkern g1="lcaron.alt1" u2="\" k="-201" />
    <hkern g1="lcaron.alt1" u2="&#x3f;" k="-94" />
    <hkern g1="lcaron.alt1" u2="&#x2a;" k="-154" />
    <hkern g1="lcaron.alt1" u2="&#x29;" k="-193" />
    <hkern g1="lcaron.alt1" u2="&#x27;" k="-127" />
    <hkern g1="lcaron.alt1" u2="&#x26;" k="-12" />
    <hkern g1="lcaron.alt1" u2="&#x22;" k="-127" />
    <hkern g1="lcaron.alt1" u2="&#x21;" k="-125" />
    <hkern g1="aogonek.alt1" u2="&#x201e;" k="-27" />
    <hkern g1="aogonek.alt1" u2="&#x201a;" k="-27" />
    <hkern g1="aogonek.alt1" u2="&#x7d;" k="68" />
    <hkern g1="aogonek.alt1" u2="j" k="-111" />
    <hkern g1="aogonek.alt1" u2="&#x29;" k="70" />
    <hkern g1="tcaron.alt1" g2="l.alt1" k="-74" />
    <hkern g1="tcaron.alt1" u2="&#x2122;" k="-133" />
    <hkern g1="tcaron.alt1" u2="&#x161;" k="-76" />
    <hkern g1="tcaron.alt1" u2="&#x133;" k="-70" />
    <hkern g1="tcaron.alt1" u2="&#xfe;" k="-66" />
    <hkern g1="tcaron.alt1" u2="&#x7d;" k="-123" />
    <hkern g1="tcaron.alt1" u2="&#x7c;" k="-61" />
    <hkern g1="tcaron.alt1" u2="l" k="-66" />
    <hkern g1="tcaron.alt1" u2="k" k="-66" />
    <hkern g1="tcaron.alt1" u2="j" k="-68" />
    <hkern g1="tcaron.alt1" u2="i" k="-70" />
    <hkern g1="tcaron.alt1" u2="h" k="-66" />
    <hkern g1="tcaron.alt1" u2="b" k="-66" />
    <hkern g1="tcaron.alt1" u2="]" k="-121" />
    <hkern g1="tcaron.alt1" u2="\" k="-133" />
    <hkern g1="tcaron.alt1" u2="&#x3f;" k="-14" />
    <hkern g1="tcaron.alt1" u2="&#x2a;" k="-68" />
    <hkern g1="tcaron.alt1" u2="&#x29;" k="-129" />
    <hkern g1="tcaron.alt1" u2="&#x27;" k="-61" />
    <hkern g1="tcaron.alt1" u2="&#x22;" k="-61" />
    <hkern g1="tcaron.alt1" u2="&#x21;" k="-57" />
    <hkern g1="Tcaron.sc.alt1" u2="&#x20;" k="66" />
    <hkern g1="space"
  g2="t,tcommaaccent,tcaron,tbar"
  k="31" />
    <hkern g1="space"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="49" />
    <hkern g1="space"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="47" />
    <hkern g1="space"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="space"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="55" />
    <hkern g1="space"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="86" />
    <hkern g1="space"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="63" />
    <hkern g1="space"
  g2="AE,AEacute"
  k="78" />
    <hkern g1="space"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="space"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="49" />
    <hkern g1="space"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="51" />
    <hkern g1="space"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="66" />
    <hkern g1="space"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="space"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="49" />
    <hkern g1="space"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t,tcommaaccent,tcaron,tbar"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="63" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="109" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="145" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="92" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="70" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="68" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="119" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="61" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="41" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="v"
  k="63" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="35" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V"
  k="96" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quotedbl,quotesingle"
  k="223" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one"
  k="51" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteright,quotedblright"
  k="231" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteleft,quotedblleft"
  k="219" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one.oldstyle"
  k="51" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="three.oldstyle"
  k="33" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="seven.oldstyle"
  k="76" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V.sc"
  k="68" />
    <hkern g1="colon,semicolon"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="106" />
    <hkern g1="colon,semicolon"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="113" />
    <hkern g1="colon,semicolon"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="35" />
    <hkern g1="colon,semicolon"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="51" />
    <hkern g1="colon,semicolon"
  g2="V"
  k="45" />
    <hkern g1="colon,semicolon"
  g2="V.sc"
  k="20" />
    <hkern g1="quotedbl,quotesingle"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="57" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE,AEacute"
  k="133" />
    <hkern g1="quotedbl,quotesingle"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE.sc,AEacute.sc"
  k="131" />
    <hkern g1="quotedbl,quotesingle"
  g2="three.oldstyle"
  k="23" />
    <hkern g1="quotedbl,quotesingle"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="quotedbl,quotesingle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="223" />
    <hkern g1="quotedbl,quotesingle"
  g2="four"
  k="68" />
    <hkern g1="quotedbl,quotesingle"
  g2="slash"
  k="90" />
    <hkern g1="quotedbl,quotesingle"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="quotedbl,quotesingle"
  g2="four.oldstyle"
  k="137" />
    <hkern g1="quotedbl,quotesingle"
  g2="five.oldstyle"
  k="27" />
    <hkern g1="quotedbl,quotesingle"
  g2="nine.oldstyle"
  k="20" />
    <hkern g1="exclamdown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="106" />
    <hkern g1="exclamdown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="106" />
    <hkern g1="exclamdown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="exclamdown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="43" />
    <hkern g1="bracketleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="bracketleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="31" />
    <hkern g1="bracketleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="35" />
    <hkern g1="bracketleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-33" />
    <hkern g1="bracketleft"
  g2="AE,AEacute"
  k="41" />
    <hkern g1="bracketleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="51" />
    <hkern g1="bracketleft"
  g2="AE.sc,AEacute.sc"
  k="51" />
    <hkern g1="bracketleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="bracketleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="43" />
    <hkern g1="bracketleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="74" />
    <hkern g1="bracketleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="61" />
    <hkern g1="bracketleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="51" />
    <hkern g1="bracketleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="53" />
    <hkern g1="bracketleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="49" />
    <hkern g1="bracketleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="20" />
    <hkern g1="bracketleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="63" />
    <hkern g1="bracketleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="41" />
    <hkern g1="bracketleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="bracketleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="61" />
    <hkern g1="slash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="98" />
    <hkern g1="slash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-47" />
    <hkern g1="slash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-18" />
    <hkern g1="slash"
  g2="AE,AEacute"
  k="168" />
    <hkern g1="slash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="123" />
    <hkern g1="slash"
  g2="AE.sc,AEacute.sc"
  k="184" />
    <hkern g1="slash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="slash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="slash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="100" />
    <hkern g1="slash"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="slash"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="55" />
    <hkern g1="slash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="51" />
    <hkern g1="slash"
  g2="z,zacute,zdotaccent,zcaron"
  k="53" />
    <hkern g1="slash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="72" />
    <hkern g1="slash"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="41" />
    <hkern g1="slash"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="90" />
    <hkern g1="backslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="49" />
    <hkern g1="backslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="41" />
    <hkern g1="backslash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-31" />
    <hkern g1="backslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="100" />
    <hkern g1="backslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="143" />
    <hkern g1="backslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="86" />
    <hkern g1="backslash"
  g2="AE,AEacute"
  k="-61" />
    <hkern g1="backslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="55" />
    <hkern g1="backslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="61" />
    <hkern g1="backslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="104" />
    <hkern g1="backslash"
  g2="AE.sc,AEacute.sc"
  k="-18" />
    <hkern g1="backslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="49" />
    <hkern g1="backslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="39" />
    <hkern g1="backslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="backslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="27" />
    <hkern g1="backslash"
  g2="quotedbl,quotesingle"
  k="90" />
    <hkern g1="hyphen,endash,emdash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="hyphen,endash,emdash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="100" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="133" />
    <hkern g1="hyphen,endash,emdash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="47" />
    <hkern g1="hyphen,endash,emdash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="66" />
    <hkern g1="hyphen,endash,emdash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="25" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="92" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE.sc,AEacute.sc"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="hyphen,endash,emdash"
  g2="v"
  k="16" />
    <hkern g1="hyphen,endash,emdash"
  g2="V"
  k="53" />
    <hkern g1="hyphen,endash,emdash"
  g2="V.sc"
  k="29" />
    <hkern g1="hyphen,endash,emdash"
  g2="x"
  k="41" />
    <hkern g1="hyphen,endash,emdash"
  g2="X"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="X.sc"
  k="41" />
    <hkern g1="hyphen,endash,emdash"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="16" />
    <hkern g1="registered"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="20" />
    <hkern g1="registered"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="61" />
    <hkern g1="registered"
  g2="AE,AEacute"
  k="47" />
    <hkern g1="registered"
  g2="AE.sc,AEacute.sc"
  k="55" />
    <hkern g1="ampersand"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="76" />
    <hkern g1="ampersand"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="92" />
    <hkern g1="ampersand"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="ampersand"
  g2="AE,AEacute"
  k="-25" />
    <hkern g1="ampersand"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="quoteright,quotedblright"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="86" />
    <hkern g1="quoteright,quotedblright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-41" />
    <hkern g1="quoteright,quotedblright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-12" />
    <hkern g1="quoteright,quotedblright"
  g2="AE,AEacute"
  k="172" />
    <hkern g1="quoteright,quotedblright"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="90" />
    <hkern g1="quoteright,quotedblright"
  g2="AE.sc,AEacute.sc"
  k="168" />
    <hkern g1="quoteright,quotedblright"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="23" />
    <hkern g1="quoteright,quotedblright"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="80" />
    <hkern g1="quoteright,quotedblright"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="285" />
    <hkern g1="quoteright,quotedblright"
  g2="guillemotleft,guilsinglleft"
  k="84" />
    <hkern g1="quoteright,quotedblright"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="55" />
    <hkern g1="quoteright,quotedblright"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="29" />
    <hkern g1="quoteright,quotedblright"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="27" />
    <hkern g1="quoteright,quotedblright"
  g2="z,zacute,zdotaccent,zcaron"
  k="25" />
    <hkern g1="quoteright,quotedblright"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="49" />
    <hkern g1="quoteright,quotedblright"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="57" />
    <hkern g1="quoteright,quotedblright"
  g2="M"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="colon,semicolon"
  k="33" />
    <hkern g1="quoteright,quotedblright"
  g2="hyphen,endash,emdash"
  k="63" />
    <hkern g1="quoteright,quotedblright"
  g2="M.sc"
  k="23" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="80" />
    <hkern g1="quoteleft,quotedblleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-51" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE,AEacute"
  k="162" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="86" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE.sc,AEacute.sc"
  k="160" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="57" />
    <hkern g1="quoteleft,quotedblleft"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="256" />
    <hkern g1="quoteleft,quotedblleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="41" />
    <hkern g1="quoteleft,quotedblleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="23" />
    <hkern g1="quoteleft,quotedblleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="20" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="quoteleft,quotedblleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="45" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M.sc"
  k="25" />
    <hkern g1="trademark"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="37" />
    <hkern g1="trademark"
  g2="AE,AEacute"
  k="96" />
    <hkern g1="trademark"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="37" />
    <hkern g1="trademark"
  g2="AE.sc,AEacute.sc"
  k="96" />
    <hkern g1="braceleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="braceleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="33" />
    <hkern g1="braceleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="76" />
    <hkern g1="braceleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-35" />
    <hkern g1="braceleft"
  g2="AE,AEacute"
  k="88" />
    <hkern g1="braceleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="96" />
    <hkern g1="braceleft"
  g2="AE.sc,AEacute.sc"
  k="104" />
    <hkern g1="braceleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="braceleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="55" />
    <hkern g1="braceleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="braceleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="80" />
    <hkern g1="braceleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="66" />
    <hkern g1="braceleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="63" />
    <hkern g1="braceleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="63" />
    <hkern g1="braceleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="29" />
    <hkern g1="braceleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="88" />
    <hkern g1="braceleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="55" />
    <hkern g1="braceleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="27" />
    <hkern g1="braceleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="84" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="119" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="94" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="29" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="V"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="137" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="158" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="66" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE,AEacute"
  k="20" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="66" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="43" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="113" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE.sc,AEacute.sc"
  k="37" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="v"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V"
  k="74" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quotedbl,quotesingle"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quoteright,quotedblright"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V.sc"
  k="45" />
    <hkern g1="guillemotright,guilsinglright"
  g2="x"
  k="43" />
    <hkern g1="guillemotright,guilsinglright"
  g2="X.sc"
  k="47" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="questiondown"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="31" />
    <hkern g1="questiondown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="129" />
    <hkern g1="questiondown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="150" />
    <hkern g1="questiondown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="80" />
    <hkern g1="questiondown"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="35" />
    <hkern g1="questiondown"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="53" />
    <hkern g1="questiondown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="90" />
    <hkern g1="questiondown"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="29" />
    <hkern g1="questiondown"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="35" />
    <hkern g1="questiondown"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="43" />
    <hkern g1="questiondown"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="45" />
    <hkern g1="questiondown"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="31" />
    <hkern g1="questiondown"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="35" />
    <hkern g1="questiondown"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="35" />
    <hkern g1="questiondown"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="37" />
    <hkern g1="questiondown"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="25" />
    <hkern g1="questiondown"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="23" />
    <hkern g1="questiondown"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="37" />
    <hkern g1="asterisk"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="57" />
    <hkern g1="asterisk"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-27" />
    <hkern g1="asterisk"
  g2="AE,AEacute"
  k="131" />
    <hkern g1="asterisk"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="asterisk"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="-20" />
    <hkern g1="asterisk"
  g2="AE.sc,AEacute.sc"
  k="133" />
    <hkern g1="asterisk"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="31" />
    <hkern g1="asterisk"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="parenleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="parenleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="33" />
    <hkern g1="parenleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="57" />
    <hkern g1="parenleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-37" />
    <hkern g1="parenleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="-10" />
    <hkern g1="parenleft"
  g2="AE,AEacute"
  k="66" />
    <hkern g1="parenleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="parenleft"
  g2="AE.sc,AEacute.sc"
  k="80" />
    <hkern g1="parenleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="31" />
    <hkern g1="parenleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="parenleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="parenleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="80" />
    <hkern g1="parenleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="63" />
    <hkern g1="parenleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="63" />
    <hkern g1="parenleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="59" />
    <hkern g1="parenleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="31" />
    <hkern g1="parenleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="88" />
    <hkern g1="parenleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="55" />
    <hkern g1="parenleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="31" />
    <hkern g1="parenleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="82" />
    <hkern g1="ampersand.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="39" />
    <hkern g1="ampersand.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="ampersand.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="82" />
    <hkern g1="questiondown.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="43" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE.sc,AEacute.sc"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="v"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="V"
  k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="x"
  k="18" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="X"
  k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="parenright.case"
  k="55" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="bracketright.case"
  k="35" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright.case"
  k="53" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="39" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="113" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="125" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="61" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="55" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="94" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="v"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V"
  k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quotedbl,quotesingle"
  k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteright,quotedblright"
  k="51" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteleft,quotedblleft"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V.sc"
  k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="slash"
  k="-29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="space"
  k="66" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="bracketright"
  k="29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="backslash"
  k="98" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="trademark"
  k="76" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright"
  k="76" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="asterisk"
  k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright"
  k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="hyphen.case,endash.case,emdash.case"
  k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="45" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="33" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="82" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE,AEacute"
  k="63" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE.sc,AEacute.sc"
  k="72" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V"
  k="33" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="35" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="slash"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="x"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X"
  k="55" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X.sc"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright.case"
  k="102" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright.case"
  k="61" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright.case"
  k="100" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright"
  k="41" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="backslash"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="trademark"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright"
  k="55" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright"
  k="59" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="v"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t,tcommaaccent,tcaron,tbar"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="49" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="94" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="94" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="123" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="170" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="109" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="92" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="96" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="158" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="92" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="72" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="53" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="v"
  k="94" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V"
  k="119" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quotedbl,quotesingle"
  k="145" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteright,quotedblright"
  k="145" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteleft,quotedblleft"
  k="145" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V.sc"
  k="94" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="space"
  k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="bracketright"
  k="49" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="backslash"
  k="145" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="trademark"
  k="143" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="braceright"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="asterisk"
  k="143" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="parenright"
  k="78" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen.case,endash.case,emdash.case"
  k="139" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft.case,guilsinglleft.case"
  k="80" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="parenright.case"
  k="29" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="braceright.case"
  k="33" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="trademark"
  k="-10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="33" />
    <hkern g1="F"
  g2="t,tcommaaccent,tcaron,tbar"
  k="14" />
    <hkern g1="F"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="F"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="F"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="78" />
    <hkern g1="F"
  g2="AE,AEacute"
  k="131" />
    <hkern g1="F"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="96" />
    <hkern g1="F"
  g2="AE.sc,AEacute.sc"
  k="178" />
    <hkern g1="F"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="F"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="18" />
    <hkern g1="F"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="20" />
    <hkern g1="F"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="F"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="70" />
    <hkern g1="F"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="115" />
    <hkern g1="F"
  g2="guillemotleft,guilsinglleft"
  k="35" />
    <hkern g1="F"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="61" />
    <hkern g1="F"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="57" />
    <hkern g1="F"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="49" />
    <hkern g1="F"
  g2="z,zacute,zdotaccent,zcaron"
  k="53" />
    <hkern g1="F"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="F"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="F"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="80" />
    <hkern g1="F"
  g2="colon,semicolon"
  k="29" />
    <hkern g1="F"
  g2="hyphen,endash,emdash"
  k="18" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="23" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="74" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="18" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE,AEacute"
  k="57" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE.sc,AEacute.sc"
  k="66" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V"
  k="31" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="27" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="slash"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="z,zacute,zdotaccent,zcaron"
  k="14" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="x"
  k="27" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X"
  k="49" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X.sc"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright.case"
  k="59" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright.case"
  k="92" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright"
  k="33" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="backslash"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="trademark"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright"
  k="49" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright"
  k="51" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="B"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="B"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="B"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="B"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="29" />
    <hkern g1="B"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="61" />
    <hkern g1="B"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="B"
  g2="AE,AEacute"
  k="20" />
    <hkern g1="B"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="B"
  g2="AE.sc,AEacute.sc"
  k="23" />
    <hkern g1="B"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="B"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="B"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="23" />
    <hkern g1="B"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="B"
  g2="z,zacute,zdotaccent,zcaron"
  k="16" />
    <hkern g1="P"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="P"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="25" />
    <hkern g1="P"
  g2="AE,AEacute"
  k="129" />
    <hkern g1="P"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="84" />
    <hkern g1="P"
  g2="AE.sc,AEacute.sc"
  k="172" />
    <hkern g1="P"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="35" />
    <hkern g1="P"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="133" />
    <hkern g1="P"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="P"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="P"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="P"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="P"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="29" />
    <hkern g1="P"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="37" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="V"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="43" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="27" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t,tcommaaccent,tcaron,tbar"
  k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="143" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="143" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="113" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE,AEacute"
  k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="121" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE.sc,AEacute.sc"
  k="170" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="147" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="76" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="78" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="v"
  k="143" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="109" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="slash"
  k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft,guilsinglleft"
  k="137" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="143" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="131" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="129" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="z,zacute,zdotaccent,zcaron"
  k="150" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="90" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="45" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="145" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="x"
  k="152" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M"
  k="27" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="colon,semicolon"
  k="106" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen,endash,emdash"
  k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M.sc"
  k="41" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="space"
  k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="asterisk"
  k="-49" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen.case,endash.case,emdash.case"
  k="98" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft.case,guilsinglleft.case"
  k="76" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotright,guilsinglright"
  k="119" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t,tcommaaccent,tcaron,tbar"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="37" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="39" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="37" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="v"
  k="37" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen.case,endash.case,emdash.case"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="63" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="72" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE,AEacute"
  k="35" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE.sc,AEacute.sc"
  k="39" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V.sc"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="x"
  k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X"
  k="33" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X.sc"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright.case"
  k="78" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright.case"
  k="45" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright.case"
  k="78" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright"
  k="49" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright"
  k="49" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="16" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE,AEacute"
  k="47" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE.sc,AEacute.sc"
  k="63" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="25" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="slash"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="z,zacute,zdotaccent,zcaron"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="25" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="x"
  k="25" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="K,Kcommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="47" />
    <hkern g1="K,Kcommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="84" />
    <hkern g1="K,Kcommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="84" />
    <hkern g1="K,Kcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="35" />
    <hkern g1="K,Kcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="35" />
    <hkern g1="K,Kcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="45" />
    <hkern g1="K,Kcommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="86" />
    <hkern g1="K,Kcommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="84" />
    <hkern g1="K,Kcommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="59" />
    <hkern g1="K,Kcommaaccent"
  g2="v"
  k="84" />
    <hkern g1="K,Kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="92" />
    <hkern g1="K,Kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="K,Kcommaaccent"
  g2="quoteright,quotedblright"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="quoteleft,quotedblleft"
  k="35" />
    <hkern g1="K,Kcommaaccent"
  g2="V.sc"
  k="37" />
    <hkern g1="K,Kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="43" />
    <hkern g1="K,Kcommaaccent"
  g2="slash"
  k="-55" />
    <hkern g1="K,Kcommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="25" />
    <hkern g1="K,Kcommaaccent"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="72" />
    <hkern g1="K,Kcommaaccent"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="43" />
    <hkern g1="K,Kcommaaccent"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="parenright.case"
  k="-10" />
    <hkern g1="K,Kcommaaccent"
  g2="braceright.case"
  k="-16" />
    <hkern g1="K,Kcommaaccent"
  g2="space"
  k="25" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen.case,endash.case,emdash.case"
  k="63" />
    <hkern g1="K,Kcommaaccent"
  g2="guillemotleft.case,guilsinglleft.case"
  k="109" />
    <hkern g1="K,Kcommaaccent"
  g2="exclam"
  k="-14" />
    <hkern g1="K,Kcommaaccent"
  g2="registered"
  k="33" />
    <hkern g1="M"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="M"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="M"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="M"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="27" />
    <hkern g1="M"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="43" />
    <hkern g1="M"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="M"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="10" />
    <hkern g1="M"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="10" />
    <hkern g1="M"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="M"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="M"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="M"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="18" />
    <hkern g1="M"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="M"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="V"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="V"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="V"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="V"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="70" />
    <hkern g1="V"
  g2="AE,AEacute"
  k="145" />
    <hkern g1="V"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="V"
  g2="AE.sc,AEacute.sc"
  k="141" />
    <hkern g1="V"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="V"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="31" />
    <hkern g1="V"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="V"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="V"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="100" />
    <hkern g1="V"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="96" />
    <hkern g1="V"
  g2="guillemotleft,guilsinglleft"
  k="74" />
    <hkern g1="V"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="100" />
    <hkern g1="V"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="76" />
    <hkern g1="V"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="74" />
    <hkern g1="V"
  g2="z,zacute,zdotaccent,zcaron"
  k="70" />
    <hkern g1="V"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="14" />
    <hkern g1="V"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="55" />
    <hkern g1="V"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="V"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="10" />
    <hkern g1="V"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="102" />
    <hkern g1="V"
  g2="colon,semicolon"
  k="45" />
    <hkern g1="V"
  g2="hyphen,endash,emdash"
  k="53" />
    <hkern g1="V"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="18" />
    <hkern g1="V"
  g2="hyphen.case,endash.case,emdash.case"
  k="27" />
    <hkern g1="V"
  g2="guillemotleft.case,guilsinglleft.case"
  k="47" />
    <hkern g1="V"
  g2="guillemotright,guilsinglright"
  k="25" />
    <hkern g1="X"
  g2="t,tcommaaccent,tcaron,tbar"
  k="49" />
    <hkern g1="X"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="88" />
    <hkern g1="X"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="82" />
    <hkern g1="X"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="20" />
    <hkern g1="X"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="X"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="20" />
    <hkern g1="X"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="90" />
    <hkern g1="X"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="84" />
    <hkern g1="X"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="61" />
    <hkern g1="X"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="51" />
    <hkern g1="X"
  g2="quoteleft,quotedblleft"
  k="18" />
    <hkern g1="X"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="49" />
    <hkern g1="X"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="X"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="X"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="43" />
    <hkern g1="X"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="49" />
    <hkern g1="X"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="X"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="23" />
    <hkern g1="X"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="X"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="X"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="X"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="18" />
    <hkern g1="X"
  g2="hyphen.case,endash.case,emdash.case"
  k="57" />
    <hkern g1="X"
  g2="guillemotleft.case,guilsinglleft.case"
  k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t,tcommaaccent,tcaron,tbar"
  k="72" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="125" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE,AEacute"
  k="199" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="145" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE.sc,AEacute.sc"
  k="217" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="v"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="205" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="147" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="slash"
  k="143" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft,guilsinglleft"
  k="158" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="188" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="154" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="160" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="z,zacute,zdotaccent,zcaron"
  k="170" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="70" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="137" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="111" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="190" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="x"
  k="139" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="X.sc"
  k="27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M"
  k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="colon,semicolon"
  k="113" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen,endash,emdash"
  k="133" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M.sc"
  k="106" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="parenright.case"
  k="-12" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="braceright.case"
  k="-10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="space"
  k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="bracketright"
  k="-33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="backslash"
  k="-47" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="trademark"
  k="-57" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="braceright"
  k="-35" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="asterisk"
  k="-25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="parenright"
  k="-37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen.case,endash.case,emdash.case"
  k="90" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft.case,guilsinglleft.case"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright,guilsinglright"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="registered"
  k="53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="ampersand"
  k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright.case,guilsinglright.case"
  k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="61" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE,AEacute"
  k="135" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE.sc,AEacute.sc"
  k="135" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="v"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="94" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="94" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="slash"
  k="86" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="92" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="70" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="z,zacute,zdotaccent,zcaron"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="49" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="94" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="x"
  k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M"
  k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="colon,semicolon"
  k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen,endash,emdash"
  k="47" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M.sc"
  k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="space"
  k="63" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="backslash"
  k="-20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="trademark"
  k="-31" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="asterisk"
  k="-25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="parenright"
  k="-10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen.case,endash.case,emdash.case"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft.case,guilsinglleft.case"
  k="35" />
    <hkern g1="Thorn"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="25" />
    <hkern g1="Thorn"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="27" />
    <hkern g1="Thorn"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="80" />
    <hkern g1="Thorn"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="Thorn"
  g2="AE,AEacute"
  k="74" />
    <hkern g1="Thorn"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="53" />
    <hkern g1="Thorn"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="Thorn"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="25" />
    <hkern g1="d,dcaron,dslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="f,f_f"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="33" />
    <hkern g1="f,f_f"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="f,f_f"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="37" />
    <hkern g1="f,f_f"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="154" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="205" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="94" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="AE,AEacute"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="v"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="V"
  k="100" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="x"
  k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="X"
  k="51" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="M"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="bracketright"
  k="74" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="backslash"
  k="100" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="trademark"
  k="59" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="braceright"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="asterisk"
  k="33" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="parenright"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="question"
  k="20" />
    <hkern g1="l,lacute,lcommaaccent,lcaron,lslash,fl,f_f_l"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="guillemotleft,guilsinglleft"
  k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="hyphen,endash,emdash"
  k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="bracketright"
  k="53" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="backslash"
  k="72" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="trademark"
  k="47" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="braceright"
  k="68" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="parenright"
  k="66" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="t,tcommaaccent,tcaron,tbar"
  k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="152" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="199" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="94" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="v"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="V"
  k="102" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="x"
  k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="X"
  k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="M"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="bracketright"
  k="66" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="backslash"
  k="94" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="trademark"
  k="57" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="braceright"
  k="90" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="asterisk"
  k="29" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="parenright"
  k="88" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="k,kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="61" />
    <hkern g1="k,kcommaaccent"
  g2="slash"
  k="-12" />
    <hkern g1="k,kcommaaccent"
  g2="guillemotleft,guilsinglleft"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="33" />
    <hkern g1="k,kcommaaccent"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="27" />
    <hkern g1="k,kcommaaccent"
  g2="hyphen,endash,emdash"
  k="53" />
    <hkern g1="k,kcommaaccent"
  g2="bracketright"
  k="23" />
    <hkern g1="k,kcommaaccent"
  g2="backslash"
  k="33" />
    <hkern g1="k,kcommaaccent"
  g2="trademark"
  k="43" />
    <hkern g1="k,kcommaaccent"
  g2="braceright"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="parenright"
  k="37" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="v"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="x"
  k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="bracketright"
  k="61" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="backslash"
  k="88" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="trademark"
  k="57" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="braceright"
  k="84" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="asterisk"
  k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="parenright"
  k="82" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="154" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="197" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="76" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="v"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="V"
  k="80" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="X"
  k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="M"
  k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="bracketright"
  k="63" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="backslash"
  k="94" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="trademark"
  k="57" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="braceright"
  k="88" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="asterisk"
  k="27" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="parenright"
  k="84" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="33" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="37" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="space"
  k="41" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="bracketright"
  k="23" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="braceright"
  k="25" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="parenright"
  k="23" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="86" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="115" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="slash"
  k="72" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="8" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="space"
  k="47" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="bracketright"
  k="39" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="trademark"
  k="23" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="braceright"
  k="39" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="parenright"
  k="39" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="v"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="49" />
    <hkern g1="v"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="v"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="63" />
    <hkern g1="v"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="v"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="v"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="v"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="bracketright"
  k="47" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="backslash"
  k="49" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="trademark"
  k="43" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="braceright"
  k="59" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="parenright"
  k="57" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="61" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="slash"
  k="53" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="guillemotleft,guilsinglleft"
  k="23" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="space"
  k="49" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="bracketright"
  k="29" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="trademark"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="braceright"
  k="31" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="parenright"
  k="31" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="39" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="49" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="slash"
  k="39" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="8" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="space"
  k="47" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="bracketright"
  k="33" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="trademark"
  k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="braceright"
  k="35" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="parenright"
  k="35" />
    <hkern g1="x"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="x"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="27" />
    <hkern g1="x"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="37" />
    <hkern g1="x"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="x"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="x"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="x"
  g2="hyphen,endash,emdash"
  k="41" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="131" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="154" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="72" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="V"
  k="76" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="X"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="bracketright"
  k="51" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="backslash"
  k="55" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="trademark"
  k="43" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="braceright"
  k="66" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="parenright"
  k="63" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="germandbls"
  g2="t,tcommaaccent,tcaron,tbar"
  k="14" />
    <hkern g1="germandbls"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="germandbls"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="germandbls"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="germandbls"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="14" />
    <hkern g1="germandbls"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="16" />
    <hkern g1="germandbls"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="germandbls"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="germandbls"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="germandbls"
  g2="quoteleft,quotedblleft"
  k="16" />
    <hkern g1="germandbls"
  g2="z,zacute,zdotaccent,zcaron"
  k="8" />
    <hkern g1="germandbls"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="20" />
    <hkern g1="eth"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="eth"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="eth"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="eth"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="eth"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="eth"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="eth"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="eth"
  g2="quotedbl,quotesingle"
  k="31" />
    <hkern g1="eth"
  g2="quoteright,quotedblright"
  k="29" />
    <hkern g1="eth"
  g2="quoteleft,quotedblleft"
  k="20" />
    <hkern g1="eth"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="eth"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="23" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="76" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="113" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quotedbl,quotesingle"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteright,quotedblright"
  k="51" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteleft,quotedblleft"
  k="49" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="V.sc"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="20" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="8" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="14" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="space"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="bracketright"
  k="51" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="backslash"
  k="123" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="trademark"
  k="86" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="braceright"
  k="96" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="asterisk"
  k="63" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="parenright"
  k="76" />
    <hkern g1="B.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="8" />
    <hkern g1="B.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="20" />
    <hkern g1="B.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="B.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="59" />
    <hkern g1="B.sc"
  g2="AE.sc,AEacute.sc"
  k="18" />
    <hkern g1="B.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="8" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="12" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="bracketright"
  k="20" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="braceright"
  k="33" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="parenright"
  k="33" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="70" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="AE.sc,AEacute.sc"
  k="47" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="X.sc"
  k="35" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="16" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="bracketright"
  k="61" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="backslash"
  k="68" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="trademark"
  k="43" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="braceright"
  k="84" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="parenright"
  k="84" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="12" />
    <hkern g1="F.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="59" />
    <hkern g1="F.sc"
  g2="AE.sc,AEacute.sc"
  k="117" />
    <hkern g1="F.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="66" />
    <hkern g1="F.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="10" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="10" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="29" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="66" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="27" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="V.sc"
  k="25" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="X.sc"
  k="20" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="bracketright"
  k="51" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="backslash"
  k="61" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="trademark"
  k="39" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="braceright"
  k="76" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="parenright"
  k="76" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="bracketright"
  k="20" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="braceright"
  k="29" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="parenright"
  k="31" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="slash"
  k="-20" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="45" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="76" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="35" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="8" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="37" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="90" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="98" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="131" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quotedbl,quotesingle"
  k="98" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="92" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="94" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="V.sc"
  k="96" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="12" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="53" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="space"
  k="55" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="bracketright"
  k="49" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="backslash"
  k="139" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="trademark"
  k="119" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="braceright"
  k="98" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="asterisk"
  k="98" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="parenright"
  k="80" />
    <hkern g1="M.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="18" />
    <hkern g1="M.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="M.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="45" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="20" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="25" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="76" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="AE.sc,AEacute.sc"
  k="53" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="V.sc"
  k="29" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="X.sc"
  k="39" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="18" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="bracketright"
  k="63" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="backslash"
  k="72" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="trademark"
  k="43" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="braceright"
  k="88" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="parenright"
  k="88" />
    <hkern g1="P.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="51" />
    <hkern g1="P.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="25" />
    <hkern g1="P.sc"
  g2="AE.sc,AEacute.sc"
  k="119" />
    <hkern g1="P.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="80" />
    <hkern g1="P.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="39" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="V.sc"
  k="16" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="bracketright"
  k="47" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="backslash"
  k="45" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="trademark"
  k="31" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="braceright"
  k="63" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="parenright"
  k="57" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="14" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="43" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="V.sc"
  k="20" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="X.sc"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="bracketright"
  k="45" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="backslash"
  k="49" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="trademark"
  k="39" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="braceright"
  k="61" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="parenright"
  k="61" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="AE.sc,AEacute.sc"
  k="117" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="70" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="slash"
  k="55" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotleft,guilsinglleft"
  k="66" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="hyphen,endash,emdash"
  k="66" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="M.sc"
  k="18" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="space"
  k="49" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="asterisk"
  k="-10" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="14" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="AE.sc,AEacute.sc"
  k="37" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="X.sc"
  k="8" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="bracketright"
  k="20" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="braceright"
  k="27" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="parenright"
  k="31" />
    <hkern g1="V.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="V.sc"
  g2="AE.sc,AEacute.sc"
  k="129" />
    <hkern g1="V.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="V.sc"
  g2="guillemotleft,guilsinglleft"
  k="45" />
    <hkern g1="V.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="29" />
    <hkern g1="V.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="V.sc"
  g2="colon,semicolon"
  k="20" />
    <hkern g1="V.sc"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="AE.sc,AEacute.sc"
  k="123" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="slash"
  k="61" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="hyphen,endash,emdash"
  k="27" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="M.sc"
  k="23" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="space"
  k="51" />
    <hkern g1="X.sc"
  g2="guillemotleft,guilsinglleft"
  k="49" />
    <hkern g1="X.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="X.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="X.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="8" />
    <hkern g1="X.sc"
  g2="hyphen,endash,emdash"
  k="41" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="113" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="AE.sc,AEacute.sc"
  k="176" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="119" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="slash"
  k="104" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotleft,guilsinglleft"
  k="113" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="76" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="colon,semicolon"
  k="49" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="hyphen,endash,emdash"
  k="92" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="M.sc"
  k="45" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="space"
  k="66" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="asterisk"
  k="-25" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotright,guilsinglright"
  k="29" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="ampersand.sc"
  k="35" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="Thorn.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="Thorn.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="18" />
    <hkern g1="Thorn.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="Thorn.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="74" />
    <hkern g1="Thorn.sc"
  g2="AE.sc,AEacute.sc"
  k="61" />
    <hkern g1="Thorn.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="31" />
    <hkern g1="Thorn.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="18" />
    <hkern g1="seven"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="125" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="23" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="98" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="90" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="AE,AEacute"
  k="57" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="V"
  k="27" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="X"
  k="57" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="27" />
    <hkern g1="questiondown.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="20" />
    <hkern g1="questiondown.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="29" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="35" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="AE,AEacute"
  k="31" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="76" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="117" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="41" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="AE,AEacute"
  k="96" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="V"
  k="53" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="X"
  k="88" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="63" />
    <hkern g1="three.oldstyle"
  g2="quotedbl,quotesingle"
  k="35" />
    <hkern g1="four.oldstyle"
  g2="quotedbl,quotesingle"
  k="39" />
    <hkern g1="seven.oldstyle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="66" />
    <hkern g1="nine.oldstyle"
  g2="quotedbl,quotesingle"
  k="35" />
    <hkern g1="longs"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="-39" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="v"
  k="27" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="space"
  k="23" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="braceright"
  k="23" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="bracketright"
  k="68" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="backslash"
  k="102" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="trademark"
  k="59" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="braceright"
  k="94" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="asterisk"
  k="31" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="parenright"
  k="88" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="question"
  k="20" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="18" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="61" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="slash"
  k="53" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="guillemotleft,guilsinglleft"
  k="23" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="space"
  k="49" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="bracketright"
  k="29" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="trademark"
  k="16" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="braceright"
  k="31" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="parenright"
  k="27" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="bracketright"
  k="41" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="backslash"
  k="41" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="trademark"
  k="33" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="braceright"
  k="53" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="parenright"
  k="49" />
    <hkern g1="parenleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-12" />
    <hkern g1="parenleft.case"
  g2="AE,AEacute"
  k="-18" />
    <hkern g1="parenleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="102" />
    <hkern g1="parenleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="55" />
    <hkern g1="bracketleft.case"
  g2="AE,AEacute"
  k="-14" />
    <hkern g1="bracketleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="61" />
    <hkern g1="bracketleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="41" />
    <hkern g1="braceleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-10" />
    <hkern g1="braceleft.case"
  g2="AE,AEacute"
  k="-23" />
    <hkern g1="braceleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="100" />
    <hkern g1="braceleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="53" />
  </font>
</defs></svg>
