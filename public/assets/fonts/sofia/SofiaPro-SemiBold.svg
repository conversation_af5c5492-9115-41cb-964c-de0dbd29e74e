<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Mon Dec 14 13:22:27 2020
 By <PERSON><PERSON><PERSON>,,,
Copyright (c) Olivier <PERSON> - Mostardesign Studio, 2012. All rights reserved.
</metadata>
<defs>
<font id="SofiaPro-SemiBold" horiz-adv-x="1161" >
  <font-face 
    font-family="Sofia Pro Semi Bold"
    font-weight="600"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 0 0 0 0 0 0 0 0"
    ascent="1638"
    descent="-410"
    x-height="956"
    cap-height="1411"
    bbox="-362 -561 2310 2273"
    underline-thickness="102"
    underline-position="-307"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="1515" 
d="M1421 1477h105v-240h-105q-155 0 -155 -205v-76h202v-200h-202v-756h-252v756h-533v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5h104v-240h-104q-156 0 -156 -205v-76h533v76q0 212 105 328.5t302 116.5z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1347" 
d="M481 956h727v-956h-247v756h-480v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5h104v-240h-104q-156 0 -156 -205v-76zM1085 1176q-67 0 -114 44t-47 107t47.5 106.5t113.5 43.5t112 -43.5t46 -106.5t-46 -107t-112 -44z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1361" 
d="M637 1477h584v-1477h-250v1237h-334q-156 0 -156 -205v-76h203v-200h-203v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="2131" 
d="M1266 956h727v-956h-248v756h-479v-756h-252v756h-533v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5h104v-240h-104q-156 0 -156 -205v-76h533v76q0 212 105 328.5t302 116.5h105v-240h-105q-155 0 -155 -205v-76zM1870 1176q-68 0 -115 44t-47 107
t48 106.5t114 43.5t112 -43.5t46 -106.5t-46 -107t-112 -44z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="2146" 
d="M2005 0h-250v1237h-334q-155 0 -155 -205v-76h202v-200h-202v-756h-252v756h-533v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5h104v-240h-104q-156 0 -156 -205v-76h533v76q0 212 105 328.5t302 116.5h584v-1477z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="2107" 
d="M1612 -477h-158v229h121q144 0 141 180v824h-450v-756h-252v756h-533v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5h104v-240h-104q-156 0 -156 -205v-76h533v76q0 212 105 328.5t302 116.5h105v-240h-105q-155 0 -155 -205v-76h702v-1019
q0 -414 -356 -414zM1681 1327q0 62 47 106t113 44q67 0 113.5 -43.5t46.5 -106.5t-46.5 -107t-113.5 -44q-66 0 -113 44t-47 107z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="1323" 
d="M827 -477h-157v229h121q144 0 141 180v824h-451v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5h104v-240h-104q-156 0 -156 -205v-76h703v-1019q0 -414 -357 -414zM897 1327q0 62 47 106t113 44q67 0 113.5 -43.5t46.5 -106.5t-46.5 -107t-113.5 -44
q-66 0 -113 44t-47 107z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="507" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="573" 
d="M180 412l-39 999h293l-39 -999h-215zM285 291q68 0 116 -44.5t48 -111.5t-48 -111t-116 -44q-66 0 -113 44.5t-47 110.5t47 111t113 45z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="722" 
d="M295 809h-141l-21 602h182zM569 809h-141l-20 602h182z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1431" 
d="M1237 1411l-104 -356h227l-51 -185h-230l-102 -348h227l-51 -184h-227l-107 -338h-207l103 338h-211l-105 -338h-204l102 338h-225l51 184h225l101 348h-222l49 185h226l104 356h209l-102 -356h211l104 356h209zM870 870h-213l-102 -348h211z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1165" 
d="M516 1423v215h141v-217q174 -24 280.5 -136.5t106.5 -283.5h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -171 -131 -275q-120 -105 -299 -120v-232h-141v232
q-191 21 -311 141q-119 119 -119 289h262q5 -84 71.5 -141.5t172.5 -57.5q101 0 164.5 47.5t68.5 122.5q4 69 -43.5 116t-150.5 76l-174 46q-357 105 -357 393q0 167 119 280q120 114 297 129z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1409" 
d="M1098 1354l-666 -1354h-139l665 1354h140zM346 1364q113 0 193 -80q82 -79 82 -194t-80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80zM463 1090q0 47 -35 81.5t-82 34.5q-36 3 -64.5 -18.5t-41 -52.5t-7.5 -67t31 -60q24 -26 60 -31
t67.5 7.5t53 41t18.5 64.5zM1335 252q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195q0 113 78 194q80 80 195 80t194 -82q80 -80 80 -192zM1178 252q0 47 -35 82t-82 35q-36 3 -64.5 -18.5t-41 -53t-7.5 -67.5t31 -60q24 -26 60 -31t67.5 7.5t53 41
t18.5 64.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1396" 
d="M864 653h-256q-126 0 -186 -75q-60 -78 -51 -158q11 -92 73 -144.5t164 -52.5q109 0 182.5 77t73.5 212v141zM580 -20q-205 0 -340 120q-134 122 -134 306q0 128 58.5 222.5t167.5 141.5q-68 33 -110 106t-42 166q0 153 113 263t282 116q245 9 383 -188l-210 -129
q-58 76 -142 76q-72 0 -116 -44t-44 -108q0 -21 11 -63q14 -41 56.5 -75t102.5 -34h248v168h234v-168h170v-203h-170v-233q0 -127 39 -172q41 -47 159 -47v-221q-163 2 -249.5 39t-120.5 128q-43 -76 -144 -122q-99 -45 -202 -45z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="448" 
d="M295 809h-141l-21 602h182z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="774" 
d="M86 469q0 425 174 690q176 268 445 268v-208q-162 0 -283 -222q-119 -219 -119 -528q0 -306 119 -522q120 -217 283 -217v-207q-269 0 -445 262q-174 259 -174 684z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="774" 
d="M688 469q0 -425 -174 -684q-176 -262 -444 -262v207q160 0 280 217q121 219 121 522q0 306 -121 528t-280 222v208q268 0 444 -268q174 -265 174 -690z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="970" 
d="M334 670l-146 110l170 209l-262 74l60 176l252 -98l-17 270h189l-19 -268l256 96l55 -176l-260 -70l172 -215l-147 -104l-148 223z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1046" 
d="M944 436h-321v-323h-199v323h-322v199h322v319h199v-319h321v-199z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="555" 
d="M176 -270l-166 61q71 99 112 231.5t40 270.5h258q-6 -199 -80 -344q-73 -144 -164 -219z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="976" 
d="M848 391h-719v209h719v-209z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="557" 
d="M276 291q68 0 116 -44.5t48 -111.5t-48 -111t-116 -44q-66 0 -112.5 44.5t-46.5 110.5t46.5 111t112.5 45z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="1026" 
d="M61 0l684 1411h220l-684 -1411h-220z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1234" 
d="M123 670q0 694 498 694q280 0 393 -205q98 -178 98 -489q0 -121 -13.5 -219.5t-48 -188t-89 -150.5t-141 -96.5t-199.5 -35.5q-498 0 -498 690zM375 670q0 -445 246 -445q241 0 241 445q0 448 -241 448q-246 0 -246 -448z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="741" 
d="M80 920v225l477 211v-1356h-252v1010z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1107" 
d="M969 240v-240h-856v129l546 655q82 94 82 177q0 68 -54 113.5t-138 45.5q-82 0 -139.5 -54t-69.5 -140l-234 43q26 178 152 288q122 107 301 107q178 0 305 -115t127 -288q0 -181 -143 -343l-332 -378h453z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1118" 
d="M528 596h-143v223h143q83 -3 117 49q35 53 35 99q0 54 -30 97t-89 54q-132 15 -207 -82l-196 129q57 97 167 151.5t238 47.5q171 -9 279 -117t108 -258q0 -79 -45 -149t-121 -113q104 -40 168 -147q65 -109 56 -218q-18 -180 -168 -288q-149 -107 -348 -92
q-135 7 -247.5 84.5t-168.5 197.5l108 53l111 58q31 -64 91.5 -107t125.5 -43q97 0 161 56.5t64 142.5q0 14 -4 32.5t-18 44.5t-35.5 47t-61 35.5t-90.5 12.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1226" 
d="M707 977l-297 -467h297v467zM1135 510v-219h-179v-291h-249v291h-662l668 1048h243v-829h179z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1191" 
d="M842 459q0 110 -71 170.5t-187 60.5q-182 0 -308 -135l-143 86l113 698h762v-231h-562l-47 -274q83 71 248 71q193 0 318 -119q127 -118 127 -327q0 -213 -148 -346q-145 -133 -387 -133q-139 0 -266 77t-199 205q7 2 105 66q93 61 106 65q42 -74 111.5 -120t142.5 -46
q127 0 206 66.5t79 165.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1173" 
d="M805 424q2 85 -55.5 145.5t-141.5 61.5q-81 3 -145 -58q-61 -58 -64 -143q0 -86 58 -146.5t143 -60.5q85 -2 144.5 56.5t60.5 144.5zM649 864q169 0 291 -125q126 -126 123 -329q-3 -190 -137 -312q-133 -121 -338 -118q-187 3 -316 129q-130 127 -145 335
q-6 73 -7 142.5t4 144t16 141.5t31.5 132.5t49 119.5t70 100.5t92.5 77.5t118 47.5t146 14.5q143 -6 242 -65.5t162 -194.5l-232 -98q-58 122 -180 122q-138 0 -205 -122q-64 -119 -55 -293q21 60 98 105.5t172 45.5z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1107" 
d="M98 1104v235h981l-641 -1339h-286l530 1104h-584z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1196" 
d="M446 991q0 -62 43 -103.5t111 -41.5q66 0 107 42t41 103q0 58 -42.5 98.5t-107.5 40.5q-66 0 -109 -41t-43 -98zM354 442q0 -90 72 -159q72 -66 172 -66q98 0 170 66q72 69 72 159q0 89 -68 154q-68 68 -160 68h-37q-92 0 -157 -68q-64 -67 -64 -154zM203 999
q0 150 114 258q113 107 281 107q166 0 279 -107q116 -107 116 -258q0 -136 -100 -229q93 -54 145 -142.5t52 -193.5q0 -189 -144 -321q-145 -133 -348 -133q-205 0 -350 133q-144 132 -144 321q0 107 52 194t145 142q-98 94 -98 229z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1175" 
d="M778 911q0 86 -57.5 147t-142.5 62t-144 -57t-61 -143t55 -145.5t141 -61.5q85 -3 146 55q60 57 63 143zM526 479q-165 0 -290 125q-124 127 -121 330q3 189 135 309q134 122 338 119q189 -3 315 -129q130 -127 148 -334q5 -90 7 -183t-11.5 -184.5t-34.5 -174.5
t-63.5 -153.5t-98 -121.5t-137.5 -78.5t-183 -23.5q-143 6 -241.5 65.5t-161.5 194.5l231 98q58 -123 181 -123q137 0 202 121q67 119 58 295q-21 -60 -99 -106t-174 -46z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="581" 
d="M176 23q-47 44 -47 112t47 111q47 44 115 44t115 -44q47 -43 47 -111t-47 -112q-47 -43 -115 -43t-115 43zM176 709q-47 44 -47 112t47 111q47 44 115 44t115 -44q47 -43 47 -111t-47 -112q-47 -43 -115 -43t-115 43z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="595" 
d="M193 -270l-170 61q156 211 153 502h260q-6 -199 -80 -344t-163 -219zM197 709q-47 44 -47 112t47 111q47 44 114.5 44t114.5 -44q47 -43 47 -111t-47 -112q-47 -43 -114.5 -43t-114.5 43z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1286" 
d="M1126 307v-215l-1032 393v213l1032 394v-215l-755 -285z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1228" 
d="M1085 877v-193h-942v193h942zM143 276v193h942v-193h-942z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1286" 
d="M1192 698v-213l-1032 -393v215l755 285l-755 285v215z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="1005" 
d="M528 477l-4 -67h-243l-5 67q0 136 50 230q17 36 69 73q55 43 86 56l121 61q78 35 78 133q0 63 -52 105.5t-126 42.5q-76 0 -125 -50t-49 -129h-248q0 195 117 312q119 116 305 116q188 0 307 -110q121 -109 121 -289q0 -140 -78 -244t-205 -143q-64 -21 -91.5 -57
t-27.5 -107zM244 137q0 66 47 111t115 45t114.5 -45t46.5 -111q0 -67 -46.5 -112t-114.5 -45t-115 45t-47 112z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1980" 
d="M1208 272v260q0 107 -73 177q-71 71 -174 71t-177 -71q-71 -71 -71 -177q0 -101 71 -172q74 -71 177 -71q94 0 176 67v-186q-88 -49 -176 -49q-173 0 -295 119q-121 121 -121 292q0 174 121 295q124 121 295 121q167 0 288 -112q123 -114 129 -283v-293q0 -127 131 -127
q64 0 111.5 42t71.5 112q41 122 41 272q0 295 -222 496q-219 198 -530 198q-301 0 -508 -213t-207 -518q0 -302 207 -512q206 -209 508 -209h59v-170h-59q-374 0 -629 258q-256 259 -256 633q0 375 256 637q258 264 629 264q144 4 284 -38t255.5 -122.5t203 -187
t135.5 -240.5t46 -276q0 -201 -86 -387q-44 -94 -126 -150.5t-184 -56.5q-136 0 -218.5 76.5t-82.5 230.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1339" 
d="M1339 0h-276l-129 342h-526l-129 -342h-279l528 1411h283zM848 571l-178 539l-174 -539h352z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1298" 
d="M1135 1028q0 -93 -46.5 -166t-140.5 -108q122 -30 193 -129q69 -97 69 -201q0 -178 -120 -301t-318 -123h-596v1411h553q186 0 296 -103t110 -280zM438 1178v-334h273q68 0 109.5 47.5t41.5 122.5t-42.5 119.5t-117.5 44.5h-264zM752 625h-314v-392h320q79 0 128.5 57
t49.5 138q0 79 -50.5 138t-133.5 59z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1419" 
d="M1167 418l207 -152q-95 -135 -245 -210.5t-328 -75.5q-302 0 -506 210q-205 211 -205 512q0 304 205 515q204 210 506 210q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-189 0 -320 -140q-129 -138 -129 -338q0 -199 129 -337t320 -138q109 0 205.5 51
t160.5 140z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1462" 
d="M727 1171h-289v-931h289q183 0 277 131q98 131 98 331q0 205 -98 336q-95 133 -277 133zM727 0h-551v1411h551q294 0 465 -201q172 -199 172 -508q0 -306 -172 -505q-170 -197 -465 -197z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1171" 
d="M1069 0h-893v1411h873v-233h-613v-367h537v-231h-537v-347h633v-233z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1093" 
d="M438 0h-262v1411h862v-233h-600v-367h475v-233h-475v-578z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1548" 
d="M1321 1212l-193 -168q-132 138 -319 138q-193 0 -326 -142q-131 -137 -131 -338q0 -200 131 -340q132 -141 326 -141q149 0 262 82q116 84 119 211h-387v215h688q0 -368 -180 -558.5t-502 -190.5q-303 0 -510 210q-209 212 -209 512t209 512q210 213 510 213
q312 0 512 -215z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1441" 
d="M1001 819v592h265v-1411h-265v584h-565v-584h-260v1411h260v-592h565z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="612" 
d="M436 0h-260v1411h260v-1411z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="614" 
d="M-203 -446l25 237q59 -20 164 -20q94 0 142 60.5t48 162.5v1417h262v-1417q0 -218 -113 -344.5t-339 -126.5q-62 0 -189 31z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1224" 
d="M1126 1411l-536 -700l663 -711h-342l-471 522v-522h-264v1411h264v-514l379 514h307z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1036" 
d="M983 0h-807v1411h262v-1178h545v-233z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1882" 
d="M489 1411l451 -954l449 954h225l172 -1411h-262l-109 940l-368 -780h-209l-371 780l-111 -940h-262l174 1411h221z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1396" 
d="M958 1411h263v-1411h-240l-545 928v-928h-260v1411h244l538 -917v917z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1611" 
d="M481 1032q-131 -134 -131 -330q0 -195 131 -329q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1253" 
d="M745 1178h-307v-443h299q90 0 144.5 64.5t54.5 154.5q0 92 -51.5 158t-139.5 66zM438 0h-262v1411h574q219 0 333 -135q115 -133 115 -322q0 -188 -115 -321q-113 -131 -333 -131h-312v-502z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1622" 
d="M1214 102l140 -227l-189 -115l-153 248q-106 -28 -203 -28q-303 0 -510 210q-209 212 -209 512t209 512q210 213 510 213q307 0 514 -210q209 -212 209 -515q0 -182 -84.5 -341t-233.5 -259zM748 442l188 113l143 -233q189 136 189 380q0 199 -133 338q-134 140 -326 140
t-326 -140q-131 -137 -131 -338q0 -174 109 -307q108 -129 276 -153q63 -10 133 -4z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1292" 
d="M729 1178h-291v-443h283q93 0 146 63.5t53 155.5t-51.5 158t-139.5 66zM834 512l360 -512h-315l-332 502h-109v-502h-262v1411h565q220 0 334 -135q117 -132 117 -322q0 -161 -92 -288q-89 -127 -266 -154z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1181" 
d="M94 412h262q5 -84 71.5 -141.5t172.5 -57.5q101 0 165 47.5t69 122.5q4 69 -44 116t-151 76l-174 46q-356 105 -356 393q0 184 137 297q137 116 338 116q205 0 336 -116q133 -115 133 -310h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162
l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -186 -152 -293q-140 -107 -344 -107q-224 0 -364 125q-142 127 -142 310z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1173" 
d="M455 1171h-398v240h1059v-240h-397v-1171h-264v1171z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1177" 
d="M471 0l-430 1411h272l275 -983l274 983h273l-428 -1411h-236z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1826" 
d="M1018 1214l213 -786l303 983h285l-480 -1411h-211l-217 801l-213 -801h-213l-475 1411h283l301 -983l215 786h209z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1316" 
d="M942 1411h313l-442 -657l492 -754h-312l-336 543l-335 -543h-310l490 754l-443 657h314l284 -448z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1318" 
d="M793 639v-639h-269v643l-540 768h311l362 -543l367 543h313z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1202" 
d="M86 0v205l680 971h-653v235h991v-207l-686 -968h686v-236h-1018z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="679" 
d="M606 -465h-450v1876h450v-194h-229v-1485h229v-197z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="1026" 
d="M745 0l-684 1411h220l684 -1411h-220z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="679" 
d="M74 1411h450v-1876h-450v197h229v1485h-229v194z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1490" 
d="M1362 -254h-1233v191h1233v-191z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="1103" 
d="M547 1114l-283 334h262l215 -334h-194z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1241" 
d="M649 219q107 0 179.5 73.5t72.5 188.5q0 114 -72.5 186t-179.5 72q-101 0 -178 -71q-76 -70 -76 -187q0 -115 74 -188.5t180 -73.5zM391 129v-129h-250v1477h250v-648q36 68 118 108t181 40q193 0 332 -139q137 -137 137 -361q0 -223 -137 -360t-332 -137
q-98 0 -180.5 40.5t-118.5 108.5z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1064" 
d="M596 977q121 0 225 -49t172 -137l-198 -150q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l200 -147q-67 -90 -172 -139t-227 -49q-215 0 -365 141q-151 142 -151 360t151 357t365 139z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1241" 
d="M1100 0h-248v129q-38 -68 -120.5 -108.5t-180.5 -40.5q-195 0 -332 137t-137 360q0 224 137 361q139 139 332 139q99 0 181 -40t120 -108v648h248v-1477zM592 739q-107 0 -179.5 -72t-72.5 -186q0 -115 72 -190q75 -72 180 -72t180 72q76 73 76 190q0 115 -78 187
q-77 71 -178 71z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="731" 
d="M637 1477h104v-240h-104q-156 0 -156 -205v-76h203v-200h-203v-756h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1251" 
d="M860 479q0 116 -74.5 187t-185.5 71q-107 0 -182.5 -73.5t-75.5 -184.5q0 -115 74 -188.5t184 -73.5q102 0 180 72q80 74 80 190zM119 -195l203 103q31 -69 103 -113.5t150 -44.5q161 0 232.5 102.5t58.5 278.5q-35 -68 -120 -109.5t-185 -41.5q-195 0 -338 137
q-141 138 -141 358q0 225 141 363q145 139 338 139q98 0 182.5 -41t122.5 -109v129h244v-819q0 -308 -137.5 -461t-397.5 -153q-149 0 -271 75.5t-185 206.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1202" 
d="M391 510v-510h-250v1477h250v-668q29 78 121 123q89 45 176 45q184 0 284.5 -117t100.5 -328v-532h-250v520q0 96 -58.5 154.5t-143.5 58.5q-99 0 -164.5 -56.5t-65.5 -166.5z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="530" 
d="M141 956h248v-956h-248v956zM266 1176q-68 0 -115 44t-47 107t48 106.5t114 43.5t112 -43.5t46 -106.5t-46 -107t-112 -44z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="534" 
d="M37 -477h-158v229h121q144 0 141 180v1024h252v-1019q0 -414 -356 -414zM106 1327q0 62 47 106t113 44q67 0 113.5 -43.5t46.5 -106.5t-46.5 -107t-113.5 -44q-66 0 -113 44t-47 107z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1062" 
d="M1063 0h-313l-361 410v-410h-248v1477h248v-807l264 286h324l-402 -419z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="532" 
d="M391 0h-250v1477h250v-1477z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1769" 
d="M1212 733q-89 0 -141.5 -59t-52.5 -164v-510h-250v526q-1 95 -50.5 151t-125.5 56q-91 0 -146 -56t-55 -167v-510h-250v956h250v-141q35 76 112.5 118t162.5 42q229 -12 305 -178q101 180 301 180q178 0 273 -110.5t95 -319.5v-547h-247v520q0 98 -50 155.5t-131 57.5z
" />
    <glyph glyph-name="n" unicode="n" 
d="M391 510v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 263.5 -106t92.5 -306v-565h-250v520q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1159" 
d="M829 481q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5q0 -114 71.5 -185t178.5 -71t178 71t71 185zM932 121q-145 -139 -352 -139q-210 0 -355 139q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1239" 
d="M141 956h250v-129q36 68 118.5 109t180.5 41q191 0 330 -139q137 -137 137 -359q0 -225 -137 -362q-134 -137 -330 -137q-98 0 -180.5 39.5t-118.5 107.5v-592h-250v1421zM395 475q0 -116 73.5 -187t180.5 -71t179.5 72t72.5 186q0 115 -74 189q-73 73 -178 73
q-108 0 -181 -73.5t-73 -188.5z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1239" 
d="M844 475q0 115 -73 188.5t-181 73.5q-106 0 -178 -73.5t-72 -188.5q0 -114 72 -186t178 -72q102 0 178 70t76 188zM848 827v129h250v-1421h-250v592q-36 -68 -118.5 -107.5t-180.5 -39.5q-195 0 -332 137q-135 138 -135 362q0 222 137 359q139 139 330 139
q98 0 180.5 -41t118.5 -109z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="841" 
d="M391 338v-338h-250v956h250v-225q34 119 107 182.5t164 63.5q92 0 157 -35l-43 -225q-56 26 -145 26q-240 0 -240 -405z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="929" 
d="M504 367l-131 32q-71 20 -122 46.5t-79.5 53t-44.5 61.5t-20.5 63.5t-4.5 68.5q0 116 105 199q106 86 248 86q149 0 258 -74q108 -73 114 -227h-237q-1 47 -38.5 72.5t-90.5 25.5q-121 0 -121 -94q0 -54 100 -80l160 -39q77 -20 130.5 -57t77.5 -81t33 -79t9 -70
q0 -140 -117 -217q-115 -77 -274 -77q-151 0 -269 86q-113 88 -116 235h233q5 -51 57 -87t119 -34q54 2 89.5 28.5t35.5 67.5q2 35 -23 57t-81 34z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="718" 
d="M221 1321h248v-365h203v-200h-203v-756h-248v756h-174v200h174v365z" />
    <glyph glyph-name="u" unicode="u" 
d="M770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1060" 
d="M641 0h-221l-402 956h267l245 -637l246 637h268z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1585" 
d="M1288 956h266l-342 -956h-208l-213 641l-207 -641h-207l-352 956h266l194 -604l193 604h221l195 -604z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1064" 
d="M14 0l377 520l-313 436h293l164 -235l159 235h293l-313 -436l375 -520h-291l-226 319l-227 -319h-291z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1046" 
d="M762 956h266l-592 -1423h-264l219 541l-373 882h267l237 -596z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1040" 
d="M115 723v233h825v-192l-502 -531h502v-233h-856v190l494 533h-463z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="833" 
d="M70 389v170q51 0 88 33t37 82v332q0 182 116 292q119 113 293 113h160v-192h-166q-78 0 -132 -56.5t-54 -140.5v-313q0 -83 -38.5 -147t-101.5 -89q62 -24 101 -87t39 -146v-301q0 -85 55 -146t131 -61h166v-197h-160q-174 0 -293 113q-116 110 -116 293v335
q0 48 -37 80.5t-88 32.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="452" 
d="M156 -250v1888h141v-1888h-141z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="833" 
d="M764 559v-170q-51 0 -88 -32.5t-37 -80.5v-335q0 -183 -119 -293q-116 -113 -291 -113h-159v197h166q76 0 131 61t55 146v301q0 83 39 146t100 87q-62 25 -100.5 88.5t-38.5 147.5v313q0 84 -54 140.5t-132 56.5h-166v192h159q175 0 291 -113q119 -110 119 -292v-332
q0 -49 37 -82t88 -33z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="542" 
d="M379 545l39 -1010h-293l39 1010h215zM268 977q68 0 116 -44.5t48 -111.5t-48 -111t-116 -44q-66 0 -112.5 44.5t-46.5 110.5t46.5 111t112.5 45z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1095" 
d="M508 1188h141v-213q226 -18 352 -184l-198 -150q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l201 -147q-128 -168 -355 -186v-232h-141v238q-181 34 -301 166q-119 131 -119 327q0 193 119 324q117 129 301 164v219z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1357" 
d="M809 705v-127h-262v-349h389q82 0 82 64v80h248v-111q0 -113 -67 -187.5t-171 -74.5h-940v229h209v349h-166v127h166v258q0 211 117 337q121 127 313 127q190 0 315 -116q127 -118 127 -334h-256q0 96 -52.5 144.5t-137.5 43.5q-76 -2 -126 -50.5t-50 -125.5v-284h262z
" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1265" 
d="M414 346l-146 -147l-131 131l146 147q-64 94 -64 217q0 122 66 219l-148 148l131 131l148 -148q101 64 217 64q125 0 219 -64l145 148l131 -131l-145 -148q64 -107 64 -219q0 -129 -64 -217l145 -147l-131 -131l-145 147q-97 -66 -219 -66t-219 66zM444 694
q0 -78 55.5 -133t133.5 -55q79 0 134.5 55t55.5 133q0 80 -55.5 135.5t-134.5 55.5q-78 0 -133.5 -56t-55.5 -135z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1480" 
d="M1417 1411l-532 -752h325v-137h-338v-180h338v-135h-338v-207h-268v207h-336v135h336v180h-336v137h324l-529 752h312l362 -543l367 543h313z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="460" 
d="M160 -250v789h141v-789h-141zM160 1638h141v-788h-141v788z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1089" 
d="M647 436v-225q-55 -14 -98 -14q-178 0 -301 114q-123 117 -123 303v385q0 187 123 301q123 117 301 117q144 0 260 -82q118 -81 156 -233l-215 -66q-24 76 -78.5 116t-122.5 40q-83 0 -140 -54t-57 -139v-385q0 -89 57.5 -139.5t147.5 -50.5q41 0 90 12zM442 618v226
q55 14 99 14q178 0 301 -117q123 -114 123 -301v-385q0 -187 -123 -301q-122 -116 -301 -116q-144 0 -262 81q-116 82 -154 234l215 65q24 -76 78 -115.5t123 -39.5q83 0 139.5 53.5t56.5 138.5v385q0 89 -57.5 140t-147.5 51q-36 0 -90 -13z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="1128" 
d="M387 1427q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM643 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95t-41 -96q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1640" 
d="M1108 455q-127 -133 -283 -133q-154 0 -262 108q-106 106 -106 262q0 154 106 260q109 109 262 109q152 0 281 -135l-94 -97q-95 95 -187 95q-96 0 -162.5 -67.5t-66.5 -164.5q0 -94 66 -166q67 -67 163 -67q93 0 191 98zM311 190q-207 213 -207 512t207 512
q210 213 508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210zM401 1124q-172 -175 -172 -422q0 -246 172 -421t418 -175t418 175t172 421q0 247 -172 422q-173 176 -418 176t-418 -176z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="976" 
d="M815 664h-145v94q-35 -49 -95.5 -79t-123.5 -30q-140 0 -244 96q-101 98 -101 256q0 160 101 254q102 99 244 99q146 0 219 -107v92h145v-675zM670 1004q0 91 -62 147.5t-147 56.5q-87 0 -145 -57t-58 -147t58 -149.5t145 -59.5q90 0 149.5 57t59.5 152zM811 416h-645
v127h645v-127z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1021" 
d="M367 53l-283 424l283 424h200l-282 -424l282 -424h-200zM729 53l-283 424l283 424h201l-283 -424l283 -424h-201z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1296" 
d="M143 532v222h998v-531h-223v309h-775z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1640" 
d="M1114 338h-172l-180 250h-47v-250h-142v723h281q107 0 170 -68.5t63 -169.5q0 -82 -40.5 -143.5t-116.5 -81.5zM715 922v-195h135q43 0 70.5 28.5t27.5 67.5q0 42 -26 70.5t-68 28.5h-139zM311 190q-207 213 -207 512t207 512q210 213 508 213t508 -213
q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210zM401 1124q-172 -175 -172 -422q0 -246 172 -421t418 -175t418 175t172 421q0 247 -172 422q-173 176 -418 176t-418 -176z" />
    <glyph glyph-name="overscore" unicode="&#xaf;" horiz-adv-x="1042" 
d="M797 1198h-551v170h551v-170z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="763" 
d="M96 1143q0 116 84 200t201 84q119 0 203 -84t84 -200q0 -119 -84 -203t-203 -84q-117 0 -201 84t-84 203zM242 1143q0 -60 41 -101t98 -41q60 0 100.5 41t40.5 101t-40.5 99.5t-100.5 39.5q-57 0 -98 -41t-41 -98z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1118" 
d="M977 588h-320v-295h-198v295h-318v196h318v291h198v-291h320v-196zM977 193v-195h-836v195h836z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="649" 
d="M63 887v76l322 383q45 54 45 102q0 42 -30.5 68t-80.5 26q-48 0 -81.5 -31.5t-40.5 -85.5l-138 29q19 106 91 169t176 63q107 0 180.5 -67.5t73.5 -170.5q0 -105 -84 -201l-191 -217h262v-143h-504z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="655" 
d="M203 1495l-58 37q-47 31 -55 39q36 56 100 87.5t138 27.5q99 -6 163 -69.5t64 -150.5q0 -100 -98 -155q61 -23 99 -85t32 -126q-9 -100 -99 -166t-202 -57q-79 6 -146.5 50.5t-99.5 112.5q77 37 127 66q45 -79 129 -88q51 -1 89.5 28t43.5 72q2 49 -28.5 84.5t-94.5 34.5
h-86v129h86q47 0 71 33.5t19 68.5q-2 32 -27.5 56t-62.5 24q-63 3 -104 -53z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="1103" 
d="M264 1114l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="mu" unicode="&#xb5;" 
d="M389 -365h-233v1321h250v-524q0 -100 50 -154.5t126 -54.5q93 0 149.5 56t56.5 167v510h250v-956h-250v127q-37 -67 -108.5 -107t-147.5 -40q-98 0 -161 57l18 -258v-144z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1417" 
d="M514 1411h176v-1438q0 -59 -12 -71q-16 -13 -49 -13h-135v633q-173 9 -301 140q-123 129 -123 305q0 184 129 313q128 131 315 131zM1067 1217h-59v-1338q0 -106 -86 -192q-82 -85 -195 -88h-57h-178v176h223q41 0 70.5 28t29.5 66v1542h248q113 0 197 -84
q83 -83 83 -197v-174h-184v162q0 41 -27 70t-65 29z" />
    <glyph glyph-name="middot" unicode="&#xb7;" horiz-adv-x="569" 
d="M283 797q68 0 115.5 -44.5t47.5 -111.5t-47.5 -111.5t-115.5 -44.5q-66 0 -113 45t-47 111t47 111t113 45z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="1081" 
d="M485 -250q10 -26 34.5 -37.5t53.5 -3.5q57 20 35 94l-94 256h121l104 -192q19 -36 19 -94q0 -86 -62.5 -147.5t-152.5 -61.5q-89 0 -149.5 68t-59.5 178l141 -9q0 -24 10 -51z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="434" 
d="M330 887h-150v581l-135 -61v133l285 143v-796z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="950" 
d="M469 649q-150 0 -252 99t-102 258q0 154 102 249q105 99 252 99q148 0 250 -99q104 -94 104 -249q0 -157 -104 -258q-102 -99 -250 -99zM676 1006q0 89 -59.5 145.5t-147.5 56.5t-147.5 -56.5t-59.5 -145.5q0 -92 59.5 -151.5t147.5 -59.5t147.5 59.5t59.5 151.5z
M807 416h-655v127h655v-127z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1021" 
d="M293 901l282 -424l-282 -424h-201l283 424l-283 424h201zM655 901l283 -424l-283 -424h-200l282 424l-282 424h200z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1591" 
d="M1516 170h-105v-170h-149v170h-390l396 618h143v-487h105v-131zM1262 567l-168 -266h168v266zM1139 1370h155l-921 -1390h-160zM365 553h-150v582l-135 -62v135l285 142v-797z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1622" 
d="M1014 0v78l321 383q45 54 45 102q0 41 -30.5 67.5t-79.5 26.5q-48 0 -82 -31.5t-41 -84.5l-137 26q19 109 90.5 171.5t175.5 62.5q106 0 180 -68t74 -170q0 -105 -84 -201l-191 -219h263v-143h-504zM1139 1370h155l-921 -1390h-160zM365 553h-150v582l-135 -62v135
l285 142v-797z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1746" 
d="M236 1161l-113 78q36 56 99.5 87.5t137.5 27.5q99 -6 163.5 -69t64.5 -150q0 -101 -99 -156q61 -23 99.5 -85t32.5 -128q-9 -100 -99 -166t-203 -57q-79 6 -146 51.5t-99 114.5l127 65q46 -88 129 -88q51 -1 89.5 27.5t43.5 70.5q2 49 -28.5 85.5t-94.5 35.5h-86v129h86
q47 0 71 34t19 69q-2 32 -27.5 56t-62.5 24q-66 3 -104 -56zM1671 170h-104v-170h-150v170h-389l395 618h144v-487h104v-131zM1417 567l-168 -266h168v266zM1294 1370h156l-922 -1390h-159z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="987" 
d="M469 473l4 74h244l4 -74q0 -135 -49 -231q-16 -31 -72 -74q-53 -38 -86 -55l-119 -60q-78 -35 -78 -133q0 -62 52.5 -104.5t126.5 -42.5q76 0 125 49.5t49 128.5h248q0 -195 -119 -311q-117 -117 -303 -117q-189 0 -308 110q-120 108 -120 289q0 140 77.5 244t204.5 143
q64 21 91.5 57t27.5 107zM754 819q0 -66 -47 -110.5t-115 -44.5t-115 44.5t-47 110.5q0 67 47 112.5t115 45.5t115 -45.5t47 -112.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1339" 
d="M1339 0h-276l-129 342h-526l-129 -342h-279l528 1411h283zM848 571l-178 539l-174 -539h352zM592 1569l-283 334h262l215 -334h-194z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1339" 
d="M934 342h-526l-129 -342h-279l528 1411h283l528 -1411h-276zM496 571h352l-178 539zM547 1569l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1339" 
d="M1339 0h-276l-129 342h-526l-129 -342h-279l528 1411h283zM848 571l-178 539l-174 -539h352zM319 1575l224 334h252l225 -334h-193l-159 180l-156 -180h-193z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1339" 
d="M1339 0h-276l-129 342h-526l-129 -342h-279l528 1411h283zM848 571l-178 539l-174 -539h352zM578 1698q-19 0 -36 -24t-18 -64h-143q6 126 62 191t135 65q49 0 104 -43q56 -41 76 -41t37 22t18 62h143q-6 -123 -63 -187.5t-135 -64.5q-46 0 -105 41q-58 43 -75 43z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1339" 
d="M934 342h-526l-129 -342h-279l528 1411h283l528 -1411h-276zM496 571h352l-178 539zM492 1882q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -101 38t-41 95t41 95t101 38zM748 1653q-41 38 -41 95.5t41 94.5q41 38 100.5 38t97.5 -38q41 -37 41 -94.5t-41 -95.5
q-38 -37 -97.5 -37t-100.5 37z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1339" 
d="M934 342h-526l-129 -342h-279l528 1411h283l528 -1411h-276zM496 571h352l-178 539zM432 1794q0 99 68 170q70 70 166 70q100 0 170 -70q69 -66 69 -170q0 -98 -69.5 -165.5t-169.5 -67.5q-96 0 -166 67q-68 68 -68 166zM549 1794q0 -49 34.5 -83t82.5 -34
q51 0 85.5 33.5t34.5 83.5q0 51 -34.5 86t-85.5 35q-48 0 -82.5 -35t-34.5 -86z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1910" 
d="M-29 0l891 1411h926v-237h-610v-363h534v-233h-534v-340h630v-238h-890v2l-3 -2v334h-419l-213 -334h-312zM915 586v471l-270 -471h270z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1421" 
d="M670 -250q10 -26 34.5 -37.5t53.5 -3.5q57 20 35 94l-68 181q-271 27 -453 233q-180 204 -180 485q0 304 205 515q204 210 506 210q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-189 0 -320 -140q-129 -138 -129 -338q0 -199 129 -337t320 -138
q109 0 205.5 51t160.5 140l207 -152q-87 -123 -221 -197.5t-295 -86.5l64 -115q18 -35 18 -94q0 -86 -63 -146q-63 -63 -152 -63t-149.5 68t-59.5 178l141 -9q0 -22 11 -51z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1171" 
d="M1069 0h-893v1411h873v-233h-613v-367h537v-231h-537v-347h633v-233zM594 1569l-283 334h262l215 -334h-194z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1171" 
d="M176 1411h873v-233h-613v-367h537v-231h-537v-347h633v-233h-893v1411zM391 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1171" 
d="M1069 0h-893v1411h873v-233h-613v-367h537v-231h-537v-347h633v-233zM231 1575l224 334h252l225 -334h-193l-159 180l-156 -180h-193z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1171" 
d="M176 1411h873v-233h-613v-367h537v-231h-537v-347h633v-233h-893v1411zM432 1882q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM688 1653q-41 38 -41 95.5t41 94.5q41 38 101 38t98 -38q41 -37 41 -94.5t-41 -95.5q-38 -37 -98 -37
t-101 37z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="612" 
d="M240 1569l-283 334h262l215 -334h-194zM436 0h-260v1411h260v-1411z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="612" 
d="M176 1411h260v-1411h-260v1411zM178 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="612" 
d="M-43 1575l223 334h252l225 -334h-192l-160 180l-155 -180h-193zM436 0h-260v1411h260v-1411z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="612" 
d="M176 1411h260v-1411h-260v1411zM129 1882q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM385 1653q-41 38 -41 95.5t41 94.5q41 38 101 38t98 -38q41 -37 41 -94.5t-41 -95.5q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1478" 
d="M199 600h-193v180h193v631h546q296 0 465 -199q170 -200 170 -510q0 -308 -170 -505t-465 -197h-546v600zM741 600h-282v-356h286q183 0 277 129q98 128 98 329t-98 332q-95 133 -277 133h-286v-387h282v-180z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1396" 
d="M958 1411h263v-1411h-240l-545 928v-928h-260v1411h244l538 -917v917zM606 1698q-19 0 -35.5 -24t-17.5 -64h-143q6 126 61.5 191t134.5 65q50 0 105 -43q56 -41 75 -41q20 0 37.5 22.5t18.5 61.5h143q-6 -123 -63.5 -187.5t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43z
" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1611" 
d="M481 1032q-131 -134 -131 -330q0 -195 131 -329q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM709 1569l-283 334h262l215 -334h-194z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1611" 
d="M481 373q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137q-131 -134 -131 -330q0 -195 131 -329zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM694 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1611" 
d="M481 1032q-131 -134 -131 -330q0 -195 131 -329q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM455 1575l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1611" 
d="M715 1698q-19 0 -35.5 -24t-17.5 -64h-144q6 126 62 191t135 65q49 0 104 -43q56 -41 76 -41t37 22t18 62h144q-6 -123 -63.5 -187.5t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43zM481 1032q-131 -134 -131 -330q0 -195 131 -329q132 -135 324 -135q189 0 321 135
q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210q-207 213 -207 512z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1611" 
d="M481 373q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137q-131 -134 -131 -330q0 -195 131 -329zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM629 1882q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -101 38t-41 95t41 95t101 38zM885 1653q-41 38 -41 95.5t41 94.5q41 38 100.5 38t97.5 -38q41 -37 41 -94.5t-41 -95.5q-38 -37 -97.5 -37t-100.5 37z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1007" 
d="M889 784l-246 -243l246 -244l-139 -141l-246 243l-242 -239l-139 139l242 242l-244 243l139 138l244 -242l246 246z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1607" 
d="M354 135l-164 -168l-96 96l164 168q-170 203 -170 471q0 299 207 512t508 213q265 0 457 -165l161 165l97 -94l-164 -168q166 -202 166 -463q0 -300 -209 -512q-207 -210 -508 -210q-231 0 -414 133q-10 3 -35 22zM348 702q0 -154 92 -284l637 657q-116 94 -274 94
q-193 0 -324 -137q-131 -134 -131 -330zM803 238q189 0 321 135q133 136 133 329q0 150 -88 275l-632 -653q115 -86 266 -86z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM657 1569l-282 334h262l215 -334h-195z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM524 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM346 1575l223 334h252l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM520 1882q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM776 1653
q-41 38 -41 95.5t41 94.5q41 38 101 38t98 -38q41 -37 41 -94.5t-41 -95.5q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1318" 
d="M793 639v-639h-269v643l-540 768h311l362 -543l367 543h313zM508 1569l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1173" 
d="M856 719q0 100 -58.5 161t-146.5 66h-215v-450h221q83 0 141 62.5t58 160.5zM668 266h-232v-266h-260v1411h260v-237h232q192 0 317 -125q129 -123 129 -326q0 -204 -129 -330q-127 -127 -317 -127z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" 
d="M457 4l37 236q73 -17 120 -17q98 0 164 70t66 166q0 86 -64 159q-61 76 -174 76h-63v213h49q61 0 99 43t38 107q0 62 -46.5 101.5t-119.5 39.5q-85 0 -130.5 -56.5t-45.5 -197.5v-944h-246v1004q0 196 115 311q112 112 297 112q184 0 299 -100q117 -99 117 -244
q0 -163 -129 -249q108 -52 173.5 -156t65.5 -232q0 -204 -125 -335q-122 -131 -338 -131q-76 0 -159 24z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM598 1114l-283 334h263l215 -334h-195z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM449 1114l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM279 1120l223 334h252l225 -334h-193l-159 180l-156 -180h-192z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM539 1243q-19 0 -36 -24t-18 -64h-143q6 126 62 191t135 65q49 0 104 -43q56 -41 76 -41t37 22t18 62h144q-6 -123 -63.5 -187.5t-135.5 -64.5q-46 0 -105 41q-58 43 -75 43z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM453 1427q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -101 38t-41 95t41 95t101 38zM709 1198q-41 38 -41 96t41 95q41 38 100.5 38t97.5 -38q41 -37 41 -95t-41 -96q-38 -37 -97.5 -37t-100.5 37z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM393 1339q0 99 68 170q70 70 166 70q103 0 171 -68.5t68 -171.5q0 -98 -69.5 -165.5t-169.5 -67.5q-95 0 -166 68q-68 68 -68 165zM510 1339q0 -49 34.5 -82.5t82.5 -33.5q51 0 86 33.5t35 82.5q0 51 -35 86t-86 35
q-48 0 -82.5 -35t-34.5 -86z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="2033" 
d="M1227 565h491q-12 93 -77 148t-162 55q-101 0 -170 -54t-82 -149zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM1962 481q0 -24 -4 -86h-729q13 -93 85.5 -149
t178.5 -56q77 0 143 32.5t97 90.5l192 -102q-61 -113 -180 -172t-266 -59q-246 0 -381 178v-158h-232v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h232v-153q134 174 381 174
q209 0 346 -137t137 -359z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1069" 
d="M655 -16l64 -117q18 -35 18 -94q0 -86 -63 -146q-63 -63 -152 -63t-149.5 68t-59.5 178l142 -9q0 -24 10 -51q10 -26 34.5 -37.5t53.5 -3.5q57 20 35 94l-68 183q-187 24 -313 159q-125 137 -125 336q0 218 151 357t365 139q121 0 225 -49t172 -137l-198 -150
q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l200 -147q-122 -163 -342 -184z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM512 1114l-283 334h263l215 -334h-195z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM465 1114l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM221 1120l223 334h252l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM395 1427q57 0 98.5 -39t41.5 -94t-41.5 -94t-98.5 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM651 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95t-41 -96q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="530" 
d="M188 1114l-282 334h262l215 -334h-195zM141 0v956h248v-956h-248z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="530" 
d="M389 956v-956h-248v956h248zM139 1114l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="530" 
d="M-84 1120l223 334h252l225 -334h-192l-160 180l-155 -180h-193zM141 0v956h248v-956h-248z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="530" 
d="M389 956v-956h-248v956h248zM88 1427q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM344 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95t-41 -96q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="eth" unicode="&#xf0;" 
d="M823 1288l-161 -84l321 -428q94 -128 94 -297q0 -221 -145 -360t-352 -139q-210 0 -355 139q-143 137 -143 360q0 203 125 334q127 133 309 148q37 3 90 -7l-129 156l-174 -88l-61 98l164 84l-154 207h252l86 -115l162 80zM834 479q0 111 -73 180.5t-181 69.5t-180 -69.5
t-72 -180.5q0 -114 72 -185t180 -71t181 71.5t73 184.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M391 510v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 263.5 -106t92.5 -306v-565h-250v520q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168zM489 1243q-19 0 -35.5 -24t-17.5 -64h-143q6 126 61.5 191t134.5 65q50 0 105 -43q56 -41 76 -41t37 22
t18 62h143q-6 -123 -63 -187.5t-135 -64.5q-46 0 -105 41q-58 43 -76 43z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1159" 
d="M829 481q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5q0 -114 71.5 -185t178.5 -71t178 71t71 185zM932 121q-145 -139 -352 -139q-210 0 -355 139q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360zM518 1114
l-282 334h262l215 -334h-195z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1159" 
d="M330 481q0 -114 71.5 -185t178.5 -71t178 71t71 185q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5zM225 121q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360t-352 -139q-210 0 -355 139zM449 1114l215 334h262
l-283 -334h-194z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1159" 
d="M829 481q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5q0 -114 71.5 -185t178.5 -71t178 71t71 185zM932 121q-145 -139 -352 -139q-210 0 -355 139q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360zM229 1120
l224 334h252l225 -334h-193l-159 180l-156 -180h-193z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1159" 
d="M829 481q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5q0 -114 71.5 -185t178.5 -71t178 71t71 185zM932 121q-145 -139 -352 -139q-210 0 -355 139q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360zM487 1243
q-19 0 -35.5 -24t-17.5 -64h-143q6 126 61.5 191t134.5 65q50 0 105 -43q56 -41 76 -41t37 22t18 62h143q-6 -123 -63 -187.5t-135 -64.5q-46 0 -105 41q-58 43 -76 43z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1159" 
d="M330 481q0 -114 71.5 -185t178.5 -71t178 71t71 185q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5zM225 121q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360t-352 -139q-210 0 -355 139zM401 1427q57 0 98.5 -39
t41.5 -94t-41.5 -94t-98.5 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM657 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95t-41 -96q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M475 31q-44 43 -44 104.5t44 102.5q44 43 106.5 43t106.5 -43q44 -41 44 -102.5t-44 -104.5q-44 -41 -106.5 -41t-106.5 41zM430 885q0 61 44 102t108 41q63 0 107 -41t44 -102q0 -63 -43.5 -104.5t-107.5 -41.5t-108 42t-44 104zM115 414v186h932v-186h-932z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1159" 
d="M580 -20q-166 0 -291 88l-119 -121l-96 96l114 119q-106 127 -106 317q0 222 143 359q145 139 355 139q175 0 307 -100l104 108l96 -94l-106 -109q96 -131 96 -303q0 -221 -145 -360t-352 -139zM580 731q-105 0 -177.5 -71.5t-72.5 -180.5q0 -81 35 -137l344 354
q-59 35 -129 35zM580 223q107 0 178 71t71 185q0 68 -26 119l-338 -348q53 -27 115 -27z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M557 1114l-281 334h261l217 -334h-197zM770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M422 1114l217 334h262l-283 -334h-196zM770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167zM219 1120l223 334h252l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M397 1434q57 0 97.5 -38.5t40.5 -95.5t-40.5 -95t-97.5 -38q-60 0 -100.5 38t-40.5 95t40.5 95.5t100.5 38.5zM754 1434q57 0 98 -39.5t41 -94.5t-41 -94t-98 -39q-60 0 -101 38t-41 95t41 95.5t101 38.5zM770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42
q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1046" 
d="M436 -467h-264l219 541l-373 882h267l237 -596l240 596h266zM344 1114l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1234" 
d="M393 481q0 -116 73 -189t181 -73q106 0 178 73.5t72 188.5q0 111 -72 183.5t-178 72.5q-103 0 -178 -69q-76 -70 -76 -187zM389 119v-584h-248v1942h248v-637q41 63 120.5 100t172.5 37q195 0 334 -139q137 -137 137 -361q0 -223 -137 -360t-334 -137q-92 0 -172 37
t-121 102z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1046" 
d="M436 -467h-264l219 541l-373 882h267l237 -596l240 596h266zM352 1427q57 0 98.5 -39t41.5 -94t-41.5 -94t-98.5 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM608 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95t-41 -96q-38 -37 -98 -37t-101 37z
" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1339" 
d="M1339 0h-276l-129 342h-526l-129 -342h-279l528 1411h283zM848 571l-178 539l-174 -539h352zM944 1653h-551v170h551v-170z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM905 1198h-551v170h551v-170z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1339" 
d="M934 342h-526l-129 -342h-279l528 1411h283l528 -1411h-276zM496 571h352l-178 539zM395 1862h168q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75.5 -194t-198.5 -73q-129 0 -201 76t-72 191z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM356 1407h168q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75 -193.5t-199 -72.5q-129 0 -201 75.5t-72 190.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1339" 
d="M989 -133l72 133l-127 342h-526l-129 -342h-279l528 1411h283l528 -1411h-147l-72 -197q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5l10 51l142 9q1 -110 -59.5 -178t-149.5 -68t-152 63q-63 60 -63 146q0 59 18 94zM848 571l-178 539l-174 -539h352z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1257" 
d="M795 -133l71 133v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956h-119l-71 -197q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5l10 51l141 9q1 -110 -59.5 -178t-149.5 -68
q-90 0 -152.5 61.5t-62.5 147.5q0 58 19 94zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1419" 
d="M1167 418l207 -152q-95 -135 -245 -210.5t-328 -75.5q-302 0 -506 210q-205 211 -205 512q0 304 205 515q204 210 506 210q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-189 0 -320 -140q-129 -138 -129 -338q0 -199 129 -337t320 -138q109 0 205.5 51
t160.5 140zM682 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1064" 
d="M596 977q121 0 225 -49t172 -137l-198 -150q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l200 -147q-67 -90 -172 -139t-227 -49q-215 0 -365 141q-151 142 -151 360t151 357t365 139zM489 1114l216 334h262l-283 -334h-195z
" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1419" 
d="M1167 418l207 -152q-95 -135 -245 -210.5t-328 -75.5q-302 0 -506 210q-205 211 -205 512q0 304 205 515q204 210 506 210q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-189 0 -320 -140q-129 -138 -129 -338q0 -199 129 -337t320 -138q109 0 205.5 51
t160.5 140zM449 1575l223 334h252l225 -334h-193l-159 180l-156 -180h-192z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1064" 
d="M596 977q121 0 225 -49t172 -137l-198 -150q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l200 -147q-67 -90 -172 -139t-227 -49q-215 0 -365 141q-151 142 -151 360t151 357t365 139zM233 1120l224 334h252l225 -334h-193
l-159 180l-156 -180h-193z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1419" 
d="M1167 418l207 -152q-95 -135 -245 -210.5t-328 -75.5q-302 0 -506 210q-205 211 -205 512q0 304 205 515q204 210 506 210q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-189 0 -320 -140q-129 -138 -129 -338q0 -199 129 -337t320 -138q109 0 205.5 51
t160.5 140zM643 1741q0 60 43 100.5t105 40.5q63 0 106 -40.5t43 -100.5q0 -61 -43 -102.5t-106 -41.5q-62 0 -105 41.5t-43 102.5z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1064" 
d="M596 977q121 0 225 -49t172 -137l-198 -150q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l200 -147q-67 -90 -172 -139t-227 -49q-215 0 -365 141q-151 142 -151 360t151 357t365 139zM449 1286q0 60 42.5 100.5t104.5 40.5
q63 0 106 -40.5t43 -100.5q0 -61 -43 -102t-106 -41q-62 0 -104.5 41t-42.5 102z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1419" 
d="M1167 418l207 -152q-95 -135 -245 -210.5t-328 -75.5q-302 0 -506 210q-205 211 -205 512q0 304 205 515q204 210 506 210q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-189 0 -320 -140q-129 -138 -129 -338q0 -199 129 -337t320 -138q109 0 205.5 51
t160.5 140zM911 1569h-252l-223 334h193l155 -183l160 183h193z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1064" 
d="M596 977q121 0 225 -49t172 -137l-198 -150q-70 94 -199 94q-108 0 -188 -71t-80 -183q0 -111 80 -188q80 -74 188 -74q127 0 199 96l200 -147q-67 -90 -172 -139t-227 -49q-215 0 -365 141q-151 142 -151 360t151 357t365 139zM700 1114h-251l-224 334h193l155 -182
l160 182h193z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1462" 
d="M438 240h289q183 0 277 131q98 131 98 331q0 205 -98 336q-95 133 -277 133h-289v-931zM727 0h-551v1411h551q294 0 465 -201q172 -199 172 -508q0 -306 -172 -505q-170 -197 -465 -197zM852 1569h-252l-223 334h192l156 -183l160 183h192z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1333" 
d="M1100 0h-248v129q-38 -68 -120.5 -108.5t-180.5 -40.5q-195 0 -332 137t-137 360q0 224 137 361q139 139 332 139q99 0 181 -40t120 -108v648h248v-1477zM592 739q-107 0 -179.5 -72t-72.5 -186q0 -115 72 -190q75 -72 180 -72t180 72q76 73 76 190q0 115 -78 187
q-77 71 -178 71zM1323 926l-154 51q143 199 134 434h219q-7 -164 -63 -291.5t-136 -193.5z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1478" 
d="M199 600h-193v180h193v631h546q296 0 465 -199q170 -200 170 -510q0 -308 -170 -505t-465 -197h-546v600zM741 600h-282v-356h286q183 0 277 129q98 128 98 329t-98 332q-95 133 -277 133h-286v-387h282v-180z" />
    <glyph glyph-name="dslash" unicode="&#x111;" horiz-adv-x="1241" 
d="M852 1143h-305v157h305v177h248v-177h155v-157h-155v-1143h-248v129q-38 -68 -120.5 -108.5t-180.5 -40.5q-195 0 -332 137t-137 360q0 224 137 361q139 139 332 139q99 0 181 -40t120 -108v314zM592 739q-107 0 -179.5 -72t-72.5 -186q0 -115 72 -190q75 -72 180 -72
t180 72q76 73 76 190q0 115 -78 187q-77 71 -178 71z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1171" 
d="M1069 0h-893v1411h873v-233h-613v-367h537v-231h-537v-347h633v-233zM889 1653h-551v170h551v-170z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM848 1198h-551v170h551v-170z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1171" 
d="M176 1411h873v-233h-613v-367h537v-231h-537v-347h633v-233h-893v1411zM307 1862h168q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75.5 -194t-198.5 -73q-129 0 -201 76t-72 191z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM299 1407h168q0 -113 104 -113q49 0 77 32t28 81h170q0 -121 -75.5 -193.5t-199.5 -72.5q-129 0 -200.5 75.5t-71.5 190.5z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1171" 
d="M176 1411h873v-233h-613v-367h537v-231h-537v-347h633v-233h-893v1411zM469 1741q0 60 42.5 100.5t104.5 40.5q63 0 106.5 -40.5t43.5 -100.5q0 -61 -43.5 -102.5t-106.5 -41.5q-62 0 -104.5 41.5t-42.5 102.5z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM424 1286q0 60 42.5 100.5t104.5 40.5q63 0 106.5 -40.5t43.5 -100.5q0 -61 -43.5 -102t-106.5 -41q-62 0 -104.5 41t-42.5 102z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1171" 
d="M1069 233v-233l-76 -197q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5l10 51l142 9q1 -110 -59.5 -178t-149.5 -68t-152 63q-63 60 -63 146q0 59 18 94l72 133h-758v1411h873v-233h-613v-367h537v-231h-537v-347h633z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1134" 
d="M786 -197q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5l11 51l141 9q1 -110 -59.5 -178t-149.5 -68q-90 0 -152.5 61.5t-62.5 147.5q0 59 18 94l70 131q-71 -18 -147 -18q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359q0 -24 -4 -86
h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-24 -36 -115 -143q-27 -34 -61 -105.5t-62 -159.5zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55t-82 -148z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1171" 
d="M176 1411h873v-233h-613v-367h537v-231h-537v-347h633v-233h-893v1411zM733 1569h-252l-223 334h193l155 -183l160 183h192z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1132" 
d="M1063 481q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-208 0 -353 139q-143 140 -143 362t143 359t353 137q209 0 346 -137q139 -139 139 -359zM328 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55
t-82 -148zM698 1114h-252l-223 334h193l155 -182l160 182h193z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1548" 
d="M1321 1212l-193 -168q-132 138 -319 138q-193 0 -326 -142q-131 -137 -131 -338q0 -200 131 -340q132 -141 326 -141q149 0 262 82q116 84 119 211h-387v215h688q0 -368 -180 -558.5t-502 -190.5q-303 0 -510 210q-209 212 -209 512t209 512q210 213 510 213
q312 0 512 -215zM430 1575l223 334h252l225 -334h-192l-160 180l-155 -180h-193z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1251" 
d="M860 479q0 116 -74.5 187t-185.5 71q-107 0 -182.5 -73.5t-75.5 -184.5q0 -115 74 -188.5t184 -73.5q102 0 180 72q80 74 80 190zM119 -195l203 103q31 -69 103 -113.5t150 -44.5q161 0 232.5 102.5t58.5 278.5q-35 -68 -120 -109.5t-185 -41.5q-195 0 -338 137
q-141 138 -141 358q0 225 141 363q145 139 338 139q98 0 182.5 -41t122.5 -109v129h244v-819q0 -308 -137.5 -461t-397.5 -153q-149 0 -271 75.5t-185 206.5zM276 1120l224 334h252l225 -334h-193l-159 180l-156 -180h-193z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1548" 
d="M1321 1212l-193 -168q-132 138 -319 138q-193 0 -326 -142q-131 -137 -131 -338q0 -200 131 -340q132 -141 326 -141q149 0 262 82q116 84 119 211h-387v215h688q0 -368 -180 -558.5t-502 -190.5q-303 0 -510 210q-209 212 -209 512t209 512q210 213 510 213
q312 0 512 -215zM506 1862h168q0 -113 104 -113q49 0 77 32t28 81h170q0 -121 -75.5 -194t-199.5 -73q-129 0 -200.5 76t-71.5 191z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1251" 
d="M860 479q0 116 -74.5 187t-185.5 71q-107 0 -182.5 -73.5t-75.5 -184.5q0 -115 74 -188.5t184 -73.5q102 0 180 72q80 74 80 190zM322 -92q31 -69 103 -113.5t150 -44.5q161 0 232.5 102.5t58.5 278.5q-35 -68 -120 -109.5t-185 -41.5q-195 0 -338 137q-141 138 -141 358
q0 225 141 363q145 139 338 139q98 0 182.5 -41t122.5 -109v129h244v-819q0 -308 -137.5 -461t-397.5 -153q-149 0 -271 75.5t-185 206.5zM352 1407h168q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75 -193.5t-199 -72.5q-129 0 -201 75.5t-72 190.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1548" 
d="M1321 1212l-193 -168q-132 138 -319 138q-193 0 -326 -142q-131 -137 -131 -338q0 -200 131 -340q132 -141 326 -141q149 0 262 82q116 84 119 211h-387v215h688q0 -368 -180 -558.5t-502 -190.5q-303 0 -510 210q-209 212 -209 512t209 512q210 213 510 213
q312 0 512 -215zM631 1741q0 60 42.5 100.5t104.5 40.5q63 0 106.5 -40.5t43.5 -100.5q0 -61 -43.5 -102.5t-106.5 -41.5q-62 0 -104.5 41.5t-42.5 102.5z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1251" 
d="M860 479q0 116 -74.5 187t-185.5 71q-107 0 -182.5 -73.5t-75.5 -184.5q0 -115 74 -188.5t184 -73.5q102 0 180 72q80 74 80 190zM322 -92q31 -69 103 -113.5t150 -44.5q161 0 232.5 102.5t58.5 278.5q-35 -68 -120 -109.5t-185 -41.5q-195 0 -338 137q-141 138 -141 358
q0 225 141 363q145 139 338 139q98 0 182.5 -41t122.5 -109v129h244v-819q0 -308 -137.5 -461t-397.5 -153q-149 0 -271 75.5t-185 206.5zM477 1286q0 60 43 100.5t105 40.5q63 0 106 -40.5t43 -100.5q0 -61 -43 -102t-106 -41q-62 0 -105 41t-43 102z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1548" 
d="M1321 1212l-193 -168q-132 138 -319 138q-193 0 -326 -142q-131 -137 -131 -338q0 -200 131 -340q132 -141 326 -141q149 0 262 82q116 84 119 211h-387v215h688q0 -368 -180 -558.5t-502 -190.5q-303 0 -510 210q-209 212 -209 512t209 512q210 213 510 213
q312 0 512 -215zM745 -561l-131 59q69 98 91 174q23 79 20 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1251" 
d="M651 1581l131 -59q-67 -95 -90 -174q-24 -82 -18 -211h-221q15 295 198 444zM860 479q0 116 -74.5 187t-185.5 71q-107 0 -182.5 -73.5t-75.5 -184.5q0 -115 74 -188.5t184 -73.5q102 0 180 72q80 74 80 190zM322 -92q31 -69 103 -113.5t150 -44.5q161 0 232.5 102.5
t58.5 278.5q-35 -68 -120 -109.5t-185 -41.5q-195 0 -338 137q-141 138 -141 358q0 225 141 363q145 139 338 139q98 0 182.5 -41t122.5 -109v129h244v-819q0 -308 -137.5 -461t-397.5 -153q-149 0 -271 75.5t-185 206.5z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1441" 
d="M1001 819v592h265v-1411h-265v584h-565v-584h-260v1411h260v-592h565zM371 1575l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="1202" 
d="M391 510v-510h-250v1477h250v-668q29 78 121 123q89 45 176 45q184 0 284.5 -117t100.5 -328v-532h-250v520q0 96 -58.5 154.5t-143.5 58.5q-99 0 -164.5 -56.5t-65.5 -166.5zM-80 1565l223 333h252l226 -333h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1466" 
d="M188 0v1040h-145v154h145v217h263v-217h563v217h264v-217h145v-154h-145v-1040h-264v582h-563v-582h-263zM451 1040v-221h563v221h-563z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="1189" 
d="M391 510v-510h-250v1143h-151v157h151v177h250v-177h291v-157h-291v-334q29 78 115 123q83 45 170 45q184 0 284.5 -117t100.5 -328v-532h-252v520q0 96 -55.5 154.5t-137.5 58.5q-97 0 -161 -56.5t-64 -166.5z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="612" 
d="M215 1698q-19 0 -35.5 -24t-17.5 -64h-144q6 126 62 191t135 65q49 0 104 -43q56 -41 76 -41t37.5 22.5t18.5 61.5h143q-6 -123 -63.5 -187.5t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43zM436 0h-260v1411h260v-1411z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="530" 
d="M174 1243q-19 0 -35.5 -24t-17.5 -64h-144q6 126 62 191t135 65q50 0 105 -43q56 -41 75 -41q20 0 37.5 22.5t18.5 61.5h143q-6 -123 -63.5 -187.5t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43zM141 0v956h248v-956h-248z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="612" 
d="M582 1653h-551v170h551v-170zM436 0h-260v1411h260v-1411z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="530" 
d="M543 1198h-551v170h551v-170zM141 0v956h248v-956h-248z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="612" 
d="M176 1411h260v-1411h-260v1411zM33 1862h168q0 -113 104 -113q49 0 77 32t28 81h170q0 -121 -75.5 -194t-199.5 -73q-129 0 -200.5 76t-71.5 191z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="530" 
d="M389 956v-956h-248v956h248zM-8 1407h168q0 -113 104 -113q49 0 77 32t28 81h170q0 -121 -75.5 -193.5t-199.5 -72.5q-129 0 -200.5 75.5t-71.5 190.5z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="612" 
d="M436 0h-129l-71 -197q-22 -74 34 -94q29 -8 53.5 3.5t34.5 37.5l11 51l141 9q1 -110 -59.5 -178t-149.5 -68q-88 0 -151 63q-64 61 -64 146q0 59 18 94l72 133v1411h260v-1411z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="530" 
d="M326 -250l10 51l141 9q1 -110 -59.5 -178t-149.5 -68q-88 0 -151 63q-64 61 -64 146q0 58 19 94l69 133v956h248v-956h-115l-71 -197q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5zM266 1176q-68 0 -115 44t-47 107t48 106.5t114 43.5t112 -43.5t46 -106.5t-46 -107
t-112 -44z" />
    <glyph glyph-name="Idot" unicode="&#x130;" horiz-adv-x="612" 
d="M176 1411h260v-1411h-260v1411zM158 1741q0 60 42.5 100.5t104.5 40.5q63 0 106.5 -40.5t43.5 -100.5q0 -61 -43.5 -102.5t-106.5 -41.5q-62 0 -104.5 41.5t-42.5 102.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="530" 
d="M141 0v956h248v-956h-248z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1226" 
d="M410 -446l24 237q59 -20 164 -20q94 0 142 60.5t48 162.5v1417h263v-1417q0 -218 -113.5 -344.5t-339.5 -126.5q-61 0 -188 31zM436 0h-260v1411h260v-1411z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="1064" 
d="M567 -477h-157v229h120q145 0 142 180v1024h252v-1019q0 -414 -357 -414zM637 1327q0 62 47 106t113 44q67 0 113 -43.5t46 -106.5t-46 -107t-113 -44q-66 0 -113 44t-47 107zM141 956h248v-956h-248v956zM266 1176q-68 0 -115 44t-47 107t48 106.5t114 43.5t112 -43.5
t46 -106.5t-46 -107t-112 -44z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="614" 
d="M-203 -446l25 237q59 -20 164 -20q94 0 142 60.5t48 162.5v1417h262v-1417q0 -218 -113 -344.5t-339 -126.5q-62 0 -189 31zM-47 1575l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="534" 
d="M37 -477h-158v229h121q144 0 141 180v1024h252v-1019q0 -414 -356 -414zM-68 1120l224 334h252l225 -334h-193l-159 180l-156 -180h-193z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1224" 
d="M1126 1411l-536 -700l663 -711h-342l-471 522v-522h-264v1411h264v-514l379 514h307zM588 -561l-131 59q67 95 90 174t20 213h219q-15 -300 -198 -446z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1062" 
d="M1063 0h-313l-361 410v-410h-248v1477h248v-807l264 286h324l-402 -419zM506 -561l-131 59q67 95 90 174t20 213h220q-15 -299 -199 -446z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1019" 
d="M1012 0h-316l-307 326v-326h-248v956h248v-315l266 315h301l-415 -471z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1036" 
d="M176 1411h262v-1178h545v-233h-807v1411zM186 1569l215 334h263l-283 -334h-195z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="532" 
d="M141 1477h250v-1477h-250v1477zM152 1569l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1036" 
d="M983 0h-807v1411h262v-1178h545v-233zM483 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="532" 
d="M176 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446zM391 0h-250v1477h250v-1477z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1040" 
d="M983 0h-807v1411h262v-1178h545v-233zM729 926l-154 51q143 199 134 434h219q-7 -164 -63 -291.5t-136 -193.5z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="532" 
d="M614 991l-153 51q142 198 133 435h219q-7 -164 -63 -292t-136 -194zM141 1477h250v-1477h-250v1477z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1036" 
d="M702 797q68 0 116 -44.5t48 -111.5t-48 -111.5t-116 -44.5q-66 0 -112.5 45t-46.5 111t46.5 111t112.5 45zM983 0h-807v1411h262v-1178h545v-233z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="806" 
d="M668 797q68 0 115.5 -44.5t47.5 -111.5t-47.5 -111.5t-115.5 -44.5q-66 0 -113 45t-47 111t47 111t113 45zM391 0h-250v1477h250v-1477z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1048" 
d="M188 0v623l-141 -91v175l141 90v614h263v-450l198 122v-174l-198 -123v-548h544v-238h-807z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="598" 
d="M190 1477h250v-541l170 106v-172l-170 -106v-764h-250v606l-163 -102v172l163 102v699z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1396" 
d="M1221 0h-240l-545 928v-928h-260v1411h244l538 -917v917h263v-1411zM559 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M391 510v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 263.5 -106t92.5 -306v-565h-250v520q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168zM438 1114l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1396" 
d="M958 1411h263v-1411h-240l-545 928v-928h-260v1411h244l538 -917v917zM623 -561l-131 59q67 95 90 174t20 213h219q-15 -300 -198 -446z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" 
d="M391 510v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 263.5 -106t92.5 -306v-565h-250v520q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168zM508 -561l-131 59q67 95 90 174t20 213h220q-15 -299 -199 -446z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1396" 
d="M1221 0h-240l-545 928v-928h-260v1411h244l538 -917v917h263v-1411zM823 1569h-252l-223 334h193l155 -183l160 183h193z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" 
d="M391 510v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 263.5 -106t92.5 -306v-565h-250v520q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168zM707 1114h-252l-224 334h193l156 -182l159 182h193z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" 
d="M391 510v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 263.5 -106t92.5 -306v-565h-250v520q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168zM2 979l-145 43q107 143 80 350h225q3 -261 -160 -393z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1394" 
d="M975 4l-537 920v-924h-262v1411h244l536 -934v934h263v-1391q0 -83 -14 -154t-46.5 -135.5t-83 -109.5t-128.5 -71.5t-177 -26.5q-66 0 -190 31l26 243q118 -32 215 -16q67 12 110.5 77.5t43.5 145.5z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1163" 
d="M1032 565v-512q0 -219 -105 -342t-323 -123q-49 0 -137 21l25 229q43 -14 112 -14q89 0 133.5 59t44.5 156v481q0 102 -48.5 157.5t-133.5 55.5q-97 0 -153 -55t-56 -168v-510h-250v956h250v-141q38 73 118 117.5t167 44.5q171 0 264 -106t92 -306z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1611" 
d="M481 1032q-131 -134 -131 -330q0 -195 131 -329q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM1081 1653h-551v170h551v-170z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1159" 
d="M829 481q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5q0 -114 71.5 -185t178.5 -71t178 71t71 185zM932 121q-145 -139 -352 -139q-210 0 -355 139q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360zM854 1198h-551
v170h551v-170z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1611" 
d="M481 373q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137q-131 -134 -131 -330q0 -195 131 -329zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM532 1862h168q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75.5 -194t-198.5 -73q-129 0 -201 76t-72 191z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1159" 
d="M330 481q0 -114 71.5 -185t178.5 -71t178 71t71 185q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5zM225 121q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360t-352 -139q-210 0 -355 139zM305 1407h168
q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75 -193.5t-199 -72.5q-129 0 -201 75.5t-72 190.5z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1611" 
d="M481 373q132 -135 324 -135q189 0 321 135q134 137 134 329q0 193 -134 330q-131 137 -321 137q-193 0 -324 -137q-131 -134 -131 -330q0 -195 131 -329zM90 702q0 299 207 512t508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210
q-207 213 -207 512zM1075 1903h219l-247 -334h-199zM760 1903h215l-225 -334h-193z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1159" 
d="M330 481q0 -114 71.5 -185t178.5 -71t178 71t71 185q0 109 -71 179.5t-178 70.5t-178.5 -70.5t-71.5 -179.5zM225 121q-143 137 -143 360q0 222 143 359t355 137q209 0 352 -137q145 -139 145 -359q0 -221 -145 -360t-352 -139q-210 0 -355 139zM846 1448h219l-248 -334
h-199zM530 1448h215l-225 -334h-192z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2252" 
d="M1260 1239v172h870v-237h-610v-363h534v-233h-534v-340h630v-238h-890v166q-186 -186 -457 -186q-302 0 -506 210q-207 213 -207 512t207 512t506 213q278 0 457 -188zM803 240q192 0 323 137t131 334q-3 191 -135 323q-133 133 -319 133q-191 0 -322 -137
q-129 -135 -129 -328q0 -192 129 -327q132 -135 322 -135z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1882" 
d="M1077 565h492q-13 94 -78 148.5t-162 54.5q-101 0 -170 -54t-82 -149zM829 479q0 109 -72 180.5t-177 71.5t-177.5 -71.5t-72.5 -180.5q0 -114 71.5 -185t178.5 -71t178 71t71 185zM952 145q-143 -165 -372 -165q-210 0 -355 139q-143 137 -143 360q0 222 143 359
q145 139 355 139q228 0 372 -164q144 164 377 164q206 0 333.5 -125t145.5 -332q5 -47 2 -125h-731q13 -93 85.5 -149t178.5 -56q77 0 143 32.5t97 90.5l193 -102q-61 -113 -180.5 -172t-266.5 -59q-234 0 -377 165z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1292" 
d="M729 1178h-291v-443h283q93 0 146 63.5t53 155.5t-51.5 158t-139.5 66zM834 512l360 -512h-315l-332 502h-109v-502h-262v1411h565q220 0 334 -135q117 -132 117 -322q0 -161 -92 -288q-89 -127 -266 -154zM457 1569l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="841" 
d="M156 1114l215 334h262l-283 -334h-194zM391 338v-338h-250v956h250v-225q34 119 107 182.5t164 63.5q92 0 157 -35l-43 -225q-56 26 -145 26q-240 0 -240 -405z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1292" 
d="M729 1178h-291v-443h283q93 0 146 63.5t53 155.5t-51.5 158t-139.5 66zM834 512l360 -512h-315l-332 502h-109v-502h-262v1411h565q220 0 334 -135q117 -132 117 -322q0 -161 -92 -288q-89 -127 -266 -154zM606 -561l-131 59q67 95 90 174q24 82 21 213h219
q-15 -299 -199 -446z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="841" 
d="M391 338v-338h-250v956h250v-225q34 119 107 182.5t164 63.5q92 0 157 -35l-43 -225q-56 26 -145 26q-240 0 -240 -405zM180 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1292" 
d="M729 1178h-291v-443h283q93 0 146 63.5t53 155.5t-51.5 158t-139.5 66zM834 512l360 -512h-315l-332 502h-109v-502h-262v1411h565q220 0 334 -135q117 -132 117 -322q0 -161 -92 -288q-89 -127 -266 -154zM764 1569h-252l-223 334h192l156 -183l160 183h192z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="841" 
d="M565 1114h-252l-223 334h193l155 -182l160 182h193zM391 338v-338h-250v956h250v-225q34 119 107 182.5t164 63.5q92 0 157 -35l-43 -225q-56 26 -145 26q-240 0 -240 -405z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1181" 
d="M356 412q5 -84 71.5 -141.5t172.5 -57.5q101 0 165 47.5t69 122.5q4 69 -44 116t-151 76l-174 46q-356 105 -356 393q0 184 137 297q137 116 338 116q205 0 336 -116q133 -115 133 -310h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162
l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -186 -152 -293q-140 -107 -344 -107q-224 0 -364 125q-142 127 -142 310h262zM489 1569l216 334h262l-283 -334h-195z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="929" 
d="M504 367l-131 32q-71 20 -122 46.5t-79.5 53t-44.5 61.5t-20.5 63.5t-4.5 68.5q0 116 105 199q106 86 248 86q149 0 258 -74q108 -73 114 -227h-237q-1 47 -38.5 72.5t-90.5 25.5q-121 0 -121 -94q0 -54 100 -80l160 -39q77 -20 130.5 -57t77.5 -81t33 -79t9 -70
q0 -140 -117 -217q-115 -77 -274 -77q-151 0 -269 86q-113 88 -116 235h233q5 -51 57 -87t119 -34q54 2 89.5 28.5t35.5 67.5q2 35 -23 57t-81 34zM334 1114l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1181" 
d="M94 412h262q5 -84 71.5 -141.5t172.5 -57.5q101 0 165 47.5t69 122.5q4 69 -44 116t-151 76l-174 46q-356 105 -356 393q0 184 137 297q137 116 338 116q205 0 336 -116q133 -115 133 -310h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162
l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -186 -152 -293q-140 -107 -344 -107q-224 0 -364 125q-142 127 -142 310zM246 1575l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="929" 
d="M504 367l-131 32q-71 20 -122 46.5t-79.5 53t-44.5 61.5t-20.5 63.5t-4.5 68.5q0 116 105 199q106 86 248 86q149 0 258 -74q108 -73 114 -227h-237q-1 47 -38.5 72.5t-90.5 25.5q-121 0 -121 -94q0 -54 100 -80l160 -39q77 -20 130.5 -57t77.5 -81t33 -79t9 -70
q0 -140 -117 -217q-115 -77 -274 -77q-151 0 -269 86q-113 88 -116 235h233q5 -51 57 -87t119 -34q54 2 89.5 28.5t35.5 67.5q2 35 -23 57t-81 34zM113 1120l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1185" 
d="M504 -250q10 -26 34.5 -37.5t53.5 -3.5q57 20 35 94l-66 177q-206 12 -336 133q-129 123 -129 299h262q5 -84 71.5 -141.5t172.5 -57.5q101 0 165 47.5t69 122.5q4 69 -44 116t-151 76l-174 46q-356 105 -356 393q0 184 137 297q137 116 338 116q205 0 336 -116
q133 -115 133 -310h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -163 -121 -266q-115 -103 -283 -127l64 -117q18 -35 18 -94q0 -86 -62.5 -147.5t-152.5 -61.5
q-89 0 -149.5 68t-59.5 178l142 -9q0 -24 10 -51z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="931" 
d="M590 -4l69 -129q19 -36 19 -94q0 -86 -62.5 -147.5t-152.5 -61.5q-89 0 -149.5 68t-59.5 178l141 -9q0 -22 11 -51q10 -26 34.5 -37.5t53.5 -3.5q56 20 34 94l-65 177h-2q-150 0 -268 86q-114 89 -117 235h233q5 -51 57 -87t119 -34q54 2 89.5 28.5t35.5 67.5
q2 35 -23 57t-81 34l-131 32q-71 20 -122 46.5t-79.5 53t-44.5 61.5t-20.5 63.5t-4.5 68.5q0 116 105 199q106 86 248 86q149 0 258 -74q108 -73 114 -227h-237q-1 47 -38.5 72.5t-90.5 25.5q-121 0 -121 -94q0 -54 100 -80l160 -39q77 -20 130.5 -57t77.5 -81t33 -79t9 -70
q0 -106 -71 -178.5t-191 -99.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1181" 
d="M356 412q5 -84 71.5 -141.5t172.5 -57.5q101 0 165 47.5t69 122.5q4 69 -44 116t-151 76l-174 46q-356 105 -356 393q0 184 137 297q137 116 338 116q205 0 336 -116q133 -115 133 -310h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162
l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -186 -152 -293q-140 -107 -344 -107q-224 0 -364 125q-142 127 -142 310h262zM721 1569h-252l-223 334h192l156 -183l160 183h192z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="929" 
d="M504 367l-131 32q-71 20 -122 46.5t-79.5 53t-44.5 61.5t-20.5 63.5t-4.5 68.5q0 116 105 199q106 86 248 86q149 0 258 -74q108 -73 114 -227h-237q-1 47 -38.5 72.5t-90.5 25.5q-121 0 -121 -94q0 -54 100 -80l160 -39q77 -20 130.5 -57t77.5 -81t33 -79t9 -70
q0 -140 -117 -217q-115 -77 -274 -77q-151 0 -269 86q-113 88 -116 235h233q5 -51 57 -87t119 -34q54 2 89.5 28.5t35.5 67.5q2 35 -23 57t-81 34zM588 1114h-252l-223 334h192l156 -182l160 182h192z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1173" 
d="M455 1171h-398v240h1059v-240h-397v-1171h-264v1171zM492 -561l-132 59q69 98 91 174q23 79 20 213h219q-15 -300 -198 -446z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="718" 
d="M221 1321h248v-365h203v-200h-203v-756h-248v756h-174v200h174v365zM260 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1173" 
d="M57 1411h1059v-240h-397v-1171h-264v1171h-398v240zM713 1569h-252l-223 334h192l156 -183l159 183h193z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="716" 
d="M707 1073l-134 60q69 98 91 174q23 79 20 213h219q-15 -297 -196 -447zM219 756h-172v200h172v365h250v-365h201v-200h-201v-756h-250v756z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1173" 
d="M455 0v684h-236v156h236v331h-398v240h1059v-240h-397v-331h233v-156h-233v-684h-264z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="724" 
d="M473 0h-250v406h-119v145h119v205h-172v200h172v365h250v-365h201v-200h-201v-205h148v-145h-148v-406z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1394" 
d="M606 1698q-19 0 -35.5 -24t-17.5 -64h-143q6 126 61.5 191t134.5 65q50 0 105 -43q56 -41 75 -41q20 0 37.5 22.5t18.5 61.5h143q-6 -123 -63.5 -187.5t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43zM971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143
q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195z" />
    <glyph glyph-name="utilde" unicode="&#x169;" 
d="M770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167zM504 1243q-19 0 -35.5 -24t-17.5 -64h-144q6 126 62 191t135 65q49 0 104 -43q56 -41 76 -41t37 22t18 62
h144q-6 -123 -63.5 -187.5t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM973 1653h-551v170h551v-170z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" 
d="M770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167zM870 1198h-551v170h551v-170z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM424 1862h168q0 -113 104 -113q49 0 77 32t28 81h170q0 -121 -75.5 -194t-199.5 -73q-129 0 -200.5 76
t-71.5 191z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" 
d="M770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167zM322 1407h167q0 -113 105 -113q49 0 76.5 32t27.5 81h170q0 -121 -75 -193.5t-199 -72.5
q-129 0 -200.5 75.5t-71.5 190.5z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1394" 
d="M971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195zM461 1794q0 100 67 170q70 70 166 70q100 0 170 -70q70 -67 70 -170q0 -98 -70 -165.5t-170 -67.5
q-96 0 -166 67q-67 67 -67 166zM578 1794q0 -49 34 -83t82 -34q51 0 86 34t35 83q0 51 -35 86t-86 35q-48 0 -82 -35t-34 -86z" />
    <glyph glyph-name="uring" unicode="&#x16f;" 
d="M770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167zM358 1339q0 99 68 170q70 70 166 70q100 0 170 -70q69 -66 69 -170q0 -98 -69.5 -165.5t-169.5 -67.5
q-95 0 -166 68q-68 68 -68 165zM475 1339q0 -49 34.5 -82.5t82.5 -33.5q51 0 86 33.5t35 82.5q0 51 -35 86t-86 35q-48 0 -82.5 -35t-34.5 -86z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1394" 
d="M973 1903h219l-248 -334h-199zM657 1903h215l-225 -334h-192zM971 498v913h262v-913q0 -229 -158 -375q-155 -143 -377 -143q-226 0 -381 143t-155 375v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" 
d="M811 1448h219l-248 -334h-198zM496 1448h215l-226 -334h-192zM770 446v510h250v-956h-250v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1394" 
d="M817 -250l10 51l142 9q1 -110 -59.5 -178t-149.5 -68t-152 63q-63 60 -63 146q0 59 18 94l64 117q-199 24 -332 163t-133 351v913h266v-913q0 -118 80 -195q79 -76 190 -76q112 0 191 76q82 76 82 195v913h262v-913q0 -215 -137 -355q-135 -141 -336 -159l-66 -181
q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" 
d="M952 -250l11 51l141 9q1 -110 -59.5 -178t-149.5 -68t-152 63q-63 60 -63 146q0 59 18 94l72 133v135q-37 -71 -111.5 -113t-152.5 -42q-175 0 -276 107.5t-101 303.5v565h252v-520q0 -104 51 -158.5t131 -54.5q93 0 150 56t57 167v510h250v-956h-119l-72 -197
q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1826" 
d="M1018 1214l213 -786l303 983h285l-480 -1411h-211l-217 801l-213 -801h-213l-475 1411h283l301 -983l215 786h209zM563 1575l223 334h252l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1585" 
d="M1288 956h266l-342 -956h-208l-213 641l-207 -641h-207l-352 956h266l194 -604l193 604h221l195 -604zM438 1120l224 334h251l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1318" 
d="M793 639v-639h-269v643l-540 768h311l362 -543l367 543h313zM309 1575l223 334h252l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1046" 
d="M762 956h266l-592 -1423h-264l219 541l-373 882h267l237 -596zM178 1120l223 334h252l226 -334h-193l-160 180l-155 -180h-193z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1318" 
d="M793 639v-639h-269v643l-540 768h311l362 -543l367 543h313zM483 1882q57 0 98.5 -39t41.5 -94t-41.5 -94t-98.5 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM739 1653q-41 38 -41 95.5t41 94.5q41 38 101 38t98 -38q41 -37 41 -94.5t-41 -95.5q-38 -37 -98 -37
t-101 37z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1202" 
d="M86 0v205l680 971h-653v235h991v-207l-686 -968h686v-236h-1018zM440 1569l215 334h263l-283 -334h-195z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1040" 
d="M940 956v-192l-502 -531h502v-233h-856v190l494 533h-463v233h825zM367 1114l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1202" 
d="M86 0v205l680 971h-653v235h991v-207l-686 -968h686v-236h-1018zM446 1741q0 60 43 100.5t105 40.5q63 0 106 -40.5t43 -100.5q0 -61 -43 -102.5t-106 -41.5q-62 0 -105 41.5t-43 102.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1040" 
d="M940 956v-192l-502 -531h502v-233h-856v190l494 533h-463v233h825zM362 1286q0 60 43 100.5t105 40.5q63 0 106 -40.5t43 -100.5q0 -61 -43 -102t-106 -41q-62 0 -105 41t-43 102z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1202" 
d="M86 0v205l680 971h-653v235h991v-207l-686 -968h686v-236h-1018zM721 1569h-252l-223 334h192l156 -183l160 183h192z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1040" 
d="M940 956v-192l-502 -531h502v-233h-856v190l494 533h-463v233h825zM637 1114h-252l-223 334h192l156 -182l160 182h192z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="663" 
d="M637 1477h104v-240h-104q-156 0 -156 -205v-1032h-252v756h-182v200h182v76q0 212 105.5 328.5t302.5 116.5z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="1275" 
d="M61 -387l48 235h110q146 0 193 203l143 699h-178l43 206h178l16 72q42 213 171.5 326.5t334.5 109.5l94 -2l-47 -237h-108q-93 0 -137.5 -64.5t-71.5 -204.5h203l-41 -206h-205q-108 -540 -158 -760q-41 -179 -169 -279.5t-314 -99.5q-80 0 -105 2z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1339" 
d="M934 342h-526l-129 -342h-279l528 1411h283l528 -1411h-276zM596 2023l162 250h262l-232 -272q117 -71 117 -207q0 -98 -69.5 -165.5t-169.5 -67.5q-96 0 -166 67q-68 68 -68 166q0 80 45.5 142t118.5 87zM496 571h352l-178 539zM549 1794q0 -49 34.5 -83t82.5 -34
q51 0 85.5 33.5t34.5 83.5q0 51 -34.5 86t-85.5 35q-48 0 -82.5 -35t-34.5 -86z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1257" 
d="M1116 0h-250v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h250v-956zM866 479q0 118 -80 189q-75 69 -182 69t-186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5
t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5zM502 1556l162 250h262l-232 -272q117 -70 117 -207q0 -98 -70 -165.5t-170 -67.5q-95 0 -165 67q-68 68 -68 166q0 80 45.5 142t118.5 87zM455 1327q0 -49 34 -83t82 -34q51 0 86 34t35 83q0 51 -35 86t-86 35
q-48 0 -82 -35t-34 -86z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1910" 
d="M1788 1411v-237h-610v-363h534v-233h-534v-340h630v-238h-890v2l-3 -2v334h-419l-213 -334h-312l891 1411h926zM645 586h270v471zM864 1569l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="2033" 
d="M1227 565h491q-12 93 -77 148t-162 55q-101 0 -170 -54t-82 -149zM604 737q-107 0 -186 -73q-78 -72 -78 -185q0 -111 78 -186q37 -37 86.5 -55.5t99.5 -17.5t98.5 20t85.5 51t58.5 81.5t19.5 106.5q0 118 -80 189q-75 69 -182 69zM1962 481q0 -24 -4 -86h-729
q13 -93 85.5 -149t178.5 -56q77 0 143 32.5t97 90.5l192 -102q-61 -113 -180 -172t-266 -59q-246 0 -381 178v-158h-232v123q-41 -64 -127 -102.5t-170 -38.5q-199 0 -342 137q-145 139 -145 360q0 222 143 359q145 139 344 139q86 0 169 -35.5t128 -103.5v118h232v-153
q134 174 381 174q209 0 346 -137t137 -359zM866 1114l215 334h262l-282 -334h-195z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1607" 
d="M705 1569l215 334h262l-283 -334h-194zM354 135l-164 -168l-96 96l164 168q-170 203 -170 471q0 299 207 512t508 213q265 0 457 -165l161 165l97 -94l-164 -168q166 -202 166 -463q0 -300 -209 -512q-207 -210 -508 -210q-231 0 -414 133q-10 3 -35 22zM348 702
q0 -154 92 -284l637 657q-116 94 -274 94q-193 0 -324 -137q-131 -134 -131 -330zM803 238q189 0 321 135q133 136 133 329q0 150 -88 275l-632 -653q115 -86 266 -86z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1159" 
d="M489 1114l216 334h262l-283 -334h-195zM580 -20q-166 0 -291 88l-119 -121l-96 96l114 119q-106 127 -106 317q0 222 143 359q145 139 355 139q175 0 307 -100l104 108l96 -94l-106 -109q96 -131 96 -303q0 -221 -145 -360t-352 -139zM580 731q-105 0 -177.5 -71.5
t-72.5 -180.5q0 -81 35 -137l344 354q-59 35 -129 35zM580 223q107 0 178 71t71 185q0 68 -26 119l-338 -348q53 -27 115 -27z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1181" 
d="M94 412h262q5 -84 71.5 -141.5t172.5 -57.5q101 0 165 47.5t69 122.5q4 69 -44 116t-151 76l-174 46q-356 105 -356 393q0 184 137 297q137 116 338 116q205 0 336 -116q133 -115 133 -310h-262q0 85 -56.5 135t-154.5 50q-91 0 -151 -46.5t-60 -119.5q0 -116 168 -162
l174 -51q107 -29 185 -74t123 -101t63.5 -119.5t13.5 -135.5q-9 -186 -152 -293q-140 -107 -344 -107q-224 0 -364 125q-142 127 -142 310zM520 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="929" 
d="M504 367l-131 32q-71 20 -122 46.5t-79.5 53t-44.5 61.5t-20.5 63.5t-4.5 68.5q0 116 105 199q106 86 248 86q149 0 258 -74q108 -73 114 -227h-237q-1 47 -38.5 72.5t-90.5 25.5q-121 0 -121 -94q0 -54 100 -80l160 -39q77 -20 130.5 -57t77.5 -81t33 -79t9 -70
q0 -140 -117 -217q-115 -77 -274 -77q-151 0 -269 86q-113 88 -116 235h233q5 -51 57 -87t119 -34q54 2 89.5 28.5t35.5 67.5q2 35 -23 57t-81 34zM385 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1191" 
d="M246 1120l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1191" 
d="M721 1114h-252l-223 334h192l156 -182l160 182h192z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="1038" 
d="M246 1407h168q0 -113 104 -113q49 0 77 32t28 81h170q0 -121 -75.5 -193.5t-199.5 -72.5q-129 0 -200.5 75.5t-71.5 190.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="747" 
d="M225 1286q0 60 43 100.5t105 40.5q63 0 106 -40.5t43 -100.5q0 -61 -43 -102t-106 -41q-62 0 -105 41t-43 102z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="1087" 
d="M328 1339q0 100 67 170q70 70 166 70q103 0 171.5 -68.5t68.5 -171.5q0 -98 -70 -165.5t-170 -67.5q-95 0 -166 68q-67 67 -67 165zM444 1339q0 -49 34.5 -82.5t82.5 -33.5q51 0 86 33.5t35 82.5q0 51 -35 86t-86 35q-48 0 -82.5 -35t-34.5 -86z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1081" 
d="M600 -250l10 51l142 9q1 -110 -59.5 -178t-149.5 -68t-152 63q-63 60 -63 146q0 59 18 94l105 192h120l-94 -256q-22 -74 35 -94q29 -8 53.5 3.5t34.5 37.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="903" 
d="M360 1243q-19 0 -35.5 -24t-17.5 -64h-143q6 126 61.5 191t134.5 65q50 0 105 -43q56 -41 76 -41t37 22t18 62h143q-6 -123 -63 -187.5t-135 -64.5q-46 0 -105 41q-58 43 -76 43z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="1064" 
d="M682 1448h219l-248 -334h-198zM367 1448h215l-226 -334h-192z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1826" 
d="M1018 1214l213 -786l303 983h285l-480 -1411h-211l-217 801l-213 -801h-213l-475 1411h283l301 -983l215 786h209zM772 1569l-283 334h263l215 -334h-195z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1585" 
d="M1288 956h266l-342 -956h-208l-213 641l-207 -641h-207l-352 956h266l194 -604l193 604h221l195 -604zM688 1114l-282 334h262l215 -334h-195z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1826" 
d="M1534 1411h285l-480 -1411h-211l-217 801l-213 -801h-213l-475 1411h283l301 -983l215 786h209l213 -786zM825 1569l215 334h263l-283 -334h-195z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1585" 
d="M1212 0h-208l-213 641l-207 -641h-207l-352 956h266l194 -604l193 604h221l195 -604l194 604h266zM676 1114l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1826" 
d="M1534 1411h285l-480 -1411h-211l-217 801l-213 -801h-213l-475 1411h283l301 -983l215 786h209l213 -786zM737 1882q57 0 98.5 -39t41.5 -94t-41.5 -94t-98.5 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM993 1653q-41 38 -41 95.5t41 94.5q41 38 101 38t98 -38
q41 -37 41 -94.5t-41 -95.5q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1585" 
d="M1212 0h-208l-213 641l-207 -641h-207l-352 956h266l194 -604l193 604h221l195 -604l194 604h266zM612 1427q57 0 98.5 -39t41.5 -94t-41.5 -94t-98.5 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM868 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95
t-41 -96q-38 -37 -98 -37t-101 37z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1318" 
d="M793 639v-639h-269v643l-540 768h311l362 -543l367 543h313zM596 1569l-283 334h262l216 -334h-195z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1046" 
d="M762 956h266l-592 -1423h-264l219 541l-373 882h267l237 -596zM475 1114l-282 334h262l215 -334h-195z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="28" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1267" 
d="M1139 371h-1010v221h1010v-221z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1699" 
d="M1571 371h-1442v221h1442v-221z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="546" 
d="M379 1462l110 -55q-33 -45 -83 -129q-35 -63 -61 -164t-26 -205h-213q6 167 95 324q90 159 178 229z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="542" 
d="M195 862l-111 56q50 70 82 126q36 62 63 163t27 206h211q-3 -165 -94 -326q-86 -152 -178 -225z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="512" 
d="M113 -211l-111 53q62 91 84 131q35 61 61.5 162t26.5 205h211q-3 -165 -94 -326q-86 -152 -178 -225z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="882" 
d="M715 1462l110 -55q-29 -41 -84 -129q-34 -60 -60 -161t-26 -206h-213q6 167 95 324q88 155 178 227zM381 1462l108 -55q-33 -45 -83 -129q-35 -63 -61 -163t-26 -204h-210q3 163 94 324q88 155 178 227z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="874" 
d="M195 862l-111 53q50 71 82 129q36 61 62 162.5t26 206.5h211q-3 -165 -94 -326q-88 -155 -176 -225zM528 862l-110 53q61 90 84 129q34 62 60 163t26 206h213q-6 -167 -96 -326q-87 -153 -177 -225z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="845" 
d="M113 -211l-109 53q50 71 82 129q36 61 62 162.5t26 206.5h211q-3 -165 -94 -326q-86 -152 -178 -225zM449 -211l-111 53q62 91 84 131q34 60 60 161t26 206h211q-3 -165 -94 -326q-88 -155 -176 -225z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="845" 
d="M309 1411h228v-354h227v-174h-227v-1094h-228v1094h-227v174h227v354z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="903" 
d="M338 1411h227v-354h228v-174h-228v-457h228v-172h-228v-465h-227v465h-227v172h227v457h-227v174h227v354z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="950" 
d="M248 268q-92 89 -92 219q0 131 92 220q94 88 227 88q134 0 225 -88q95 -89 95 -220q0 -130 -95 -219q-91 -88 -225 -88q-133 0 -227 88z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1660" 
d="M164 23q-47 44 -47 112t47 111q47 44 114.5 44t114.5 -44q47 -43 47 -111t-47 -112q-47 -43 -114.5 -43t-114.5 43zM715 23q-44 44 -44 112t44 111q47 44 114.5 44t114.5 -44q50 -44 50 -110.5t-50 -110.5q-47 -44 -114.5 -44.5t-114.5 42.5zM1268 23q-47 44 -47 112
t47 111q47 44 114.5 44t114.5 -44q47 -43 47 -111t-47 -112q-47 -43 -114.5 -43t-114.5 43z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2037" 
d="M1102 1354l-666 -1354h-139l666 1354h139zM350 1364q113 0 193 -80q82 -79 82 -194t-80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80zM467 1090q0 47 -35 81.5t-82 34.5q-36 3 -64.5 -18.5t-41 -52.5t-7.5 -67t31 -60q24 -26 60 -31
t67.5 7.5t53 41t18.5 64.5zM1339 252q0 -116 -79 -195q-80 -77 -195 -77t-195 77q-77 80 -77 195q0 114 77 194q80 80 195 80q116 0 195 -82q79 -79 79 -192zM1182 252q0 47 -35 82t-82 35q-36 3 -64.5 -18.5t-41 -53t-7.5 -67.5t31 -60q24 -26 60 -31t67.5 7.5t53 41
t18.5 64.5zM1966 252q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195q0 113 78 194q80 80 195 80t194 -82q80 -80 80 -192zM1808 252q0 47 -34.5 82t-81.5 35q-36 3 -64.5 -18.5t-41 -53t-7.5 -67.5t31 -60q24 -26 60 -31t67 7.5t52.5 41t18.5 64.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="659" 
d="M367 53l-283 424l283 424h200l-282 -424l282 -424h-200z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="659" 
d="M293 901l282 -424l-282 -424h-201l283 424l-283 424h201z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="368" 
d="M563 1370h156l-922 -1390h-159z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="729" 
d="M74 1278q0 408 291 408q290 0 290 -408q0 -404 -290 -404q-291 0 -291 404zM223 1278q0 -258 142 -258q141 0 141 258q0 262 -141 262q-142 0 -142 -262z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="716" 
d="M666 1055h-105v-168h-149v168h-389l395 618h143v-487h105v-131zM412 1452l-168 -266h168v266z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="696" 
d="M49 1036l125 82q23 -43 65.5 -69.5t86.5 -26.5q71 0 116 38.5t45 94.5q0 61 -41 96t-106 35q-109 0 -190 -78l-72 47l65 418h441v-139h-320l-26 -152q53 35 137 35q115 0 187.5 -70t72.5 -192q0 -125 -84.5 -203t-224.5 -78q-84 0 -159 44t-118 118z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="694" 
d="M635 1530l-137 -57q-35 71 -105 71q-77 0 -117.5 -67.5t-35.5 -171.5q18 37 61 63.5t96 26.5q98 0 172 -74q75 -75 72 -195q-1 -112 -80.5 -184t-198.5 -70q-111 2 -186.5 76.5t-85.5 196.5q-2 50 -3.5 102.5t6 106t19 103t36.5 92t57.5 73.5t81.5 48.5t110 15.5
q174 -9 238 -156zM254 1139q0 -51 33.5 -85t83.5 -36q48 0 82.5 34t35.5 83q0 49 -33 83.5t-81 36.5q-51 0 -86 -33.5t-35 -82.5z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="663" 
d="M61 1673h580l-381 -786h-170l311 645h-340v141z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="706" 
d="M121 1470q0 91 68 153.5t165 62.5t164.5 -62.5t67.5 -153.5q0 -79 -58 -131q115 -74 115 -198q0 -111 -84 -189t-205 -78q-120 0 -207 78q-84 78 -84 189q0 127 115 198q-57 57 -57 131zM213 1143q-2 -40 24.5 -71.5t64 -45.5t80.5 -8t71 35q32 27 38 66t-9 73t-48 57.5
t-74 22.5h-18q-53 0 -91 -38.5t-38 -90.5zM268 1468q-4 -26 12 -46.5t40 -29t51.5 -4.5t44.5 23q39 38 17.5 87.5t-79.5 49.5q-37 0 -61.5 -23.5t-24.5 -56.5z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="694" 
d="M76 1030l139 57q34 -73 104 -73q75 0 115.5 68t38.5 173q-20 -38 -63.5 -64t-96.5 -26q-98 0 -172 74t-71 195q5 113 82.5 183t195.5 69q113 -2 189 -76.5t86 -196.5q12 -202 -39 -356q-30 -89 -99.5 -138t-169.5 -45q-168 9 -239 156zM459 1421q0 50 -34 85t-83 36
q-50 0 -84.5 -34t-34.5 -83q-1 -50 32 -84t83 -36q50 0 85 33t36 83z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="729" 
d="M74 88q0 408 291 408q290 0 290 -408q0 -403 -290 -403q-291 0 -291 403zM223 88q0 -258 142 -258q141 0 141 258q0 262 -141 262q-142 0 -142 -262z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="434" 
d="M330 -305h-150v584l-135 -62v133l285 144v-799z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="657" 
d="M70 -305v78l321 383q45 54 45 102q0 42 -30.5 68t-79.5 26q-48 0 -82 -31.5t-41 -84.5l-137 26q19 109 90.5 171.5t175.5 62.5q107 0 180.5 -67.5t73.5 -170.5q0 -105 -84 -201l-191 -219h262v-143h-503z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="651" 
d="M201 303l-113 78q36 56 100 87.5t138 27.5q99 -6 163 -69.5t64 -150.5q0 -100 -98 -155q61 -23 99 -85t32 -128q-9 -100 -99 -166t-202 -57q-80 6 -147 51t-99 114l127 66q46 -88 129 -88q51 -1 89.5 27.5t43.5 70.5q2 49 -28.5 85.5t-94.5 35.5h-86v129h86q47 0 71 34
t19 69q-2 32 -27.5 55.5t-62.5 23.5q-67 3 -104 -55z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="710" 
d="M662 -135h-105v-170h-149v170h-390l396 618h143v-487h105v-131zM408 262l-168 -266h168v266z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="692" 
d="M111 -113l63 41q23 -43 65.5 -69.5t86.5 -26.5q71 0 116 38.5t45 94.5q0 61 -41 96t-106 35q-109 0 -190 -78l-72 48l65 417h441v-139h-320l-26 -154q52 37 137 37q115 0 187.5 -70t72.5 -192q0 -125 -85 -203.5t-224 -78.5q-82 0 -158 44t-119 117z" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="692" 
d="M633 340l-137 -57q-35 71 -105 71q-77 0 -117.5 -67.5t-35.5 -173.5q14 37 58.5 64.5t98.5 27.5q99 0 172 -76q75 -75 72 -192q-1 -112 -80.5 -184t-198.5 -70q-111 2 -186.5 76t-85.5 196q-7 56 -3.5 118.5t10.5 123t30.5 115.5t56 96.5t87.5 65t126 22.5
q174 -9 238 -156zM252 -53q0 -50 34 -84.5t83 -34.5q47 -1 81.5 33t36.5 82q0 51 -33 85.5t-81 34.5q-51 2 -86 -31.5t-35 -84.5z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="643" 
d="M55 483h580l-381 -788h-170l311 647h-340v141z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="706" 
d="M121 281q0 90 68 152.5t165 62.5t164.5 -62.5t67.5 -152.5q0 -79 -58 -131q115 -74 115 -199q0 -111 -84 -189q-83 -77 -205 -77q-121 0 -207 77q-84 78 -84 189q0 128 115 199q-57 57 -57 131zM213 -47q-2 -39 24.5 -71t64 -46t80.5 -7.5t71 36.5q32 25 38.5 64t-9 72.5
t-49 57.5t-73.5 23h-18q-53 0 -91 -38.5t-38 -90.5zM268 279q0 -36 24 -60t62 -24q33 2 55 19t26.5 41t-0.5 47.5t-27 39.5t-54 16q-38 0 -62 -23t-24 -56z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="694" 
d="M76 -162l139 60q35 -74 104 -74q75 0 115.5 67.5t38.5 174.5q-18 -37 -62.5 -64t-97.5 -27q-98 0 -172 74t-71 195q5 111 82.5 182t195.5 70q113 -2 189 -76.5t86 -196.5q13 -202 -39 -354q-30 -89 -99 -138t-170 -48q-169 9 -239 155zM459 229q0 51 -34 86t-83 35
q-49 1 -84 -32.5t-35 -81.5q-1 -51 32 -86t83 -35t85 32.5t36 81.5z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="1441" 
d="M664 -8l-78 -219l-111 41l74 209q-82 30 -135 65l-88 -242l-111 39l100 279q-111 100 -172 240.5t-61 297.5q0 295 197 504q198 210 485 219l80 224l110 -41l-67 -189q77 -8 149 -35l68 191l110 -41l-69 -197q123 -68 215 -188l-209 -150q-46 58 -96 91l-305 -859
q36 -4 43 -4q109 0 205.5 51t160.5 140l207 -152q-95 -135 -245 -210.5t-328 -75.5q-65 0 -129 12zM803 1178l-301 -840q58 -55 131 -80l319 891q-73 26 -149 29zM344 702q0 -144 70 -260l256 719q-147 -41 -236 -168q-90 -128 -90 -291z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="1366" 
d="M817 588v-123h-262v-236h420q23 0 37 18.5t14 45.5v80h248v-111q0 -113 -67 -187.5t-171 -74.5h-940v229h209v236h-166v123h166v108h-166v123h166v146q0 209 117 335q121 127 313 127q191 0 316 -116q127 -118 127 -334h-256q0 96 -53 144.5t-138 43.5q-76 -2 -126 -50.5
t-50 -125.5v-170h262v-123h-262v-108h262z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="1609" 
d="M285 776v221h-185v113h185v301h243l177 -301h362v301h262v-301h178v-113h-178v-221h178v-112h-178v-664h-239l-390 664h-155v-664h-260v664h-185v112h185zM1067 997h-295l129 -221h166v221zM1067 664h-100l100 -170v170zM633 776l-88 152v-152h88z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="1546" 
d="M264 0v788h-164v113h164v107h-164v112h164v291h574q156 0 263.5 -78.5t153.5 -212.5h207v-112h-180q4 -66 4 -70q0 -29 -2 -37h178v-113h-198q-39 -134 -148.5 -210t-277.5 -76h-312v-502h-262zM1024 938q0 39 -8 70h-490v-107h494q4 32 4 37zM834 1178h-308v-58h432
q-52 58 -124 58zM526 788v-53h299q89 0 142 53h-441z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1435" 
d="M770 928h-641v119h629q-49 131 -176 131h-453q0 44 -2 116v117h1216v-119h-434q106 -100 127 -245h307v-119h-299q-8 -164 -100.5 -277.5t-257.5 -138.5l361 -512h-316l-332 502h-270v233h444q85 0 137 54t60 139z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="2033" 
d="M594 0l-223 659h-273v113h236l-45 131h-191v113h154l-133 395h282l121 -395h340l56 198h208l54 -198h340l122 395h285l-135 -395h139v-113h-178l-43 -131h221v-113h-260l-223 -659h-211l-178 659h-78l-174 -659h-213zM831 903h-274l39 -131h201zM1485 903h-275l35 -131
h201zM766 659h-135l71 -231zM1411 659h-135l63 -231z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="1810" 
d="M1239 680v-279h-250v244q0 89 -62 148t-161 59h-84h-246v-852h-252v1102h527q90 0 147 -8q173 -15 277 -131q104 -113 104 -283zM571 422v278h250v-243q0 -89 62 -148t161 -59h84h246v852h252v-1102h-526q-74 0 -148 6q-172 15 -276 131q-105 120 -105 285z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1710" 
d="M1090 881v-123h-992v123h240q59 239 248 391q189 155 442 155q175 0 323.5 -74t243.5 -204l-209 -150q-138 181 -358 181q-142 0 -255 -81.5t-163 -217.5h480zM98 520v123h992v-123h-478q50 -132 163 -212.5t253 -80.5q109 0 206 51t161 140l207 -152
q-95 -135 -245 -210.5t-329 -75.5q-248 0 -440 151q-189 152 -248 389h-242z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="817" 
d="M360 1075v-502l70 5q64 0 64 104v393q0 133 -64 133q-70 0 -70 -133zM471 215l199 -10v-205h-240q-297 0 -297 340v747q0 340 297 340q291 0 291 -350v-383q0 -176 -76 -258t-285 -82v-14q0 -66 23.5 -95t87.5 -30z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="2004" 
d="M1227 0h-238l-545 928v-928h-262v1411h246l537 -917v917h262v-1411zM1636 1421q113 0 193 -80t80 -192q0 -113 -80 -193q-80 -77 -193 -77q-114 0 -194 77q-78 78 -78 193q0 114 78 192q80 80 194 80zM1548 1237q-35 -37 -35 -88t35 -88q37 -35 88 -35t86 35q37 37 37 88
t-37 88q-35 37 -86 37t-88 -37zM1894 666h-518v122h518v-122z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1738" 
d="M834 653h-158l74 617h-220v-617h-159v617h-242v141h774l236 -493l231 493h137l92 -758h-157l-58 488l-186 -404h-119l-188 404z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="1333" 
d="M1243 571h-878v-395q135 -129 311 -129q148 0 245.5 63.5t184.5 196.5l63 -39q-64 -98 -129.5 -158.5t-156.5 -95t-207 -34.5q-244 0 -400 172q-155 174 -155 421q0 253 153 422q153 172 410 172t406 -174q153 -172 153 -422zM365 639h632v334q-139 127 -313 127
q-195 0 -319 -127v-334z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1085" 
d="M942 635v-195h-799v195h799z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1249" 
d="M1094 276h-537l-106 -217h-144l107 217h-262v193h354l104 215h-458v193h553l102 206h145l-102 -206h244v-193h-338l-105 -215h443v-193z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1314" 
d="M1161 510v-215l-1032 393v213l1032 393v-215l-755 -284zM1151 180v-180h-1008v180h1008z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1314" 
d="M1186 901v-213l-1032 -393v215l755 285l-755 284v215zM164 0v180h1007v-180h-1007z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="616" 
d="M274 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="2379" 
d="M700 1178h-290v-443h282q93 0 146 63.5t53 155.5t-51.5 158t-139.5 66zM805 512l360 -512h-315l-332 502h-108v-502h-263v1411h566q220 0 334 -135q116 -131 116 -322q0 -161 -92 -288q-89 -127 -266 -154zM1294 956h250v-129q36 68 118.5 109t180.5 41q191 0 330 -139
q137 -137 137 -359q0 -225 -137 -362q-134 -137 -330 -137q-98 0 -180.5 39.5t-118.5 107.5v-592h-250v1421zM1548 475q0 -116 73.5 -187t180.5 -71q106 0 178 72q74 71 74 186t-74 189q-70 73 -178 73t-181 -73.5t-73 -188.5z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1734" 
d="M164 1407h1407v-1407h-1407v1407zM872 680l-536 539v-773h166l-152 154v365l522 -519l525 519v-365l-152 -154h172v773zM395 197l-28 73h-31v-108h18v76l27 -76h20l23 76v-76h18v108h-28zM508 270q-51 0 -51 -55q0 -24 13.5 -38.5t37.5 -14.5q22 0 34.5 15.5t12.5 37.5
q0 24 -12.5 39.5t-34.5 15.5zM508 182q-13 0 -24 11t-11 22q0 12 11 23.5t24 11.5q24 0 24 -35q0 -33 -24 -33zM582 203h-27q0 -41 45 -41t45 35q0 22 -31 28q-32 6 -32 13q0 12 18 12q21 0 21 -12h18q0 32 -39 32t-39 -32q0 -17 33 -31q27 0 27 -10q0 -15 -21 -15
q-18 0 -18 21zM645 270v-20h35v-88h16v88h31v20h-82zM815 162l-43 108h-18l-45 -108h24l8 26h45l7 -26h22zM760 244h6l14 -37h-32zM872 270h-57v-108h19v45h26q19 0 19 -19v-26h26q-6 6 -6 26q0 19 -14 27q14 6 14 23q0 32 -27 32zM860 221h-26v29h26q19 0 19 -12
q0 -17 -19 -17zM954 270h-41v-108h41q52 0 52 59q0 49 -52 49zM954 182h-16v68h8q35 0 35 -35q0 -12 -8.5 -22.5t-18.5 -10.5zM1087 250v20h-75v-108h82v20h-58v25h51v18h-51v25h51zM1112 203h-18q0 -41 45 -41t45 35q0 22 -31 28q-35 6 -35 13q0 12 15 12q24 0 24 -12h21
q0 32 -39 32q-11 0 -23 -5.5t-19.5 -13.5t1 -20t35.5 -24q24 0 24 -10q0 -15 -18 -15q-27 0 -27 21zM1206 162v108h-14v-108h14zM1296 176l9 -14h12v59h-45v-18h24q-9 -21 -24 -21q-13 0 -23 11t-10 22q0 13 10 24t23 11q18 0 18 -21h27q-9 41 -45 41q-23 0 -37 -15.5
t-14 -39.5q0 -21 14.5 -37t36.5 -16q18 0 24 14zM1391 203l-45 67h-17v-108h17v67l45 -67h26v108h-26v-67z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="CR" horiz-adv-x="681" 
 />
    <glyph glyph-name="published" horiz-adv-x="1640" 
d="M748 573v-249h-142v723h279q109 0 171 -68t62 -170q0 -91 -61 -164q-60 -72 -172 -72h-137zM748 907v-194h133q58 0 81.5 48.5t0.5 97t-78 48.5h-137zM311 190q-207 213 -207 512t207 512q210 213 508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210
t-508 210zM401 1124q-172 -175 -172 -422q0 -246 172 -421t418 -175t418 175t172 421q0 247 -172 422q-173 176 -418 176t-418 -176z" />
    <glyph glyph-name="frenchfranc" horiz-adv-x="1509" 
d="M1006 0h-242v635h-315v-635h-261v1411h854v-235h-593v-312h557v-223q70 246 256 246q118 0 176 -37l-43 -229q-60 32 -144 32q-245 0 -245 -405v-248z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="774" 
d="M86 698q0 426 174 691q176 268 445 268v-209q-163 0 -283 -221q-119 -219 -119 -529q0 -306 119 -522q120 -217 283 -217v-207q-269 0 -445 262q-174 259 -174 684z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="774" 
d="M688 698q0 -425 -174 -684q-176 -262 -444 -262v207q160 0 280 217q121 219 121 522q0 307 -121 529q-120 221 -280 221v209q268 0 444 -268q174 -265 174 -691z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="976" 
d="M848 621h-719v208h719v-208z" />
    <glyph glyph-name="at.case" horiz-adv-x="1980" 
d="M1208 502v260q0 106 -73 176q-72 72 -174 72t-177 -72q-71 -71 -71 -176q0 -101 71 -172q75 -72 177 -72q93 0 176 68v-187q-88 -49 -176 -49q-173 0 -295 119q-121 121 -121 293q0 174 121 295q124 121 295 121q166 0 288 -113q123 -114 129 -283v-293q0 -127 131 -127
q64 0 111.5 42t71.5 112q41 122 41 272q0 295 -222 496q-220 199 -530 199q-301 0 -508 -213t-207 -518q0 -302 207 -512q206 -209 508 -209h59v-170h-59q-374 0 -629 258q-256 259 -256 633q0 375 256 637q258 264 629 264q144 4 284 -38t255.5 -122.5t203 -187.5
t135.5 -241t46 -276q0 -201 -86 -387q-44 -94 -126 -150t-184 -56q-136 0 -218.5 76.5t-82.5 230.5z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="679" 
d="M606 -236h-450v1876h450v-194h-229v-1485h229v-197z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="679" 
d="M74 1640h450v-1876h-450v197h229v1485h-229v194z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="833" 
d="M70 618v170q51 0 88 33t37 82v332q0 183 116 293q118 112 293 112h160v-192h-166q-78 0 -132 -56.5t-54 -140.5v-313q0 -83 -38.5 -147t-101.5 -89q62 -24 101 -87t39 -146v-301q0 -84 55 -145q56 -62 131 -62h166v-197h-160q-174 0 -293 113q-116 110 -116 293v336
q0 48 -37 80t-88 32z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="833" 
d="M764 788v-170q-51 0 -88 -32t-37 -80v-336q0 -183 -119 -293q-116 -113 -291 -113h-159v197h166q75 0 131 62q55 61 55 145v301q0 83 39 146t100 87q-62 25 -100.5 88.5t-38.5 147.5v313q0 84 -54 140.5t-132 56.5h-166v192h159q176 0 291 -112q119 -110 119 -293v-332
q0 -49 37 -82t88 -33z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="573" 
d="M395 997l39 -997h-293l39 997h215zM285 1430q68 0 116 -44.5t48 -111.5t-48 -111.5t-116 -44.5q-66 0 -113 45t-47 111t47 111t113 45z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="1069" 
d="M385 283l-283 424l283 423h201l-283 -423l283 -424h-201zM748 283l-283 424l283 423h200l-282 -423l282 -424h-200z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="569" 
d="M283 1026q68 0 115.5 -44.5t47.5 -111.5t-47.5 -111t-115.5 -44q-66 0 -113 44.5t-47 110.5t47 111t113 45z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="1069" 
d="M322 1130l282 -423l-282 -424h-201l282 424l-282 423h201zM684 1130l283 -423l-283 -424h-201l283 424l-283 423h201z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="1005" 
d="M477 926l4 73h244l4 -73q0 -136 -49 -232q-15 -29 -72 -73q-58 -41 -86 -56l-119 -59q-77 -35 -77 -133q0 -63 52 -105.5t126 -42.5q76 0 125 49.5t49 128.5h248q0 -195 -119 -311q-117 -117 -303 -117t-307 109q-121 112 -121 291q0 140 77.5 244t204.5 143
q64 21 91.5 57t27.5 107zM762 1270q0 -66 -47 -111t-115 -45t-115 45t-47 111q0 67 47 112t115 45t115 -45t47 -112z" />
    <glyph glyph-name="endash.case" horiz-adv-x="1271" 
d="M1141 600h-1010v221h1010v-221z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1703" 
d="M1573 600h-1442v221h1442v-221z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="958" 
d="M252 498q-92 89 -92 219t92 219q94 88 227 88q135 0 226 -88q94 -88 94 -219t-94 -219q-91 -88 -226 -88q-133 0 -227 88z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="706" 
d="M385 283l-283 424l283 423h201l-283 -423l283 -424h-201z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="706" 
d="M322 1130l282 -423l-282 -424h-201l282 424l-282 423h201z" />
    <glyph glyph-name="cent.case" horiz-adv-x="1099" 
d="M508 1417h141v-213q226 -18 352 -184l-198 -150q-71 95 -199 95q-107 0 -188 -72q-80 -71 -80 -182q0 -112 80 -189q79 -73 188 -73q127 0 199 96l201 -148q-128 -168 -355 -186v-231h-141v237q-181 34 -301 166q-119 131 -119 328q0 192 119 323q117 129 301 164v219z
" />
    <glyph glyph-name="zero.tnum" horiz-adv-x="1187" 
d="M98 670q0 694 498 694q280 0 393 -205q98 -178 98 -489q0 -121 -13.5 -219.5t-48 -188t-89 -150.5t-141 -96.5t-199.5 -35.5q-498 0 -498 690zM350 670q0 -445 246 -445q242 0 242 445q0 448 -242 448q-246 0 -246 -448z" />
    <glyph glyph-name="one.tnum" horiz-adv-x="1187" 
d="M303 920v225l477 211v-1356h-252v1010z" />
    <glyph glyph-name="two.tnum" horiz-adv-x="1187" 
d="M1008 240v-240h-856v129l546 655q82 94 82 177q0 68 -54 113.5t-138 45.5q-82 0 -139.5 -54t-69.5 -140l-234 43q26 178 152 288q122 107 301 107q178 0 305 -115t127 -288q0 -181 -143 -343l-332 -378h453z" />
    <glyph glyph-name="three.tnum" horiz-adv-x="1187" 
d="M563 596h-143v223h143q83 -3 117 49q35 53 35 99q0 54 -30 97t-89 54q-132 15 -207 -82l-196 129q57 97 167 151.5t238 47.5q171 -9 279 -117t108 -258q0 -79 -45 -149t-121 -113q104 -40 168 -147q64 -108 55 -218q-18 -180 -168 -288q-149 -107 -348 -92
q-135 7 -247 84.5t-168 197.5l108 53l111 58q31 -64 91.5 -107t125.5 -43q97 0 161 56.5t64 142.5q0 14 -4 32.5t-18 44.5t-35.5 47t-61 35.5t-90.5 12.5z" />
    <glyph glyph-name="four.tnum" horiz-adv-x="1187" 
d="M686 977l-297 -467h297v467zM1114 510v-219h-178v-291h-250v291h-661l667 1048h244v-829h178z" />
    <glyph glyph-name="five.tnum" horiz-adv-x="1187" 
d="M840 459q0 110 -71 170.5t-187 60.5q-182 0 -308 -135l-143 86l113 698h762v-231h-562l-47 -274q83 71 248 71q193 0 318 -119q127 -118 127 -327q0 -213 -148 -346q-145 -133 -387 -133q-139 0 -266 77t-199 205q11 4 107 66t104 65q42 -74 111.5 -120t142.5 -46
q127 0 206 66.5t79 165.5z" />
    <glyph glyph-name="six.tnum" horiz-adv-x="1187" 
d="M811 424q2 85 -55.5 145.5t-141.5 61.5q-81 3 -145 -58q-60 -57 -63 -143q0 -87 58 -147t142 -60q85 -2 144.5 56.5t60.5 144.5zM655 864q169 0 291 -125q126 -126 123 -329q-3 -190 -137 -312q-133 -121 -338 -118q-186 3 -315 129q-131 128 -146 335q-6 73 -7 142.5
t4 144t16 141.5t31.5 132.5t49 119.5t70 100.5t92.5 77.5t118 47.5t146 14.5q143 -6 242 -65.5t162 -194.5l-232 -98q-58 122 -180 122q-138 0 -205 -122q-64 -119 -55 -293q21 60 98 105.5t172 45.5z" />
    <glyph glyph-name="seven.tnum" horiz-adv-x="1187" 
d="M137 1104v235h981l-641 -1339h-287l531 1104h-584z" />
    <glyph glyph-name="eight.tnum" horiz-adv-x="1187" 
d="M442 991q0 -62 43 -103.5t111 -41.5q66 0 106.5 41.5t40.5 103.5q0 58 -42 98.5t-107 40.5q-66 0 -109 -41t-43 -98zM350 442q0 -90 72 -159q72 -66 172 -66q98 0 170 66q72 69 72 159q0 89 -68 154q-68 68 -160 68h-37q-92 0 -157 -68q-64 -67 -64 -154zM199 999
q0 150 114 258q113 107 281 107q165 0 278 -107q117 -108 117 -258q0 -136 -100 -229q93 -54 144.5 -142.5t51.5 -193.5q0 -190 -143 -321q-145 -133 -348 -133q-205 0 -350 133q-144 132 -144 321q0 107 52 194t145 142q-98 94 -98 229z" />
    <glyph glyph-name="nine.tnum" horiz-adv-x="1187" 
d="M784 911q0 86 -57.5 147t-142.5 62t-144 -57t-61 -143t55 -145.5t141 -61.5q85 -3 146 55q60 57 63 143zM532 479q-165 0 -290 125q-124 127 -121 330q3 189 135 309q134 122 338 119q189 -3 315 -129q130 -127 148 -334q7 -82 7 -160t-6.5 -161.5t-23.5 -157
t-43.5 -143.5t-68.5 -124.5t-95.5 -95t-127.5 -60.5t-162 -17q-143 6 -242 65.5t-162 194.5l232 98q59 -123 180 -123q138 0 203 121q66 118 57 295q-21 -60 -99 -106t-174 -46z" />
    <glyph glyph-name="zero.taboldstyle" horiz-adv-x="1187" 
d="M594 -18q-211 0 -363 149q-151 151 -151 387q0 239 151 387q150 150 363 150q214 0 364 -150q152 -149 152 -387q0 -235 -152 -387q-149 -149 -364 -149zM594 219q110 0 194 84t84 215q0 134 -81 215q-82 82 -197 82q-113 0 -195 -82q-80 -83 -80 -215q0 -133 82 -215
q84 -84 193 -84z" />
    <glyph glyph-name="one.taboldstyle" horiz-adv-x="1187" 
d="M289 805l530 254v-1059h-252v684l-278 -125v246z" />
    <glyph glyph-name="two.taboldstyle" horiz-adv-x="1187" 
d="M993 227v-227h-819v135l481 492q70 64 70 110q0 42 -37.5 71.5t-97.5 29.5q-64 0 -106.5 -34.5t-49.5 -96.5l-244 32q19 153 127 243.5t277 90.5q171 0 287 -96q114 -95 114 -229q0 -136 -143 -273l-262 -248h403z" />
    <glyph glyph-name="six.taboldstyle" horiz-adv-x="1187" 
d="M811 424q2 85 -55.5 145.5t-141.5 61.5q-81 3 -145 -58q-60 -57 -63 -143q0 -87 58 -147t142 -60q85 -2 144.5 56.5t60.5 144.5zM655 864q169 0 291 -125q126 -126 123 -329q-3 -190 -137 -312q-133 -121 -338 -118q-186 3 -315 129q-131 128 -146 335q-6 73 -7 142.5
t4 144t16 141.5t31.5 132.5t49 119.5t70 100.5t92.5 77.5t118 47.5t146 14.5q143 -6 242 -65.5t162 -194.5l-232 -98q-58 122 -180 122q-138 0 -205 -122q-64 -119 -55 -293q21 60 98 105.5t172 45.5z" />
    <glyph glyph-name="eight.taboldstyle" horiz-adv-x="1187" 
d="M442 991q0 -62 43 -103.5t111 -41.5q66 0 106.5 41.5t40.5 103.5q0 58 -42 98.5t-107 40.5q-66 0 -109 -41t-43 -98zM350 442q0 -90 72 -159q72 -66 172 -66q98 0 170 66q72 69 72 159q0 89 -68 154q-68 68 -160 68h-37q-92 0 -157 -68q-64 -67 -64 -154zM199 999
q0 150 114 258q113 107 281 107q165 0 278 -107q117 -108 117 -258q0 -136 -100 -229q93 -54 144.5 -142.5t51.5 -193.5q0 -190 -143 -321q-145 -133 -348 -133q-205 0 -350 133q-144 132 -144 321q0 107 52 194t145 142q-98 94 -98 229z" />
    <glyph glyph-name="three.taboldstyle" horiz-adv-x="1187" 
d="M567 209h-143v223h143q83 -3 117 49q35 53 35 99q0 54 -30 97t-89 54q-132 15 -207 -82l-196 129q57 97 167 151.5t238 47.5q171 -9 279 -117t108 -258q0 -79 -45 -149t-121 -113q104 -40 168 -147q65 -109 56 -218q-18 -178 -168 -286q-152 -110 -349 -95
q-135 7 -247 85t-168 198q12 4 108 55l111 56q31 -64 91.5 -107t125.5 -43q97 0 161 56.5t64 142.5q0 14 -4 32.5t-18 44.5t-35.5 47t-61 35.5t-90.5 12.5z" />
    <glyph glyph-name="four.taboldstyle" horiz-adv-x="1187" 
d="M690 590l-297 -465h297v465zM1120 125v-219h-178v-291h-252v291h-661l667 1048h246v-829h178z" />
    <glyph glyph-name="five.taboldstyle" horiz-adv-x="1187" 
d="M840 74q0 110 -70 170.5t-188 60.5q-184 0 -308 -133l-143 86l113 696h762v-231h-562l-47 -274q85 73 248 73q193 0 318 -119q127 -121 127 -329q0 -213 -148 -346q-146 -134 -387 -134q-139 0 -266 77.5t-199 205.5l107 66l106 65q38 -74 107.5 -119t144.5 -45
q128 0 206.5 65.5t78.5 164.5z" />
    <glyph glyph-name="seven.taboldstyle" horiz-adv-x="1187" 
d="M139 719v235h981l-643 -1339h-287l531 1104h-582z" />
    <glyph glyph-name="nine.taboldstyle" horiz-adv-x="1187" 
d="M791 526q0 86 -58 147t-143 62t-144 -57t-61 -143t55.5 -145.5t141.5 -61.5q84 -3 145 55t64 143zM539 94q-166 0 -291 125q-124 127 -121 330q3 189 135 309q134 122 338 119q189 -3 315 -129q130 -127 148 -334q6 -73 7 -142t-4 -143.5t-16 -142t-31.5 -133t-49 -120
t-70 -101t-92.5 -77.5t-118 -47.5t-146 -13.5q-143 6 -242 66t-162 195l232 98q59 -123 180 -123q138 0 203 121q66 118 57 295q-21 -60 -98.5 -106t-173.5 -46z" />
    <glyph glyph-name="zero.oldstyle" horiz-adv-x="1228" 
d="M612 -18q-210 0 -362 149q-152 152 -152 387q0 238 152 387q150 150 362 150q215 0 365 -150q151 -148 151 -387q0 -236 -151 -387q-149 -149 -365 -149zM612 219q111 0 195 84t84 215t-84 215q-82 82 -195 82q-112 0 -194 -82q-80 -83 -80 -215q0 -133 82 -215
q84 -84 192 -84z" />
    <glyph glyph-name="one.oldstyle" horiz-adv-x="776" 
d="M84 805l530 254v-1059h-249v684l-281 -125v246z" />
    <glyph glyph-name="two.oldstyle" horiz-adv-x="1069" 
d="M936 227v-227h-819v135l481 492q68 65 68 110q0 42 -37 71.5t-97 29.5q-64 0 -106 -34.5t-49 -96.5l-244 32q17 153 125 243.5t279 90.5q170 0 286 -96q113 -94 113 -229q0 -139 -143 -273l-263 -248h406z" />
    <glyph glyph-name="three.oldstyle" horiz-adv-x="1079" 
d="M514 209h-143v223h143q83 -3 117 49q35 53 35 99q0 54 -30 97t-89 54q-132 15 -207 -82l-197 129q57 97 167.5 151.5t238.5 47.5q170 -9 278 -117q109 -109 109 -258q0 -79 -45 -149t-121 -113q104 -40 168 -147q64 -108 55 -218q-18 -178 -168 -286q-152 -110 -348 -95
q-135 7 -247.5 85t-168.5 198q14 5 109 55l111 56q31 -64 91.5 -107t125.5 -43q97 0 161 56.5t64 142.5q0 14 -4 32.5t-18 44.5t-35.5 47t-61 35.5t-90.5 12.5z" />
    <glyph glyph-name="four.oldstyle" horiz-adv-x="1200" 
d="M696 590l-297 -465h297v465zM1126 125v-219h-178v-291h-252v291h-659l665 1048h246v-829h178z" />
    <glyph glyph-name="five.oldstyle" 
d="M829 72q0 110 -71.5 171.5t-186.5 61.5q-183 0 -309 -135l-141 86l112 698h760v-233h-559l-49 -272q92 71 248 71q192 0 317 -119q127 -121 127 -329q0 -212 -147 -344q-146 -134 -387 -134q-139 0 -266 77.5t-199 205.5q15 8 108 68l105 63q38 -75 108 -120.5t144 -45.5
q128 0 207 65.5t79 164.5z" />
    <glyph glyph-name="six.oldstyle" horiz-adv-x="1173" 
d="M805 424q2 85 -55.5 145.5t-141.5 61.5q-81 3 -145 -58q-61 -58 -64 -143q0 -86 58 -146.5t143 -60.5q85 -2 144.5 56.5t60.5 144.5zM649 864q169 0 291 -125q126 -126 123 -329q-3 -190 -137 -312q-133 -121 -338 -118q-187 3 -316 129q-130 127 -145 335
q-6 73 -7 142.5t4 144t16 141.5t31.5 132.5t49 119.5t70 100.5t92.5 77.5t118 47.5t146 14.5q143 -6 242 -65.5t162 -194.5l-232 -98q-58 122 -180 122q-138 0 -205 -122q-64 -119 -55 -293q21 60 98 105.5t172 45.5z" />
    <glyph glyph-name="seven.oldstyle" horiz-adv-x="1060" 
d="M76 719v235h981l-641 -1339h-287l530 1104h-583z" />
    <glyph glyph-name="eight.oldstyle" horiz-adv-x="1196" 
d="M446 991q0 -62 43 -103.5t111 -41.5q66 0 107 42t41 103q0 58 -42.5 98.5t-107.5 40.5q-66 0 -109 -41t-43 -98zM354 442q0 -90 72 -159q72 -66 172 -66q98 0 170 66q72 69 72 159q0 89 -68 154q-68 68 -160 68h-37q-92 0 -157 -68q-64 -67 -64 -154zM203 999
q0 150 114 258q113 107 281 107q166 0 279 -107q116 -107 116 -258q0 -136 -100 -229q93 -54 145 -142.5t52 -193.5q0 -189 -144 -321q-145 -133 -348 -133q-205 0 -350 133q-144 132 -144 321q0 107 52 194t145 142q-98 94 -98 229z" />
    <glyph glyph-name="nine.oldstyle" horiz-adv-x="1134" 
d="M764 526q0 86 -58 147t-143 62t-144 -57t-61 -143t55.5 -145.5t141.5 -61.5q84 -3 145 55t64 143zM512 94q-166 0 -291 125q-124 127 -121 330q3 188 136 309q134 122 337 119q190 -3 316 -129q129 -126 147 -334q6 -73 7 -142t-4 -143.5t-16 -142t-31.5 -133t-49 -120
t-70 -101t-92.5 -77.5t-118 -47.5t-146 -13.5q-142 6 -241 66t-162 195l231 98q58 -123 180 -123q138 0 203 121q66 118 57 295q-21 -60 -98.5 -106t-173.5 -46z" />
    <glyph glyph-name="Lslash.sc" horiz-adv-x="888" 
d="M408 1171v-364l161 102v-149l-161 -103v-448h446v-209h-676v514l-119 -72v150l119 74v505h230z" />
    <glyph glyph-name="Zcaron.sc" horiz-adv-x="1052" 
d="M403 1575l127 -143l129 143h172l-192 -285h-215l-193 285h172zM96 0v178l555 789h-530v204h825v-176l-557 -788h557v-207h-850z" />
    <glyph glyph-name="exclam.sc" horiz-adv-x="593" 
d="M397 340h-198l-33 827h264zM434 113q0 -56 -40.5 -94.5t-98.5 -38.5q-60 0 -100.5 38t-40.5 95q0 59 40 97t101 38q58 0 98.5 -38t40.5 -97z" />
    <glyph glyph-name="ampersand.sc" horiz-adv-x="1196" 
d="M530 715h199v137h205v-139h139v-176h-139v-187q0 -100 33.5 -137t130.5 -37v-194q-135 2 -210 30.5t-106 98.5q-37 -60 -116 -95.5t-162 -35.5q-175 0 -287 100q-113 101 -113 258q0 218 177 301q-113 58 -113 221q0 125 96 221q99 99 236 99q207 0 321 -164l-182 -115
q-54 66 -115 66q-58 0 -92.5 -34t-34.5 -85q0 -36 29 -86q30 -47 104 -47zM729 438v99h-170q-105 0 -162 -43.5t-63 -118.5q-1 -79 53 -130.5t139 -51.5q89 0 146 66t57 179z" />
    <glyph glyph-name="question.sc" horiz-adv-x="874" 
d="M291 831h-211q0 163 97.5 260t256.5 97q153 0 256 -92q101 -88 101 -242q0 -119 -65 -206t-171 -120q-45 -12 -66 -36t-24 -52l-8 -102h-213q0 7 -1 31t-1 32q0 130 50 204.5t163 115.5q65 24 91.5 54.5t26.5 78.5q0 49 -40.5 82t-98.5 33q-61 0 -101.5 -38.5
t-41.5 -99.5zM213 113q0 59 40.5 97t98.5 38q61 0 101.5 -38t40.5 -97q0 -57 -41 -95t-101 -38q-58 0 -98.5 38.5t-40.5 94.5z" />
    <glyph glyph-name="A.sc" horiz-adv-x="1167" 
d="M582 899l-136 -418h275zM1143 0h-242l-104 281h-424l-107 -281h-241l438 1171h242z" />
    <glyph glyph-name="B.sc" horiz-adv-x="1128" 
d="M969 850q0 -158 -142 -223q93 -28 148 -106t55 -167q0 -152 -100 -252q-102 -102 -266 -102h-496v1171h461q158 0 249 -86t91 -235zM395 967v-258h217q53 0 86 36.5t33 94.5q0 60 -32.5 93.5t-92.5 33.5h-211zM647 512h-252v-307h256q61 0 101.5 46t40.5 109
q0 60 -41.5 106t-104.5 46z" />
    <glyph glyph-name="C.sc" horiz-adv-x="1228" 
d="M987 356l182 -133q-80 -114 -206.5 -178.5t-272.5 -64.5q-251 0 -422 174q-172 175 -172 426q0 252 170 428q173 176 424 176q293 0 475 -238l-182 -131q-122 152 -293 152q-156 0 -260 -113t-104 -274q0 -160 104 -273q104 -110 260 -110q183 0 297 159z" />
    <glyph glyph-name="D.sc" horiz-adv-x="1257" 
d="M627 0h-459v1171h459q249 0 389 -165q141 -164 141 -424q0 -256 -141 -420q-140 -162 -389 -162zM395 963v-754h232q139 0 220 104.5t81 268.5q0 171 -79 276t-222 105h-232z" />
    <glyph glyph-name="E.sc" horiz-adv-x="1021" 
d="M168 0v1171h729v-204h-504v-287h443v-205h-443v-270h518v-205h-743z" />
    <glyph glyph-name="F.sc" horiz-adv-x="954" 
d="M168 0v1171h721v-204h-494v-287h391v-205h-391v-475h-227z" />
    <glyph glyph-name="G.sc" horiz-adv-x="1327" 
d="M1120 999l-164 -145h-4q-106 115 -260 115q-153 0 -262 -115q-106 -112 -106 -274q0 -163 106 -275t262 -112q118 0 209 61q93 66 96 164h-317v190h577q0 -308 -149 -468t-416 -160q-248 0 -424 176q-174 174 -174 424q0 253 172 428q176 176 426 176q258 0 428 -185z
" />
    <glyph glyph-name="H.sc" horiz-adv-x="1243" 
d="M846 1171h229v-1171h-229v479h-453v-479h-225v1171h225v-485h453v485z" />
    <glyph glyph-name="I.sc" horiz-adv-x="561" 
d="M168 0v1171h225v-1171h-225z" />
    <glyph glyph-name="J.sc" horiz-adv-x="563" 
d="M-145 -379l20 209q53 -18 141 -18q75 0 113.5 47.5t38.5 128.5v1183h227v-1183q0 -184 -95 -289t-284 -105q-66 0 -161 27z" />
    <glyph glyph-name="K.sc" horiz-adv-x="1069" 
d="M969 1171l-447 -581l551 -590h-297l-379 420v-420h-229v1171h229v-407l303 407h269z" />
    <glyph glyph-name="L.sc" horiz-adv-x="876" 
d="M168 0v1171h227v-966h447v-205h-674z" />
    <glyph glyph-name="M.sc" horiz-adv-x="1605" 
d="M436 1171l365 -774l366 774h195l141 -1171h-227l-88 739l-293 -608h-180l-297 608l-90 -739h-228l144 1171h192z" />
    <glyph glyph-name="N.sc" horiz-adv-x="1206" 
d="M1038 1171v-1171h-207l-438 745v-745h-225v1171h209l432 -733v733h229z" />
    <glyph glyph-name="O.sc" horiz-adv-x="1380" 
d="M96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267q0 156 -109 268q-107 110 -262 110
q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="P.sc" horiz-adv-x="1085" 
d="M168 0v1171h477q188 0 281 -112q96 -113 96 -268q0 -156 -96 -269q-94 -108 -281 -108h-250v-414h-227zM641 967h-246v-351h238q100 -4 141 85.5t1 179t-134 86.5z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="1386" 
d="M1034 88l115 -188l-168 -105l-127 207q-83 -22 -162 -22q-248 0 -424 176q-174 174 -174 424q0 253 172 428q176 176 426 176t426 -176q174 -174 174 -428q0 -146 -69 -276t-189 -216zM629 369l168 102l118 -192q67 52 107.5 133t40.5 168q0 159 -109 274
q-107 113 -262 113t-262 -113q-106 -112 -106 -274q0 -148 94 -252q94 -102 244 -123h69l-65 104z" />
    <glyph glyph-name="R.sc" horiz-adv-x="1124" 
d="M725 422l299 -422h-276l-275 414h-78v-414h-227v1171h471q184 0 281 -112q98 -113 98 -268q0 -138 -77 -239.5t-216 -129.5zM629 967h-234v-351h226q99 -4 140 85.5t1.5 179t-133.5 86.5z" />
    <glyph glyph-name="S.sc" horiz-adv-x="1030" 
d="M100 348h230q2 -69 55 -115.5t137 -46.5q81 0 131.5 36.5t55.5 96.5q9 111 -156 152l-143 41q-297 79 -297 328q0 152 114 247q120 97 283 97q174 0 282.5 -98.5t108.5 -262.5h-225q0 68 -46.5 110t-121.5 42q-70 0 -119 -37.5t-49 -93.5q0 -89 135 -127l141 -41
q167 -46 247.5 -142.5t74.5 -220.5q-9 -155 -129 -243q-122 -90 -287 -90q-187 0 -305 106q-117 108 -117 262z" />
    <glyph glyph-name="T.sc" horiz-adv-x="976" 
d="M47 965v206h883v-206h-326v-965h-231v965h-326z" />
    <glyph glyph-name="U.sc" horiz-adv-x="1204" 
d="M821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="V.sc" horiz-adv-x="1034" 
d="M57 1171h238l221 -786l221 786h240l-356 -1171h-205z" />
    <glyph glyph-name="W.sc" horiz-adv-x="1576" 
d="M1051 397l245 774h246l-399 -1171h-182l-173 631l-172 -631h-182l-397 1171h246l243 -776l172 617h183z" />
    <glyph glyph-name="X.sc" horiz-adv-x="1155" 
d="M1079 1171l-368 -544l409 -627h-270l-275 436l-270 -436h-270l405 627l-366 544h274l227 -360l234 360h270z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="1171" 
d="M700 532v-532h-231v532l-451 639h271l295 -440l297 440h276z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="1052" 
d="M96 0v178l555 789h-530v204h825v-176l-557 -788h557v-207h-850z" />
    <glyph glyph-name="Scaron.sc" horiz-adv-x="1030" 
d="M379 1575l129 -143l129 143h172l-195 -285h-215l-190 285h170zM330 348q2 -69 55 -115.5t137 -46.5q81 0 131.5 36.5t55.5 96.5q9 111 -156 152l-143 41q-297 79 -297 328q0 152 114 247q120 97 283 97q174 0 282.5 -98.5t108.5 -262.5h-225q0 68 -46.5 110t-121.5 42
q-70 0 -119 -37.5t-49 -93.5q0 -89 135 -127l141 -41q167 -46 247.5 -142.5t74.5 -220.5q-9 -155 -129 -243q-122 -90 -287 -90q-187 0 -305 106q-117 108 -117 262h230z" />
    <glyph glyph-name="OE.sc" horiz-adv-x="1910" 
d="M1055 1036v135h731v-206h-502v-285h438v-207h-438v-264h516v-209h-745v125q-160 -145 -367 -145q-249 0 -422 176q-172 175 -172 424q0 253 172 428q173 176 422 176q219 0 367 -148zM322 580q0 -156 106 -265q108 -108 260 -108q154 0 260 112q107 113 107 267
q-3 152 -111 260q-107 110 -256 110q-153 0 -260 -110q-106 -106 -106 -266z" />
    <glyph glyph-name="Ydieresis.sc" horiz-adv-x="1171" 
d="M549 1450q0 -51 -34 -84t-85 -33t-87 34t-36 83t36 82t87 33t85 -33t34 -82zM723 1565q51 0 86 -33t35 -82q0 -51 -35 -84t-86 -33q-50 0 -85.5 34t-35.5 83t35.5 82t85.5 33zM700 532v-532h-231v532l-451 639h271l295 -440l297 440h276z" />
    <glyph glyph-name="exclamdown.sc" horiz-adv-x="593" 
d="M199 827h198l33 -827h-264zM434 1055q0 -59 -40.5 -97t-98.5 -38q-61 0 -101 38t-40 97q0 57 40.5 95t100.5 38q58 0 98.5 -38.5t40.5 -94.5z" />
    <glyph glyph-name="questiondown.sc" horiz-adv-x="874" 
d="M584 336h211q0 -163 -97.5 -259.5t-257.5 -96.5q-152 0 -256 90q-100 90 -100 243q0 119 64.5 206t170.5 120q43 11 66 37q20 22 23 49l10 104h213q0 -7 1 -31t1 -32q0 -130 -50 -204.5t-163 -115.5q-65 -25 -92 -55t-27 -78q0 -49 41 -81.5t98 -32.5q61 0 102 38t42 99z
M662 1055q0 -59 -41 -97t-99 -38q-61 0 -101 38t-40 97q0 57 40.5 95t100.5 38q58 0 99 -38.5t41 -94.5z" />
    <glyph glyph-name="Agrave.sc" horiz-adv-x="1167" 
d="M684 1292h-174l-242 285h232zM582 899l-136 -418h275zM1143 0h-242l-104 281h-424l-107 -281h-241l438 1171h242z" />
    <glyph glyph-name="Aacute.sc" horiz-adv-x="1167" 
d="M670 1573h229l-240 -287h-176zM721 481l-139 418l-136 -418h275zM797 281h-424l-107 -281h-241l438 1171h242l438 -1171h-242z" />
    <glyph glyph-name="Acircumflex.sc" horiz-adv-x="1167" 
d="M877 1294h-175l-127 146l-129 -146h-170l193 287h213zM582 899l-136 -418h275zM1143 0h-242l-104 281h-424l-107 -281h-241l438 1171h242z" />
    <glyph glyph-name="Atilde.sc" horiz-adv-x="1167" 
d="M502 1397q-14 0 -25.5 -19.5t-11.5 -52.5h-131q6 100 46.5 155.5t100.5 63.5q36 3 88 -31t80 -34q14 0 26 17.5t13 49.5h131q-6 -109 -54 -164t-116 -55q-33 0 -80 35t-67 35zM582 899l-136 -418h275zM1143 0h-242l-104 281h-424l-107 -281h-241l438 1171h242z" />
    <glyph glyph-name="Adieresis.sc" horiz-adv-x="1167" 
d="M418 1565q50 0 84.5 -33t34.5 -82t-34.5 -83t-84.5 -34q-51 0 -88 34t-37 83t36.5 82t88.5 33zM831 1450q0 -51 -34.5 -84t-85.5 -33t-87 34t-36 83t36 82t87 33t85.5 -33t34.5 -82zM721 481l-139 418l-136 -418h275zM797 281h-424l-107 -281h-241l438 1171h242
l438 -1171h-242z" />
    <glyph glyph-name="Aring.sc" horiz-adv-x="1167" 
d="M375 1483q0 86 56.5 144.5t141.5 58.5q86 0 144.5 -58.5t58.5 -144.5q0 -85 -58.5 -142t-144.5 -57q-85 0 -141.5 57t-56.5 142zM573 1399q37 0 64 24.5t27 59.5t-27 61.5t-64 26.5q-33 0 -58.5 -26.5t-25.5 -61.5t24.5 -59.5t59.5 -24.5zM721 481l-139 418l-136 -418
h275zM797 281h-424l-107 -281h-241l438 1171h242l438 -1171h-242z" />
    <glyph glyph-name="AE.sc" horiz-adv-x="1642" 
d="M788 274h-335l-177 -274h-272l741 1171h773v-206h-502v-285h438v-207h-438v-264h518v-209h-746v274zM788 850l-204 -354h204v354z" />
    <glyph glyph-name="Ccedilla.sc" horiz-adv-x="1230" 
d="M584 -197q6 -20 25.5 -29t39.5 -1q40 15 25 69l-53 144q-226 24 -377 194q-148 171 -148 402q0 252 170 428q173 176 424 176q293 0 475 -238l-182 -131q-116 152 -293 152q-156 0 -260 -113t-104 -274q0 -158 104 -271q104 -110 260 -110q185 0 297 157l182 -133
q-156 -215 -424 -239q2 -6 21 -41.5t22 -44.5q21 -47 21 -82q0 -73 -53.5 -125.5t-130.5 -52.5q-76 0 -128 60.5t-51 154.5l134 -7q0 -25 4 -45z" />
    <glyph glyph-name="Egrave.sc" horiz-adv-x="1021" 
d="M168 0v1171h729v-204h-504v-287h443v-205h-443v-270h518v-205h-743zM670 1292h-176l-244 285h233z" />
    <glyph glyph-name="Eacute.sc" horiz-adv-x="1021" 
d="M561 1577h234l-244 -285h-174zM897 1171v-204h-504v-287h443v-205h-443v-270h518v-205h-743v1171h729z" />
    <glyph glyph-name="Ecircumflex.sc" horiz-adv-x="1021" 
d="M168 0v1171h729v-204h-504v-287h443v-205h-443v-270h518v-205h-743zM805 1294h-170l-129 146l-129 -146h-170l190 287h215z" />
    <glyph glyph-name="Edieresis.sc" horiz-adv-x="1021" 
d="M240 1450q0 50 35.5 82.5t89.5 32.5q50 0 84 -33t34 -82t-34 -83t-84 -34q-54 0 -89.5 33t-35.5 84zM532 1450q0 50 35.5 82.5t89.5 32.5q50 0 84.5 -33t34.5 -82t-34.5 -83t-84.5 -34q-54 0 -89.5 33t-35.5 84zM897 1171v-204h-504v-287h443v-205h-443v-270h518v-205
h-743v1171h729z" />
    <glyph glyph-name="Igrave.sc" horiz-adv-x="561" 
d="M168 0v1171h225v-1171h-225zM389 1292h-176l-244 285h234z" />
    <glyph glyph-name="Iacute.sc" horiz-adv-x="561" 
d="M164 1311l192 272h236l-248 -272h-180zM393 1171v-1171h-225v1171h225z" />
    <glyph glyph-name="Icircumflex.sc" horiz-adv-x="561" 
d="M168 0v1171h225v-1171h-225zM582 1294h-170l-129 146l-129 -146h-170l190 287h215z" />
    <glyph glyph-name="Idieresis.sc" horiz-adv-x="561" 
d="M393 1171v-1171h-225v1171h225zM14 1450q0 50 35.5 82.5t89.5 32.5q50 0 84.5 -33t34.5 -82t-34.5 -83t-84.5 -34q-54 0 -89.5 33t-35.5 84zM307 1450q0 50 35.5 82.5t89.5 32.5q50 0 84.5 -33t34.5 -82t-34.5 -83t-84.5 -34q-54 0 -89.5 33t-35.5 84z" />
    <glyph glyph-name="Eth.sc" horiz-adv-x="1269" 
d="M641 0h-457v500h-157v147h157v524h457q248 0 385 -165q141 -164 141 -424q0 -256 -141 -420q-137 -162 -385 -162zM412 958v-311h231v-147h-231v-287h229q139 0 219 102.5t80 266.5q0 168 -79.5 272t-219.5 104h-229z" />
    <glyph glyph-name="Ntilde.sc" horiz-adv-x="1206" 
d="M1038 1171v-1171h-207l-438 745v-745h-225v1171h209l432 -733v733h229zM528 1397q-13 0 -24.5 -19.5t-11.5 -52.5h-134q6 109 54 165t116 56q33 0 80 -35q43 -32 68 -32q6 0 14 5t15.5 21.5t7.5 40.5h131q-4 -109 -52.5 -164t-115.5 -55q-33 0 -80 35t-68 35z" />
    <glyph glyph-name="Ograve.sc" horiz-adv-x="1380" 
d="M756 1292h-174l-244 285h231zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267
q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Oacute.sc" horiz-adv-x="1380" 
d="M786 1577h234l-244 -285h-174zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267
q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Ocircumflex.sc" horiz-adv-x="1380" 
d="M989 1294h-170l-131 148l-125 -148h-172l193 287h213zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108
q109 112 109 267q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Otilde.sc" horiz-adv-x="1380" 
d="M96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267q0 156 -109 268q-107 110 -262 110
q-153 0 -260 -110q-104 -110 -104 -268zM616 1397q-13 0 -24.5 -19.5t-11.5 -52.5h-131q6 110 52.5 165.5t114.5 55.5q35 0 82 -35q43 -32 66 -32q14 0 26 17.5t13 49.5h129q-4 -109 -52 -164t-116 -55q-31 0 -78 35t-70 35z" />
    <glyph glyph-name="Odieresis.sc" horiz-adv-x="1380" 
d="M545 1565q50 0 84.5 -33t34.5 -82t-34.5 -83t-84.5 -34q-51 0 -87 34t-36 83t36 82t87 33zM717 1450q0 49 35 82t86 33t85.5 -33t34.5 -82q0 -51 -34.5 -84t-85.5 -33t-86 33t-35 84zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428
q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Oslash.sc" horiz-adv-x="1378" 
d="M319 106l-135 -139l-86 88l133 140q-137 168 -137 385q0 253 172 428q173 176 420 176q214 0 377 -135l135 137l86 -86l-135 -137q135 -173 135 -383q0 -250 -174 -424q-176 -176 -424 -176q-198 0 -367 126zM322 580q0 -132 69 -224l514 531q-100 71 -219 71
q-153 0 -260 -110q-104 -110 -104 -268zM686 205q154 0 262 108q109 112 109 267q0 119 -68 219l-512 -529q89 -65 209 -65z" />
    <glyph glyph-name="Ugrave.sc" horiz-adv-x="1204" 
d="M737 1292h-174l-241 285h231zM821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Uacute.sc" horiz-adv-x="1204" 
d="M647 1577h230l-242 -285h-176zM821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Ucircumflex.sc" horiz-adv-x="1204" 
d="M901 1294h-172l-129 148l-127 -148h-172l193 287h213zM821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Udieresis.sc" horiz-adv-x="1204" 
d="M457 1565q50 0 83 -33t33 -82q0 -51 -33 -84t-83 -33q-51 0 -88 34t-37 83t36.5 82t88.5 33zM870 1450q0 -51 -34.5 -84t-85.5 -33t-87 34t-36 83t36 82t87 33t85.5 -33t34.5 -82zM821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120
q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Yacute.sc" horiz-adv-x="1171" 
d="M700 532v-532h-231v532l-451 639h271l295 -440l297 440h276zM639 1577h233l-243 -285h-174z" />
    <glyph glyph-name="Thorn.sc" horiz-adv-x="1017" 
d="M578 221h-183v-221h-227v1171h227v-194h183q160 0 268 -105q106 -100 106 -272q0 -171 -106 -274q-108 -105 -268 -105zM395 778v-358h174q65 0 111.5 50t46.5 126q0 79 -45.5 128t-114.5 54h-172z" />
    <glyph glyph-name="Amacron.sc" horiz-adv-x="1167" 
d="M344 1360v151h465v-151h-465zM582 899l-136 -418h275zM1143 0h-242l-104 281h-424l-107 -281h-241l438 1171h242z" />
    <glyph glyph-name="Abreve.sc" horiz-adv-x="1167" 
d="M346 1542h150q0 -92 79 -92q37 0 58.5 26t21.5 66h152q0 -104 -64.5 -166.5t-167.5 -62.5q-109 0 -169 64t-60 165zM721 481l-139 418l-136 -418h275zM797 281h-424l-107 -281h-241l438 1171h242l438 -1171h-242z" />
    <glyph glyph-name="Cacute.sc" horiz-adv-x="1228" 
d="M760 1577h231l-241 -285h-177zM1169 223q-80 -114 -206.5 -178.5t-272.5 -64.5q-251 0 -422 174q-172 175 -172 426q0 252 170 428q173 176 424 176q293 0 475 -238l-182 -131q-122 152 -293 152q-156 0 -260 -113t-104 -274q0 -160 104 -273q104 -110 260 -110
q183 0 297 159z" />
    <glyph glyph-name="Ccircumflex.sc" horiz-adv-x="1228" 
d="M983 1294h-170l-129 146l-129 -146h-170l193 287h213zM987 356l182 -133q-80 -114 -206.5 -178.5t-272.5 -64.5q-251 0 -422 174q-172 175 -172 426q0 252 170 428q173 176 424 176q293 0 475 -238l-182 -131q-122 152 -293 152q-156 0 -260 -113t-104 -274
q0 -160 104 -273q104 -110 260 -110q183 0 297 159z" />
    <glyph glyph-name="Cdotaccent.sc" horiz-adv-x="1228" 
d="M547 1462q0 51 36.5 86t90.5 35t91.5 -35t37.5 -86q0 -54 -36.5 -88.5t-92.5 -34.5q-54 0 -90.5 34.5t-36.5 88.5zM1169 223q-80 -114 -206.5 -178.5t-272.5 -64.5q-251 0 -422 174q-172 175 -172 426q0 252 170 428q173 176 424 176q293 0 475 -238l-182 -131
q-122 152 -293 152q-156 0 -260 -113t-104 -274q0 -160 104 -273q104 -110 260 -110q183 0 297 159z" />
    <glyph glyph-name="Ccaron.sc" horiz-adv-x="1228" 
d="M1169 223q-80 -114 -206.5 -178.5t-272.5 -64.5q-251 0 -422 174q-172 175 -172 426q0 252 170 428q173 176 424 176q293 0 475 -238l-182 -131q-122 152 -293 152q-156 0 -260 -113t-104 -274q0 -160 104 -273q104 -110 260 -110q183 0 297 159zM569 1575l129 -143
l129 143h172l-194 -285h-215l-191 285h170z" />
    <glyph glyph-name="Dcaron.sc" horiz-adv-x="1257" 
d="M496 1575l127 -143l131 143h172l-195 -285h-215l-192 285h172zM168 0v1171h459q249 0 389 -165q141 -164 141 -424q0 -256 -141 -420q-140 -162 -389 -162h-459zM627 209q139 0 220 104.5t81 268.5q0 171 -79 276t-222 105h-232v-754h232z" />
    <glyph glyph-name="Aogonek.sc" horiz-adv-x="1167" 
d="M1143 0h-131l-51 -158q-15 -55 26 -69q31 -13 52.5 15t15.5 60l131 7q1 -96 -50 -155.5t-128 -59.5t-130 52.5t-53 125.5q0 25 17 78l16 32l41 78l-102 275h-424l-107 -281h-241l438 1171h242zM723 481l-139 416l-138 -416h277z" />
    <glyph glyph-name="Emacron.sc" horiz-adv-x="1021" 
d="M168 0v1171h729v-204h-504v-287h443v-205h-443v-270h518v-205h-743zM285 1360v151h463v-151h-463z" />
    <glyph glyph-name="Ebreve.sc" horiz-adv-x="1021" 
d="M274 1542h150q0 -92 82 -92q37 0 57.5 26t20.5 66h151q0 -104 -62.5 -166.5t-166.5 -62.5q-107 0 -169.5 66t-62.5 163zM897 1171v-204h-504v-287h443v-205h-443v-270h518v-205h-743v1171h729z" />
    <glyph glyph-name="Edotaccent.sc" horiz-adv-x="1021" 
d="M401 1462q0 51 37.5 86t91.5 35t90.5 -35t36.5 -86q0 -54 -36.5 -88.5t-90.5 -34.5q-56 0 -92.5 34.5t-36.5 88.5zM897 1171v-204h-504v-287h443v-205h-443v-270h518v-205h-743v1171h729z" />
    <glyph glyph-name="Ecaron.sc" horiz-adv-x="1021" 
d="M897 1171v-204h-504v-287h443v-205h-443v-270h518v-205h-743v1171h729zM381 1575l129 -143l129 143h172l-195 -285h-215l-190 285h170z" />
    <glyph glyph-name="Gcircumflex.sc" horiz-adv-x="1327" 
d="M1120 999l-164 -145h-4q-106 115 -260 115q-153 0 -262 -115q-106 -112 -106 -274q0 -163 106 -275t262 -112q118 0 209 61q93 66 96 164h-317v190h577q0 -308 -149 -468t-416 -160q-248 0 -424 176q-174 174 -174 424q0 253 172 428q176 176 426 176q258 0 428 -185z
M969 1294h-172l-129 148l-127 -148h-172l192 287h213z" />
    <glyph glyph-name="Gbreve.sc" horiz-adv-x="1327" 
d="M956 854h-4q-106 115 -260 115q-153 0 -262 -115q-106 -112 -106 -274q0 -163 106 -275t262 -112q118 0 209 61q93 66 96 164h-317v190h577q0 -308 -149 -468t-416 -160q-248 0 -424 176q-174 174 -174 424q0 253 172 428q176 176 426 176q258 0 428 -185zM438 1542h150
q0 -92 82 -92q37 0 57.5 26t20.5 66h151q0 -104 -62.5 -166.5t-166.5 -62.5q-107 0 -169.5 66t-62.5 163z" />
    <glyph glyph-name="Eogonek.sc" horiz-adv-x="1021" 
d="M731 -94l55 94h-618v1171h729v-204h-504v-287h443v-205h-443v-270h518v-203l-59 -154q-22 -54 25 -69q35 -15 55 18q21 30 12 58l133 6q1 -96 -50 -154.5t-128 -58.5t-129.5 52t-52.5 126q0 55 14 80z" />
    <glyph glyph-name="Dcroat.sc" horiz-adv-x="1269" 
d="M641 0h-457v498h-157v153h157v520h457q248 0 385 -165q141 -164 141 -424q0 -256 -141 -420q-137 -162 -385 -162zM412 958v-307h231v-153h-231v-285h229q139 0 219 102.5t80 266.5q0 168 -79.5 272t-219.5 104h-229z" />
    <glyph glyph-name="Gdotaccent.sc" horiz-adv-x="1327" 
d="M956 854h-4q-106 115 -260 115q-153 0 -262 -115q-106 -112 -106 -274q0 -163 106 -275t262 -112q118 0 209 61q93 66 96 164h-317v190h577q0 -308 -149 -468t-416 -160q-248 0 -424 176q-174 174 -174 424q0 253 172 428q176 176 426 176q258 0 428 -185zM561 1462
q0 51 37.5 86t91.5 35t90.5 -35t36.5 -86q0 -54 -36.5 -88.5t-90.5 -34.5q-56 0 -92.5 34.5t-36.5 88.5z" />
    <glyph glyph-name="Hcircumflex.sc" horiz-adv-x="1243" 
d="M846 1171h229v-1171h-229v479h-453v-479h-225v1171h225v-485h453v485zM920 1294h-170l-129 146l-127 -146h-172l192 287h213z" />
    <glyph glyph-name="Hbar.sc" horiz-adv-x="1265" 
d="M1087 1171v-180h121v-125h-121v-866h-229v479h-452v-479h-228v866h-119v125h119v180h228v-180h452v180h229zM406 866v-180h452v180h-452z" />
    <glyph glyph-name="Itilde.sc" horiz-adv-x="561" 
d="M207 1397q-14 0 -25.5 -19.5t-11.5 -52.5h-131q6 109 53 165t115 56q36 0 80 -35q43 -32 67 -32q14 0 26 17.5t13 49.5h131q-6 -109 -54 -164t-116 -55q-33 0 -80 35t-67 35zM168 0v1171h225v-1171h-225z" />
    <glyph glyph-name="Imacron.sc" horiz-adv-x="561" 
d="M168 0v1171h225v-1171h-225zM49 1360v151h465v-151h-465z" />
    <glyph glyph-name="Iogonek.sc" horiz-adv-x="561" 
d="M109 -100l59 108v1163h225v-1171h-108l-56 -158q-15 -54 25 -69q31 -13 52.5 15t15.5 60l133 7q0 -96 -51 -155.5t-128 -59.5t-130.5 52.5t-53.5 125.5q0 43 17 82z" />
    <glyph glyph-name="IJ.sc" horiz-adv-x="1124" 
d="M168 0v1171h225v-1171h-225zM416 -379l20 209q52 -18 142 -18q75 0 113 47.5t38 128.5v1183h227v-1183q0 -184 -94.5 -289t-283.5 -105q-70 0 -162 27z" />
    <glyph glyph-name="Jcircumflex.sc" horiz-adv-x="563" 
d="M578 1294h-172l-130 146l-126 -146h-173l193 287h213zM-145 -379l20 209q53 -18 141 -18q75 0 113.5 47.5t38.5 128.5v1183h227v-1183q0 -184 -95 -289t-284 -105q-66 0 -161 27z" />
    <glyph glyph-name="Lacute.sc" horiz-adv-x="876" 
d="M350 1571h234l-242 -287h-174zM395 1171v-966h447v-205h-674v1171h227z" />
    <glyph glyph-name="Lcaron.sc" horiz-adv-x="888" 
d="M637 766l-139 41q89 124 104 256q8 39 8 106h191q-6 -136 -51.5 -242.5t-112.5 -160.5zM168 0v1171h227v-966h447v-205h-674z" />
    <glyph glyph-name="Ldot.sc" horiz-adv-x="876" 
d="M168 0v1171h227v-966h447v-205h-674zM518 498q0 45 35 77.5t84 32.5q47 0 82 -33t35 -77q0 -49 -33.5 -82t-83.5 -33t-84.5 33t-34.5 82z" />
    <glyph glyph-name="Nacute.sc" horiz-adv-x="1206" 
d="M668 1577h231l-244 -285h-174zM831 0l-438 745v-745h-225v1171h209l432 -733v733h229v-1171h-207z" />
    <glyph glyph-name="Ncaron.sc" horiz-adv-x="1206" 
d="M465 1575l129 -143l127 143h172l-193 -285h-215l-190 285h170zM831 0l-438 745v-745h-225v1171h209l432 -733v733h229v-1171h-207z" />
    <glyph glyph-name="Omacron.sc" horiz-adv-x="1380" 
d="M457 1360v151h465v-151h-465zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267
q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Obreve.sc" horiz-adv-x="1380" 
d="M459 1542h149q0 -92 80 -92q39 0 59.5 25.5t20.5 66.5h152q0 -104 -63.5 -166.5t-168.5 -62.5q-107 0 -168 64.5t-61 164.5zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176
q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108q154 0 262 108q109 112 109 267q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Ohungarumlaut.sc" horiz-adv-x="1380" 
d="M651 1575h193l-195 -285h-174zM911 1575h195l-213 -285h-178zM96 580q0 253 172 428q173 176 420 176q251 0 424 -176q174 -174 174 -428q0 -250 -174 -424q-176 -176 -424 -176q-247 0 -420 176q-172 175 -172 424zM324 580q0 -157 104 -267q108 -108 260 -108
q154 0 262 108q109 112 109 267q0 156 -109 268q-107 110 -262 110q-153 0 -260 -110q-104 -110 -104 -268z" />
    <glyph glyph-name="Racute.sc" horiz-adv-x="1124" 
d="M578 1577h231l-244 -285h-174zM1024 0h-276l-275 414h-78v-414h-227v1171h471q184 0 281 -112q98 -113 98 -268q0 -138 -77 -239.5t-216 -129.5zM395 967v-351h226q99 -4 140 85.5t1.5 179t-133.5 86.5h-234z" />
    <glyph glyph-name="Rcaron.sc" horiz-adv-x="1124" 
d="M428 1575l127 -143l129 143h172l-192 -285h-215l-195 285h174zM1024 0h-276l-275 414h-78v-414h-227v1171h471q184 0 281 -112q98 -113 98 -268q0 -138 -77 -239.5t-216 -129.5zM395 967v-351h226q99 -4 140 85.5t1.5 179t-133.5 86.5h-234z" />
    <glyph glyph-name="Sacute.sc" horiz-adv-x="1030" 
d="M575 1577h232l-242 -285h-176zM330 348q2 -69 55 -115.5t137 -46.5q81 0 131.5 36.5t55.5 96.5q9 111 -156 152l-143 41q-297 79 -297 328q0 152 114 247q120 97 283 97q174 0 282.5 -98.5t108.5 -262.5h-225q0 68 -46.5 110t-121.5 42q-70 0 -119 -37.5t-49 -93.5
q0 -89 135 -127l141 -41q167 -46 247.5 -142.5t74.5 -220.5q-9 -155 -129 -243q-122 -90 -287 -90q-187 0 -305 106q-117 108 -117 262h230z" />
    <glyph glyph-name="Scircumflex.sc" horiz-adv-x="1030" 
d="M813 1294h-170l-129 146l-129 -146h-172l193 287h215zM100 348h230q2 -69 55 -115.5t137 -46.5q81 0 131.5 36.5t55.5 96.5q9 111 -156 152l-143 41q-297 79 -297 328q0 152 114 247q120 97 283 97q174 0 282.5 -98.5t108.5 -262.5h-225q0 68 -46.5 110t-121.5 42
q-70 0 -119 -37.5t-49 -93.5q0 -89 135 -127l141 -41q167 -46 247.5 -142.5t74.5 -220.5q-9 -155 -129 -243q-122 -90 -287 -90q-187 0 -305 106q-117 108 -117 262z" />
    <glyph glyph-name="Scedilla.sc" horiz-adv-x="1028" 
d="M610 -10q4 -8 24 -47t21 -41q21 -47 21 -84q0 -72 -54.5 -125t-129.5 -53q-77 0 -128.5 59.5t-50.5 155.5l133 -7q-8 -31 14.5 -59.5t53.5 -15.5q43 15 27 69l-56 142q-167 9 -276 112q-107 104 -107 254h228q2 -68 55 -116t137 -48q83 0 133 36t56 97q9 105 -156 152
l-143 39q-297 79 -297 330q0 152 114 247q117 97 281 97q170 0 281 -99q110 -98 110 -262h-225q0 68 -45.5 109t-122.5 41q-70 0 -119 -36.5t-49 -92.5q0 -92 135 -127l141 -43q158 -47 236.5 -135t83.5 -197q3 -143 -92 -238q-90 -92 -234 -114z" />
    <glyph glyph-name="Utilde.sc" horiz-adv-x="1204" 
d="M602 197q90 0 154.5 61.5t64.5 153.5v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -91 63.5 -153t151.5 -62zM528 1397q-13 0 -24.5 -19.5t-11.5 -52.5h-132q6 109 53 165t115 56q34 0 78 -35q43 -32 70 -32
q14 0 26 17.5t13 49.5h129q-4 -109 -52 -164t-116 -55q-35 0 -82 35t-66 35z" />
    <glyph glyph-name="Umacron.sc" horiz-adv-x="1204" 
d="M369 1360v151h467v-151h-467zM821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Ubreve.sc" horiz-adv-x="1204" 
d="M373 1542h147q0 -92 80 -92q37 0 58.5 26t21.5 66h154q0 -104 -64.5 -166.5t-169.5 -62.5q-107 0 -167 64.5t-60 164.5zM821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5
q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Uring.sc" horiz-adv-x="1204" 
d="M401 1483q0 86 57 144.5t142 58.5q86 0 144.5 -58.5t58.5 -144.5q0 -85 -58.5 -142t-144.5 -57q-85 0 -142 57t-57 142zM600 1399q37 0 62.5 24.5t25.5 59.5q0 36 -26 62t-62 26q-33 0 -58.5 -26.5t-25.5 -61.5t24.5 -59.5t59.5 -24.5zM821 412v759h228v-759
q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5z" />
    <glyph glyph-name="Uhungarumlaut.sc" horiz-adv-x="1204" 
d="M821 412v759h228v-759q0 -193 -131 -312q-132 -120 -316 -120q-183 0 -315 120q-131 119 -131 312v759h231v-759q0 -92 63 -153.5t152 -61.5q90 0 154.5 61.5t64.5 153.5zM532 1575h193l-195 -285h-174zM793 1575h194l-213 -285h-178z" />
    <glyph glyph-name="Uogonek.sc" horiz-adv-x="1208" 
d="M471 -182q0 45 31 112l30 60q-158 21 -268 137q-106 115 -106 287v757h231v-757q0 -91 63 -152t152 -61t154 61t65 152v757h228v-757q0 -177 -113 -293q-114 -117 -279 -135q-36 -92 -53 -144q-15 -54 25 -69q33 -13 54.5 15t14.5 60l134 7q1 -97 -51 -156t-128 -59
q-77 0 -130.5 52.5t-53.5 125.5z" />
    <glyph glyph-name="Wcircumflex.sc" horiz-adv-x="1576" 
d="M1090 1294h-172l-130 146l-129 -146h-170l193 287h213zM1051 397l245 774h246l-399 -1171h-182l-173 631l-172 -631h-182l-397 1171h246l243 -776l172 617h183z" />
    <glyph glyph-name="Ycircumflex.sc" horiz-adv-x="1171" 
d="M885 1294h-172l-127 146l-129 -146h-170l192 287h213zM700 532v-532h-231v532l-451 639h271l295 -440l297 440h276z" />
    <glyph glyph-name="Tcaron.sc" horiz-adv-x="976" 
d="M371 1575l129 -143l129 143h172l-195 -285h-215l-190 285h170zM930 1171v-206h-326v-965h-231v965h-326v206h883z" />
    <glyph glyph-name="Tbar.sc" horiz-adv-x="976" 
d="M373 0v504h-195v137h195v324h-326v206h883v-206h-326v-324h201v-137h-201v-504h-231z" />
    <glyph glyph-name="Zacute.sc" horiz-adv-x="1052" 
d="M584 1577h233l-246 -285h-174zM96 0v178l555 789h-530v204h825v-176l-557 -788h557v-207h-850z" />
    <glyph glyph-name="Zdotaccent.sc" horiz-adv-x="1052" 
d="M414 1462q0 51 36.5 86t90.5 35q55 0 92 -35t37 -86q0 -54 -36.5 -88.5t-92.5 -34.5q-54 0 -90.5 34.5t-36.5 88.5zM96 0v178l555 789h-530v204h825v-176l-557 -788h557v-207h-850z" />
    <glyph glyph-name="Wgrave.sc" horiz-adv-x="1576" 
d="M899 1292h-176l-242 285h232zM1051 397l245 774h246l-399 -1171h-182l-173 631l-172 -631h-182l-397 1171h246l243 -776l172 617h183z" />
    <glyph glyph-name="Wacute.sc" horiz-adv-x="1576" 
d="M864 1577h232l-244 -285h-174zM1542 1171l-399 -1171h-182l-173 631l-172 -631h-182l-397 1171h246l243 -776l172 617h183l170 -615l245 774h246z" />
    <glyph glyph-name="Wdieresis.sc" horiz-adv-x="1576" 
d="M643 1565q50 0 85.5 -33t35.5 -82t-35.5 -83t-85.5 -34q-51 0 -87 34t-36 83t36 82t87 33zM813 1450q0 50 35.5 82.5t89.5 32.5q50 0 85.5 -33t35.5 -82t-35.5 -83t-85.5 -34q-54 0 -89.5 33t-35.5 84zM1542 1171l-399 -1171h-182l-173 631l-172 -631h-182l-397 1171h246
l243 -776l172 617h183l170 -615l245 774h246z" />
    <glyph glyph-name="Aringacute.sc" horiz-adv-x="1167" 
d="M651 1872h230l-193 -230q92 -59 92 -169q0 -85 -59.5 -143t-147.5 -58q-80 0 -138 58.5t-58 142.5q0 66 39.5 120t101.5 74zM494 1473q0 -35 24.5 -61t56.5 -26q38 0 64.5 25.5t26.5 61.5q0 37 -26.5 62.5t-64.5 25.5q-35 0 -58 -25.5t-23 -62.5zM721 481l-139 418
l-136 -418h275zM797 281h-424l-107 -281h-241l438 1171h242l438 -1171h-242z" />
    <glyph glyph-name="AEacute.sc" horiz-adv-x="1642" 
d="M975 1577h231l-243 -285h-175zM276 0h-272l741 1171h773v-206h-502v-285h438v-207h-438v-264h518v-209h-746v274h-335zM788 496v354l-204 -354h204z" />
    <glyph glyph-name="Oslashacute.sc" horiz-adv-x="1378" 
d="M780 1577h234l-246 -285h-172zM184 -33l-86 88l133 140q-137 168 -137 385q0 253 172 428q173 176 420 176q214 0 377 -135l135 137l86 -86l-135 -137q135 -173 135 -383q0 -250 -174 -424q-176 -176 -424 -176q-198 0 -367 126zM322 580q0 -132 69 -224l514 531
q-100 71 -219 71q-153 0 -260 -110q-104 -110 -104 -268zM686 205q154 0 262 108q109 112 109 267q0 119 -68 219l-512 -529q89 -65 209 -65z" />
    <glyph glyph-name="Ibreve.sc" horiz-adv-x="561" 
d="M393 1171v-1171h-225v1171h225zM51 1542h150q0 -92 82 -92q37 0 57 26t20 66h152q0 -104 -62.5 -166.5t-166.5 -62.5q-107 0 -169.5 66t-62.5 163z" />
    <glyph glyph-name="Eng.sc" horiz-adv-x="1206" 
d="M809 8l-414 733v-741h-227v1171h211l428 -755v755h231v-1148q0 -193 -92.5 -303.5t-283.5 -110.5q-60 0 -164 26l22 222l101 -19q59 -3 73 4q54 10 83.5 56t31.5 110z" />
    <glyph glyph-name="Ygrave.sc" horiz-adv-x="1171" 
d="M709 1292h-177l-241 285h231zM700 532v-532h-231v532l-451 639h271l295 -440l297 440h276z" />
    <glyph glyph-name="Gcommaaccent.sc" horiz-adv-x="1327" 
d="M1120 999l-164 -145h-4q-106 115 -260 115q-153 0 -262 -115q-106 -112 -106 -274q0 -163 106 -275t262 -112q118 0 209 61q93 66 96 164h-317v190h577q0 -308 -149 -468t-416 -160q-248 0 -424 176q-174 174 -174 424q0 253 172 428q176 176 426 176q258 0 428 -185z
M631 -508l-115 49q89 121 74 377h198q3 -135 -47 -256q-48 -117 -110 -170z" />
    <glyph glyph-name="Kcommaaccent.sc" horiz-adv-x="1069" 
d="M969 1171l-447 -581l551 -590h-297l-379 420v-420h-229v1171h229v-407l303 407h269zM545 -508l-115 49q89 121 74 377h198q3 -135 -47 -256q-48 -117 -110 -170z" />
    <glyph glyph-name="Lcommaaccent.sc" horiz-adv-x="876" 
d="M168 0v1171h227v-966h447v-205h-674zM438 -508l-114 49q88 120 73 377h199q3 -135 -47 -256q-48 -116 -111 -170z" />
    <glyph glyph-name="Ncommaaccent.sc" horiz-adv-x="1206" 
d="M1038 1171v-1171h-207l-438 737v-737h-225v1171h209l432 -727v727h229zM545 -508l-115 49q89 121 74 377h198q3 -135 -47 -256q-48 -117 -110 -170z" />
    <glyph glyph-name="Rcommaaccent.sc" horiz-adv-x="1124" 
d="M725 422l299 -422h-276l-275 414h-78v-414h-227v1171h471q184 0 281 -112q98 -113 98 -268q0 -138 -77 -239.5t-216 -129.5zM629 967h-234v-351h226q99 -4 140 85.5t1.5 179t-133.5 86.5zM545 -508l-115 49q89 121 74 377h198q3 -135 -47 -256q-48 -117 -110 -170z" />
    <glyph glyph-name="Tcommaaccent.sc" horiz-adv-x="976" 
d="M47 965v206h883v-206h-326v-965h-231v965h-326zM428 -508l-115 49q89 121 74 377h199q3 -135 -47 -256q-48 -116 -111 -170z" />
    <glyph glyph-name="Scommaaccent.sc" horiz-adv-x="1030" 
d="M100 348h230q2 -69 55 -115.5t137 -46.5q80 0 131 37.5t56 95.5q9 105 -156 152l-143 41q-297 79 -297 328q0 152 114 247q120 97 283 97q174 0 282.5 -98.5t108.5 -262.5h-225q0 68 -46.5 110t-121.5 42q-70 0 -119 -37.5t-49 -93.5q0 -89 135 -127l141 -41
q170 -49 248.5 -143.5t73.5 -219.5q-9 -155 -129 -243q-122 -90 -287 -90q-187 0 -305 106q-117 108 -117 262zM473 -508l-115 49q89 121 74 377h199q3 -135 -47 -256q-48 -116 -111 -170z" />
    <glyph glyph-name="Idotaccent.sc" horiz-adv-x="561" 
d="M393 1171v-1171h-225v1171h225zM154 1462q0 51 37.5 86t91.5 35t90.5 -35t36.5 -86q0 -54 -36.5 -88.5t-90.5 -34.5q-56 0 -92.5 34.5t-36.5 88.5z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="729" 
d="M74 944q0 410 291 410q290 0 290 -410q0 -403 -290 -403q-291 0 -291 403zM223 944q0 -258 142 -258q141 0 141 258q0 262 -141 262q-142 0 -142 -262z" />
    <glyph glyph-name="one.numr" horiz-adv-x="436" 
d="M330 553h-150v582l-135 -62v135l285 142v-797z" />
    <glyph glyph-name="two.numr" horiz-adv-x="651" 
d="M66 553v76l321 385q45 51 45 102q0 42 -30.5 68t-79.5 26q-48 0 -82 -32t-41 -86l-138 28q19 108 90.5 171t176.5 63q106 0 180 -68t74 -170q0 -107 -84 -203l-191 -217h262v-143h-503z" />
    <glyph glyph-name="three.numr" horiz-adv-x="649" 
d="M199 1161l-113 78q36 56 100 87.5t138 27.5q99 -6 163 -69t64 -150q0 -101 -98 -156q61 -23 99 -85t32 -128q-9 -100 -99 -166t-202 -57q-79 6 -146.5 51.5t-99.5 114.5l127 65q46 -88 129 -88q51 -1 89.5 27.5t43.5 70.5q2 49 -28.5 85.5t-94.5 35.5h-86v129h86
q47 0 71 34t19 69q-2 32 -27.5 56t-62.5 24q-66 3 -104 -56z" />
    <glyph glyph-name="four.numr" horiz-adv-x="708" 
d="M659 723h-104v-170h-149v170h-390l396 616h143v-487h104v-129zM406 1118l-168 -266h168v266z" />
    <glyph glyph-name="five.numr" horiz-adv-x="694" 
d="M51 702l125 82q23 -43 65.5 -69.5t86.5 -26.5q71 0 116 38.5t45 94.5q0 61 -41 96t-106 35q-109 0 -190 -78l-72 48l65 417h441v-139h-320l-26 -151q52 34 137 34q115 0 187.5 -70t72.5 -192q0 -125 -84.5 -202.5t-224.5 -77.5q-84 0 -159 43.5t-118 117.5z" />
    <glyph glyph-name="six.numr" horiz-adv-x="692" 
d="M633 1196l-137 -57q-34 73 -105 73q-77 0 -117.5 -67.5t-35.5 -173.5q18 37 61 63.5t96 26.5q98 0 172 -74q75 -75 72 -194q-1 -112 -80.5 -184t-198.5 -70q-111 2 -186.5 76t-85.5 196q-2 50 -3.5 102.5t6 106t19 103t36.5 92t57.5 73.5t81.5 48.5t110 15.5
q174 -9 238 -156zM252 805q0 -51 33.5 -85t83.5 -36q48 0 82.5 34t35.5 83q0 49 -33.5 84t-80.5 37q-51 0 -86 -34t-35 -83z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="643" 
d="M53 1339h580l-381 -786h-170l311 647h-340v139z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="706" 
d="M121 1139q0 90 68 152.5t165 62.5t164.5 -62.5t67.5 -152.5q0 -81 -58 -133q115 -71 115 -199q0 -111 -84 -189q-83 -77 -205 -77q-121 0 -207 77q-84 78 -84 189q0 128 115 199q-57 57 -57 133zM213 809q-2 -40 24.5 -71.5t64 -45.5t80.5 -8t71 35q32 27 38 66t-9 73
t-48 57.5t-74 22.5h-18q-53 0 -91 -38.5t-38 -90.5zM268 1135q0 -36 24 -59t62 -23q54 0 75.5 41t-1 81.5t-74.5 38.5q-38 0 -62 -23t-24 -56z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="692" 
d="M74 696l139 58q35 -74 104 -74q75 0 115.5 68.5t38.5 173.5q-20 -38 -63.5 -64.5t-96.5 -26.5q-98 0 -172 74t-71 195q5 113 82.5 183t195.5 69q113 -2 189 -76t86 -197q13 -199 -39 -354q-30 -89 -99.5 -138.5t-169.5 -45.5q-169 9 -239 155zM457 1087q0 50 -34 85
t-83 36t-84 -32.5t-35 -83.5q-1 -51 32 -85t83 -36q50 0 85 33.5t36 82.5z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="729" 
d="M74 393q0 408 291 408q290 0 290 -408q0 -405 -290 -405q-291 0 -291 405zM365 653q-142 0 -142 -260t142 -260q141 0 141 260t-141 260z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="436" 
d="M332 0h-150v582l-135 -62v135l285 142v-797z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="651" 
d="M66 0v78l321 383q45 54 45 102q0 41 -30.5 67.5t-79.5 26.5q-48 0 -82 -31.5t-41 -84.5l-138 26q19 108 90.5 171t176.5 63q106 0 180 -68t74 -170q0 -105 -84 -201l-191 -219h262v-143h-503z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="653" 
d="M203 608l-113 78q36 56 100 87.5t138 27.5q99 -6 163 -69t64 -150q0 -101 -98 -156q61 -23 99 -85t32 -128q-9 -100 -99 -166t-202 -57q-79 6 -146.5 51.5t-99.5 114.5l127 65q46 -88 129 -88q51 -1 89.5 27.5t43.5 70.5q2 49 -28.5 85.5t-94.5 35.5h-86v129h86
q47 0 71 34t19 69q-2 32 -27.5 56t-62.5 24q-66 3 -104 -56z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="710" 
d="M662 170h-105v-170h-149v170h-390l396 618h143v-487h105v-131zM408 567l-168 -266h168v266z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="694" 
d="M49 150l125 81q23 -43 65.5 -69.5t86.5 -26.5q71 0 116 38.5t45 96.5q0 61 -41.5 96t-105.5 35q-111 0 -190 -79l-72 47l65 417h441v-139h-320l-26 -151q52 34 137 34q115 0 187.5 -69.5t72.5 -190.5q0 -125 -85 -203.5t-224 -78.5q-84 0 -159 44t-118 118z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="692" 
d="M633 645l-137 -59q-34 73 -105 73q-77 0 -117.5 -67.5t-35.5 -173.5q14 37 58.5 63.5t98.5 26.5q98 0 172 -74q75 -75 72 -194q-1 -110 -80.5 -182t-198.5 -70q-112 2 -187 75.5t-85 196.5q-7 56 -3.5 118.5t10.5 123t30.5 115.5t56 96t87.5 64.5t126 21.5
q170 -9 238 -154zM369 133q47 -2 82 32t36 83q0 51 -33.5 86t-80.5 35q-51 0 -86 -34t-35 -83q0 -50 34 -84.5t83 -34.5z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="649" 
d="M55 788h580l-381 -788h-170l311 647h-340v141z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="706" 
d="M121 586q0 90 68 152.5t165 62.5t164.5 -62.5t67.5 -152.5q0 -81 -58 -133q115 -71 115 -199q0 -110 -84 -188t-205 -78q-120 0 -207 78q-84 78 -84 188q0 128 115 199q-57 57 -57 133zM213 258q0 -54 40.5 -91.5t100.5 -37.5q57 0 98.5 37.5t41.5 91.5q0 50 -39.5 88.5
t-94.5 38.5h-18q-54 0 -91.5 -37.5t-37.5 -89.5zM268 584q0 -36 24 -60t62 -24q38 1 62 25t24 59q0 31 -25 54.5t-61 23.5q-38 0 -62 -22.5t-24 -55.5z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="694" 
d="M76 143l139 60q34 -76 104 -76q75 0 115.5 68.5t38.5 175.5q-18 -38 -62.5 -65t-97.5 -27q-99 0 -172 73q-74 77 -71 195q5 113 82.5 183t195.5 69q113 -2 189 -76t86 -197q13 -199 -39 -354q-30 -89 -99.5 -138.5t-169.5 -45.5q-169 9 -239 155zM459 535
q0 51 -33.5 85.5t-83.5 34.5q-49 1 -84 -32.5t-35 -83.5q-2 -50 32 -84.5t83 -34.5q49 -1 84 32t37 83z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="624" 
d="M524 229h97v-229h-134q-356 0 -356 414v1063h252v-1067q-3 -181 141 -181z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="1042" 
d="M332 -74l57 137l-369 893h265l237 -587l238 587h266l-469 -1124q-77 -170 -160 -238q-81 -69 -231 -69q-62 0 -141 20v219q75 -10 139 -10q59 0 93.5 37.5t74.5 134.5z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="667" 
d="M563 229h96v-229h-133q-356 0 -356 414v186l-154 -96v172l154 96v705h252v-547l178 112v-172l-178 -112v-348q-3 -181 141 -181z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="624" 
d="M143 1569l215 334h263l-283 -334h-195zM621 0h-134q-356 0 -356 414v1063h252v-1067q-3 -181 141 -181h97v-229z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="624" 
d="M524 229h97v-229h-134q-356 0 -356 414v1063h252v-1067q-3 -181 141 -181zM350 -561l-131 59q67 95 90 174q24 82 21 213h219q-15 -299 -199 -446z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="624" 
d="M606 991l-153 51q142 198 133 435h219q-7 -164 -63 -292t-136 -194zM621 0h-134q-356 0 -356 414v1063h252v-1067q-3 -181 141 -181h97v-229z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM457 1114l215 334h262l-283 -334h-194z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM479 1114l-282 334h262l215 -334h-195z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM197 1120l223 334h252l225 -334h-192l-160 180l-156 -180h-192z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM371 1427q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -101 38t-41 95t41 95t101 38zM627 1198q-41 38 -41 96t41 95q41 38 100.5 38t97.5 -38
q41 -37 41 -95t-41 -96q-38 -37 -97.5 -37t-100.5 37z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM473 1243q-19 0 -35.5 -24t-17.5 -64h-144q6 126 62 191t135 65q50 0 105 -43q56 -41 75 -41q20 0 37.5 22.5t18.5 61.5h143q-6 -123 -63.5 -187.5
t-135.5 -64.5q-45 0 -104 41q-58 43 -76 43z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM311 1339q0 99 68 170q70 70 166 70q103 0 171 -68.5t68 -171.5q0 -98 -69.5 -165.5t-169.5 -67.5q-95 0 -166 68q-68 68 -68 165zM428 1339
q0 -49 34.5 -82.5t82.5 -33.5q51 0 86 33.5t35 82.5q0 51 -35 86t-86 35q-48 0 -82.5 -35t-34.5 -86z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM825 1198h-551v170h551v-170z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="1105" 
d="M463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5
q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM285 1407h168q0 -113 104 -113q49 0 77 32t28 81h169q0 -121 -75 -193.5t-199 -72.5q-129 0 -200.5 75.5t-71.5 190.5z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="1105" 
d="M268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-115l-71 -197q-22 -74 34 -94q29 -8 53.5 3.5t34.5 37.5l11 51l141 9q1 -110 -59.5 -178t-149.5 -68q-90 0 -152.5 61.5t-62.5 147.5q0 59 18 94l72 133v98q-47 -57 -128 -87.5t-159 -30.5
q-171 0 -270.5 83.5t-99.5 229.5q0 142 102 221.5t311 79.5q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5zM463 199q59 0 145 28q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="1042" 
d="M360 1114l215 334h263l-283 -334h-195zM389 63l-369 893h265l237 -587l238 587h266l-469 -1124q-77 -170 -160 -238q-81 -69 -231 -69q-62 0 -141 20v219q75 -10 139 -10q59 0 93.5 37.5t74.5 134.5z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="1042" 
d="M338 1427q57 0 98 -39t41 -94t-41 -94t-98 -39q-60 0 -100.5 38t-40.5 95t40.5 95t100.5 38zM594 1198q-41 38 -41 96t41 95q41 38 101 38t98 -38q41 -37 41 -95t-41 -96q-38 -37 -98 -37t-101 37zM389 63l-369 893h265l237 -587l238 587h266l-469 -1124
q-77 -170 -160 -238q-81 -69 -231 -69q-62 0 -141 20v219q75 -10 139 -10q59 0 93.5 37.5t74.5 134.5z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="1783" 
d="M977 43v-43h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5q0 142 102 221.5t311 79.5q49 0 127 -16q74 -15 117 -43v32q0 84 -56 137.5t-151 53.5q-70 0 -140.5 -31t-115.5 -90l-147 143q138 197 418 197q230 0 344 -129q141 129 344 129
q209 0 346 -137q139 -139 139 -359q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-139 0 -250 63zM977 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55t-82 -148zM451 199q62 0 151 28q84 29 127 84v37
q-43 28 -117 43q-83 17 -120 17q-90 0 -130 -33t-40 -82q0 -40 34 -67t95 -27z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="1783" 
d="M803 1114l215 334h262l-283 -334h-194zM977 0h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5q0 142 102 221.5t311 79.5q49 0 127 -16q74 -15 117 -43v32q0 84 -56 137.5t-151 53.5q-70 0 -140.5 -31t-115.5 -90l-147 143q138 197 418 197
q230 0 344 -129q141 129 344 129q209 0 346 -137q139 -139 139 -359q0 -24 -4 -86h-729q12 -93 85 -149t179 -56q77 0 143 32.5t97 90.5l190 -102q-60 -113 -179 -172t-267 -59q-139 0 -250 63v-43zM977 565h491q-13 95 -78.5 149t-160.5 54q-101 0 -170 -55t-82 -148z
M451 199q62 0 151 28q84 29 127 84v37q-43 28 -117 43q-83 17 -120 17q-90 0 -130 -33t-40 -82q0 -40 34 -67t95 -27z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="1042" 
d="M332 -74l57 137l-369 893h265l237 -587l238 587h266l-469 -1124q-77 -170 -160 -238q-81 -69 -231 -69q-62 0 -141 20v219q75 -10 139 -10q59 0 93.5 37.5t74.5 134.5zM174 1120l223 334h252l225 -334h-192l-160 180l-155 -180h-193z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="827" 
d="M524 229h97v-229h-134q-356 0 -356 414v1063h252v-1067q-3 -181 141 -181zM659 797q68 0 116 -44.5t48 -111.5t-48 -111.5t-116 -44.5q-66 0 -112.5 45t-46.5 111t46.5 111t112.5 45z" />
    <glyph glyph-name="t.alt1" horiz-adv-x="784" 
d="M616 229h93v-229h-129q-189 0 -273 93.5t-84 291.5v371h-174v200h174v199l252 80v-279h199v-200h-199v-375q-2 -77 29 -114.5t112 -37.5z" />
    <glyph glyph-name="tcaron.alt1" horiz-adv-x="784" 
d="M688 1073l-133 60q67 95 90 174q24 82 21 213h219q-15 -297 -197 -447zM709 0h-129q-189 0 -273 93.5t-84 291.5v371h-174v200h174v199l252 80v-279h199v-200h-199v-375q-2 -77 29 -114.5t112 -37.5h93v-229z" />
    <glyph glyph-name="tbar.alt1" horiz-adv-x="788" 
d="M621 229h92v-229h-129q-189 0 -273 93.5t-84 291.5v21h-121v145h121v205h-174v200h174v199l252 80v-279h199v-200h-199v-205h144v-145h-144v-25q-2 -77 29.5 -114.5t112.5 -37.5z" />
    <glyph glyph-name="tcommaaccent.alt1" horiz-adv-x="784" 
d="M616 229h93v-229h-129q-189 0 -273 93.5t-84 291.5v371h-174v200h174v199l252 80v-279h199v-200h-199v-375q-2 -77 29 -114.5t112 -37.5zM420 -561l-131 59q67 95 90 174t20 213h219q-15 -300 -198 -446z" />
    <glyph glyph-name="T.sc.alt1" horiz-adv-x="976" 
d="M47 965v206h883v-206h-326v-965h-231v965h-326z" />
    <glyph glyph-name="Tcaron.sc.alt1" horiz-adv-x="976" 
d="M371 1575l129 -143l129 143h172l-195 -285h-215l-190 285h170zM930 1171v-206h-326v-965h-231v965h-326v206h883z" />
    <glyph glyph-name="Tbar.sc.alt1" horiz-adv-x="976" 
d="M373 0v504h-195v137h195v324h-326v206h883v-206h-326v-324h201v-137h-201v-504h-231z" />
    <glyph glyph-name="Tcommaaccent.sc.alt1" horiz-adv-x="976" 
d="M47 965v206h883v-206h-326v-965h-231v965h-326zM428 -508l-115 49q89 121 74 377h199q3 -135 -47 -256q-48 -116 -111 -170z" />
    <glyph glyph-name="aringacute.alt1" horiz-adv-x="1105" 
d="M485 1556l162 250h262l-231 -272q117 -70 117 -207q0 -98 -70 -165.5t-170 -67.5q-96 0 -166 67q-67 67 -67 166q0 80 45 142t118 87zM438 1327q0 -49 34.5 -83t82.5 -34q51 0 86 34t35 83q0 51 -35 86t-86 35q-48 0 -82.5 -35t-34.5 -86zM463 199q59 0 145 28
q78 28 121 84v45q-43 28 -117 43q-83 17 -120 17q-170 0 -170 -107q0 -46 38.5 -78t102.5 -32zM268 639l-147 141q138 197 418 197q208 0 323 -106.5t115 -303.5v-567h-246v98q-47 -57 -128 -87.5t-159 -30.5q-171 0 -270.5 83.5t-99.5 229.5q0 142 102 221.5t311 79.5
q47 0 125 -19q68 -13 119 -45v37q0 84 -56 137.5t-151 53.5q-71 0 -141 -30.5t-115 -88.5z" />
    <hkern u1="&#x20;" g2="lslash.alt1" k="39" />
    <hkern u1="&#x20;" g2="V.sc" k="51" />
    <hkern u1="&#x20;" u2="&#x2019;" k="27" />
    <hkern u1="&#x20;" u2="&#x142;" k="49" />
    <hkern u1="&#x20;" u2="&#x110;" k="23" />
    <hkern u1="&#x20;" u2="&#xd0;" k="23" />
    <hkern u1="&#x20;" u2="v" k="51" />
    <hkern u1="&#x20;" u2="f" k="25" />
    <hkern u1="&#x20;" u2="V" k="63" />
    <hkern u1="&#x23;" g2="five.oldstyle" k="27" />
    <hkern u1="&#x23;" g2="four.oldstyle" k="115" />
    <hkern u1="&#x23;" g2="three.oldstyle" k="29" />
    <hkern u1="&#x23;" u2="&#x34;" k="33" />
    <hkern u1="&#x24;" g2="four.oldstyle" k="45" />
    <hkern u1="&#x24;" g2="three.oldstyle" k="31" />
    <hkern u1="&#x26;" u2="V" k="37" />
    <hkern u1="&#x28;" g2="Jcircumflex.sc" k="-143" />
    <hkern u1="&#x28;" g2="Hbar.sc" k="41" />
    <hkern u1="&#x28;" g2="Icircumflex.sc" k="-14" />
    <hkern u1="&#x28;" g2="M.sc" k="55" />
    <hkern u1="&#x28;" g2="J.sc" k="-143" />
    <hkern u1="&#x28;" g2="Lslash.sc" k="41" />
    <hkern u1="&#x28;" g2="nine.oldstyle" k="20" />
    <hkern u1="&#x28;" g2="eight.oldstyle" k="51" />
    <hkern u1="&#x28;" g2="six.oldstyle" k="49" />
    <hkern u1="&#x28;" g2="four.oldstyle" k="72" />
    <hkern u1="&#x28;" g2="two.oldstyle" k="84" />
    <hkern u1="&#x28;" g2="one.oldstyle" k="63" />
    <hkern u1="&#x28;" g2="zero.oldstyle" k="86" />
    <hkern u1="&#x28;" u2="&#x135;" k="-115" />
    <hkern u1="&#x28;" u2="&#x134;" k="-205" />
    <hkern u1="&#x28;" u2="&#xef;" k="-47" />
    <hkern u1="&#x28;" u2="&#xee;" k="-27" />
    <hkern u1="&#x28;" u2="&#xec;" k="-70" />
    <hkern u1="&#x28;" u2="&#x7b;" k="31" />
    <hkern u1="&#x28;" u2="x" k="47" />
    <hkern u1="&#x28;" u2="v" k="37" />
    <hkern u1="&#x28;" u2="j" k="-115" />
    <hkern u1="&#x28;" u2="f" k="27" />
    <hkern u1="&#x28;" u2="M" k="35" />
    <hkern u1="&#x28;" u2="J" k="-205" />
    <hkern u1="&#x28;" u2="&#x38;" k="51" />
    <hkern u1="&#x28;" u2="&#x36;" k="49" />
    <hkern u1="&#x28;" u2="&#x35;" k="25" />
    <hkern u1="&#x28;" u2="&#x34;" k="123" />
    <hkern u1="&#x28;" u2="&#x30;" k="43" />
    <hkern u1="&#x28;" u2="&#x28;" k="49" />
    <hkern u1="&#x29;" u2="&#x7d;" k="47" />
    <hkern u1="&#x29;" u2="]" k="31" />
    <hkern u1="&#x29;" u2="&#x29;" k="49" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-82" />
    <hkern u1="&#x2a;" u2="&#x12d;" k="-14" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-29" />
    <hkern u1="&#x2a;" u2="&#x127;" k="-18" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-53" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-98" />
    <hkern u1="&#x2b;" g2="four.oldstyle" k="98" />
    <hkern u1="&#x2b;" g2="three.oldstyle" k="53" />
    <hkern u1="&#x2b;" u2="&#x37;" k="31" />
    <hkern u1="&#x2d;" g2="four.oldstyle" k="72" />
    <hkern u1="&#x2d;" g2="three.oldstyle" k="61" />
    <hkern u1="&#x2d;" u2="&#x37;" k="49" />
    <hkern u1="&#x2d;" u2="&#x32;" k="20" />
    <hkern u1="&#x2d;" u2="&#x31;" k="27" />
    <hkern u1="&#x2f;" g2="M.sc" k="45" />
    <hkern u1="&#x2f;" g2="nine.oldstyle" k="94" />
    <hkern u1="&#x2f;" g2="eight.oldstyle" k="35" />
    <hkern u1="&#x2f;" g2="seven.oldstyle" k="25" />
    <hkern u1="&#x2f;" g2="six.oldstyle" k="27" />
    <hkern u1="&#x2f;" g2="five.oldstyle" k="94" />
    <hkern u1="&#x2f;" g2="four.oldstyle" k="197" />
    <hkern u1="&#x2f;" g2="three.oldstyle" k="96" />
    <hkern u1="&#x2f;" g2="two.oldstyle" k="63" />
    <hkern u1="&#x2f;" g2="one.oldstyle" k="59" />
    <hkern u1="&#x2f;" g2="zero.oldstyle" k="84" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-27" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-68" />
    <hkern u1="&#x2f;" u2="x" k="35" />
    <hkern u1="&#x2f;" u2="&#x38;" k="35" />
    <hkern u1="&#x2f;" u2="&#x36;" k="27" />
    <hkern u1="&#x2f;" u2="&#x34;" k="123" />
    <hkern u1="&#x2f;" u2="&#x30;" k="20" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="389" />
    <hkern u1="&#x30;" u2="&#x2044;" k="-111" />
    <hkern u1="&#x30;" u2="&#xc6;" k="27" />
    <hkern u1="&#x30;" u2="&#x7d;" k="37" />
    <hkern u1="&#x30;" u2="]" k="29" />
    <hkern u1="&#x30;" u2="\" k="23" />
    <hkern u1="&#x30;" u2="Y" k="49" />
    <hkern u1="&#x30;" u2="V" k="23" />
    <hkern u1="&#x30;" u2="&#x29;" k="41" />
    <hkern u1="&#x31;" u2="&#x2044;" k="-244" />
    <hkern u1="&#x32;" u2="&#x2044;" k="-287" />
    <hkern u1="&#x32;" u2="Y" k="33" />
    <hkern u1="&#x33;" u2="&#x2044;" k="-139" />
    <hkern u1="&#x33;" u2="&#x7d;" k="31" />
    <hkern u1="&#x33;" u2="]" k="25" />
    <hkern u1="&#x33;" u2="\" k="23" />
    <hkern u1="&#x33;" u2="Y" k="47" />
    <hkern u1="&#x33;" u2="V" k="25" />
    <hkern u1="&#x33;" u2="&#x29;" k="35" />
    <hkern u1="&#x34;" u2="&#x2044;" k="-158" />
    <hkern u1="&#x34;" u2="&#xb0;" k="20" />
    <hkern u1="&#x34;" u2="&#x7d;" k="43" />
    <hkern u1="&#x34;" u2="]" k="29" />
    <hkern u1="&#x34;" u2="\" k="27" />
    <hkern u1="&#x34;" u2="Y" k="45" />
    <hkern u1="&#x34;" u2="W" k="20" />
    <hkern u1="&#x34;" u2="V" k="31" />
    <hkern u1="&#x34;" u2="T" k="25" />
    <hkern u1="&#x34;" u2="&#x29;" k="43" />
    <hkern u1="&#x35;" u2="&#x2044;" k="-135" />
    <hkern u1="&#x36;" u2="&#x2044;" k="-141" />
    <hkern u1="&#x36;" u2="Y" k="29" />
    <hkern u1="&#x37;" u2="&#x2212;" k="68" />
    <hkern u1="&#x37;" u2="&#x2044;" k="20" />
    <hkern u1="&#x37;" u2="&#xc6;" k="156" />
    <hkern u1="&#x37;" u2="&#xb7;" k="59" />
    <hkern u1="&#x37;" u2="&#xa2;" k="78" />
    <hkern u1="&#x37;" u2="Y" k="-41" />
    <hkern u1="&#x37;" u2="W" k="-10" />
    <hkern u1="&#x37;" u2="A" k="92" />
    <hkern u1="&#x37;" u2="&#x3d;" k="31" />
    <hkern u1="&#x37;" u2="&#x38;" k="25" />
    <hkern u1="&#x37;" u2="&#x34;" k="111" />
    <hkern u1="&#x37;" u2="&#x2f;" k="129" />
    <hkern u1="&#x37;" u2="&#x2d;" k="78" />
    <hkern u1="&#x37;" u2="&#x2b;" k="72" />
    <hkern u1="&#x37;" u2="&#x23;" k="57" />
    <hkern u1="&#x38;" u2="&#x2044;" k="-131" />
    <hkern u1="&#x38;" u2="&#x7d;" k="49" />
    <hkern u1="&#x38;" u2="]" k="35" />
    <hkern u1="&#x38;" u2="\" k="35" />
    <hkern u1="&#x38;" u2="Y" k="61" />
    <hkern u1="&#x38;" u2="W" k="20" />
    <hkern u1="&#x38;" u2="V" k="31" />
    <hkern u1="&#x38;" u2="T" k="23" />
    <hkern u1="&#x38;" u2="&#x29;" k="51" />
    <hkern u1="&#x39;" u2="&#x2044;" k="-90" />
    <hkern u1="&#x39;" u2="&#xc6;" k="33" />
    <hkern u1="&#x39;" u2="&#x7d;" k="35" />
    <hkern u1="&#x39;" u2="]" k="25" />
    <hkern u1="&#x39;" u2="Y" k="45" />
    <hkern u1="&#x39;" u2="V" k="20" />
    <hkern u1="&#x39;" u2="&#x29;" k="37" />
    <hkern u1="&#x3d;" g2="four.oldstyle" k="47" />
    <hkern u1="&#x40;" g2="Y.sc" k="25" />
    <hkern u1="&#x40;" u2="Y" k="82" />
    <hkern u1="&#x40;" u2="W" k="20" />
    <hkern u1="&#x40;" u2="V" k="29" />
    <hkern u1="&#x40;" u2="T" k="51" />
    <hkern u1="A" u2="&#x31;" k="23" />
    <hkern u1="B" g2="V.sc" k="12" />
    <hkern u1="B" g2="braceright.case" k="84" />
    <hkern u1="B" g2="bracketright.case" k="51" />
    <hkern u1="B" g2="parenright.case" k="74" />
    <hkern u1="B" u2="&#x7d;" k="51" />
    <hkern u1="B" u2="x" k="31" />
    <hkern u1="B" u2="v" k="20" />
    <hkern u1="B" u2="]" k="31" />
    <hkern u1="B" u2="X" k="20" />
    <hkern u1="B" u2="V" k="27" />
    <hkern u1="B" u2="&#x29;" k="49" />
    <hkern u1="C" u2="&#x135;" k="-72" />
    <hkern u1="C" u2="&#x129;" k="-29" />
    <hkern u1="C" u2="&#x127;" k="-18" />
    <hkern u1="C" u2="&#xef;" k="-27" />
    <hkern u1="C" u2="&#xee;" k="-84" />
    <hkern u1="C" u2="&#x34;" k="-20" />
    <hkern u1="D" u2="&#x141;" k="12" />
    <hkern u1="D" u2="&#x126;" k="12" />
    <hkern u1="F" g2="Jcircumflex.sc" k="-37" />
    <hkern u1="F" g2="Icircumflex.sc" k="-29" />
    <hkern u1="F" g2="M.sc" k="29" />
    <hkern u1="F" u2="&#x135;" k="-49" />
    <hkern u1="F" u2="&#x131;" k="61" />
    <hkern u1="F" u2="&#x12d;" k="-14" />
    <hkern u1="F" u2="&#x12b;" k="-14" />
    <hkern u1="F" u2="&#x129;" k="-25" />
    <hkern u1="F" u2="&#x127;" k="-14" />
    <hkern u1="F" u2="&#xef;" k="-57" />
    <hkern u1="F" u2="&#xee;" k="-61" />
    <hkern u1="F" u2="&#xed;" k="23" />
    <hkern u1="F" u2="&#xec;" k="-66" />
    <hkern u1="F" u2="&#xdf;" k="20" />
    <hkern u1="F" u2="x" k="53" />
    <hkern u1="F" u2="v" k="18" />
    <hkern u1="F" u2="M" k="16" />
    <hkern u1="F" u2="&#x34;" k="61" />
    <hkern u1="F" u2="&#x2f;" k="88" />
    <hkern u1="F" u2="&#x20;" k="49" />
    <hkern u1="K" u2="&#xec;" k="-23" />
    <hkern u1="L" g2="periodcentered.case" k="543" />
    <hkern u1="L" g2="at.case" k="23" />
    <hkern u1="L" u2="&#xb7;" k="410" />
    <hkern u1="L" u2="&#x31;" k="35" />
    <hkern u1="M" g2="V.sc" k="14" />
    <hkern u1="M" u2="&#x2122;" k="16" />
    <hkern u1="M" u2="&#x7d;" k="29" />
    <hkern u1="M" u2="v" k="20" />
    <hkern u1="M" u2="V" k="29" />
    <hkern u1="M" u2="&#x29;" k="29" />
    <hkern u1="O" u2="&#x141;" k="12" />
    <hkern u1="O" u2="&#x126;" k="12" />
    <hkern u1="P" g2="M.sc" k="12" />
    <hkern u1="P" g2="braceright.case" k="78" />
    <hkern u1="P" g2="bracketright.case" k="47" />
    <hkern u1="P" g2="parenright.case" k="76" />
    <hkern u1="P" u2="&#x135;" k="-43" />
    <hkern u1="P" u2="&#xee;" k="-59" />
    <hkern u1="P" u2="X" k="37" />
    <hkern u1="P" u2="M" k="10" />
    <hkern u1="P" u2="&#x34;" k="59" />
    <hkern u1="P" u2="&#x2f;" k="82" />
    <hkern u1="P" u2="&#x20;" k="53" />
    <hkern u1="Q" u2="&#x141;" k="12" />
    <hkern u1="Q" u2="&#x126;" k="12" />
    <hkern u1="R" u2="&#xee;" k="-14" />
    <hkern u1="R" u2="&#x34;" k="27" />
    <hkern u1="T" g2="ycircumflex.alt1" k="143" />
    <hkern u1="T" g2="Jcircumflex.sc" k="-35" />
    <hkern u1="T" g2="Icircumflex.sc" k="-29" />
    <hkern u1="T" u2="&#x161;" k="137" />
    <hkern u1="T" u2="&#x15d;" k="106" />
    <hkern u1="T" u2="&#x159;" k="115" />
    <hkern u1="T" u2="&#x155;" k="133" />
    <hkern u1="T" u2="&#x135;" k="-51" />
    <hkern u1="T" u2="&#x131;" k="154" />
    <hkern u1="T" u2="&#x12d;" k="-12" />
    <hkern u1="T" u2="&#x12b;" k="-12" />
    <hkern u1="T" u2="&#x129;" k="-25" />
    <hkern u1="T" u2="&#x127;" k="-14" />
    <hkern u1="T" u2="&#xef;" k="-55" />
    <hkern u1="T" u2="&#xee;" k="-66" />
    <hkern u1="T" u2="&#xed;" k="33" />
    <hkern u1="T" u2="&#xec;" k="-66" />
    <hkern u1="T" u2="&#xdf;" k="23" />
    <hkern u1="T" u2="&#x40;" k="59" />
    <hkern u1="T" u2="&#x34;" k="104" />
    <hkern u1="V" g2="Jcircumflex.sc" k="-14" />
    <hkern u1="V" g2="Idieresis.sc" k="-6" />
    <hkern u1="V" g2="Icircumflex.sc" k="-8" />
    <hkern u1="V" g2="M.sc" k="45" />
    <hkern u1="V" u2="&#x159;" k="61" />
    <hkern u1="V" u2="&#x131;" k="74" />
    <hkern u1="V" u2="&#x12d;" k="-23" />
    <hkern u1="V" u2="&#x12b;" k="-14" />
    <hkern u1="V" u2="&#xef;" k="-43" />
    <hkern u1="V" u2="&#xee;" k="-12" />
    <hkern u1="V" u2="&#xed;" k="31" />
    <hkern u1="V" u2="&#xec;" k="-80" />
    <hkern u1="V" u2="&#xdf;" k="31" />
    <hkern u1="V" u2="&#xae;" k="25" />
    <hkern u1="V" u2="x" k="51" />
    <hkern u1="V" u2="v" k="18" />
    <hkern u1="V" u2="M" k="29" />
    <hkern u1="V" u2="&#x40;" k="35" />
    <hkern u1="V" u2="&#x38;" k="29" />
    <hkern u1="V" u2="&#x36;" k="23" />
    <hkern u1="V" u2="&#x35;" k="20" />
    <hkern u1="V" u2="&#x34;" k="72" />
    <hkern u1="V" u2="&#x30;" k="20" />
    <hkern u1="V" u2="&#x2f;" k="90" />
    <hkern u1="V" u2="&#x26;" k="23" />
    <hkern u1="V" u2="&#x20;" k="63" />
    <hkern u1="W" g2="Jcircumflex.sc" k="-43" />
    <hkern u1="W" g2="Imacron.sc" k="-12" />
    <hkern u1="W" g2="Itilde.sc" k="-10" />
    <hkern u1="W" g2="Idieresis.sc" k="-41" />
    <hkern u1="W" g2="Icircumflex.sc" k="-37" />
    <hkern u1="W" u2="&#x135;" k="-18" />
    <hkern u1="W" u2="&#x131;" k="72" />
    <hkern u1="W" u2="&#x12d;" k="-55" />
    <hkern u1="W" u2="&#x12b;" k="-45" />
    <hkern u1="W" u2="&#x129;" k="-31" />
    <hkern u1="W" u2="&#x127;" k="-23" />
    <hkern u1="W" u2="&#xef;" k="-74" />
    <hkern u1="W" u2="&#xee;" k="-35" />
    <hkern u1="W" u2="&#xed;" k="25" />
    <hkern u1="W" u2="&#xec;" k="-113" />
    <hkern u1="W" u2="&#xdf;" k="25" />
    <hkern u1="W" u2="&#x40;" k="27" />
    <hkern u1="W" u2="&#x34;" k="70" />
    <hkern u1="X" g2="V.sc" k="16" />
    <hkern u1="X" u2="&#xef;" k="-27" />
    <hkern u1="X" u2="&#xec;" k="-57" />
    <hkern u1="X" u2="&#xae;" k="27" />
    <hkern u1="X" u2="v" k="88" />
    <hkern u1="Y" g2="ydieresis.alt1" k="104" />
    <hkern u1="Y" g2="adieresis.alt1" k="158" />
    <hkern u1="Y" g2="agrave.alt1" k="154" />
    <hkern u1="Y" g2="Ibreve.sc" k="6" />
    <hkern u1="Y" g2="Lacute.sc" k="49" />
    <hkern u1="Y" g2="Jcircumflex.sc" k="-45" />
    <hkern u1="Y" g2="Imacron.sc" k="-35" />
    <hkern u1="Y" g2="Itilde.sc" k="-33" />
    <hkern u1="Y" g2="Hbar.sc" k="78" />
    <hkern u1="Y" g2="Idieresis.sc" k="-63" />
    <hkern u1="Y" g2="Icircumflex.sc" k="-41" />
    <hkern u1="Y" g2="Iacute.sc" k="47" />
    <hkern u1="Y" g2="Igrave.sc" k="29" />
    <hkern u1="Y" g2="Ecircumflex.sc" k="53" />
    <hkern u1="Y" g2="Lslash.sc" k="78" />
    <hkern u1="Y" g2="at.case" k="33" />
    <hkern u1="Y" u2="&#x17e;" k="115" />
    <hkern u1="Y" u2="&#x161;" k="63" />
    <hkern u1="Y" u2="&#x15d;" k="174" />
    <hkern u1="Y" u2="&#x159;" k="41" />
    <hkern u1="Y" u2="&#x131;" k="154" />
    <hkern u1="Y" u2="&#x12d;" k="-78" />
    <hkern u1="Y" u2="&#x12b;" k="-57" />
    <hkern u1="Y" u2="&#x129;" k="-23" />
    <hkern u1="Y" u2="&#x127;" k="-31" />
    <hkern u1="Y" u2="&#x11b;" k="174" />
    <hkern u1="Y" u2="&#x10d;" k="176" />
    <hkern u1="Y" u2="&#xf6;" k="182" />
    <hkern u1="Y" u2="&#xf2;" k="190" />
    <hkern u1="Y" u2="&#xf0;" k="178" />
    <hkern u1="Y" u2="&#xef;" k="-86" />
    <hkern u1="Y" u2="&#xed;" k="66" />
    <hkern u1="Y" u2="&#xec;" k="-137" />
    <hkern u1="Y" u2="&#xeb;" k="176" />
    <hkern u1="Y" u2="&#xe8;" k="186" />
    <hkern u1="Y" u2="&#xdf;" k="61" />
    <hkern u1="Y" u2="&#x40;" k="92" />
    <hkern u1="Y" u2="&#x39;" k="33" />
    <hkern u1="Y" u2="&#x38;" k="55" />
    <hkern u1="Y" u2="&#x36;" k="51" />
    <hkern u1="Y" u2="&#x35;" k="31" />
    <hkern u1="Y" u2="&#x34;" k="145" />
    <hkern u1="Y" u2="&#x33;" k="27" />
    <hkern u1="Y" u2="&#x32;" k="31" />
    <hkern u1="Y" u2="&#x30;" k="45" />
    <hkern u1="Z" u2="&#x131;" k="20" />
    <hkern u1="Z" u2="&#xef;" k="-14" />
    <hkern u1="Z" u2="&#xee;" k="-14" />
    <hkern u1="Z" u2="&#xec;" k="-23" />
    <hkern u1="[" g2="Jcircumflex.sc" k="-139" />
    <hkern u1="[" g2="Icircumflex.sc" k="-10" />
    <hkern u1="[" g2="M.sc" k="41" />
    <hkern u1="[" g2="J.sc" k="-139" />
    <hkern u1="[" g2="eight.oldstyle" k="35" />
    <hkern u1="[" g2="six.oldstyle" k="33" />
    <hkern u1="[" g2="four.oldstyle" k="68" />
    <hkern u1="[" g2="two.oldstyle" k="53" />
    <hkern u1="[" g2="one.oldstyle" k="53" />
    <hkern u1="[" g2="zero.oldstyle" k="66" />
    <hkern u1="[" u2="&#x135;" k="-109" />
    <hkern u1="[" u2="&#x134;" k="-201" />
    <hkern u1="[" u2="&#xef;" k="-41" />
    <hkern u1="[" u2="&#xee;" k="-25" />
    <hkern u1="[" u2="&#xec;" k="-49" />
    <hkern u1="[" u2="&#x7b;" k="23" />
    <hkern u1="[" u2="x" k="33" />
    <hkern u1="[" u2="v" k="35" />
    <hkern u1="[" u2="j" k="-109" />
    <hkern u1="[" u2="f" k="23" />
    <hkern u1="[" u2="M" k="23" />
    <hkern u1="[" u2="J" k="-201" />
    <hkern u1="[" u2="&#x38;" k="35" />
    <hkern u1="[" u2="&#x36;" k="33" />
    <hkern u1="[" u2="&#x34;" k="57" />
    <hkern u1="[" u2="&#x30;" k="29" />
    <hkern u1="[" u2="&#x28;" k="31" />
    <hkern u1="\" g2="V.sc" k="63" />
    <hkern u1="\" g2="one.oldstyle" k="23" />
    <hkern u1="\" u2="&#x2019;" k="90" />
    <hkern u1="\" u2="v" k="53" />
    <hkern u1="\" u2="V" k="92" />
    <hkern u1="\" u2="&#x31;" k="31" />
    <hkern u1="c" u2="Y" k="190" />
    <hkern u1="c" u2="W" k="74" />
    <hkern u1="c" u2="V" k="80" />
    <hkern u1="c" u2="T" k="195" />
    <hkern u1="c" u2="S" k="14" />
    <hkern u1="d" u2="Z" k="12" />
    <hkern u1="f" u2="&#x2122;" k="-14" />
    <hkern u1="f" u2="&#x135;" k="-55" />
    <hkern u1="f" u2="&#x12d;" k="-74" />
    <hkern u1="f" u2="&#x12b;" k="-74" />
    <hkern u1="f" u2="&#x129;" k="-78" />
    <hkern u1="f" u2="&#xef;" k="-119" />
    <hkern u1="f" u2="&#xee;" k="-43" />
    <hkern u1="f" u2="&#xec;" k="-125" />
    <hkern u1="f" u2="&#xc6;" k="51" />
    <hkern u1="f" u2="\" k="-16" />
    <hkern u1="f" u2="Y" k="-84" />
    <hkern u1="f" u2="W" k="-59" />
    <hkern u1="f" u2="V" k="-27" />
    <hkern u1="f" u2="T" k="-12" />
    <hkern u1="f" u2="&#x2a;" k="-43" />
    <hkern u1="f" u2="&#x20;" k="41" />
    <hkern u1="i" u2="&#xec;" k="-14" />
    <hkern u1="k" u2="Y" k="145" />
    <hkern u1="k" u2="W" k="53" />
    <hkern u1="k" u2="V" k="51" />
    <hkern u1="k" u2="T" k="166" />
    <hkern u1="k" u2="S" k="18" />
    <hkern u1="l" u2="&#xb7;" k="72" />
    <hkern u1="l" u2="Z" k="12" />
    <hkern u1="r" u2="&#xc6;" k="119" />
    <hkern u1="r" u2="Z" k="55" />
    <hkern u1="r" u2="Y" k="127" />
    <hkern u1="r" u2="X" k="96" />
    <hkern u1="r" u2="W" k="25" />
    <hkern u1="r" u2="V" k="25" />
    <hkern u1="r" u2="T" k="160" />
    <hkern u1="r" u2="M" k="29" />
    <hkern u1="s" u2="Z" k="10" />
    <hkern u1="s" u2="Y" k="188" />
    <hkern u1="s" u2="X" k="10" />
    <hkern u1="s" u2="W" k="92" />
    <hkern u1="s" u2="V" k="100" />
    <hkern u1="s" u2="T" k="152" />
    <hkern u1="t" u2="&#xc6;" k="51" />
    <hkern u1="t" u2="Z" k="35" />
    <hkern u1="t" u2="Y" k="84" />
    <hkern u1="t" u2="X" k="57" />
    <hkern u1="t" u2="W" k="20" />
    <hkern u1="t" u2="V" k="23" />
    <hkern u1="t" u2="T" k="70" />
    <hkern u1="t" u2="M" k="18" />
    <hkern u1="v" u2="&#x2122;" k="16" />
    <hkern u1="v" u2="&#xc6;" k="88" />
    <hkern u1="v" u2="&#x7d;" k="37" />
    <hkern u1="v" u2="]" k="33" />
    <hkern u1="v" u2="Z" k="39" />
    <hkern u1="v" u2="Y" k="115" />
    <hkern u1="v" u2="X" k="88" />
    <hkern u1="v" u2="W" k="16" />
    <hkern u1="v" u2="V" k="18" />
    <hkern u1="v" u2="T" k="156" />
    <hkern u1="v" u2="M" k="20" />
    <hkern u1="v" u2="&#x2f;" k="53" />
    <hkern u1="v" u2="&#x29;" k="37" />
    <hkern u1="v" u2="&#x20;" k="51" />
    <hkern u1="w" u2="&#xc6;" k="80" />
    <hkern u1="w" u2="Z" k="37" />
    <hkern u1="w" u2="Y" k="115" />
    <hkern u1="w" u2="X" k="82" />
    <hkern u1="w" u2="W" k="20" />
    <hkern u1="w" u2="V" k="23" />
    <hkern u1="w" u2="T" k="147" />
    <hkern u1="w" u2="M" k="20" />
    <hkern u1="x" u2="&#x2122;" k="39" />
    <hkern u1="x" u2="&#x7d;" k="51" />
    <hkern u1="x" u2="]" k="35" />
    <hkern u1="x" u2="\" k="35" />
    <hkern u1="x" u2="Y" k="143" />
    <hkern u1="x" u2="W" k="51" />
    <hkern u1="x" u2="V" k="49" />
    <hkern u1="x" u2="T" k="152" />
    <hkern u1="x" u2="S" k="10" />
    <hkern u1="x" u2="&#x29;" k="47" />
    <hkern u1="y" u2="&#xc6;" k="88" />
    <hkern u1="y" u2="Z" k="37" />
    <hkern u1="y" u2="Y" k="115" />
    <hkern u1="y" u2="X" k="88" />
    <hkern u1="y" u2="W" k="16" />
    <hkern u1="y" u2="V" k="18" />
    <hkern u1="y" u2="T" k="156" />
    <hkern u1="y" u2="M" k="20" />
    <hkern u1="z" u2="Y" k="158" />
    <hkern u1="z" u2="W" k="55" />
    <hkern u1="z" u2="V" k="57" />
    <hkern u1="z" u2="T" k="162" />
    <hkern u1="&#x7b;" g2="Jcircumflex.sc" k="-143" />
    <hkern u1="&#x7b;" g2="Hbar.sc" k="41" />
    <hkern u1="&#x7b;" g2="Icircumflex.sc" k="-14" />
    <hkern u1="&#x7b;" g2="M.sc" k="59" />
    <hkern u1="&#x7b;" g2="J.sc" k="-143" />
    <hkern u1="&#x7b;" g2="Lslash.sc" k="41" />
    <hkern u1="&#x7b;" g2="eight.oldstyle" k="49" />
    <hkern u1="&#x7b;" g2="six.oldstyle" k="49" />
    <hkern u1="&#x7b;" g2="four.oldstyle" k="100" />
    <hkern u1="&#x7b;" g2="two.oldstyle" k="90" />
    <hkern u1="&#x7b;" g2="one.oldstyle" k="63" />
    <hkern u1="&#x7b;" g2="zero.oldstyle" k="86" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-113" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-207" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-45" />
    <hkern u1="&#x7b;" u2="&#xee;" k="-27" />
    <hkern u1="&#x7b;" u2="&#xec;" k="-53" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="29" />
    <hkern u1="&#x7b;" u2="x" k="51" />
    <hkern u1="&#x7b;" u2="v" k="37" />
    <hkern u1="&#x7b;" u2="j" k="-113" />
    <hkern u1="&#x7b;" u2="f" k="27" />
    <hkern u1="&#x7b;" u2="M" k="35" />
    <hkern u1="&#x7b;" u2="J" k="-207" />
    <hkern u1="&#x7b;" u2="&#x38;" k="49" />
    <hkern u1="&#x7b;" u2="&#x36;" k="49" />
    <hkern u1="&#x7b;" u2="&#x35;" k="23" />
    <hkern u1="&#x7b;" u2="&#x34;" k="113" />
    <hkern u1="&#x7b;" u2="&#x30;" k="39" />
    <hkern u1="&#x7b;" u2="&#x28;" k="47" />
    <hkern u1="&#x7c;" g2="Jcircumflex.sc" k="-43" />
    <hkern u1="&#x7c;" g2="J.sc" k="-43" />
    <hkern u1="&#x7c;" u2="&#x134;" k="-98" />
    <hkern u1="&#x7c;" u2="J" k="-98" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="29" />
    <hkern u1="&#x7d;" u2="]" k="23" />
    <hkern u1="&#x7d;" u2="&#x29;" k="31" />
    <hkern u1="&#xa1;" g2="Jcircumflex.sc" k="-80" />
    <hkern u1="&#xa1;" g2="J.sc" k="-80" />
    <hkern u1="&#xa1;" u2="&#x135;" k="-55" />
    <hkern u1="&#xa1;" u2="&#x134;" k="-141" />
    <hkern u1="&#xa1;" u2="j" k="-55" />
    <hkern u1="&#xa1;" u2="V" k="39" />
    <hkern u1="&#xa1;" u2="J" k="-141" />
    <hkern u1="&#xa3;" g2="three.oldstyle" k="29" />
    <hkern u1="&#xae;" u2="X" k="33" />
    <hkern u1="&#xae;" u2="V" k="25" />
    <hkern u1="&#xb0;" g2="nine.oldstyle" k="51" />
    <hkern u1="&#xb0;" g2="five.oldstyle" k="39" />
    <hkern u1="&#xb0;" g2="four.oldstyle" k="160" />
    <hkern u1="&#xb0;" g2="three.oldstyle" k="47" />
    <hkern u1="&#xb0;" g2="zero.oldstyle" k="35" />
    <hkern u1="&#xb0;" u2="&#x34;" k="104" />
    <hkern u1="&#xb7;" g2="l.alt1" k="72" />
    <hkern u1="&#xb7;" g2="four.oldstyle" k="113" />
    <hkern u1="&#xb7;" u2="l" k="72" />
    <hkern u1="&#xb7;" u2="&#x37;" k="47" />
    <hkern u1="&#xb7;" u2="&#x33;" k="29" />
    <hkern u1="&#xb7;" u2="&#x32;" k="27" />
    <hkern u1="&#xbf;" g2="Jcircumflex.sc" k="-111" />
    <hkern u1="&#xbf;" g2="V.sc" k="57" />
    <hkern u1="&#xbf;" g2="J.sc" k="-111" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-70" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-150" />
    <hkern u1="&#xbf;" u2="v" k="33" />
    <hkern u1="&#xbf;" u2="j" k="-70" />
    <hkern u1="&#xbf;" u2="V" k="84" />
    <hkern u1="&#xbf;" u2="J" k="-150" />
    <hkern u1="&#xce;" g2="braceright.case" k="-45" />
    <hkern u1="&#xce;" g2="bracketright.case" k="-39" />
    <hkern u1="&#xce;" g2="parenright.case" k="-45" />
    <hkern u1="&#xd0;" u2="&#x141;" k="12" />
    <hkern u1="&#xd0;" u2="&#x126;" k="12" />
    <hkern u1="&#xd2;" u2="&#x141;" k="12" />
    <hkern u1="&#xd2;" u2="&#x126;" k="12" />
    <hkern u1="&#xd3;" u2="&#x141;" k="12" />
    <hkern u1="&#xd3;" u2="&#x126;" k="12" />
    <hkern u1="&#xd4;" u2="&#x141;" k="12" />
    <hkern u1="&#xd4;" u2="&#x126;" k="12" />
    <hkern u1="&#xd5;" u2="&#x141;" k="12" />
    <hkern u1="&#xd5;" u2="&#x126;" k="12" />
    <hkern u1="&#xd6;" u2="&#x141;" k="12" />
    <hkern u1="&#xd6;" u2="&#x126;" k="12" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="178" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="61" />
    <hkern u1="&#xde;" g2="AE.sc" k="72" />
    <hkern u1="&#xde;" g2="Y.sc" k="14" />
    <hkern u1="&#xde;" g2="X.sc" k="14" />
    <hkern u1="&#xde;" g2="A.sc" k="29" />
    <hkern u1="&#xde;" g2="braceright.case" k="96" />
    <hkern u1="&#xde;" g2="bracketright.case" k="53" />
    <hkern u1="&#xde;" g2="parenright.case" k="102" />
    <hkern u1="&#xde;" u2="&#x2122;" k="29" />
    <hkern u1="&#xde;" u2="&#x7d;" k="57" />
    <hkern u1="&#xde;" u2="]" k="37" />
    <hkern u1="&#xde;" u2="\" k="25" />
    <hkern u1="&#xde;" u2="X" k="59" />
    <hkern u1="&#xde;" u2="V" k="29" />
    <hkern u1="&#xde;" u2="&#x2f;" k="31" />
    <hkern u1="&#xde;" u2="&#x29;" k="61" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="25" />
    <hkern u1="&#xdf;" u2="&#xc6;" k="12" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="45" />
    <hkern u1="&#xdf;" u2="x" k="29" />
    <hkern u1="&#xdf;" u2="v" k="25" />
    <hkern u1="&#xdf;" u2="]" k="35" />
    <hkern u1="&#xdf;" u2="\" k="31" />
    <hkern u1="&#xdf;" u2="Z" k="23" />
    <hkern u1="&#xdf;" u2="Y" k="104" />
    <hkern u1="&#xdf;" u2="X" k="35" />
    <hkern u1="&#xdf;" u2="W" k="55" />
    <hkern u1="&#xdf;" u2="V" k="63" />
    <hkern u1="&#xdf;" u2="U" k="25" />
    <hkern u1="&#xdf;" u2="T" k="53" />
    <hkern u1="&#xdf;" u2="S" k="10" />
    <hkern u1="&#xdf;" u2="M" k="12" />
    <hkern u1="&#xdf;" u2="A" k="10" />
    <hkern u1="&#xdf;" u2="&#x29;" k="45" />
    <hkern u1="&#xed;" u2="&#x2122;" k="-57" />
    <hkern u1="&#xed;" u2="&#x161;" k="-18" />
    <hkern u1="&#xed;" u2="&#x159;" k="-43" />
    <hkern u1="&#xed;" u2="&#x133;" k="-12" />
    <hkern u1="&#xed;" u2="&#x7d;" k="-47" />
    <hkern u1="&#xed;" u2="i" k="-12" />
    <hkern u1="&#xed;" u2="]" k="-43" />
    <hkern u1="&#xed;" u2="\" k="-59" />
    <hkern u1="&#xed;" u2="&#x29;" k="-59" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-31" />
    <hkern u1="&#xee;" u2="&#x7d;" k="-27" />
    <hkern u1="&#xee;" u2="]" k="-25" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-57" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-100" />
    <hkern u1="&#xee;" u2="&#x29;" k="-29" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-57" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-47" />
    <hkern u1="&#xef;" u2="]" k="-43" />
    <hkern u1="&#xef;" u2="\" k="-27" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-51" />
    <hkern u1="&#xef;" u2="&#x29;" k="-45" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="51" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="20" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="78" />
    <hkern u1="&#xf0;" u2="x" k="33" />
    <hkern u1="&#xf0;" u2="v" k="23" />
    <hkern u1="&#xf0;" u2="]" k="59" />
    <hkern u1="&#xf0;" u2="\" k="70" />
    <hkern u1="&#xf0;" u2="Z" k="29" />
    <hkern u1="&#xf0;" u2="Y" k="162" />
    <hkern u1="&#xf0;" u2="X" k="45" />
    <hkern u1="&#xf0;" u2="W" k="92" />
    <hkern u1="&#xf0;" u2="V" k="94" />
    <hkern u1="&#xf0;" u2="U" k="23" />
    <hkern u1="&#xf0;" u2="T" k="133" />
    <hkern u1="&#xf0;" u2="S" k="10" />
    <hkern u1="&#xf0;" u2="M" k="14" />
    <hkern u1="&#xf0;" u2="A" k="12" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="20" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="35" />
    <hkern u1="&#xf0;" u2="&#x29;" k="78" />
    <hkern u1="&#x104;" g2="y.alt1" k="-86" />
    <hkern u1="&#x104;" g2="J.sc" k="-252" />
    <hkern u1="&#x104;" g2="braceright.case" k="-59" />
    <hkern u1="&#x104;" g2="bracketright.case" k="-53" />
    <hkern u1="&#x104;" g2="parenright.case" k="-59" />
    <hkern u1="&#x104;" u2="&#x201e;" k="-70" />
    <hkern u1="&#x104;" u2="&#x201a;" k="-70" />
    <hkern u1="&#x104;" u2="&#x134;" k="-311" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-47" />
    <hkern u1="&#x104;" u2="&#x7d;" k="-49" />
    <hkern u1="&#x104;" u2="j" k="-229" />
    <hkern u1="&#x104;" u2="]" k="-45" />
    <hkern u1="&#x104;" u2="J" k="-311" />
    <hkern u1="&#x104;" u2="&#x3b;" k="-92" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-104" />
    <hkern u1="&#x104;" u2="&#x29;" k="-47" />
    <hkern u1="&#x105;" u2="j" k="-115" />
    <hkern u1="&#x10e;" u2="&#x141;" k="12" />
    <hkern u1="&#x10e;" u2="&#x126;" k="12" />
    <hkern u1="&#x10f;" g2="t.alt1" k="-23" />
    <hkern u1="&#x10f;" g2="y.alt1" k="-49" />
    <hkern u1="&#x10f;" g2="l.alt1" k="-115" />
    <hkern u1="&#x10f;" g2="f_j" k="-25" />
    <hkern u1="&#x10f;" g2="f_f_j" k="-25" />
    <hkern u1="&#x10f;" g2="f_f_l" k="-25" />
    <hkern u1="&#x10f;" g2="f_f_i" k="-25" />
    <hkern u1="&#x10f;" g2="fl" k="-25" />
    <hkern u1="&#x10f;" g2="fi" k="-25" />
    <hkern u1="&#x10f;" g2="f_f" k="-25" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-193" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-39" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-39" />
    <hkern u1="&#x10f;" u2="&#x17f;" k="-25" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-57" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-135" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-104" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-37" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-180" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-94" />
    <hkern u1="&#x10f;" u2="y" k="-51" />
    <hkern u1="&#x10f;" u2="w" k="-45" />
    <hkern u1="&#x10f;" u2="v" k="-53" />
    <hkern u1="&#x10f;" u2="t" k="-25" />
    <hkern u1="&#x10f;" u2="l" k="-104" />
    <hkern u1="&#x10f;" u2="k" k="-104" />
    <hkern u1="&#x10f;" u2="j" k="-133" />
    <hkern u1="&#x10f;" u2="i" k="-135" />
    <hkern u1="&#x10f;" u2="h" k="-104" />
    <hkern u1="&#x10f;" u2="f" k="-25" />
    <hkern u1="&#x10f;" u2="b" k="-104" />
    <hkern u1="&#x10f;" u2="]" k="-176" />
    <hkern u1="&#x10f;" u2="\" k="-193" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-102" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-147" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-180" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-117" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-117" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-111" />
    <hkern u1="&#x10f;" u2="&#x20;" k="31" />
    <hkern u1="&#x110;" u2="&#x141;" k="12" />
    <hkern u1="&#x110;" u2="&#x126;" k="12" />
    <hkern u1="&#x111;" u2="&#x2122;" k="-18" />
    <hkern u1="&#x111;" u2="&#x2a;" k="-55" />
    <hkern u1="&#x118;" g2="y.alt1" k="-127" />
    <hkern u1="&#x118;" g2="J.sc" k="-295" />
    <hkern u1="&#x118;" g2="braceright.case" k="-100" />
    <hkern u1="&#x118;" g2="bracketright.case" k="-96" />
    <hkern u1="&#x118;" g2="parenright.case" k="-100" />
    <hkern u1="&#x118;" u2="&#x201e;" k="-113" />
    <hkern u1="&#x118;" u2="&#x201a;" k="-115" />
    <hkern u1="&#x118;" u2="&#x134;" k="-352" />
    <hkern u1="&#x118;" u2="&#x12e;" k="-88" />
    <hkern u1="&#x118;" u2="&#xfe;" k="-16" />
    <hkern u1="&#x118;" u2="&#x7d;" k="-90" />
    <hkern u1="&#x118;" u2="&#x7c;" k="-12" />
    <hkern u1="&#x118;" u2="p" k="-14" />
    <hkern u1="&#x118;" u2="j" k="-270" />
    <hkern u1="&#x118;" u2="g" k="-37" />
    <hkern u1="&#x118;" u2="]" k="-84" />
    <hkern u1="&#x118;" u2="J" k="-352" />
    <hkern u1="&#x118;" u2="&#x3b;" k="-133" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-147" />
    <hkern u1="&#x118;" u2="&#x29;" k="-88" />
    <hkern u1="&#x126;" g2="l.alt1" k="18" />
    <hkern u1="&#x126;" u2="&#x2122;" k="-23" />
    <hkern u1="&#x126;" u2="&#x153;" k="33" />
    <hkern u1="&#x126;" u2="&#x152;" k="12" />
    <hkern u1="&#x126;" u2="&#x150;" k="12" />
    <hkern u1="&#x126;" u2="&#x14e;" k="12" />
    <hkern u1="&#x126;" u2="&#x14c;" k="12" />
    <hkern u1="&#x126;" u2="&#x141;" k="14" />
    <hkern u1="&#x126;" u2="&#x126;" k="14" />
    <hkern u1="&#x126;" u2="&#x122;" k="12" />
    <hkern u1="&#x126;" u2="&#x121;" k="33" />
    <hkern u1="&#x126;" u2="&#x120;" k="12" />
    <hkern u1="&#x126;" u2="&#x11e;" k="12" />
    <hkern u1="&#x126;" u2="&#x11c;" k="12" />
    <hkern u1="&#x126;" u2="&#x10c;" k="12" />
    <hkern u1="&#x126;" u2="&#x10a;" k="12" />
    <hkern u1="&#x126;" u2="&#x108;" k="12" />
    <hkern u1="&#x126;" u2="&#x106;" k="12" />
    <hkern u1="&#x126;" u2="&#xf0;" k="33" />
    <hkern u1="&#x126;" u2="&#xe6;" k="33" />
    <hkern u1="&#x126;" u2="&#xd6;" k="12" />
    <hkern u1="&#x126;" u2="&#xd5;" k="12" />
    <hkern u1="&#x126;" u2="&#xd4;" k="12" />
    <hkern u1="&#x126;" u2="&#xd3;" k="12" />
    <hkern u1="&#x126;" u2="&#xd2;" k="12" />
    <hkern u1="&#x126;" u2="&#xc7;" k="12" />
    <hkern u1="&#x126;" u2="u" k="18" />
    <hkern u1="&#x126;" u2="q" k="33" />
    <hkern u1="&#x126;" u2="o" k="33" />
    <hkern u1="&#x126;" u2="g" k="33" />
    <hkern u1="&#x126;" u2="e" k="33" />
    <hkern u1="&#x126;" u2="d" k="33" />
    <hkern u1="&#x126;" u2="c" k="33" />
    <hkern u1="&#x126;" u2="a" k="33" />
    <hkern u1="&#x126;" u2="Q" k="12" />
    <hkern u1="&#x126;" u2="O" k="12" />
    <hkern u1="&#x126;" u2="G" k="12" />
    <hkern u1="&#x126;" u2="C" k="12" />
    <hkern u1="&#x126;" u2="&#x2a;" k="-12" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-27" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-16" />
    <hkern u1="&#x129;" u2="]" k="-12" />
    <hkern u1="&#x129;" u2="\" k="-29" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-14" />
    <hkern u1="&#x129;" u2="&#x29;" k="-14" />
    <hkern u1="&#x12b;" u2="&#x2122;" k="-16" />
    <hkern u1="&#x12d;" u2="&#x2122;" k="-12" />
    <hkern u1="&#x12e;" g2="J.sc" k="-96" />
    <hkern u1="&#x12e;" u2="&#x134;" k="-156" />
    <hkern u1="&#x12e;" u2="j" k="-74" />
    <hkern u1="&#x12e;" u2="J" k="-156" />
    <hkern u1="&#x12f;" u2="j" k="-119" />
    <hkern u1="&#x133;" u2="&#xec;" k="-14" />
    <hkern u1="&#x134;" g2="braceright.case" k="-39" />
    <hkern u1="&#x134;" g2="bracketright.case" k="-33" />
    <hkern u1="&#x134;" g2="parenright.case" k="-39" />
    <hkern u1="&#x135;" g2="l.alt1" k="-23" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-39" />
    <hkern u1="&#x135;" u2="&#xfe;" k="-12" />
    <hkern u1="&#x135;" u2="&#x7d;" k="-35" />
    <hkern u1="&#x135;" u2="l" k="-12" />
    <hkern u1="&#x135;" u2="k" k="-12" />
    <hkern u1="&#x135;" u2="h" k="-12" />
    <hkern u1="&#x135;" u2="b" k="-12" />
    <hkern u1="&#x135;" u2="]" k="-33" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-70" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-113" />
    <hkern u1="&#x135;" u2="&#x29;" k="-35" />
    <hkern u1="&#x135;" u2="&#x27;" k="-14" />
    <hkern u1="&#x135;" u2="&#x22;" k="-14" />
    <hkern u1="&#x13d;" g2="Y.sc" k="100" />
    <hkern u1="&#x13d;" u2="&#x2122;" k="86" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="20" />
    <hkern u1="&#x13d;" u2="&#x1e84;" k="47" />
    <hkern u1="&#x13d;" u2="&#x1e82;" k="47" />
    <hkern u1="&#x13d;" u2="&#x1e80;" k="47" />
    <hkern u1="&#x13d;" u2="&#x178;" k="20" />
    <hkern u1="&#x13d;" u2="&#x176;" k="20" />
    <hkern u1="&#x13d;" u2="&#x174;" k="47" />
    <hkern u1="&#x13d;" u2="&#x166;" k="92" />
    <hkern u1="&#x13d;" u2="&#x164;" k="92" />
    <hkern u1="&#x13d;" u2="&#x162;" k="92" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="20" />
    <hkern u1="&#x13d;" u2="\" k="96" />
    <hkern u1="&#x13d;" u2="Y" k="20" />
    <hkern u1="&#x13d;" u2="W" k="47" />
    <hkern u1="&#x13d;" u2="V" k="76" />
    <hkern u1="&#x13d;" u2="T" k="92" />
    <hkern u1="&#x13d;" u2="&#x2a;" k="131" />
    <hkern u1="&#x13e;" g2="t.alt1" k="-53" />
    <hkern u1="&#x13e;" g2="y.alt1" k="-78" />
    <hkern u1="&#x13e;" g2="l.alt1" k="-207" />
    <hkern u1="&#x13e;" g2="f_j" k="-55" />
    <hkern u1="&#x13e;" g2="f_f_j" k="-55" />
    <hkern u1="&#x13e;" g2="f_f_l" k="-55" />
    <hkern u1="&#x13e;" g2="f_f_i" k="-55" />
    <hkern u1="&#x13e;" g2="fl" k="-55" />
    <hkern u1="&#x13e;" g2="fi" k="-55" />
    <hkern u1="&#x13e;" g2="f_f" k="-55" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-279" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="-84" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="-104" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-82" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-104" />
    <hkern u1="&#x13e;" u2="&#x17f;" k="-55" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-170" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-217" />
    <hkern u1="&#x13e;" u2="&#x148;" k="-100" />
    <hkern u1="&#x13e;" u2="&#x133;" k="-219" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-106" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-197" />
    <hkern u1="&#x13e;" u2="&#xf4;" k="-23" />
    <hkern u1="&#x13e;" u2="&#xf0;" k="-78" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-104" />
    <hkern u1="&#x13e;" u2="&#xae;" k="-33" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-268" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-186" />
    <hkern u1="&#x13e;" u2="y" k="-80" />
    <hkern u1="&#x13e;" u2="x" k="-20" />
    <hkern u1="&#x13e;" u2="w" k="-74" />
    <hkern u1="&#x13e;" u2="v" k="-82" />
    <hkern u1="&#x13e;" u2="t" k="-96" />
    <hkern u1="&#x13e;" u2="l" k="-197" />
    <hkern u1="&#x13e;" u2="k" k="-197" />
    <hkern u1="&#x13e;" u2="j" k="-217" />
    <hkern u1="&#x13e;" u2="i" k="-219" />
    <hkern u1="&#x13e;" u2="h" k="-197" />
    <hkern u1="&#x13e;" u2="f" k="-55" />
    <hkern u1="&#x13e;" u2="b" k="-197" />
    <hkern u1="&#x13e;" u2="]" k="-262" />
    <hkern u1="&#x13e;" u2="\" k="-281" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-170" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-217" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-268" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-203" />
    <hkern u1="&#x13e;" u2="&#x26;" k="-78" />
    <hkern u1="&#x13e;" u2="&#x22;" k="-203" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-197" />
    <hkern u1="&#x13e;" u2="&#x20;" k="-61" />
    <hkern u1="&#x142;" g2="t.alt1" k="-18" />
    <hkern u1="&#x142;" g2="y.alt1" k="-45" />
    <hkern u1="&#x142;" g2="f_j" k="-20" />
    <hkern u1="&#x142;" g2="f_f_j" k="-20" />
    <hkern u1="&#x142;" g2="f_f_l" k="-20" />
    <hkern u1="&#x142;" g2="f_f_i" k="-20" />
    <hkern u1="&#x142;" g2="fl" k="-20" />
    <hkern u1="&#x142;" g2="fi" k="-20" />
    <hkern u1="&#x142;" g2="f_f" k="-20" />
    <hkern u1="&#x142;" u2="&#x17f;" k="-20" />
    <hkern u1="&#x142;" u2="y" k="-47" />
    <hkern u1="&#x142;" u2="w" k="-41" />
    <hkern u1="&#x142;" u2="v" k="-49" />
    <hkern u1="&#x142;" u2="t" k="-23" />
    <hkern u1="&#x142;" u2="f" k="-20" />
    <hkern u1="&#x142;" u2="&#x2a;" k="-70" />
    <hkern u1="&#x14c;" u2="&#x141;" k="12" />
    <hkern u1="&#x14c;" u2="&#x126;" k="12" />
    <hkern u1="&#x14e;" u2="&#x141;" k="12" />
    <hkern u1="&#x14e;" u2="&#x126;" k="12" />
    <hkern u1="&#x150;" u2="&#x141;" k="12" />
    <hkern u1="&#x150;" u2="&#x126;" k="12" />
    <hkern u1="&#x162;" g2="Icircumflex.sc" k="-29" />
    <hkern u1="&#x162;" u2="&#xee;" k="-66" />
    <hkern u1="&#x162;" u2="&#xdf;" k="23" />
    <hkern u1="&#x164;" u2="&#xdf;" k="23" />
    <hkern u1="&#x165;" g2="l.alt1" k="-111" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-180" />
    <hkern u1="&#x165;" u2="&#x161;" k="-119" />
    <hkern u1="&#x165;" u2="&#x133;" k="-113" />
    <hkern u1="&#x165;" u2="&#x11b;" k="-8" />
    <hkern u1="&#x165;" u2="&#x10d;" k="-6" />
    <hkern u1="&#x165;" u2="&#xfe;" k="-98" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-168" />
    <hkern u1="&#x165;" u2="&#x7c;" k="-92" />
    <hkern u1="&#x165;" u2="l" k="-98" />
    <hkern u1="&#x165;" u2="k" k="-98" />
    <hkern u1="&#x165;" u2="j" k="-111" />
    <hkern u1="&#x165;" u2="i" k="-113" />
    <hkern u1="&#x165;" u2="h" k="-98" />
    <hkern u1="&#x165;" u2="b" k="-98" />
    <hkern u1="&#x165;" u2="]" k="-164" />
    <hkern u1="&#x165;" u2="\" k="-182" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-49" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-106" />
    <hkern u1="&#x165;" u2="&#x29;" k="-170" />
    <hkern u1="&#x165;" u2="&#x27;" k="-104" />
    <hkern u1="&#x165;" u2="&#x22;" k="-104" />
    <hkern u1="&#x165;" u2="&#x21;" k="-96" />
    <hkern u1="&#x166;" u2="&#xdf;" k="23" />
    <hkern u1="&#x167;" u2="&#x2122;" k="16" />
    <hkern u1="&#x167;" u2="&#x2f;" k="20" />
    <hkern u1="&#x173;" u2="j" k="-115" />
    <hkern u1="&#x174;" u2="&#xdf;" k="25" />
    <hkern u1="&#x176;" u2="&#xf0;" k="178" />
    <hkern u1="&#x176;" u2="&#xdf;" k="61" />
    <hkern u1="&#x178;" u2="&#xf0;" k="178" />
    <hkern u1="&#x178;" u2="&#xdf;" k="61" />
    <hkern u1="&#x17f;" u2="&#x159;" k="-41" />
    <hkern u1="&#x17f;" u2="&#x149;" k="-199" />
    <hkern u1="&#x17f;" u2="&#x135;" k="-125" />
    <hkern u1="&#x17f;" u2="&#x131;" k="-2" />
    <hkern u1="&#x17f;" u2="&#x12d;" k="-143" />
    <hkern u1="&#x17f;" u2="&#x12b;" k="-143" />
    <hkern u1="&#x17f;" u2="&#x129;" k="-147" />
    <hkern u1="&#x17f;" u2="&#xef;" k="-188" />
    <hkern u1="&#x17f;" u2="&#xee;" k="-115" />
    <hkern u1="&#x17f;" u2="&#xed;" k="-2" />
    <hkern u1="&#x17f;" u2="&#xec;" k="-197" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="25" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="25" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="25" />
    <hkern u1="&#x1ef2;" u2="&#xf0;" k="178" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="61" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-29" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-63" />
    <hkern u1="&#x2019;" u2="&#xef;" k="-29" />
    <hkern u1="&#x2019;" u2="&#xee;" k="-14" />
    <hkern u1="&#x2019;" u2="&#xec;" k="-47" />
    <hkern u1="&#x2019;" u2="&#x40;" k="47" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="129" />
    <hkern u1="&#x2019;" u2="&#x20;" k="37" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-29" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-63" />
    <hkern u1="&#x201d;" u2="&#xef;" k="-29" />
    <hkern u1="&#x201d;" u2="&#xee;" k="-14" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-49" />
    <hkern u1="&#x2044;" g2="four.dnom" k="53" />
    <hkern u1="&#x2044;" g2="eight.oldstyle" k="-82" />
    <hkern u1="&#x2044;" g2="seven.oldstyle" k="-72" />
    <hkern u1="&#x2044;" g2="six.oldstyle" k="-78" />
    <hkern u1="&#x2044;" g2="four.oldstyle" k="90" />
    <hkern u1="&#x2044;" g2="one.oldstyle" k="-25" />
    <hkern u1="&#x2044;" u2="&#x39;" k="-131" />
    <hkern u1="&#x2044;" u2="&#x38;" k="-82" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-309" />
    <hkern u1="&#x2044;" u2="&#x36;" k="-78" />
    <hkern u1="&#x2044;" u2="&#x35;" k="-162" />
    <hkern u1="&#x2044;" u2="&#x33;" k="-143" />
    <hkern u1="&#x2044;" u2="&#x32;" k="-131" />
    <hkern u1="&#x2044;" u2="&#x31;" k="-188" />
    <hkern u1="&#x2044;" u2="&#x30;" k="-90" />
    <hkern u1="&#x20ac;" g2="four.oldstyle" k="47" />
    <hkern u1="&#x20ac;" g2="three.oldstyle" k="49" />
    <hkern u1="&#x2122;" g2="Dcroat.sc" k="-53" />
    <hkern u1="&#x2122;" g2="Eth.sc" k="-53" />
    <hkern u1="&#x2122;" u2="&#xee;" k="-18" />
    <hkern u1="&#x2212;" g2="four.oldstyle" k="78" />
    <hkern u1="&#x2212;" g2="three.oldstyle" k="47" />
    <hkern u1="&#x2212;" u2="&#x37;" k="45" />
    <hkern g1="f_f" u2="&#x149;" k="-129" />
    <hkern g1="f_f" u2="&#x135;" k="-55" />
    <hkern g1="f_f" u2="&#x12d;" k="-74" />
    <hkern g1="f_f" u2="&#x12b;" k="-74" />
    <hkern g1="f_f" u2="&#x129;" k="-78" />
    <hkern g1="f_f" u2="&#xef;" k="-119" />
    <hkern g1="f_f" u2="&#xee;" k="-43" />
    <hkern g1="f_f" u2="&#xec;" k="-125" />
    <hkern g1="fi" u2="&#x149;" k="-12" />
    <hkern g1="fi" u2="&#xec;" k="-12" />
    <hkern g1="fl" u2="&#x149;" k="-57" />
    <hkern g1="f_f_i" u2="&#x149;" k="-12" />
    <hkern g1="f_f_i" u2="&#xec;" k="-16" />
    <hkern g1="f_f_l" u2="&#x149;" k="-57" />
    <hkern g1="f_j" u2="&#xec;" k="-16" />
    <hkern g1="parenleft.case" u2="&#x134;" k="-186" />
    <hkern g1="parenleft.case" u2="&#xce;" k="-43" />
    <hkern g1="parenleft.case" u2="J" k="-186" />
    <hkern g1="at.case" u2="&#xc6;" k="66" />
    <hkern g1="at.case" u2="Y" k="29" />
    <hkern g1="bracketleft.case" u2="&#x134;" k="-178" />
    <hkern g1="bracketleft.case" u2="&#xce;" k="-37" />
    <hkern g1="bracketleft.case" u2="J" k="-178" />
    <hkern g1="braceleft.case" u2="&#x134;" k="-184" />
    <hkern g1="braceleft.case" u2="&#xce;" k="-43" />
    <hkern g1="braceleft.case" u2="J" k="-184" />
    <hkern g1="questiondown.case" u2="V" k="27" />
    <hkern g1="zero.oldstyle" g2="four.oldstyle" k="63" />
    <hkern g1="zero.oldstyle" g2="three.oldstyle" k="23" />
    <hkern g1="zero.oldstyle" u2="&#x2044;" k="-113" />
    <hkern g1="zero.oldstyle" u2="&#xb0;" k="35" />
    <hkern g1="zero.oldstyle" u2="&#x7d;" k="86" />
    <hkern g1="zero.oldstyle" u2="]" k="66" />
    <hkern g1="zero.oldstyle" u2="\" k="84" />
    <hkern g1="zero.oldstyle" u2="&#x29;" k="86" />
    <hkern g1="one.oldstyle" u2="&#x2044;" k="-260" />
    <hkern g1="one.oldstyle" u2="&#x7d;" k="45" />
    <hkern g1="one.oldstyle" u2="]" k="35" />
    <hkern g1="one.oldstyle" u2="\" k="33" />
    <hkern g1="one.oldstyle" u2="&#x29;" k="43" />
    <hkern g1="two.oldstyle" u2="&#x2044;" k="-291" />
    <hkern g1="two.oldstyle" u2="&#x7d;" k="90" />
    <hkern g1="two.oldstyle" u2="]" k="53" />
    <hkern g1="two.oldstyle" u2="\" k="61" />
    <hkern g1="two.oldstyle" u2="&#x29;" k="84" />
    <hkern g1="three.oldstyle" u2="&#x2044;" k="-348" />
    <hkern g1="three.oldstyle" u2="&#xb0;" k="61" />
    <hkern g1="three.oldstyle" u2="&#x7d;" k="23" />
    <hkern g1="three.oldstyle" u2="]" k="20" />
    <hkern g1="three.oldstyle" u2="\" k="109" />
    <hkern g1="three.oldstyle" u2="&#x29;" k="27" />
    <hkern g1="four.oldstyle" g2="one.oldstyle" k="27" />
    <hkern g1="four.oldstyle" u2="&#x2212;" k="33" />
    <hkern g1="four.oldstyle" u2="&#x2044;" k="-362" />
    <hkern g1="four.oldstyle" u2="&#xb7;" k="43" />
    <hkern g1="four.oldstyle" u2="&#xb0;" k="45" />
    <hkern g1="four.oldstyle" u2="&#x7d;" k="49" />
    <hkern g1="four.oldstyle" u2="]" k="41" />
    <hkern g1="four.oldstyle" u2="\" k="109" />
    <hkern g1="four.oldstyle" u2="&#x2d;" k="35" />
    <hkern g1="four.oldstyle" u2="&#x2b;" k="37" />
    <hkern g1="four.oldstyle" u2="&#x29;" k="47" />
    <hkern g1="five.oldstyle" u2="&#x2044;" k="-342" />
    <hkern g1="five.oldstyle" u2="&#x7d;" k="29" />
    <hkern g1="five.oldstyle" u2="]" k="23" />
    <hkern g1="five.oldstyle" u2="\" k="68" />
    <hkern g1="five.oldstyle" u2="&#x29;" k="31" />
    <hkern g1="six.oldstyle" g2="four.oldstyle" k="51" />
    <hkern g1="six.oldstyle" g2="three.oldstyle" k="27" />
    <hkern g1="six.oldstyle" u2="&#x2044;" k="-141" />
    <hkern g1="seven.oldstyle" g2="four.oldstyle" k="119" />
    <hkern g1="seven.oldstyle" u2="&#x2044;" k="-18" />
    <hkern g1="seven.oldstyle" u2="&#x7d;" k="29" />
    <hkern g1="seven.oldstyle" u2="]" k="29" />
    <hkern g1="seven.oldstyle" u2="&#x2f;" k="51" />
    <hkern g1="seven.oldstyle" u2="&#x29;" k="31" />
    <hkern g1="eight.oldstyle" g2="four.oldstyle" k="53" />
    <hkern g1="eight.oldstyle" g2="three.oldstyle" k="31" />
    <hkern g1="eight.oldstyle" u2="&#x2044;" k="-131" />
    <hkern g1="eight.oldstyle" u2="&#x7d;" k="49" />
    <hkern g1="eight.oldstyle" u2="]" k="35" />
    <hkern g1="eight.oldstyle" u2="\" k="35" />
    <hkern g1="eight.oldstyle" u2="&#x29;" k="51" />
    <hkern g1="nine.oldstyle" u2="&#x2044;" k="-295" />
    <hkern g1="nine.oldstyle" u2="&#xb0;" k="59" />
    <hkern g1="nine.oldstyle" u2="&#x7d;" k="53" />
    <hkern g1="nine.oldstyle" u2="]" k="41" />
    <hkern g1="nine.oldstyle" u2="\" k="100" />
    <hkern g1="nine.oldstyle" u2="&#x29;" k="55" />
    <hkern g1="ampersand.sc" g2="V.sc" k="27" />
    <hkern g1="B.sc" g2="X.sc" k="14" />
    <hkern g1="B.sc" g2="V.sc" k="25" />
    <hkern g1="B.sc" u2="&#x2122;" k="39" />
    <hkern g1="B.sc" u2="&#x7d;" k="78" />
    <hkern g1="B.sc" u2="]" k="55" />
    <hkern g1="B.sc" u2="\" k="55" />
    <hkern g1="B.sc" u2="&#x29;" k="76" />
    <hkern g1="D.sc" g2="Hbar.sc" k="12" />
    <hkern g1="D.sc" g2="Lslash.sc" k="12" />
    <hkern g1="F.sc" g2="M.sc" k="12" />
    <hkern g1="F.sc" u2="&#x2f;" k="59" />
    <hkern g1="F.sc" u2="&#x20;" k="41" />
    <hkern g1="L.sc" u2="&#xb7;" k="410" />
    <hkern g1="M.sc" g2="V.sc" k="25" />
    <hkern g1="M.sc" u2="&#x2122;" k="31" />
    <hkern g1="M.sc" u2="&#x7d;" k="57" />
    <hkern g1="M.sc" u2="]" k="41" />
    <hkern g1="M.sc" u2="\" k="45" />
    <hkern g1="M.sc" u2="&#x29;" k="55" />
    <hkern g1="O.sc" g2="Hbar.sc" k="12" />
    <hkern g1="O.sc" g2="Lslash.sc" k="12" />
    <hkern g1="P.sc" g2="X.sc" k="27" />
    <hkern g1="P.sc" g2="M.sc" k="8" />
    <hkern g1="P.sc" u2="&#x2122;" k="18" />
    <hkern g1="P.sc" u2="&#x7d;" k="49" />
    <hkern g1="P.sc" u2="]" k="39" />
    <hkern g1="P.sc" u2="\" k="20" />
    <hkern g1="P.sc" u2="&#x2f;" k="57" />
    <hkern g1="P.sc" u2="&#x29;" k="49" />
    <hkern g1="P.sc" u2="&#x20;" k="47" />
    <hkern g1="Q.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Q.sc" g2="Lslash.sc" k="12" />
    <hkern g1="V.sc" g2="M.sc" k="25" />
    <hkern g1="V.sc" g2="ampersand.sc" k="20" />
    <hkern g1="V.sc" u2="&#x2f;" k="63" />
    <hkern g1="V.sc" u2="&#x20;" k="51" />
    <hkern g1="Y.sc" u2="&#x40;" k="33" />
    <hkern g1="questiondown.sc" g2="V.sc" k="20" />
    <hkern g1="Icircumflex.sc" u2="&#x2122;" k="-35" />
    <hkern g1="Icircumflex.sc" u2="&#x7d;" k="-18" />
    <hkern g1="Icircumflex.sc" u2="]" k="-14" />
    <hkern g1="Icircumflex.sc" u2="&#x29;" k="-18" />
    <hkern g1="Idieresis.sc" u2="&#x29;" k="14" />
    <hkern g1="Eth.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Eth.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Oacute.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Thorn.sc" g2="X.sc" k="41" />
    <hkern g1="Thorn.sc" g2="V.sc" k="23" />
    <hkern g1="Thorn.sc" u2="&#x2122;" k="39" />
    <hkern g1="Thorn.sc" u2="&#x7d;" k="76" />
    <hkern g1="Thorn.sc" u2="]" k="51" />
    <hkern g1="Thorn.sc" u2="\" k="61" />
    <hkern g1="Thorn.sc" u2="&#x29;" k="78" />
    <hkern g1="Aogonek.sc" g2="J.sc" k="-211" />
    <hkern g1="Aogonek.sc" u2="&#x201e;" k="-78" />
    <hkern g1="Aogonek.sc" u2="&#x201a;" k="-80" />
    <hkern g1="Aogonek.sc" u2="&#x7d;" k="10" />
    <hkern g1="Aogonek.sc" u2="]" k="14" />
    <hkern g1="Aogonek.sc" u2="&#x3b;" k="-55" />
    <hkern g1="Aogonek.sc" u2="&#x2c;" k="-68" />
    <hkern g1="Aogonek.sc" u2="&#x29;" k="10" />
    <hkern g1="Eogonek.sc" g2="J.sc" k="-248" />
    <hkern g1="Eogonek.sc" u2="&#x201e;" k="-115" />
    <hkern g1="Eogonek.sc" u2="&#x201a;" k="-117" />
    <hkern g1="Eogonek.sc" u2="&#x7d;" k="-20" />
    <hkern g1="Eogonek.sc" u2="]" k="-27" />
    <hkern g1="Eogonek.sc" u2="&#x3b;" k="-92" />
    <hkern g1="Eogonek.sc" u2="&#x2c;" k="-104" />
    <hkern g1="Eogonek.sc" u2="&#x29;" k="-20" />
    <hkern g1="Hbar.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Gdotaccent.sc" k="12" />
    <hkern g1="Hbar.sc" g2="OE.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Y.sc" k="16" />
    <hkern g1="Hbar.sc" g2="Q.sc" k="12" />
    <hkern g1="Hbar.sc" g2="O.sc" k="12" />
    <hkern g1="Hbar.sc" g2="G.sc" k="12" />
    <hkern g1="Hbar.sc" g2="C.sc" k="12" />
    <hkern g1="Hbar.sc" u2="&#x7d;" k="41" />
    <hkern g1="Hbar.sc" u2="&#x2a;" k="-12" />
    <hkern g1="Hbar.sc" u2="&#x29;" k="41" />
    <hkern g1="Iogonek.sc" g2="J.sc" k="-86" />
    <hkern g1="Jcircumflex.sc" u2="&#x2122;" k="-29" />
    <hkern g1="Jcircumflex.sc" u2="&#x7d;" k="-12" />
    <hkern g1="Jcircumflex.sc" u2="]" k="-8" />
    <hkern g1="Jcircumflex.sc" u2="&#x29;" k="-12" />
    <hkern g1="Lcaron.sc" g2="Tcommaaccent.sc.alt1" k="68" />
    <hkern g1="Lcaron.sc" g2="Tbar.sc.alt1" k="68" />
    <hkern g1="Lcaron.sc" g2="Tcaron.sc.alt1" k="68" />
    <hkern g1="Lcaron.sc" g2="T.sc.alt1" k="68" />
    <hkern g1="Lcaron.sc" g2="Y.sc" k="45" />
    <hkern g1="Lcaron.sc" g2="W.sc" k="57" />
    <hkern g1="Lcaron.sc" g2="V.sc" k="80" />
    <hkern g1="Lcaron.sc" g2="T.sc" k="68" />
    <hkern g1="Lcaron.sc" u2="&#x2a;" k="53" />
    <hkern g1="Lcaron.sc" u2="&#x20;" k="68" />
    <hkern g1="seven.numr" u2="&#x2044;" k="72" />
    <hkern g1="l.alt1" u2="&#xb7;" k="143" />
    <hkern g1="lslash.alt1" u2="x" k="-33" />
    <hkern g1="lslash.alt1" u2="&#x2a;" k="-31" />
    <hkern g1="lcaron.alt1" g2="l.alt1" k="-106" />
    <hkern g1="lcaron.alt1" u2="&#x2122;" k="-178" />
    <hkern g1="lcaron.alt1" u2="&#x201c;" k="-12" />
    <hkern g1="lcaron.alt1" u2="&#x2018;" k="-12" />
    <hkern g1="lcaron.alt1" u2="&#x17e;" k="-68" />
    <hkern g1="lcaron.alt1" u2="&#x161;" k="-117" />
    <hkern g1="lcaron.alt1" u2="&#x133;" k="-119" />
    <hkern g1="lcaron.alt1" u2="&#xfe;" k="-96" />
    <hkern g1="lcaron.alt1" u2="&#xdf;" k="-20" />
    <hkern g1="lcaron.alt1" u2="&#x7d;" k="-166" />
    <hkern g1="lcaron.alt1" u2="&#x7c;" k="-86" />
    <hkern g1="lcaron.alt1" u2="l" k="-96" />
    <hkern g1="lcaron.alt1" u2="k" k="-96" />
    <hkern g1="lcaron.alt1" u2="j" k="-117" />
    <hkern g1="lcaron.alt1" u2="i" k="-119" />
    <hkern g1="lcaron.alt1" u2="h" k="-96" />
    <hkern g1="lcaron.alt1" u2="b" k="-96" />
    <hkern g1="lcaron.alt1" u2="]" k="-162" />
    <hkern g1="lcaron.alt1" u2="\" k="-178" />
    <hkern g1="lcaron.alt1" u2="&#x3f;" k="-70" />
    <hkern g1="lcaron.alt1" u2="&#x2a;" k="-117" />
    <hkern g1="lcaron.alt1" u2="&#x29;" k="-168" />
    <hkern g1="lcaron.alt1" u2="&#x27;" k="-102" />
    <hkern g1="lcaron.alt1" u2="&#x22;" k="-102" />
    <hkern g1="lcaron.alt1" u2="&#x21;" k="-94" />
    <hkern g1="aogonek.alt1" u2="&#x7d;" k="57" />
    <hkern g1="aogonek.alt1" u2="j" k="-129" />
    <hkern g1="aogonek.alt1" u2="&#x29;" k="57" />
    <hkern g1="tcaron.alt1" g2="l.alt1" k="-23" />
    <hkern g1="tcaron.alt1" u2="&#x2122;" k="-94" />
    <hkern g1="tcaron.alt1" u2="&#x161;" k="-33" />
    <hkern g1="tcaron.alt1" u2="&#x133;" k="-27" />
    <hkern g1="tcaron.alt1" u2="&#xfe;" k="-12" />
    <hkern g1="tcaron.alt1" u2="&#x7d;" k="-82" />
    <hkern g1="tcaron.alt1" u2="l" k="-12" />
    <hkern g1="tcaron.alt1" u2="k" k="-12" />
    <hkern g1="tcaron.alt1" u2="j" k="-25" />
    <hkern g1="tcaron.alt1" u2="i" k="-27" />
    <hkern g1="tcaron.alt1" u2="h" k="-12" />
    <hkern g1="tcaron.alt1" u2="b" k="-12" />
    <hkern g1="tcaron.alt1" u2="]" k="-78" />
    <hkern g1="tcaron.alt1" u2="\" k="-94" />
    <hkern g1="tcaron.alt1" u2="&#x2a;" k="-39" />
    <hkern g1="tcaron.alt1" u2="&#x29;" k="-86" />
    <hkern g1="tcaron.alt1" u2="&#x27;" k="-18" />
    <hkern g1="tcaron.alt1" u2="&#x22;" k="-18" />
    <hkern g1="space"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="space"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="51" />
    <hkern g1="space"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="47" />
    <hkern g1="space"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="space"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="57" />
    <hkern g1="space"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="84" />
    <hkern g1="space"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="63" />
    <hkern g1="space"
  g2="AE,AEacute"
  k="76" />
    <hkern g1="space"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="space"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="51" />
    <hkern g1="space"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="53" />
    <hkern g1="space"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="66" />
    <hkern g1="space"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="space"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="51" />
    <hkern g1="space"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="39" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="68" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="55" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="111" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="150" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="98" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="76" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="74" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="123" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="66" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="41" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="v"
  k="68" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="39" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V"
  k="100" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quotedbl,quotesingle"
  k="233" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one"
  k="49" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteright,quotedblright"
  k="242" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteleft,quotedblleft"
  k="246" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one.oldstyle"
  k="59" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="three.oldstyle"
  k="27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="seven.oldstyle"
  k="72" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="16" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V.sc"
  k="72" />
    <hkern g1="colon,semicolon"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="84" />
    <hkern g1="colon,semicolon"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="82" />
    <hkern g1="colon,semicolon"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="colon,semicolon"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="colon,semicolon"
  g2="V"
  k="29" />
    <hkern g1="quotedbl,quotesingle"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE,AEacute"
  k="139" />
    <hkern g1="quotedbl,quotesingle"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="63" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE.sc,AEacute.sc"
  k="137" />
    <hkern g1="quotedbl,quotesingle"
  g2="three.oldstyle"
  k="25" />
    <hkern g1="quotedbl,quotesingle"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="quotedbl,quotesingle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="233" />
    <hkern g1="quotedbl,quotesingle"
  g2="four"
  k="80" />
    <hkern g1="quotedbl,quotesingle"
  g2="slash"
  k="94" />
    <hkern g1="quotedbl,quotesingle"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="quotedbl,quotesingle"
  g2="four.oldstyle"
  k="143" />
    <hkern g1="quotedbl,quotesingle"
  g2="five.oldstyle"
  k="29" />
    <hkern g1="quotedbl,quotesingle"
  g2="nine.oldstyle"
  k="25" />
    <hkern g1="exclamdown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="119" />
    <hkern g1="exclamdown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="104" />
    <hkern g1="exclamdown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="31" />
    <hkern g1="exclamdown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="47" />
    <hkern g1="bracketleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="35" />
    <hkern g1="bracketleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="37" />
    <hkern g1="bracketleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-14" />
    <hkern g1="bracketleft"
  g2="AE,AEacute"
  k="43" />
    <hkern g1="bracketleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="51" />
    <hkern g1="bracketleft"
  g2="AE.sc,AEacute.sc"
  k="49" />
    <hkern g1="bracketleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="bracketleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="43" />
    <hkern g1="bracketleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="61" />
    <hkern g1="bracketleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="72" />
    <hkern g1="bracketleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="59" />
    <hkern g1="bracketleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="51" />
    <hkern g1="bracketleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="51" />
    <hkern g1="bracketleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="47" />
    <hkern g1="bracketleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="20" />
    <hkern g1="bracketleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="39" />
    <hkern g1="bracketleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="bracketleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="59" />
    <hkern g1="slash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="102" />
    <hkern g1="slash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-31" />
    <hkern g1="slash"
  g2="AE,AEacute"
  k="170" />
    <hkern g1="slash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="123" />
    <hkern g1="slash"
  g2="AE.sc,AEacute.sc"
  k="182" />
    <hkern g1="slash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="slash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="slash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="68" />
    <hkern g1="slash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="98" />
    <hkern g1="slash"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="slash"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="55" />
    <hkern g1="slash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="49" />
    <hkern g1="slash"
  g2="z,zacute,zdotaccent,zcaron"
  k="49" />
    <hkern g1="slash"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="37" />
    <hkern g1="slash"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="88" />
    <hkern g1="backslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="51" />
    <hkern g1="backslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="backslash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="-16" />
    <hkern g1="backslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="102" />
    <hkern g1="backslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="143" />
    <hkern g1="backslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="88" />
    <hkern g1="backslash"
  g2="AE,AEacute"
  k="-45" />
    <hkern g1="backslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="61" />
    <hkern g1="backslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="63" />
    <hkern g1="backslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="106" />
    <hkern g1="backslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="51" />
    <hkern g1="backslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="backslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="backslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="backslash"
  g2="quotedbl,quotesingle"
  k="94" />
    <hkern g1="hyphen,endash,emdash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="hyphen,endash,emdash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="hyphen,endash,emdash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="106" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="139" />
    <hkern g1="hyphen,endash,emdash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="51" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE,AEacute"
  k="16" />
    <hkern g1="hyphen,endash,emdash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="74" />
    <hkern g1="hyphen,endash,emdash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="100" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE.sc,AEacute.sc"
  k="33" />
    <hkern g1="hyphen,endash,emdash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="hyphen,endash,emdash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="hyphen,endash,emdash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="hyphen,endash,emdash"
  g2="v"
  k="20" />
    <hkern g1="hyphen,endash,emdash"
  g2="V"
  k="55" />
    <hkern g1="hyphen,endash,emdash"
  g2="V.sc"
  k="33" />
    <hkern g1="hyphen,endash,emdash"
  g2="z,zacute,zdotaccent,zcaron"
  k="18" />
    <hkern g1="hyphen,endash,emdash"
  g2="x"
  k="43" />
    <hkern g1="hyphen,endash,emdash"
  g2="X"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="X.sc"
  k="41" />
    <hkern g1="hyphen,endash,emdash"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="registered"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="27" />
    <hkern g1="registered"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="66" />
    <hkern g1="registered"
  g2="AE,AEacute"
  k="51" />
    <hkern g1="registered"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="20" />
    <hkern g1="registered"
  g2="AE.sc,AEacute.sc"
  k="57" />
    <hkern g1="ampersand"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="78" />
    <hkern g1="ampersand"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="92" />
    <hkern g1="ampersand"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="29" />
    <hkern g1="ampersand"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="41" />
    <hkern g1="quoteright,quotedblright"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="90" />
    <hkern g1="quoteright,quotedblright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-10" />
    <hkern g1="quoteright,quotedblright"
  g2="AE,AEacute"
  k="176" />
    <hkern g1="quoteright,quotedblright"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="94" />
    <hkern g1="quoteright,quotedblright"
  g2="AE.sc,AEacute.sc"
  k="170" />
    <hkern g1="quoteright,quotedblright"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="45" />
    <hkern g1="quoteright,quotedblright"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="78" />
    <hkern g1="quoteright,quotedblright"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="291" />
    <hkern g1="quoteright,quotedblright"
  g2="guillemotleft,guilsinglleft"
  k="82" />
    <hkern g1="quoteright,quotedblright"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="55" />
    <hkern g1="quoteright,quotedblright"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="27" />
    <hkern g1="quoteright,quotedblright"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="23" />
    <hkern g1="quoteright,quotedblright"
  g2="z,zacute,zdotaccent,zcaron"
  k="18" />
    <hkern g1="quoteright,quotedblright"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="quoteright,quotedblright"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="57" />
    <hkern g1="quoteright,quotedblright"
  g2="M"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="hyphen,endash,emdash"
  k="68" />
    <hkern g1="quoteright,quotedblright"
  g2="M.sc"
  k="20" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="90" />
    <hkern g1="quoteleft,quotedblleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-27" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE,AEacute"
  k="174" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="94" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE.sc,AEacute.sc"
  k="168" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="68" />
    <hkern g1="quoteleft,quotedblleft"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="276" />
    <hkern g1="quoteleft,quotedblleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="47" />
    <hkern g1="quoteleft,quotedblleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="quoteleft,quotedblleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="18" />
    <hkern g1="quoteleft,quotedblleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="49" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M"
  k="20" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M.sc"
  k="25" />
    <hkern g1="trademark"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="39" />
    <hkern g1="trademark"
  g2="AE,AEacute"
  k="98" />
    <hkern g1="trademark"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="39" />
    <hkern g1="trademark"
  g2="AE.sc,AEacute.sc"
  k="96" />
    <hkern g1="braceleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="braceleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="37" />
    <hkern g1="braceleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="80" />
    <hkern g1="braceleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-18" />
    <hkern g1="braceleft"
  g2="AE,AEacute"
  k="92" />
    <hkern g1="braceleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="96" />
    <hkern g1="braceleft"
  g2="AE.sc,AEacute.sc"
  k="104" />
    <hkern g1="braceleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="braceleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="braceleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="86" />
    <hkern g1="braceleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="braceleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="braceleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="66" />
    <hkern g1="braceleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="63" />
    <hkern g1="braceleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="61" />
    <hkern g1="braceleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="29" />
    <hkern g1="braceleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="53" />
    <hkern g1="braceleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="29" />
    <hkern g1="braceleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="84" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="123" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="96" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="33" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="V"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="139" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="158" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="68" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE,AEacute"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="74" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="47" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="115" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE.sc,AEacute.sc"
  k="39" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="20" />
    <hkern g1="guillemotright,guilsinglright"
  g2="v"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V"
  k="72" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quotedbl,quotesingle"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quoteright,quotedblright"
  k="33" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V.sc"
  k="47" />
    <hkern g1="guillemotright,guilsinglright"
  g2="z,zacute,zdotaccent,zcaron"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="x"
  k="41" />
    <hkern g1="guillemotright,guilsinglright"
  g2="X.sc"
  k="45" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="questiondown"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="questiondown"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="29" />
    <hkern g1="questiondown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="133" />
    <hkern g1="questiondown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="145" />
    <hkern g1="questiondown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="78" />
    <hkern g1="questiondown"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="41" />
    <hkern g1="questiondown"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="53" />
    <hkern g1="questiondown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="90" />
    <hkern g1="questiondown"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="27" />
    <hkern g1="questiondown"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="questiondown"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="39" />
    <hkern g1="questiondown"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="35" />
    <hkern g1="questiondown"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="45" />
    <hkern g1="questiondown"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="29" />
    <hkern g1="questiondown"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="35" />
    <hkern g1="questiondown"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="35" />
    <hkern g1="questiondown"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="questiondown"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="20" />
    <hkern g1="questiondown"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="35" />
    <hkern g1="asterisk"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="61" />
    <hkern g1="asterisk"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="-33" />
    <hkern g1="asterisk"
  g2="AE,AEacute"
  k="133" />
    <hkern g1="asterisk"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="63" />
    <hkern g1="asterisk"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="-41" />
    <hkern g1="asterisk"
  g2="AE.sc,AEacute.sc"
  k="135" />
    <hkern g1="asterisk"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="29" />
    <hkern g1="asterisk"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="16" />
    <hkern g1="asterisk"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="parenleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="parenleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="39" />
    <hkern g1="parenleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="parenleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="-18" />
    <hkern g1="parenleft"
  g2="AE,AEacute"
  k="72" />
    <hkern g1="parenleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="80" />
    <hkern g1="parenleft"
  g2="AE.sc,AEacute.sc"
  k="82" />
    <hkern g1="parenleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="parenleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="59" />
    <hkern g1="parenleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="88" />
    <hkern g1="parenleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="parenleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="80" />
    <hkern g1="parenleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="63" />
    <hkern g1="parenleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="63" />
    <hkern g1="parenleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="59" />
    <hkern g1="parenleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="29" />
    <hkern g1="parenleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="55" />
    <hkern g1="parenleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="29" />
    <hkern g1="parenleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="82" />
    <hkern g1="ampersand.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="47" />
    <hkern g1="ampersand.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="ampersand.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="80" />
    <hkern g1="questiondown.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE.sc,AEacute.sc"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="v"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="V"
  k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="x"
  k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="X"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="parenright.case"
  k="55" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="bracketright.case"
  k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright.case"
  k="57" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="41" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="106" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="129" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="66" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="96" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="v"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quotedbl,quotesingle"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteright,quotedblright"
  k="59" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteleft,quotedblleft"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V.sc"
  k="45" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="slash"
  k="-16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="space"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="bracketright"
  k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="backslash"
  k="102" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="trademark"
  k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright"
  k="80" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="asterisk"
  k="61" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="hyphen.case,endash.case,emdash.case"
  k="29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="49" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="37" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="84" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE,AEacute"
  k="66" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="29" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="18" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE.sc,AEacute.sc"
  k="74" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V"
  k="33" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V.sc"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="slash"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="x"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X"
  k="49" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X.sc"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright.case"
  k="102" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright.case"
  k="57" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright.case"
  k="100" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright"
  k="41" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="backslash"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="trademark"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright"
  k="59" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright"
  k="61" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="29" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="29" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="20" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="v"
  k="29" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="25" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="41" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="131" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="170" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="104" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="88" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="92" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="164" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="96" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="70" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="47" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="v"
  k="102" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V"
  k="113" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quotedbl,quotesingle"
  k="150" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteright,quotedblright"
  k="150" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteleft,quotedblleft"
  k="150" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V.sc"
  k="94" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="space"
  k="57" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="bracketright"
  k="45" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="backslash"
  k="147" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="trademark"
  k="150" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="braceright"
  k="94" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="asterisk"
  k="150" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="parenright"
  k="78" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen.case,endash.case,emdash.case"
  k="147" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft.case,guilsinglleft.case"
  k="88" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="registered"
  k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="parenright.case"
  k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="braceright.case"
  k="27" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="trademark"
  k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="asterisk"
  k="-43" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="29" />
    <hkern g1="F"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="F"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="F"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="F"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="86" />
    <hkern g1="F"
  g2="AE,AEacute"
  k="143" />
    <hkern g1="F"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="92" />
    <hkern g1="F"
  g2="AE.sc,AEacute.sc"
  k="190" />
    <hkern g1="F"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="F"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="F"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="20" />
    <hkern g1="F"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="F"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="F"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="72" />
    <hkern g1="F"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="129" />
    <hkern g1="F"
  g2="guillemotleft,guilsinglleft"
  k="39" />
    <hkern g1="F"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="66" />
    <hkern g1="F"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="61" />
    <hkern g1="F"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="53" />
    <hkern g1="F"
  g2="z,zacute,zdotaccent,zcaron"
  k="55" />
    <hkern g1="F"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="F"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="82" />
    <hkern g1="F"
  g2="hyphen,endash,emdash"
  k="18" />
    <hkern g1="F"
  g2="colon,semicolon"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="76" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE,AEacute"
  k="59" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="14" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE.sc,AEacute.sc"
  k="66" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V"
  k="31" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V.sc"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="33" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="slash"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="x"
  k="27" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X"
  k="45" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X.sc"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright.case"
  k="55" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright"
  k="35" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="backslash"
  k="23" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="trademark"
  k="18" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright"
  k="51" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright"
  k="53" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="B"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="B"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="B"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="B"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="29" />
    <hkern g1="B"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="59" />
    <hkern g1="B"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="18" />
    <hkern g1="B"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="B"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="B"
  g2="AE.sc,AEacute.sc"
  k="23" />
    <hkern g1="B"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="B"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="B"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="23" />
    <hkern g1="B"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="P"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="70" />
    <hkern g1="P"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="27" />
    <hkern g1="P"
  g2="AE,AEacute"
  k="137" />
    <hkern g1="P"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="90" />
    <hkern g1="P"
  g2="AE.sc,AEacute.sc"
  k="180" />
    <hkern g1="P"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="35" />
    <hkern g1="P"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="141" />
    <hkern g1="P"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="P"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="P"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="P"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="29" />
    <hkern g1="P"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="P"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="37" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="V"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="41" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t,tcommaaccent,tcaron,tbar"
  k="49" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="156" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="147" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="106" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE,AEacute"
  k="158" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="125" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE.sc,AEacute.sc"
  k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="156" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="80" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="76" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="v"
  k="156" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="37" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="100" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="166" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="111" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="slash"
  k="102" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft,guilsinglleft"
  k="139" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="152" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="154" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="152" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="z,zacute,zdotaccent,zcaron"
  k="158" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="55" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="156" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="x"
  k="152" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M"
  k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen,endash,emdash"
  k="106" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M.sc"
  k="41" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="space"
  k="57" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen.case,endash.case,emdash.case"
  k="102" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft.case,guilsinglleft.case"
  k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="registered"
  k="23" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="colon,semicolon"
  k="84" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotright,guilsinglright"
  k="123" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="39" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="39" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="v"
  k="39" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen.case,endash.case,emdash.case"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="66" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="74" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE,AEacute"
  k="33" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE.sc,AEacute.sc"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V.sc"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="x"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X.sc"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright.case"
  k="78" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright.case"
  k="43" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright.case"
  k="80" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="backslash"
  k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright"
  k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright"
  k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="16" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="18" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE,AEacute"
  k="45" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="29" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE.sc,AEacute.sc"
  k="63" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="27" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="slash"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="14" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="z,zacute,zdotaccent,zcaron"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="x"
  k="25" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="K,Kcommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="31" />
    <hkern g1="K,Kcommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="80" />
    <hkern g1="K,Kcommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="80" />
    <hkern g1="K,Kcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="31" />
    <hkern g1="K,Kcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="K,Kcommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="82" />
    <hkern g1="K,Kcommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="78" />
    <hkern g1="K,Kcommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="37" />
    <hkern g1="K,Kcommaaccent"
  g2="v"
  k="80" />
    <hkern g1="K,Kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="88" />
    <hkern g1="K,Kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="K,Kcommaaccent"
  g2="quoteright,quotedblright"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="quoteleft,quotedblleft"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="76" />
    <hkern g1="K,Kcommaaccent"
  g2="V.sc"
  k="31" />
    <hkern g1="K,Kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="39" />
    <hkern g1="K,Kcommaaccent"
  g2="slash"
  k="-47" />
    <hkern g1="K,Kcommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="39" />
    <hkern g1="K,Kcommaaccent"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="space"
  k="25" />
    <hkern g1="K,Kcommaaccent"
  g2="asterisk"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen.case,endash.case,emdash.case"
  k="70" />
    <hkern g1="K,Kcommaaccent"
  g2="guillemotleft.case,guilsinglleft.case"
  k="104" />
    <hkern g1="K,Kcommaaccent"
  g2="registered"
  k="31" />
    <hkern g1="M"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="M"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="M"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="M"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="25" />
    <hkern g1="M"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="45" />
    <hkern g1="M"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="M"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="10" />
    <hkern g1="M"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="M"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="M"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="M"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="18" />
    <hkern g1="M"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="16" />
    <hkern g1="M"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="V"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="V"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="V"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="V"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="V"
  g2="AE,AEacute"
  k="145" />
    <hkern g1="V"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="V"
  g2="AE.sc,AEacute.sc"
  k="141" />
    <hkern g1="V"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="V"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="29" />
    <hkern g1="V"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="27" />
    <hkern g1="V"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="33" />
    <hkern g1="V"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="55" />
    <hkern g1="V"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="98" />
    <hkern g1="V"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="100" />
    <hkern g1="V"
  g2="guillemotleft,guilsinglleft"
  k="74" />
    <hkern g1="V"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="100" />
    <hkern g1="V"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="74" />
    <hkern g1="V"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="74" />
    <hkern g1="V"
  g2="z,zacute,zdotaccent,zcaron"
  k="66" />
    <hkern g1="V"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="12" />
    <hkern g1="V"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="V"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="100" />
    <hkern g1="V"
  g2="hyphen,endash,emdash"
  k="55" />
    <hkern g1="V"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="18" />
    <hkern g1="V"
  g2="hyphen.case,endash.case,emdash.case"
  k="29" />
    <hkern g1="V"
  g2="guillemotleft.case,guilsinglleft.case"
  k="49" />
    <hkern g1="V"
  g2="colon,semicolon"
  k="29" />
    <hkern g1="V"
  g2="guillemotright,guilsinglright"
  k="25" />
    <hkern g1="X"
  g2="t,tcommaaccent,tcaron,tbar"
  k="41" />
    <hkern g1="X"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="88" />
    <hkern g1="X"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="82" />
    <hkern g1="X"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="16" />
    <hkern g1="X"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="X"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="14" />
    <hkern g1="X"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="88" />
    <hkern g1="X"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="80" />
    <hkern g1="X"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="51" />
    <hkern g1="X"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="51" />
    <hkern g1="X"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="51" />
    <hkern g1="X"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="45" />
    <hkern g1="X"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="X"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="10" />
    <hkern g1="X"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="39" />
    <hkern g1="X"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="X"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="X"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="X"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="X"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="18" />
    <hkern g1="X"
  g2="hyphen.case,endash.case,emdash.case"
  k="61" />
    <hkern g1="X"
  g2="guillemotleft.case,guilsinglleft.case"
  k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t,tcommaaccent,tcaron,tbar"
  k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="129" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE,AEacute"
  k="205" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE.sc,AEacute.sc"
  k="221" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="111" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="86" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="v"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="137" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="209" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="slash"
  k="143" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft,guilsinglleft"
  k="158" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="190" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="154" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="160" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="z,zacute,zdotaccent,zcaron"
  k="168" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="109" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="55" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="186" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="x"
  k="141" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="X.sc"
  k="18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="39" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M"
  k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen,endash,emdash"
  k="139" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M.sc"
  k="104" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="31" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="space"
  k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="bracketright"
  k="-14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="backslash"
  k="-33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="trademark"
  k="-41" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="braceright"
  k="-18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="parenright"
  k="-18" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen.case,endash.case,emdash.case"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft.case,guilsinglleft.case"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="registered"
  k="59" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="colon,semicolon"
  k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright,guilsinglright"
  k="94" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="ampersand"
  k="37" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright.case,guilsinglright.case"
  k="35" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE,AEacute"
  k="139" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="74" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE.sc,AEacute.sc"
  k="139" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="v"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="49" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="92" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="100" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="slash"
  k="88" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="68" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="92" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="72" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="z,zacute,zdotaccent,zcaron"
  k="63" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="92" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="x"
  k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen,endash,emdash"
  k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M.sc"
  k="41" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="space"
  k="63" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="trademark"
  k="-18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen.case,endash.case,emdash.case"
  k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft.case,guilsinglleft.case"
  k="39" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="colon,semicolon"
  k="20" />
    <hkern g1="Thorn"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="29" />
    <hkern g1="Thorn"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="37" />
    <hkern g1="Thorn"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="84" />
    <hkern g1="Thorn"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="Thorn"
  g2="AE,AEacute"
  k="78" />
    <hkern g1="Thorn"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="59" />
    <hkern g1="Thorn"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="29" />
    <hkern g1="d,dcaron,dslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="f,f_f"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="37" />
    <hkern g1="f,f_f"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="f,f_f"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="f,f_f"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="f,f_f"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="f,f_f"
  g2="hyphen,endash,emdash"
  k="33" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="166" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="209" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="92" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="AE,AEacute"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="v"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="V"
  k="98" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quotedbl,quotesingle"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteright,quotedblright"
  k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteleft,quotedblleft"
  k="31" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="x"
  k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="X"
  k="45" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="M"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="bracketright"
  k="72" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="backslash"
  k="98" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="trademark"
  k="57" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="braceright"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="asterisk"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="parenright"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="question"
  k="20" />
    <hkern g1="l,lacute,lcommaaccent,lcaron,lslash,fl,f_f_l"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="v"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="bracketright"
  k="51" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="backslash"
  k="68" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="trademark"
  k="47" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="braceright"
  k="66" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="parenright"
  k="66" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="156" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="203" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="92" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="AE,AEacute"
  k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="v"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="V"
  k="100" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteleft,quotedblleft"
  k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="x"
  k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="X"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="bracketright"
  k="61" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="backslash"
  k="90" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="trademark"
  k="55" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="braceright"
  k="88" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="asterisk"
  k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="parenright"
  k="88" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="k,kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="61" />
    <hkern g1="k,kcommaaccent"
  g2="guillemotleft,guilsinglleft"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="29" />
    <hkern g1="k,kcommaaccent"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="hyphen,endash,emdash"
  k="57" />
    <hkern g1="k,kcommaaccent"
  g2="bracketright"
  k="25" />
    <hkern g1="k,kcommaaccent"
  g2="backslash"
  k="35" />
    <hkern g1="k,kcommaaccent"
  g2="trademark"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="braceright"
  k="45" />
    <hkern g1="k,kcommaaccent"
  g2="parenright"
  k="39" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="v"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="x"
  k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="bracketright"
  k="59" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="backslash"
  k="84" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="trademark"
  k="55" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="braceright"
  k="82" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="asterisk"
  k="18" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="parenright"
  k="82" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="168" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="201" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="74" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="v"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="V"
  k="78" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quotedbl,quotesingle"
  k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteleft,quotedblleft"
  k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="bracketright"
  k="61" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="backslash"
  k="92" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="trademark"
  k="57" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="braceright"
  k="88" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="asterisk"
  k="25" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="parenright"
  k="84" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="37" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="hyphen,endash,emdash"
  k="33" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="space"
  k="41" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="bracketright"
  k="25" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="braceright"
  k="27" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="parenright"
  k="27" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="92" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="115" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="slash"
  k="72" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="8" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="hyphen,endash,emdash"
  k="33" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="space"
  k="47" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="bracketright"
  k="41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="trademark"
  k="23" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="braceright"
  k="41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="parenright"
  k="43" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="v"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="49" />
    <hkern g1="v"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="v"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="70" />
    <hkern g1="v"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="v"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="v"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="v"
  g2="hyphen,endash,emdash"
  k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="guillemotleft,guilsinglleft"
  k="20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="hyphen,endash,emdash"
  k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="bracketright"
  k="45" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="backslash"
  k="45" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="trademark"
  k="43" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="braceright"
  k="57" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="parenright"
  k="57" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="slash"
  k="53" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="hyphen,endash,emdash"
  k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="space"
  k="51" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="bracketright"
  k="31" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="trademark"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="braceright"
  k="31" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="parenright"
  k="37" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="39" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="55" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="slash"
  k="43" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="space"
  k="47" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="bracketright"
  k="37" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="trademark"
  k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="braceright"
  k="39" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="parenright"
  k="39" />
    <hkern g1="x"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="x"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="x"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="37" />
    <hkern g1="x"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="x"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="x"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="x"
  g2="hyphen,endash,emdash"
  k="43" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="154" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="154" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="72" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="14" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="V"
  k="74" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="X"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="bracketright"
  k="51" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="backslash"
  k="55" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="trademark"
  k="43" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="braceright"
  k="66" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="parenright"
  k="63" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="16" />
    <hkern g1="germandbls"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="germandbls"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="germandbls"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="germandbls"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="germandbls"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="16" />
    <hkern g1="germandbls"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="16" />
    <hkern g1="germandbls"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="12" />
    <hkern g1="germandbls"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="germandbls"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="germandbls"
  g2="quoteleft,quotedblleft"
  k="16" />
    <hkern g1="germandbls"
  g2="z,zacute,zdotaccent,zcaron"
  k="8" />
    <hkern g1="germandbls"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="18" />
    <hkern g1="eth"
  g2="t,tcommaaccent,tcaron,tbar"
  k="12" />
    <hkern g1="eth"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="eth"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="16" />
    <hkern g1="eth"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="eth"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="12" />
    <hkern g1="eth"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="eth"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="eth"
  g2="quotedbl,quotesingle"
  k="35" />
    <hkern g1="eth"
  g2="quoteright,quotedblright"
  k="37" />
    <hkern g1="eth"
  g2="quoteleft,quotedblleft"
  k="41" />
    <hkern g1="eth"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="eth"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="18" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="88" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="59" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="115" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quotedbl,quotesingle"
  k="63" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteright,quotedblright"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteleft,quotedblleft"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="V.sc"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="16" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="space"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="bracketright"
  k="51" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="backslash"
  k="123" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="trademark"
  k="90" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="braceright"
  k="96" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="asterisk"
  k="63" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="parenright"
  k="80" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="registered"
  k="20" />
    <hkern g1="B.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="10" />
    <hkern g1="B.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="20" />
    <hkern g1="B.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="B.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="57" />
    <hkern g1="B.sc"
  g2="AE.sc,AEacute.sc"
  k="18" />
    <hkern g1="B.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="8" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="braceright"
  k="23" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="asterisk"
  k="-14" />
    <hkern g1="C.sc,Ccedilla.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc"
  g2="parenright"
  k="23" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="23" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="68" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="AE.sc,AEacute.sc"
  k="49" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="X.sc"
  k="31" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="14" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="bracketright"
  k="59" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="backslash"
  k="63" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="trademark"
  k="41" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="braceright"
  k="82" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="parenright"
  k="82" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="12" />
    <hkern g1="F.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="68" />
    <hkern g1="F.sc"
  g2="AE.sc,AEacute.sc"
  k="113" />
    <hkern g1="F.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="10" />
    <hkern g1="F.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="80" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="31" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="66" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="27" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="V.sc"
  k="25" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="X.sc"
  k="18" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="bracketright"
  k="49" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="backslash"
  k="59" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="trademark"
  k="39" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="braceright"
  k="76" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="parenright"
  k="76" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="bracketright"
  k="20" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="braceright"
  k="29" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="parenright"
  k="29" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="72" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="slash"
  k="-16" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="43" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="33" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="39" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="92" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="96" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="139" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quotedbl,quotesingle"
  k="104" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="100" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="100" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="29" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="V.sc"
  k="100" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="14" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="80" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="space"
  k="55" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="bracketright"
  k="47" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="backslash"
  k="139" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="trademark"
  k="127" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="braceright"
  k="94" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="asterisk"
  k="104" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="parenright"
  k="80" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="registered"
  k="27" />
    <hkern g1="M.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="18" />
    <hkern g1="M.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="M.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="43" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="29" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="25" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="76" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="AE.sc,AEacute.sc"
  k="55" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="16" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="X.sc"
  k="35" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="18" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="bracketright"
  k="59" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="backslash"
  k="68" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="trademark"
  k="43" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="braceright"
  k="86" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="parenright"
  k="86" />
    <hkern g1="P.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="55" />
    <hkern g1="P.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="25" />
    <hkern g1="P.sc"
  g2="AE.sc,AEacute.sc"
  k="115" />
    <hkern g1="P.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="94" />
    <hkern g1="P.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="V.sc"
  k="16" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="bracketright"
  k="43" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="backslash"
  k="41" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="trademark"
  k="31" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="braceright"
  k="61" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="parenright"
  k="57" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="14" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="41" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="V.sc"
  k="20" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="bracketright"
  k="43" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="backslash"
  k="45" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="trademark"
  k="39" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="braceright"
  k="59" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="parenright"
  k="59" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="88" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="AE.sc,AEacute.sc"
  k="115" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="29" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="76" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="slash"
  k="61" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotleft,guilsinglleft"
  k="74" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="hyphen,endash,emdash"
  k="74" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="M.sc"
  k="18" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="space"
  k="51" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="AE.sc,AEacute.sc"
  k="39" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="bracketright"
  k="20" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="braceright"
  k="29" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="parenright"
  k="29" />
    <hkern g1="V.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="V.sc"
  g2="AE.sc,AEacute.sc"
  k="129" />
    <hkern g1="V.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="29" />
    <hkern g1="V.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="72" />
    <hkern g1="V.sc"
  g2="guillemotleft,guilsinglleft"
  k="47" />
    <hkern g1="V.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="V.sc"
  g2="hyphen,endash,emdash"
  k="33" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="59" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="AE.sc,AEacute.sc"
  k="125" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="74" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="slash"
  k="63" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="guillemotleft,guilsinglleft"
  k="47" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="hyphen,endash,emdash"
  k="31" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="M.sc"
  k="23" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="space"
  k="53" />
    <hkern g1="X.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="X.sc"
  g2="guillemotleft,guilsinglleft"
  k="47" />
    <hkern g1="X.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="8" />
    <hkern g1="X.sc"
  g2="hyphen,endash,emdash"
  k="41" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="115" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="AE.sc,AEacute.sc"
  k="180" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="76" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="123" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="slash"
  k="104" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotleft,guilsinglleft"
  k="115" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="29" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="hyphen,endash,emdash"
  k="100" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="M.sc"
  k="43" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="space"
  k="68" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="colon,semicolon"
  k="37" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotright,guilsinglright"
  k="33" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="ampersand.sc"
  k="39" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="hyphen,endash,emdash"
  k="18" />
    <hkern g1="Thorn.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="Thorn.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="27" />
    <hkern g1="Thorn.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="Thorn.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="74" />
    <hkern g1="Thorn.sc"
  g2="AE.sc,AEacute.sc"
  k="66" />
    <hkern g1="Thorn.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="Thorn.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="seven"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="20" />
    <hkern g1="seven"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="141" />
    <hkern g1="four"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="nine"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="29" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="102" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="94" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="AE,AEacute"
  k="63" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="V"
  k="29" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="X"
  k="61" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="35" />
    <hkern g1="questiondown.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="33" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="39" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="AE,AEacute"
  k="35" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="51" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="82" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="117" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="45" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="AE,AEacute"
  k="98" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="V"
  k="53" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="X"
  k="86" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="66" />
    <hkern g1="three.oldstyle"
  g2="quotedbl,quotesingle"
  k="37" />
    <hkern g1="four.oldstyle"
  g2="quotedbl,quotesingle"
  k="41" />
    <hkern g1="seven.oldstyle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="78" />
    <hkern g1="nine.oldstyle"
  g2="quotedbl,quotesingle"
  k="35" />
    <hkern g1="longs"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="-31" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="v"
  k="20" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="space"
  k="20" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="8" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quotedbl,quotesingle"
  k="23" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteright,quotedblright"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteleft,quotedblleft"
  k="25" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="bracketright"
  k="63" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="backslash"
  k="96" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="trademark"
  k="57" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="braceright"
  k="90" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="asterisk"
  k="29" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="parenright"
  k="88" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="slash"
  k="53" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="guillemotleft,guilsinglleft"
  k="25" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="hyphen,endash,emdash"
  k="20" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="space"
  k="51" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="bracketright"
  k="29" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="trademark"
  k="16" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="braceright"
  k="31" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="parenright"
  k="33" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="bracketright"
  k="41" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="backslash"
  k="45" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="trademark"
  k="35" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="braceright"
  k="55" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="parenright"
  k="51" />
    <hkern g1="parenleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="100" />
    <hkern g1="parenleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="parenleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="57" />
    <hkern g1="bracketleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="57" />
    <hkern g1="bracketleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="39" />
    <hkern g1="braceleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="100" />
    <hkern g1="braceleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="braceleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="57" />
  </font>
</defs></svg>
