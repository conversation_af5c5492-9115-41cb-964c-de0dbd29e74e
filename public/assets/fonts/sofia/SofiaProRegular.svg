<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20170731 at Mon Dec 14 13:22:25 2020
 By <PERSON><PERSON><PERSON>,,,
Copyright (c) Olivier <PERSON> - Mostardesign Studio, 2012. All rights reserved.
</metadata>
<defs>
<font id="SofiaProRegular" horiz-adv-x="1187" >
  <font-face 
    font-family="Sofia Pro"
    font-weight="400"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="2 11 0 0 0 0 0 0 0 0"
    ascent="1548"
    descent="-500"
    x-height="956"
    cap-height="1411"
    bbox="-352 -514 2337 2234"
    underline-thickness="102"
    underline-position="-307"
    unicode-range="U+0020-FB04"
  />
<missing-glyph horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="1447" 
d="M1352 1477h104v-179h-104q-181 0 -178 -245v-97h221v-157h-221v-799h-189v799h-557v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h105v-179h-105q-181 0 -178 -245v-97h557v97q0 202 92.5 313t274.5 111z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1292" 
d="M1141 0h-185v799h-528v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h105v-179h-105q-181 0 -178 -245v-97h713v-956zM956 1223q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1261" 
d="M1106 0h-186v1298h-314q-181 0 -178 -245v-97h221v-157h-221v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h500v-1477z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="2037" 
d="M1702 0v799h-528v-799h-189v799h-557v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h105v-179h-105q-181 0 -178 -245v-97h557v97q0 202 92.5 313t274.5 111h104v-179h-104q-181 0 -178 -245v-97h712v-956h-184zM1702 1223q-37 35 -37 86t37 86q38 35 91.5 35
t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="2007" 
d="M1851 0h-186v1298h-313q-181 0 -178 -245v-97h221v-157h-221v-799h-189v799h-557v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h105v-179h-105q-181 0 -178 -245v-97h557v97q0 202 92.5 313t274.5 111h499v-1477z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="2021" 
d="M1526 -469h-127v174h110q175 0 172 238v856h-507v-799h-189v799h-557v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h105v-179h-105q-181 0 -178 -245v-97h557v97q0 202 92.5 313t274.5 111h104v-179h-104q-181 0 -178 -245v-97h696v-1015q0 -410 -344 -410z
M1683 1223q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="1275" 
d="M780 -469h-127v174h111q175 0 172 238v856h-508v-799h-188v799h-185v157h185v97q0 202 92 313t274 111h105v-179h-105q-181 0 -178 -245v-97h696v-1015q0 -410 -344 -410zM938 1223q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35
t-91.5 35z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1300" 
d="M1137 0h-973v1362h973v-1362zM379 883h127q0 75 42.5 122.5t108.5 47.5q67 0 115.5 -42.5t48.5 -107.5q0 -62 -49 -102q-30 -34 -67 -43q-96 -27 -143.5 -80t-47.5 -146v-77h127v77q0 43 24.5 66t77.5 37q90 27 146.5 99.5t56.5 168.5q0 122 -86 199q-84 78 -203 78
q-116 0 -198 -82q-80 -83 -80 -215zM580 180q37 0 64.5 26t27.5 62t-27.5 62t-64.5 26q-39 0 -67 -26t-28 -62t28 -62t67 -26z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="532" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="540" 
d="M178 1411h184l-18 -1032h-145zM174 14q-38 37 -38 92.5t38 92.5t94 37t94 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94 -35t-94 35z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="727" 
d="M154 821v590h145v-590h-145zM428 821v590h145v-590h-145z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1343" 
d="M1153 1411l-98 -356h215l-47 -174h-217l-99 -359h215l-47 -174h-215l-100 -348h-176l98 348h-219l-101 -348h-174l99 348h-213l47 174h213l96 359h-209l45 174h213l99 356h178l-99 -356h220l98 356h178zM827 881h-219l-98 -359h219z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="1150" 
d="M643 -18v-226h-127v228q-182 21 -303 145q-117 123 -117 279h191q3 -99 88 -175q84 -75 215 -75q123 0 196.5 57.5t87.5 149.5q7 100 -58.5 163t-180.5 88l-182 46q-344 89 -344 368q0 162 118 268q123 110 289 125v205h127v-207q170 -24 273.5 -133t103.5 -274h-191
q0 104 -74 167.5t-192 63.5q-106 0 -186 -59q-78 -60 -78 -154q0 -146 201 -196l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-19 -151 -131 -252t-287 -114z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1396" 
d="M954 1378h136l-674 -1378h-135zM350 1378q114 0 193 -82q82 -79 82 -192q0 -115 -80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80zM481 1104q0 54 -38.5 92.5t-92.5 38.5q-41 3 -73.5 -21t-46 -59.5t-7.5 -75.5t35 -67
q27 -29 67 -35t75.5 7.5t59.5 46t21 73.5zM1047 526q113 0 192 -82q82 -79 82 -192q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195q0 113 78 194q80 80 195 80zM1178 252q0 54 -38.5 92.5t-92.5 38.5q-41 3 -73.5 -21t-46.5 -59.5t-8 -75.5t35 -67
q27 -29 67.5 -35t75.5 7.5t59.5 46t21.5 73.5z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1345" 
d="M580 864h276v183h176v-185h178v-149h-178v-281q0 -144 45.5 -211t167.5 -67v-172q-110 0 -195.5 41t-127.5 110q-54 -71 -146.5 -111t-202.5 -40q-199 0 -331 129q-129 132 -129 321q0 115 55 213t154 160q-101 94 -101 254q0 149 107 250q108 102 260 102
q103 0 194 -54.5t146 -146.5l-152 -94q-70 117 -188 117q-82 0 -135.5 -49.5t-53.5 -124.5q0 -77 52.5 -136t128.5 -59zM856 420v293h-281q-118 0 -202 -84q-82 -82 -82 -197q0 -118 79 -195t203 -77q121 0 199.5 73.5t83.5 186.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="452" 
d="M154 821v590h145v-590h-145z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="743" 
d="M662 1427v-182q-175 0 -283 -209q-105 -208 -105 -561q0 -352 105 -561q108 -209 283 -209v-182q-256 0 -410 256q-152 258 -152 696t152 696q154 256 410 256z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="743" 
d="M82 -477v182q175 0 280 209q107 207 107 561t-107 561q-105 209 -280 209v182q256 0 407 -256q154 -257 154 -696t-154 -696q-151 -256 -407 -256z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="956" 
d="M483 948l-159 -231l-123 94l174 215l-269 76l27 78l25 71l256 -100l-17 276h162l-18 -276l127 49l133 51l47 -149l-266 -72l178 -221l-127 -88z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="1062" 
d="M111 625h331v331h179v-331h331v-179h-331v-333h-179v333h-331v179z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="526" 
d="M166 -258l-131 43q145 201 139 451h197q-11 -168 -68.5 -298t-136.5 -196z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="995" 
d="M141 389v178h713v-178h-713z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="544" 
d="M178 14q-38 37 -38 92.5t38 92.5t94.5 37t94.5 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94.5 -35t-94.5 35z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="1026" 
d="M760 1411h203l-697 -1411h-203z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="1239" 
d="M135 666q0 300 96 485q112 203 390 203q282 0 387 -203q52 -95 74 -210.5t22 -274.5q0 -158 -22 -272.5t-74 -209.5q-105 -202 -387 -202q-112 0 -197.5 36t-139.5 96.5t-88 149.5t-47.5 186.5t-13.5 215.5zM319 666q0 -245 70.5 -373.5t231.5 -128.5t231 128.5t70 373.5
q0 162 -28 270.5t-96 170.5t-177 62q-161 0 -231.5 -128.5t-70.5 -374.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="684" 
d="M487 0h-184v1104l-217 -76v176l401 146v-1350z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1079" 
d="M123 0v82l543 657q98 113 98 219q0 93 -68.5 152t-169.5 59q-94 0 -161 -57t-80 -154l-179 43q27 162 144 256q120 97 276 97q179 0 299 -111q121 -112 121 -285q0 -178 -145 -346l-363 -428h510v-184h-825z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1095" 
d="M303 1051l-137 108q56 97 160.5 150.5t226.5 46.5q162 -9 266 -119q106 -106 103 -252q0 -35 -13 -82q-38 -121 -145 -182q103 -38 160 -143q60 -106 51 -218q-15 -168 -160 -280q-144 -111 -330 -96q-129 7 -235.5 83t-161.5 195l162 88q33 -73 103 -125.5t147 -58.5h-2
q114 -6 198 57q89 65 92 162q3 96 -75 168t-183 72h-151v178h151q69 -1 121 42t64 111q15 84 -23 138q-47 71 -155 77q-145 12 -234 -120z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1212" 
d="M1124 274h-192v-274h-182v274h-697l742 1063h137v-888h192v-175zM750 1006l-383 -557h383v557z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1155" 
d="M98 285l158 86q38 -98 115.5 -155.5t171.5 -57.5q156 0 238.5 73.5t82.5 198.5q0 120 -77 188t-199 68q-86 0 -156 -24q-61 -18 -176 -87l-115 95l117 665h713v-176h-561l-74 -379q45 32 118 55t134 23q192 0 325 -115q131 -113 131 -313q0 -204 -139 -328
q-137 -122 -362 -122q-141 0 -262.5 83t-182.5 222z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1138" 
d="M1026 1096l-164 -70q-77 154 -227 154q-140 0 -223 -133q-82 -131 -64 -334q59 151 275 151q178 0 299 -139q120 -138 114 -319q-9 -181 -139 -308q-128 -125 -309 -116q-179 8 -294 130t-132 328q-18 253 22 506q30 184 154 299q127 115 305 109q135 -6 229.5 -65.5
t153.5 -192.5zM346 434q0 -108 72 -186q74 -77 178 -80q107 -3 178 65q77 71 80 181q4 109 -64 185t-174 81q-106 3 -186 -68q-81 -72 -84 -178z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="1085" 
d="M127 1337h924l-734 -1337h-215l637 1155h-612v182z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="1150" 
d="M219 1012q0 141 103 241q101 101 253 101t256 -101q105 -99 105 -241t-105 -236q97 -60 151 -154t54 -204q0 -182 -133 -309t-328 -127q-199 0 -329 124q-131 125 -131 312q0 110 54 205.5t148 152.5q-98 94 -98 236zM299 418q0 -106 80 -180t196 -74q117 0 197 74
t80 180q0 105 -80 182q-79 76 -197 76h-14q-109 -3 -186 -80q-76 -76 -76 -178zM565 848h17q73 1 121.5 49t48.5 115q0 66 -51 111.5t-126 45.5q-73 0 -123.5 -45.5t-50.5 -111.5t47.5 -114.5t116.5 -49.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1136" 
d="M135 238l164 69q77 -153 227 -153q140 0 222 131q83 133 65 336q-59 -152 -274 -152q-180 0 -299 137q-121 139 -115 322q9 180 139 307q129 126 309 117q179 -8 294 -130.5t132 -328.5q18 -253 -22 -506q-30 -185 -156 -299q-123 -114 -303 -108q-135 6 -229.5 65.5
t-153.5 192.5zM815 899q0 109 -74 186q-71 77 -176 80q-104 3 -180 -67q-75 -72 -78 -178q-5 -109 63.5 -186t174.5 -81q108 -3 186 66q81 72 84 180z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="550" 
d="M180 14q-38 37 -38 92.5t38 92.5q41 37 97 37t94 -37t38 -92.5t-38 -92.5q-38 -35 -94 -35t-97 35zM180 758q-38 37 -38 92t38 90q41 37 97 37t94 -37q38 -35 38 -90t-38 -92t-94 -37t-97 37z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="581" 
d="M201 -258l-142 43q146 202 140 451h207q-11 -169 -68 -299t-137 -195zM209 758q-38 37 -38 92t38 90q38 37 94 37t94 -37q41 -35 41 -90t-41 -92q-38 -37 -94 -37t-94 37z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="1273" 
d="M1135 260v-194l-1039 395v178l1039 397v-198l-781 -287z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="1245" 
d="M152 621v178h942v-178h-942zM152 225v178h942v-178h-942z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="1271" 
d="M1176 639v-178l-1037 -398v197l779 291l-779 287v198z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="989" 
d="M264 999h-176q0 193 117 310q115 118 287 118t294 -110q125 -110 125 -289q0 -139 -82 -243.5t-213 -143.5q-79 -25 -112 -55.5t-33 -93.5l-4 -113h-180v113q0 134 67 208t207 117q68 19 118 81t50 130q0 96 -72 156q-70 61 -165 61t-161.5 -69t-66.5 -177zM283 16
q-38 37 -38 92.5t38 92.5q41 37 96.5 37t93.5 -37t38 -92.5t-38 -92.5q-38 -35 -93.5 -35t-96.5 35z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1986" 
d="M1229 262v264q0 109 -78 187t-186 78q-111 0 -189 -78q-76 -76 -76 -187q0 -108 78 -186q76 -76 187 -76q104 0 202 98v-174q-97 -69 -202 -69q-172 0 -291 119t-119 288q0 170 119 289q121 121 291 121q163 0 282 -113q121 -115 127 -278v-297q0 -135 148 -135
q67 0 117.5 43t76.5 114q45 131 45 281q0 308 -229 512q-230 207 -547 207q-311 0 -526 -221q-213 -219 -213 -533q0 -311 213 -530q211 -217 526 -217h47v-146h-47q-375 0 -629 260q-256 259 -256 633q0 373 256 635q258 264 629 264q144 4 284 -37.5t255.5 -121.5
t203 -186.5t135.5 -241t46 -277.5q0 -201 -84 -379q-44 -93 -123.5 -148t-179.5 -55q-135 0 -214 72t-79 221z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1320" 
d="M662 1157l-234 -627h467zM1098 0l-137 358h-599l-137 -358h-202l536 1411h205l534 -1411h-200z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1286" 
d="M1108 1047q0 -228 -178 -295q110 -20 182 -127t72 -217q0 -172 -113 -291q-114 -117 -301 -117h-577v1411h532q177 0 280 -98.5t103 -265.5zM385 1235v-410h340q88 0 138 60t50 155q0 92 -55.5 143.5t-152.5 51.5h-320zM756 657h-371v-479h377q98 0 161.5 68t63.5 164
q0 93 -65 170t-166 77z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1398" 
d="M1206 344l144 -121q-97 -116 -236 -179.5t-299 -63.5q-301 0 -508 210q-207 213 -207 512t207 512q210 213 508 213q158 0 295.5 -62.5t235.5 -176.5l-144 -119q-158 174 -387 174q-221 0 -373 -158q-151 -157 -151 -383t151 -383q149 -155 373 -155q239 0 391 180z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1482" 
d="M739 0h-546v1411h546q292 0 463 -201q172 -199 172 -508q0 -305 -170 -505q-170 -197 -465 -197zM383 1233v-1055h356q212 0 328 146q117 147 117 378q0 234 -117 381q-114 150 -328 150h-356z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1157" 
d="M193 0v1411h835v-178h-645v-451h563v-178h-563v-426h666v-178h-856z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1077" 
d="M193 0v1411h823v-178h-633v-451h502v-178h-502v-604h-190z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1548" 
d="M1290 1241l-137 -127q-146 131 -340 131q-220 0 -371 -160q-151 -157 -151 -383t151 -383t371 -157q191 0 317 102q125 104 134 279h-402v166h619q0 -357 -175.5 -543t-492.5 -186q-302 0 -506 210q-207 213 -207 512q0 302 207 515q204 210 506 210q279 0 477 -186z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1449" 
d="M1065 1411h192v-1411h-192v614h-682v-614h-190v1411h190v-623h682v623z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="575" 
d="M193 0v1411h190v-1411h-190z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="575" 
d="M-205 -492l19 181q56 -17 135 -17q121 0 182.5 73.5t61.5 199.5v1466h190v-1466q0 -211 -109.5 -335t-324.5 -124q-41 0 -154 22z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1183" 
d="M1085 1411l-542 -704l655 -707h-256l-559 602v-602h-190v1411h190v-610l467 610h235z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1013" 
d="M193 0v1411h190v-1233h575v-178h-765z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1912" 
d="M459 1411l497 -1036l498 1036h174l172 -1411h-188l-129 1061l-441 -905h-168l-444 905l-129 -1061h-188l172 1411h174z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1400" 
d="M1208 1411v-1411h-182l-643 1063v-1063h-190v1411h188l637 -1055v1055h190z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1241" 
d="M193 0v1411h563q208 0 313 -129q107 -128 107 -313q0 -186 -107 -314q-106 -127 -313 -127h-371v-528h-192zM754 1233h-369v-526h358q117 0 179.5 74t62.5 188q0 115 -60 189.5t-171 74.5z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1630" 
d="M1194 88l147 -233l-147 -91l-158 250q-106 -34 -223 -34q-302 0 -506 210q-207 213 -207 512q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -189 -90.5 -352t-245.5 -262zM788 414l146 92l160 -258q113 73 178 193t65 261q0 226 -151 383
q-152 158 -373 158q-219 0 -371 -158q-151 -157 -151 -383q0 -217 137 -366q140 -152 348 -164q88 -6 156 8z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1267" 
d="M784 535l377 -535h-231l-365 528h-180v-528h-192v1411h542q209 0 314 -129q104 -128 104 -313q0 -171 -92 -295q-89 -124 -277 -139zM729 1233h-344v-526h336q116 0 175.5 72.5t59.5 189.5q0 115 -58.5 189.5t-168.5 74.5z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1173" 
d="M106 408h191q3 -99 88 -175q84 -75 215 -75q123 0 197 57.5t88 149.5q7 100 -58 162.5t-182 88.5l-182 46q-344 89 -344 368q0 174 135 285q133 112 321 112q197 0 326 -114t129 -299h-190q0 104 -74.5 167.5t-192.5 63.5q-106 0 -186 -59q-78 -60 -78 -154
q0 -146 201 -196l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-18 -158 -145 -264q-125 -104 -326 -104q-211 0 -352 126q-142 130 -142 302z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1163" 
d="M57 1231v180h1049v-180h-428v-1231h-193v1231h-428z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1150" 
d="M57 1411h199l319 -1089l320 1089h197l-424 -1411h-183z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1845" 
d="M1255 307l355 1104h205l-478 -1411h-166l-249 881l-248 -881h-166l-477 1411h205l354 -1104l260 914h145z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1259" 
d="M1184 1411l-441 -653l494 -758h-229l-379 600l-379 -600h-227l491 758l-438 653h229l324 -498l325 498h230z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1286" 
d="M741 649v-649h-196v649l-535 762h228l405 -571l410 571h229z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1173" 
d="M86 0v166l745 1065h-716v180h958v-166l-749 -1065h749v-180h-987z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="669" 
d="M584 -465h-412v1876h412v-170h-228v-1536h228v-170z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="1026" 
d="M963 0h-203l-697 1411h203z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="669" 
d="M86 1411h412v-1876h-412v170h227v1536h-227v170z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="1431" 
d="M141 -223v166h1149v-166h-1149z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="1048" 
d="M686 1135h-178l-244 315h223z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1253" 
d="M342 147v-147h-186v1477h186v-664q47 78 136.5 121t199.5 43q207 0 344 -137q139 -139 139 -361t-141 -360q-142 -139 -344 -139q-105 0 -196.5 44.5t-137.5 122.5zM344 481q0 -147 92 -233t226 -86q129 0 219 90t90 229t-90 226q-88 88 -219 88q-126 0 -222 -84
t-96 -230z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1048" 
d="M598 977q227 0 369 -154l-142 -118q-90 90 -227 90q-133 0 -227 -88q-95 -86 -95 -226q0 -142 95 -231q96 -90 227 -90q139 0 227 94l144 -119q-143 -155 -371 -155q-210 0 -358 139q-148 142 -148 362t148 359q146 137 358 137z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1253" 
d="M1098 0h-187v147q-46 -78 -138 -122.5t-198 -44.5q-202 0 -344 139q-139 139 -139 360q0 222 139 361q137 137 342 137q111 0 201.5 -43t136.5 -121v664h187v-1477zM592 795q-133 0 -221 -88q-88 -85 -88 -226t88 -229q90 -90 221 -90t223 86q94 88 94 233q0 146 -96 230
t-221 84z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="702" 
d="M606 1477h105v-179h-105q-181 0 -178 -245v-97h221v-157h-221v-799h-188v799h-185v157h185v97q0 202 92 313t274 111z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1251" 
d="M225 -176l74 39q40 -76 116 -120t160 -44q340 0 340 434q0 3 -1 11.5t-1 11.5q-40 -80 -134 -128t-201 -48q-205 0 -345 137q-141 138 -141 358q0 225 141 363q142 139 345 139q106 0 199 -46t136 -124v149h185v-811q0 -622 -523 -622q-136 0 -250.5 68t-181.5 190
q16 7 82 43zM911 477q0 149 -92 232q-92 86 -225 86q-131 0 -221 -90t-90 -228q0 -144 88 -229q89 -86 223 -86q126 0 221 86q96 87 96 229z" />
    <glyph glyph-name="h" unicode="h" 
d="M342 516v-516h-186v1477h186v-664q41 81 131 121q93 43 184 43q181 0 284.5 -112t102.5 -312v-553h-186v541q0 116 -62 185t-163 69q-115 0 -203 -70q-88 -67 -88 -209z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="495" 
d="M340 0h-184v956h184v-956zM156 1223q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="499" 
d="M0 -469h-127v174h111q175 0 172 238v1013h188v-1015q0 -410 -344 -410zM158 1223q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1028" 
d="M776 0l-436 467v-467h-184v1477h184v-854l330 333h254l-410 -413l508 -543h-246z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="497" 
d="M156 0v1477h186v-1477h-186z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1804" 
d="M1255 795q-116 1 -185 -76.5t-69 -202.5v-516h-186v543q0 116 -56.5 184t-150.5 68q-116 0 -191 -72.5t-75 -206.5v-516h-186v956h186v-143q33 68 109.5 110.5t154.5 49.5q56 3 105 -6q181 -27 250 -187q102 197 331 197q178 0 273.5 -110.5t95.5 -313.5v-553h-186v541
q0 119 -58.5 186.5t-161.5 67.5z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1153" 
d="M342 516v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 273.5 -110.5t95.5 -313.5v-553h-187v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM907 481q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86q-91 -88 -91 -228q0 -142 91 -233q91 -88 225 -88q135 0 223 88
q92 89 92 233z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1247" 
d="M156 956h186v-149q47 78 138.5 124t195.5 46q201 0 340 -139t139 -363q0 -219 -139 -358q-137 -137 -340 -137q-108 0 -199.5 43t-134.5 120v-608h-186v1421zM344 473q0 -147 94 -229t219 -82q135 0 222.5 86.5t87.5 224.5q0 142 -90 232t-220 90q-132 0 -223 -88
q-90 -87 -90 -234z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1247" 
d="M905 807v149h187v-1421h-187v608q-43 -77 -134 -120t-200 -43q-205 0 -342 137t-137 358q0 224 139 363t340 139q105 0 198 -46t136 -124zM905 473q0 148 -92 234q-88 88 -223 88q-129 0 -219 -90q-88 -88 -88 -232q0 -140 88 -225q89 -86 219 -86q125 0 219 82
q96 84 96 229z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="808" 
d="M342 348v-348h-186v956h186v-215q70 236 266 236q114 0 166 -37l-29 -174q-65 33 -141 33q-127 0 -194.5 -121.5t-67.5 -329.5z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="899" 
d="M504 391l-131 31q-140 38 -204 112t-52 199q9 101 106 174q94 70 226 70q139 0 229 -69t102 -201h-178q-7 50 -49 79t-100 29q-54 0 -93.5 -24t-50.5 -62q-12 -47 2 -76q20 -45 113 -73l141 -35q248 -61 248 -279q0 -130 -108 -209q-109 -77 -263 -77q-136 0 -245 83
q-108 82 -111 224h170q12 -73 78 -109q62 -39 151 -33q65 4 104.5 38.5t41.5 86.5q3 92 -127 121z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="671" 
d="M414 1317v-361h202v-157h-202v-799h-187v799h-172v157h172v361h187z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1044" 
d="M432 0l-397 956h196l291 -718l289 718h199l-400 -956h2h-180z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1617" 
d="M1575 956l-354 -956h-154l-260 702l-252 -702h-152l-364 956h199l239 -669l240 669h180l240 -669l239 669h199z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1024" 
d="M403 524l-311 432h223l197 -280l199 280h223l-313 -432l378 -524h-221l-266 371l-268 -371h-219z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1042" 
d="M1008 956l-594 -1421h-195l201 494l-383 927h199l286 -692l287 692h199z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="1028" 
d="M119 956h809v-153l-580 -625h580v-178h-842v154l569 624h-536v178z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="819" 
d="M381 293v-356q0 -99 65 -170q64 -70 156 -70h137v-162h-131q-169 0 -286 111t-117 291v356q0 41 -37.5 72.5t-87.5 31.5v166q49 0 87 32t38 73v360q-3 181 114 289q116 110 289 110h131v-161h-137q-92 0 -156.5 -69.5t-64.5 -168.5v-360q0 -59 -27 -112t-73 -77
q43 -25 71.5 -78.5t28.5 -107.5z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="471" 
d="M172 1628h127v-1872h-127v1872z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="819" 
d="M438 -63v356q0 54 29 107.5t72 78.5q-46 24 -73.5 77t-27.5 112v360q0 99 -64.5 168.5t-156.5 69.5h-137v161h131q173 0 289 -110q117 -108 114 -289v-360q0 -41 38 -73t87 -32v-166q-50 0 -87.5 -31.5t-37.5 -72.5v-356q0 -181 -116 -291q-117 -111 -287 -111h-131v162
h137q92 0 156 70q65 71 65 170z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="493" 
d="M174 578h145l19 -1043h-184zM150 758q-38 37 -38 92t38 90q38 37 94 37t94 -37q41 -35 41 -90t-41 -92q-38 -37 -94 -37t-94 37z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="1087" 
d="M641 -240h-127v228q-176 30 -295 164q-117 132 -117 329q0 194 117 326t295 162v217h127v-211q202 -9 336 -152l-141 -118q-90 90 -228 90q-133 0 -227 -88q-94 -85 -94 -226q0 -143 94 -231q96 -90 227 -90q140 0 228 94l143 -119q-129 -141 -338 -153v-222z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1294" 
d="M131 713h156v315q0 176 123 287q124 112 297 112q171 0 286 -118q117 -117 117 -310h-184q0 87 -40.5 150t-107.5 82q-47 14 -92 8q-85 -3 -151 -63t-66 -148v-315h313v-125h-313v-404h500q35 0 35 45v117h184v-100q0 -112 -51.5 -179t-145.5 -67h-893v184h189v404h-156
v125z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1249" 
d="M465 854q-66 -66 -66 -160q0 -93 66 -159t160 -66q93 0 159 66q68 65 68 159q0 95 -68 160q-65 68 -159 68q-95 0 -160 -68zM403 362l-143 -145l-113 113l144 145q-66 97 -66 219q0 118 68 221l-146 146l113 113l146 -146q95 68 219 68q118 0 221 -68l143 146l113 -113
l-144 -146q68 -106 68 -221q0 -124 -68 -219l144 -145l-113 -113l-143 145q-100 -68 -221.5 -68t-221.5 68z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1406" 
d="M1174 518h-375v-160h375v-131h-375v-227h-197v227h-371v131h371v160h-371v131h371l-534 762h227l405 -571l410 571h229l-540 -762h375v-131z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="479" 
d="M303 829h-127v799h127v-799zM176 551h127v-795h-127v795z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1056" 
d="M309 1006v-328q0 -98 65 -159.5t163 -61.5q59 0 114 22v-182q-66 -16 -114 -16q-170 0 -287 108q-117 111 -117 289v328q0 177 117 288t287 111q132 0 237.5 -75.5t149.5 -203.5l-168 -49q-31 72 -89 112t-130 40q-96 0 -162 -62.5t-66 -160.5zM748 160v327q0 98 -65 160
t-163 62q-52 0 -110 -23v182q63 17 110 17q170 0 287 -111q117 -108 117 -287v-327q0 -178 -117 -289t-287 -111q-132 0 -237.5 75.5t-149.5 203.5l168 49q31 -72 89 -111.5t130 -39.5q96 0 162 62.5t66 160.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="1091" 
d="M373 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM629 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1656" 
d="M1116 455q-127 -133 -282 -133t-263 108q-106 106 -106 262q0 154 106 260q109 109 263 109q151 0 280 -135l-94 -97q-95 95 -186 95q-97 0 -163.5 -67.5t-66.5 -164.5q0 -94 66 -166q67 -67 164 -67q92 0 190 98zM319 190q-206 212 -206 512t206 512q210 213 508 213
t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210t-508 210zM410 1124q-172 -175 -172 -422q0 -246 172 -421t417 -175q246 0 418 175t172 421q0 247 -172 422q-173 176 -418 176q-244 0 -417 -176z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="1034" 
d="M854 666h-129v102q-30 -54 -94.5 -85.5t-138.5 -31.5q-137 0 -242 97q-98 98 -98 253q0 158 98 252q102 97 242 97q74 0 138.5 -30.5t94.5 -84.5v100h129v-669zM725 1004q0 99 -65.5 159t-157.5 60q-93 0 -155 -61t-62 -158q0 -98 62 -162t155 -64q96 0 159.5 62
t63.5 164zM846 440h-645v103h645v-103z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1001" 
d="M377 53l-283 424l283 424h162l-283 -424l283 -424h-162zM737 53l-282 424l282 424h162l-283 -424l283 -424h-162z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="1312" 
d="M152 735h1001v-493h-178v315h-823v178z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1656" 
d="M1122 338h-172l-180 250h-47v-250h-141v723h280q107 0 170.5 -69t63.5 -169q0 -82 -41 -143.5t-117 -81.5zM723 922v-195h135q43 0 70.5 28.5t27.5 67.5q0 42 -26 70.5t-68 28.5h-139zM319 190q-206 212 -206 512t206 512q210 213 508 213t508 -213q209 -212 209 -512
t-209 -512q-207 -210 -508 -210t-508 210zM410 1124q-172 -175 -172 -422q0 -246 172 -421t417 -175q246 0 418 175t172 421q0 247 -172 422q-173 176 -418 176q-244 0 -417 -176z" />
    <glyph glyph-name="overscore" unicode="&#xaf;" horiz-adv-x="1007" 
d="M246 1188v164h516v-164h-516z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="757" 
d="M188 969q-77 80 -77 190q0 112 77 189q79 79 191 79t188 -79q80 -77 80 -189q0 -110 -80 -190q-78 -78 -188 -78t-191 78zM289 1249q-37 -37 -37 -90t37 -90q38 -37 89.5 -37t88.5 37q38 37 38 90t-38 90q-37 37 -88.5 37t-89.5 -37z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="1110" 
d="M141 745h324v293h178v-293h326v-178h-326v-297h-178v297h-324v178zM969 168v-168h-828v168h828z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="655" 
d="M76 881v57l323 391q39 49 49.5 96.5t-4.5 80.5t-48 53t-77 20q-55 0 -96.5 -37t-48.5 -96l-108 29q13 99 84 160t169 61q109 0 181.5 -68.5t72.5 -173.5q0 -111 -88 -209l-206 -244h296v-120h-499z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="655" 
d="M178 1511l-82 66q36 58 97.5 89.5t134.5 27.5q94 -3 161 -70q63 -63 60 -151q0 -26 -6 -50q-20 -71 -88 -110q60 -23 96.5 -86t30.5 -129q-9 -103 -97 -170t-196 -58q-77 6 -142 52t-98 116l98 54q19 -44 61.5 -77t88.5 -34h-2q69 -5 121 33.5t53 97.5q2 57 -44.5 100
t-109.5 43h-92v107h92q41 0 72 26.5t39 65.5q9 50 -14 82q-27 44 -93 47q-88 9 -141 -72z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="1048" 
d="M463 1450h223l-244 -315h-178z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1136" 
d="M356 -313h-186v1269h186v-553q0 -113 49.5 -176t139.5 -63q117 0 193.5 70.5t76.5 201.5v520h186v-956h-186v117q-40 -63 -115.5 -99t-154.5 -36q-131 0 -189 61v-356z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1333" 
d="M516 1411h150v-1526h-170v637q-173 9 -301 140q-123 129 -123 305q0 184 129 313q128 131 315 131zM784 -131v1542h191q112 0 196 -84t84 -197v-174h-168v174q0 44 -26 76.5t-66 32.5h-41v-1339q0 -107 -84 -191t-196 -90h-180v156h180q43 0 76.5 28.5t33.5 65.5z" />
    <glyph glyph-name="middot" unicode="&#xb7;" horiz-adv-x="550" 
d="M180 422q-38 37 -38 92t38 92t94.5 37t94.5 -37q41 -37 41 -92t-41 -92q-38 -35 -94.5 -35t-94.5 35z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="1069" 
d="M465 -240q30 -68 102 -47q30 13 41.5 45t-0.5 64l-88 246h101l104 -195q16 -43 16 -86q0 -62 -34 -112t-95 -76q-43 -17 -80 -17q-60 0 -109 35t-75 96q-20 44 -20 101l131 -9q-3 -30 6 -45z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="425" 
d="M315 881h-122v624l-140 -71v116l262 144v-813z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="1017" 
d="M158 1004q0 156 100 249q105 97 250 97t250 -97q102 -95 102 -249q0 -155 -102 -254t-250 -99t-250 99q-100 97 -100 254zM729 1004q0 97 -63.5 158t-157.5 61t-157.5 -61t-63.5 -158q0 -98 63.5 -162t157.5 -64t157.5 64t63.5 162zM852 440h-678v103h678v-103z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1001" 
d="M625 901l282 -424l-282 -424h-162l282 424l-282 424h162zM264 901l283 -424l-283 -424h-162l283 424l-283 424h162z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1501" 
d="M334 1362v-809h-109v664l-131 -46v105zM1192 268v336l-231 -336h231zM1417 268v-104h-117v-164h-108v164h-418l445 639h81v-535h117zM1083 1372h138l-922 -1392h-139z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1579" 
d="M336 1362v-809h-109v664l-131 -46v105zM1475 0h-496v49l326 395q43 53 53.5 102t-6.5 82.5t-53.5 53.5t-84.5 20q-58 0 -98 -34t-47 -93l-100 27q13 96 83.5 153.5t168.5 57.5q107 0 179.5 -67.5t72.5 -170.5q0 -105 -89 -208l-217 -256h308v-111zM1079 1372h138
l-922 -1392h-139z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1662" 
d="M1122 268h232v336zM1462 164v-164h-108v164h-418l444 639h82v-535h117v-104h-117zM213 1184q-9 9 -41.5 33.5t-40.5 31.5q32 57 95 89t136 28q94 -6 160 -72q65 -65 62 -149q0 -47 -26 -91t-69 -69q60 -23 96.5 -86t30.5 -129q-9 -104 -96 -170t-198 -57q-77 6 -142 52
t-96 116l96 51q19 -43 62 -75t90 -36h-2q68 -5 120.5 34.5t53.5 98.5q1 57 -45.5 100.5t-108.5 43.5h-92v106h92q43 0 73 25.5t38 66.5q7 51 -23 87t-86 44q-87 6 -139 -73zM479 -20h-139l924 1392h137z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="966" 
d="M715 -49h176q0 -192 -117 -309q-116 -119 -287 -119q-172 0 -297 110q-122 110 -122 289q0 139 81.5 243.5t212.5 143.5q79 25 112.5 56t33.5 94l4 121h180v-121q0 -134 -67 -208.5t-207 -117.5q-68 -19 -118 -81t-50 -130q0 -95 72 -155q71 -62 165 -62q95 0 161.5 69
t66.5 177zM694 940q41 -35 41 -90t-41 -92q-38 -37 -94 -37t-94 37t-38 92t38 90q38 37 94 37t94 -37z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1320" 
d="M662 1157l-234 -627h467zM1098 0l-137 358h-599l-137 -358h-202l536 1411h205l534 -1411h-200zM762 1589h-178l-244 316h223z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1320" 
d="M895 530l-233 627l-234 -627h467zM362 358l-137 -358h-202l536 1411h205l534 -1411h-200l-137 358h-599zM764 1905h223l-244 -316h-178z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1320" 
d="M662 1157l-234 -627h467zM1098 0l-137 358h-599l-137 -358h-202l536 1411h205l534 -1411h-200zM981 1589h-168l-154 178l-151 -178h-168l225 316h191z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1320" 
d="M662 1157l-234 -627h467zM1098 0l-137 358h-599l-137 -358h-202l536 1411h205l534 -1411h-200zM567 1704q-20 0 -37 -24t-18 -66h-125q6 118 56 178.5t124 60.5q52 0 119 -49q59 -46 85 -32t36 81h125q-6 -116 -57 -174.5t-125 -58.5q-54 0 -117 51q-47 33 -66 33z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1320" 
d="M895 530l-233 627l-234 -627h467zM362 358l-137 -358h-202l536 1411h205l534 -1411h-200l-137 358h-599zM487 1866q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM743 1659q-37 35 -37 86t37 86t90.5 35t90.5 -35t37 -86t-37 -86
t-90.5 -35t-90.5 35z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1320" 
d="M895 530l-233 627l-234 -627h467zM362 358l-137 -358h-202l536 1411h205l534 -1411h-200l-137 358h-599zM444 1776q0 92 61 154.5t152 62.5q93 0 156.5 -62.5t63.5 -154.5q0 -90 -63.5 -151.5t-156.5 -61.5q-88 0 -150.5 62.5t-62.5 150.5zM553 1776q0 -43 30.5 -74
t73.5 -31q44 0 77.5 31t33.5 74q0 45 -32 76.5t-79 31.5q-43 0 -73.5 -32t-30.5 -76z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1912" 
d="M952 354h-501l-228 -354h-223l891 1411h891v-188h-641v-437h561v-188h-561v-410h663v-188h-852v354zM952 1147l-389 -610h389v610z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1400" 
d="M651 -240q30 -69 103 -47q30 13 41.5 45t-0.5 64l-60 162q-270 27 -452 233q-181 205 -181 485q0 299 207 512q210 213 508 213q158 0 295.5 -62.5t235.5 -176.5l-144 -119q-158 174 -387 174q-221 0 -373 -158q-151 -157 -151 -383t151 -383q149 -155 373 -155
q239 0 391 180l144 -121q-191 -226 -498 -241l57 -109q17 -46 17 -86q0 -62 -34 -112t-95 -76q-43 -17 -80 -17q-60 0 -109 35t-75 96q-21 46 -21 101l131 -9q-3 -30 6 -45z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1157" 
d="M193 0v1411h835v-178h-645v-451h563v-178h-563v-426h666v-178h-856zM725 1589h-178l-244 316h223z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1157" 
d="M1028 1411v-178h-645v-451h563v-178h-563v-426h666v-178h-856v1411h835zM639 1905h223l-244 -316h-178z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1157" 
d="M193 0v1411h835v-178h-645v-451h563v-178h-563v-426h666v-178h-856zM899 1589h-168l-153 178l-152 -178h-168l225 316h191z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1157" 
d="M1028 1411v-178h-645v-451h563v-178h-563v-426h666v-178h-856v1411h835zM414 1866q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM670 1659q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="575" 
d="M383 1589h-178l-244 316h223zM193 0v1411h190v-1411h-190z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="575" 
d="M383 1411v-1411h-190v1411h190zM389 1905h223l-243 -316h-179z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="575" 
d="M608 1589h-168l-153 178l-152 -178h-168l226 316h190zM193 0v1411h190v-1411h-190z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="575" 
d="M383 1411v-1411h-190v1411h190zM115 1866q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM371 1659q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1482" 
d="M743 0h-538v637h-191v139h191v635h538q292 0 461 -199q168 -198 168 -510q0 -310 -168 -505q-170 -197 -461 -197zM395 1223v-447h338v-139h-338v-449h348q209 0 324 142q115 145 115 372q0 230 -115 375q-116 146 -324 146h-348z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1400" 
d="M608 1704q-20 0 -37 -24t-18 -66h-125q6 118 56 178.5t124 60.5q52 0 119 -49q59 -46 85 -32t36 81h125q-6 -116 -57 -174.5t-125 -58.5q-54 0 -117 51q-47 33 -66 33zM1208 1411v-1411h-182l-643 1063v-1063h-190v1411h188l637 -1055v1055h190z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM924 1589h-179l-243 316h223z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM881 1905h223l-244 -316h-178z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM1135 1589h-168l-154 178l-151 -178h-168l225 316h190z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM723 1704q-20 0 -37 -24t-18 -66h-125q6 118 56 178.5t124 60.5q52 0 119 -49q59 -46 85 -32t36 81h124q-6 -116 -57 -174.5t-125 -58.5q-54 0 -117 51q-47 33 -65 33z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM641 1866q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM897 1659q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="1017" 
d="M129 303l256 256l-256 254l127 123l252 -252l258 256l123 -129l-254 -252l254 -254l-123 -129l-258 256l-252 -252z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1628" 
d="M365 135l-164 -168l-97 96l164 168q-170 203 -170 471q0 302 207 515q204 210 506 210q261 0 459 -165l162 165l96 -94l-164 -168q164 -203 164 -463q0 -300 -209 -512q-204 -210 -508 -210q-257 0 -446 155zM289 702q0 -192 110 -335l740 759q-144 117 -328 117
q-219 0 -371 -158q-151 -157 -151 -383zM811 164q224 0 373 155q151 157 151 383q0 183 -104 326l-735 -758q132 -106 315 -106z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231zM821 1589h-178l-244 316h224z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231zM745 1905h224l-244 -316h-178z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231zM1014 1589h-168l-154 178l-151 -178h-168l225 316h190z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1388" 
d="M520 1866q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM776 1659q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35zM1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366
v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1286" 
d="M741 649v-649h-196v649l-535 762h228l405 -571l410 571h229zM717 1905h223l-244 -316h-178z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1171" 
d="M670 283h-287v-283h-190v1411h190v-252h287q183 0 307 -121q125 -119 125 -313q0 -197 -125 -319q-123 -123 -307 -123zM383 981v-514h287q100 0 172 72q71 74 71 186q0 113 -72 180.5t-188 73.5z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1159" 
d="M422 8v174q82 -32 170 -32q128 0 213 88q88 88 88 208q0 113 -90 203t-209 90h-76v152h72q79 0 136.5 55.5t57.5 130.5q0 73 -58.5 124t-145.5 56h-19q-100 0 -164.5 -69t-64.5 -184v-1004h-176v1004q0 196 111.5 309.5t293.5 113.5h21q155 0 266 -102q110 -101 110 -248
q0 -141 -129 -243q103 -50 170 -160q70 -107 70 -228q0 -197 -137 -331q-135 -135 -336 -135q-88 -3 -174 28z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM764 1135h-178l-244 315h223z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM653 1450h224l-244 -315h-178z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM911 1135h-168l-153 178l-152 -178h-168l226 315h190z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM532 1249q-20 0 -37 -24t-18 -66h-125q6 118 56 179t124 61q52 0 119 -49q59 -46 85 -32t36 81h125q-6 -117 -57 -175.5t-125 -58.5q-53 0 -117 52q-46 32 -66 32z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM422 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM678 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM379 1321q0 92 61 154.5t152 62.5q92 0 155.5 -62.5t63.5 -154.5q0 -91 -63.5 -152t-155.5 -61q-88 0 -150.5 62.5t-62.5 150.5zM487 1321q0 -43 31 -73.5t74 -30.5q44 0 77 30.5t33 73.5q0 45 -31.5 77t-78.5 32q-43 0 -74 -32.5t-31 -76.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1959" 
d="M913 956h185v-77q125 98 305 98q209 0 340 -137q135 -135 135 -359q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-181 0 -305 100v-80h-185v147q-46 -78 -137.5 -122.5
t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90q133 0 225 86t92 233zM1100 553h594q-17 118 -95.5 187
t-195.5 69q-122 0 -204 -69t-99 -187z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1048" 
d="M647 -16l60 -111q16 -43 16 -86q0 -62 -34 -112t-95 -76q-42 -17 -80 -17q-60 0 -109 35t-75 96q-21 47 -21 101l131 -9q-3 -30 6 -45q30 -69 103 -47q30 13 41.5 45t-0.5 64l-60 162q-186 21 -313 157q-125 137 -125 340q0 220 148 359q146 137 358 137q227 0 369 -154
l-142 -118q-90 90 -227 90q-133 0 -227 -88q-95 -86 -95 -226q0 -142 95 -231q96 -90 227 -90q139 0 227 94l144 -119q-124 -136 -322 -151z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM684 1135h-178l-244 315h223z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM643 1450h223l-243 -315h-179z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM895 1135h-168l-154 178l-151 -178h-168l225 315h191z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM401 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM657 1204q-37 35 -37 86t37 86t90.5 35t90.5 -35t37 -86t-37 -86t-90.5 -35t-90.5 35z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="493" 
d="M156 956h182v-956h-182v956zM336 1135h-178l-244 315h223z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="493" 
d="M338 0h-182v956h182v-956zM352 1450h223l-243 -315h-178z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="493" 
d="M156 956h182v-956h-182v956zM578 1135h-168l-154 178l-152 -178h-167l225 315h190z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="493" 
d="M338 0h-182v956h182v-956zM84 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM340 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1183" 
d="M483 1411l82 -100l133 82l74 -86l-127 -86l350 -441q95 -130 95 -299q0 -219 -146 -362q-145 -139 -352 -139q-209 0 -354 139q-146 143 -146 362t144 357q145 139 339 139q39 0 74 -21l-151 170l-174 -108l-66 96l164 103l-154 194h215zM592 801q-138 0 -232 -88
q-92 -89 -92 -232q0 -144 94 -235q96 -90 230 -90q133 0 229 90q94 91 94 235t-94 232t-229 88z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1153" 
d="M342 516v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 273.5 -110.5t95.5 -313.5v-553h-187v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207zM485 1249q-20 0 -37 -24t-18 -66h-125q6 118 56 179t124 61q52 0 119 -49q59 -46 85 -32t36 81
h125q-6 -117 -57 -175.5t-125 -58.5q-53 0 -117 52q-46 32 -66 32z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM907 481q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86q-91 -88 -91 -228q0 -142 91 -233q91 -88 225 -88q135 0 223 88
q92 89 92 233zM696 1135h-178l-244 315h224z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM276 481q0 -142 91 -233q91 -88 225 -88q135 0 223 88q92 89 92 233q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86
q-91 -88 -91 -228zM670 1450h223l-244 -315h-178z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM907 481q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86q-91 -88 -91 -228q0 -142 91 -233q91 -88 225 -88q135 0 223 88
q92 89 92 233zM913 1135h-168l-153 178l-152 -178h-168l226 315h190z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM907 481q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86q-91 -88 -91 -228q0 -142 91 -233q91 -88 225 -88q135 0 223 88
q92 89 92 233zM500 1249q-21 0 -38 -24t-18 -66h-125q6 118 56.5 179t124.5 61q51 0 118 -49q59 -46 85 -32t36 81h125q-6 -117 -57 -175.5t-125 -58.5q-53 0 -117 52q-46 32 -65 32z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM276 481q0 -142 91 -233q91 -88 225 -88q135 0 223 88q92 89 92 233q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86
q-91 -88 -91 -228zM420 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM676 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="1099" 
d="M455 92q-38 37 -38 92t38 92t94 37t94 -37q41 -37 41 -92t-41 -92q-38 -35 -94 -35t-94 35zM455 776q-38 37 -38 92t38 90q38 37 94 37t94 -37q41 -35 41 -90t-41 -92q-38 -37 -94 -37t-94 37zM129 438v170h842v-170h-842z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1181" 
d="M295 68l-117 -119l-96 96l115 117q-107 131 -107 319q0 222 143 359t357 137q170 0 307 -96l102 104l97 -94l-103 -105q99 -132 99 -305q0 -220 -148 -362q-145 -139 -354 -139q-170 0 -295 88zM590 795q-133 0 -225 -86q-91 -88 -91 -228q0 -108 52 -184l440 448
q-77 50 -176 50zM590 160q135 0 223 88q92 89 92 233q0 91 -45 168l-434 -446q70 -43 164 -43z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM711 1135h-179l-243 315h223z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM668 1450h223l-244 -315h-178z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM879 1135h-168l-154 178l-151 -178h-168l225 315h190z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM418 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35z
M674 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1042" 
d="M219 -465l201 494l-383 927h199l286 -692l287 692h199l-594 -1421h-195zM563 1450h223l-243 -315h-178z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1232" 
d="M342 -436h-186v1911h186v-637q49 65 132.5 101t178.5 36q211 0 348 -137q140 -137 140 -359q0 -220 -140 -360q-139 -139 -348 -139q-94 0 -178 37.5t-133 103.5v-557zM956 479q0 140 -86 223q-86 86 -217 86q-122 0 -217 -83q-94 -82 -94 -226t88.5 -229.5t222.5 -85.5
q130 0 215 88q88 88 88 227z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1042" 
d="M219 -465l201 494l-383 927h199l286 -692l287 692h199l-594 -1421h-195zM354 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM610 1204q-37 35 -37 86t37 86t90.5 35t90.5 -35t37 -86t-37 -86t-90.5 -35t-90.5 35z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1320" 
d="M662 1157l-234 -627h467zM1098 0l-137 358h-599l-137 -358h-202l536 1411h205l534 -1411h-200zM401 1642v164h517v-164h-517z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM367 1188v164h516v-164h-516z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1320" 
d="M895 530l-233 627l-234 -627h467zM362 358l-137 -358h-202l536 1411h205l534 -1411h-200l-137 358h-599zM393 1853h152q0 -55 30.5 -87.5t83.5 -32.5q50 0 81.5 35.5t31.5 84.5h156q0 -121 -73 -191.5t-196 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM358 1399h152q0 -55 31 -88t84 -33q49 0 80.5 36t31.5 85h156q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -198 73.5t-69 188.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1320" 
d="M662 1157l-234 -627h467zM1028 -127l68 129l-135 356h-599l-137 -358h-202l536 1411h205l534 -1411h-90l-63 -178q-12 -32 -0.5 -64t41.5 -45q72 -21 102 47l6 45l131 9q0 -57 -20 -101q-26 -61 -75 -96t-109 -35q-37 0 -80 17q-61 26 -95 76t-34 112q0 43 16 86z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1253" 
d="M1104 -240l6 45l131 9q0 -57 -20 -101q-26 -61 -75.5 -96t-109.5 -35q-38 0 -80 17q-61 26 -95 76t-34 112q0 40 17 86l69 129v145q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120
v143h185v-956h-74l-63 -178q-13 -32 -1.5 -64t41.5 -45q73 -22 103 47zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90q133 0 225 86t92 233z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1398" 
d="M1350 223q-97 -116 -236 -179.5t-299 -63.5q-301 0 -508 210q-207 213 -207 512t207 512q210 213 508 213q158 0 295.5 -62.5t235.5 -176.5l-144 -119q-158 174 -387 174q-221 0 -373 -158q-151 -157 -151 -383t151 -383q149 -155 373 -155q239 0 391 180zM872 1905h224
l-244 -316h-178z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1048" 
d="M598 977q227 0 369 -154l-142 -118q-90 90 -227 90q-133 0 -227 -88q-95 -86 -95 -226q0 -142 95 -231q96 -90 227 -90q139 0 227 94l144 -119q-143 -155 -371 -155q-210 0 -358 139q-148 142 -148 362t148 359q146 137 358 137zM637 1450h223l-244 -315h-178z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="1398" 
d="M1206 344l144 -121q-97 -116 -236 -179.5t-299 -63.5q-301 0 -508 210q-207 213 -207 512t207 512q210 213 508 213q158 0 295.5 -62.5t235.5 -176.5l-144 -119q-158 174 -387 174q-221 0 -373 -158q-151 -157 -151 -383t151 -383q149 -155 373 -155q239 0 391 180z
M1133 1589h-168l-154 178l-152 -178h-167l225 316h190z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="1048" 
d="M598 977q227 0 369 -154l-142 -118q-90 90 -227 90q-133 0 -227 -88q-95 -86 -95 -226q0 -142 95 -231q96 -90 227 -90q139 0 227 94l144 -119q-143 -155 -371 -155q-210 0 -358 139q-148 142 -148 362t148 359q146 137 358 137zM911 1135h-168l-153 178l-152 -178h-168
l226 315h190z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="1398" 
d="M1350 223q-97 -116 -236 -179.5t-299 -63.5q-301 0 -508 210q-207 213 -207 512t207 512q210 213 508 213q158 0 295.5 -62.5t235.5 -176.5l-144 -119q-158 174 -387 174q-221 0 -373 -158q-151 -157 -151 -383t151 -383q149 -155 373 -155q239 0 391 180zM688 1677
q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="1048" 
d="M598 977q227 0 369 -154l-142 -118q-90 90 -227 90q-133 0 -227 -88q-95 -86 -95 -226q0 -142 95 -231q96 -90 227 -90q139 0 227 94l144 -119q-143 -155 -371 -155q-210 0 -358 139q-148 142 -148 362t148 359q146 137 358 137zM498 1223q-37 35 -37 86t37 86
q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1398" 
d="M1350 223q-97 -116 -236 -179.5t-299 -63.5q-301 0 -508 210q-207 213 -207 512t207 512q210 213 508 213q158 0 295.5 -62.5t235.5 -176.5l-144 -119q-158 174 -387 174q-221 0 -373 -158q-151 -157 -151 -383t151 -383q149 -155 373 -155q239 0 391 180zM631 1905
l151 -179l154 179h168l-225 -316h-191l-225 316h168z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1048" 
d="M598 977q227 0 369 -154l-142 -118q-90 90 -227 90q-133 0 -227 -88q-95 -86 -95 -226q0 -142 95 -231q96 -90 227 -90q139 0 227 94l144 -119q-143 -155 -371 -155q-210 0 -358 139q-148 142 -148 362t148 359q146 137 358 137zM420 1450l151 -178l154 178h168
l-225 -315h-191l-225 315h168z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1482" 
d="M193 0v1411h546q292 0 463 -201q172 -199 172 -508q0 -305 -170 -505q-170 -197 -465 -197h-546zM739 178q212 0 328 146q117 147 117 378q0 234 -117 381q-114 150 -328 150h-356v-1055h356zM614 1905l152 -179l154 179h167l-225 -316h-190l-226 316h168z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1232" 
d="M893 1444h184v-1444h-180v125q-50 -67 -135.5 -105t-179.5 -38q-211 0 -351 137q-139 139 -139 362t139 359q141 135 351 135q209 0 311 -135v604zM276 481q0 -138 86 -227q88 -88 220 -88q131 0 221 84q90 87 90 231q0 142 -96 226t-215 84q-131 0 -220 -86
q-86 -83 -86 -224zM1282 1016l-119 39q62 82 84 159q22 76 10 197h185q3 -263 -160 -395z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1482" 
d="M743 0h-538v637h-191v139h191v635h538q292 0 461 -199q168 -198 168 -510q0 -310 -168 -505q-170 -197 -461 -197zM395 1223v-447h338v-139h-338v-449h348q209 0 324 142q115 145 115 372q0 230 -115 375q-116 146 -324 146h-348z" />
    <glyph glyph-name="dslash" unicode="&#x111;" horiz-adv-x="1253" 
d="M1251 1145h-153v-2v-1143h-187v147q-46 -78 -138 -122.5t-198 -44.5q-202 0 -344 139q-139 139 -139 360q0 222 139 361q137 137 342 137q111 0 201.5 -43t136.5 -121v332h-307v127h307v205h187v-205h153v-127zM592 795q-133 0 -221 -88q-88 -85 -88 -226t88 -229
q90 -90 221 -90t223 86q94 88 94 233q0 146 -96 230t-221 84z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="1157" 
d="M193 0v1411h835v-178h-645v-451h563v-178h-563v-426h666v-178h-856zM358 1642v164h516v-164h-516z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM315 1188v164h516v-164h-516z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="1157" 
d="M1028 1411v-178h-645v-451h563v-178h-563v-426h666v-178h-856v1411h835zM311 1853h152q0 -55 31 -87.5t84 -32.5q49 0 80.5 35.5t31.5 84.5h156q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -198 73.5t-69 188.5z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM307 1399h152q0 -55 30.5 -88t83.5 -33q49 0 81 36t32 85h156q0 -121 -73 -191.5t-196 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="1157" 
d="M1028 1411v-178h-645v-451h563v-178h-563v-426h666v-178h-856v1411h835zM532 1677q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM492 1223q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1157" 
d="M1049 0h-11l-55 -156q-12 -32 -0.5 -63.5t41.5 -44.5q72 -21 102 47l7 45l131 8q0 -54 -21 -100q-26 -61 -75 -96t-109 -35q-40 0 -80 16q-61 26 -95 76.5t-34 112.5q0 43 16 86l56 104h-729v1411h835v-178h-645v-451h563v-178h-563v-426h666v-178z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1140" 
d="M776 -178q-12 -32 -0.5 -64t41.5 -45q73 -22 103 47l6 45l131 9q0 -54 -21 -101q-26 -61 -75 -96t-109 -35q-38 0 -80 17q-61 26 -95 76t-34 112q0 43 16 86l68 125q-71 -18 -145 -18q-209 0 -351 139q-139 139 -139 362t139 359q140 137 351 137q209 0 340 -137
q135 -135 135 -359q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5l160 -62q-4 -9 -11.5 -20t-20.5 -27t-22 -26t-27.5 -31t-25.5 -29q-83 -100 -139 -278zM279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="1157" 
d="M1028 1411v-178h-645v-451h563v-178h-563v-426h666v-178h-856v1411h835zM426 1905l152 -179l153 179h168l-225 -316h-191l-225 316h168z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="1138" 
d="M279 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM1057 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-209 0 -351 139q-139 139 -139 362t139 359
q140 137 351 137q209 0 340 -137q135 -135 135 -359zM422 1450l151 -178l154 178h168l-225 -315h-191l-225 315h168z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="1548" 
d="M1290 1241l-137 -127q-146 131 -340 131q-220 0 -371 -160q-151 -157 -151 -383t151 -383t371 -157q191 0 317 102q125 104 134 279h-402v166h619q0 -357 -175.5 -543t-492.5 -186q-302 0 -506 210q-207 213 -207 512q0 302 207 515q204 210 506 210q279 0 477 -186z
M1102 1589h-168l-154 178l-151 -178h-168l225 316h191z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="1251" 
d="M225 -176l74 39q40 -76 116 -120t160 -44q340 0 340 434q0 3 -1 11.5t-1 11.5q-40 -80 -134 -128t-201 -48q-205 0 -345 137q-141 138 -141 358q0 225 141 363q142 139 345 139q106 0 199 -46t136 -124v149h185v-811q0 -622 -523 -622q-136 0 -250.5 68t-181.5 190
q16 7 82 43zM911 477q0 149 -92 232q-92 86 -225 86q-131 0 -221 -90t-90 -228q0 -144 88 -229q89 -86 223 -86q126 0 221 86q96 87 96 229zM946 1135h-168l-153 178l-152 -178h-168l225 315h191z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="1548" 
d="M1290 1241l-137 -127q-146 131 -340 131q-220 0 -371 -160q-151 -157 -151 -383t151 -383t371 -157q191 0 317 102q125 104 134 279h-402v166h619q0 -357 -175.5 -543t-492.5 -186q-302 0 -506 210q-207 213 -207 512q0 302 207 515q204 210 506 210q279 0 477 -186z
M514 1853h152q0 -55 30.5 -87.5t83.5 -32.5q50 0 81.5 35.5t31.5 84.5h156q0 -121 -73 -191.5t-196 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="1251" 
d="M299 -137q40 -76 116 -120t160 -44q340 0 340 434q0 3 -1 11.5t-1 11.5q-40 -80 -134 -128t-201 -48q-205 0 -345 137q-141 138 -141 358q0 225 141 363q142 139 345 139q106 0 199 -46t136 -124v149h185v-811q0 -622 -523 -622q-136 0 -250.5 68t-181.5 190q16 7 82 43z
M911 477q0 149 -92 232q-92 86 -225 86q-131 0 -221 -90t-90 -228q0 -144 88 -229q89 -86 223 -86q126 0 221 86q96 87 96 229zM358 1399h152q0 -55 31 -88t84 -33q49 0 80.5 36t31.5 85h156q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -198 73.5t-69 188.5z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="1548" 
d="M1290 1241l-137 -127q-146 131 -340 131q-220 0 -371 -160q-151 -157 -151 -383t151 -383t371 -157q191 0 317 102q125 104 134 279h-402v166h619q0 -357 -175.5 -543t-492.5 -186q-302 0 -506 210q-207 213 -207 512q0 302 207 515q204 210 506 210q279 0 477 -186z
M715 1677q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="1251" 
d="M299 -137q40 -76 116 -120t160 -44q340 0 340 434q0 3 -1 11.5t-1 11.5q-40 -80 -134 -128t-201 -48q-205 0 -345 137q-141 138 -141 358q0 225 141 363q142 139 345 139q106 0 199 -46t136 -124v149h185v-811q0 -622 -523 -622q-136 0 -250.5 68t-181.5 190q16 7 82 43z
M911 477q0 149 -92 232q-92 86 -225 86q-131 0 -221 -90t-90 -228q0 -144 88 -229q89 -86 223 -86q126 0 221 86q96 87 96 229zM532 1223q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="1548" 
d="M1290 1241l-137 -127q-146 131 -340 131q-220 0 -371 -160q-151 -157 -151 -383t151 -383t371 -157q191 0 317 102q125 104 134 279h-402v166h619q0 -357 -175.5 -543t-492.5 -186q-302 0 -506 210q-207 213 -207 512q0 302 207 515q204 210 506 210q279 0 477 -186z
M760 -512l-119 39q62 82 84 160q22 76 10 196h185q3 -263 -160 -395z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="1251" 
d="M299 -137q40 -76 116 -120t160 -44q340 0 340 434q0 3 -1 11.5t-1 11.5q-40 -80 -134 -128t-201 -48q-205 0 -345 137q-141 138 -141 358q0 225 141 363q142 139 345 139q106 0 199 -46t136 -124v149h185v-811q0 -622 -523 -622q-136 0 -250.5 68t-181.5 190q16 7 82 43z
M911 477q0 149 -92 232q-92 86 -225 86q-131 0 -221 -90t-90 -228q0 -144 88 -229q89 -86 223 -86q126 0 221 86q96 87 96 229zM635 1534l119 -39q-65 -86 -86 -160q-21 -73 -9 -196h-184q-3 263 160 395z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="1449" 
d="M1065 1411h192v-1411h-192v614h-682v-614h-190v1411h190v-623h682v623zM1044 1589h-167l-154 178l-152 -178h-168l226 316h190z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" 
d="M342 516v-516h-186v1477h186v-664q41 81 131 121q93 43 184 43q181 0 284.5 -112t102.5 -312v-553h-186v541q0 116 -62 185t-163 69q-115 0 -203 -70q-88 -67 -88 -209zM571 1567h-168l-153 178l-152 -178h-168l226 315h190z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="1466" 
d="M1264 1411v-256h143v-127h-143v-1028h-191v610h-680v-610h-190v1028h-142v127h142v256h190v-256h680v256h191zM393 1028v-237h680v237h-680z" />
    <glyph glyph-name="hbar" unicode="&#x127;" 
d="M608 1145h-266v-332q41 81 131 121q93 43 184 43q181 0 284.5 -112t102.5 -312v-553h-186v541q0 116 -62 185t-163 69q-115 0 -203 -70q-88 -67 -88 -209v-516h-186v1145h-144v127h144v205h186v-205h266v-127z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="575" 
d="M197 1704q-21 0 -38 -24t-18 -66h-125q6 118 56.5 178.5t124.5 60.5q51 0 118 -49q59 -46 85 -32t36 81h125q-6 -116 -57 -174.5t-125 -58.5q-54 0 -117 51q-47 33 -65 33zM193 0v1411h190v-1411h-190z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="493" 
d="M156 956h182v-956h-182v956zM164 1249q-20 0 -37 -24t-18 -66h-125q6 118 56 179t124 61q52 0 119 -49q38 -32 63 -34.5t38 18.5t19 65h125q-6 -117 -57 -175.5t-125 -58.5q-53 0 -117 52q-46 32 -65 32z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="575" 
d="M31 1642v164h516v-164h-516zM193 0v1411h190v-1411h-190z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="493" 
d="M156 956h182v-956h-182v956zM-2 1188v164h516v-164h-516z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="575" 
d="M383 1411v-1411h-190v1411h190zM20 1853h152q0 -55 31 -87.5t84 -32.5q49 0 80.5 35.5t31.5 84.5h156q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -198 73.5t-69 188.5z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="493" 
d="M338 0h-182v956h182v-956zM-10 1399h151q0 -55 31 -88t84 -33q49 0 81 36t32 85h155q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="575" 
d="M383 0h-80l-63 -178q-12 -32 -0.5 -64t41.5 -45q72 -21 102 47l6 45l131 9q0 -57 -20 -101q-26 -61 -75.5 -96t-109.5 -35q-37 0 -79 17q-61 26 -95.5 76t-34.5 112q0 40 17 86l70 129v1409h190v-1411z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="495" 
d="M340 0h-72l-63 -178q-12 -32 -0.5 -64t41.5 -45q72 -21 102 47l6 45l131 9q0 -57 -20 -101q-26 -61 -75 -96t-109 -35q-37 0 -80 17q-61 26 -95 76t-34 112q0 43 16 86l68 127v956h184v-956zM186 1223q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86
q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="Idot" unicode="&#x130;" horiz-adv-x="575" 
d="M383 1411v-1411h-190v1411h190zM197 1677q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="495" 
d="M156 956h184v-956h-184v956z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="1150" 
d="M371 -492l18 181q56 -17 135 -17q121 0 182.5 73.5t61.5 199.5v1466h190v-1466q0 -211 -109.5 -335t-324.5 -124q-40 0 -153 22zM193 0v1411h190v-1411h-190z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="995" 
d="M496 -469h-127v174h110q175 0 172 238v1013h189v-1015q0 -410 -344 -410zM653 1223q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35zM340 0h-184v956h184v-956zM156 1223q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35
q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="575" 
d="M-205 -492l19 181q56 -17 135 -17q121 0 182.5 73.5t61.5 199.5v1466h190v-1466q0 -211 -109.5 -335t-324.5 -124q-41 0 -154 22zM594 1589h-168l-154 178l-151 -178h-168l225 316h191z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="499" 
d="M0 -469h-127v174h111q175 0 172 238v1013h188v-1015q0 -410 -344 -410zM580 1135h-168l-154 178l-152 -178h-167l225 315h190z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="1183" 
d="M1085 1411l-542 -704l655 -707h-256l-559 602v-602h-190v1411h190v-610l467 610h235zM616 -512l-118 39q62 82 84 160q22 76 10 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="1028" 
d="M776 0l-436 467v-467h-184v1477h184v-854l330 333h254l-410 -413l508 -543h-246zM506 -512l-119 39q62 82 84 160q22 76 10 196h185q3 -263 -160 -395z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="1011" 
d="M733 0l-393 383v-383h-184v956h184v-383l365 383h243l-444 -473l487 -483h-258z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="1013" 
d="M383 1411v-1233h575v-178h-765v1411h190zM403 1882h224l-244 -315h-178z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="497" 
d="M342 1477v-1477h-186v1477h186zM356 1907h224l-244 -316h-178z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="1013" 
d="M193 0v1411h190v-1233h575v-178h-765zM492 -512l-119 39q62 82 84 160q22 76 10 196h184q3 -264 -159 -395z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="497" 
d="M182 -512l-119 39q62 82 84 160q22 79 11 196h184q3 -263 -160 -395zM156 0v1477h186v-1477h-186z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="1013" 
d="M193 0v1411h190v-1233h575v-178h-765zM637 1016l-119 39q62 82 84 159q22 76 10 197h185q3 -263 -160 -395z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="497" 
d="M547 1081l-119 39q62 82 84 160q22 76 10 197h185q3 -264 -160 -396zM156 0v1477h186v-1477h-186z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="1013" 
d="M193 0v1411h190v-1233h575v-178h-765zM584 442q-38 37 -38 92.5t38 92.5t94 37t94 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94 -35t-94 35z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="690" 
d="M496 442q-38 37 -38 92.5t38 92.5t94 37t94 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94 -35t-94 35zM156 0v1477h186v-1477h-186z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1021" 
d="M391 1411v-485l213 133v-135l-213 -133v-603h574v-188h-762v672l-133 -84v135l133 84v604h188z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="522" 
d="M172 1477h184v-576l158 103v-132l-158 -102v-770h-184v653l-137 -88v131l137 88v693z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1400" 
d="M1026 0l-643 1063v-1063h-190v1411h188l637 -1055v1055h190v-1411h-182zM750 1905h223l-244 -316h-178z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1153" 
d="M342 516v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 273.5 -110.5t95.5 -313.5v-553h-187v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207zM643 1450h223l-243 -315h-179z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="1400" 
d="M1208 1411v-1411h-182l-643 1063v-1063h-190v1411h188l637 -1055v1055h190zM651 -512l-119 39q62 82 84 160q22 79 11 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="1153" 
d="M342 516v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 273.5 -110.5t95.5 -313.5v-553h-187v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207zM518 -512l-119 39q62 82 84 160q22 79 11 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1400" 
d="M1026 0l-643 1063v-1063h-190v1411h188l637 -1055v1055h190v-1411h-182zM547 1905l151 -179l154 179h168l-225 -316h-191l-225 316h168z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1153" 
d="M342 516v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 273.5 -110.5t95.5 -313.5v-553h-187v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207zM424 1450l151 -178l154 178h168l-225 -315h-191l-225 315h168z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="1153" 
d="M18 961l-133 38q98 136 107 234q3 50 -8 121h194q3 -261 -160 -393zM342 516v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 273.5 -110.5t95.5 -313.5v-553h-187v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="1396" 
d="M1014 -47v67l-633 1035v-1055h-188v1411h186l635 -1055v1055h190v-1403q0 -514 -432 -514q-62 0 -156 23l19 190q45 -12 113 -18q87 -6 139 24q127 56 127 240z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="1150" 
d="M1010 553l-2 -524q0 -478 -424 -478q-62 0 -150 21l17 186q73 -24 133 -24q119 0 178 71t61 195v541q0 118 -56.5 186t-154.5 68q-119 0 -194.5 -72t-75.5 -207v-516h-186v956h186v-143q40 77 123.5 120.5t175.5 43.5q178 0 274.5 -111t94.5 -313z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM557 1642v164h516v-164h-516z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM907 481q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86q-91 -88 -91 -228q0 -142 91 -233q91 -88 225 -88q135 0 223 88
q92 89 92 233zM315 1188v164h516v-164h-516z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM547 1853h151q0 -55 31 -87.5t84 -32.5q50 0 81.5 35.5t31.5 84.5h155q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM276 481q0 -142 91 -233q91 -88 225 -88q135 0 223 88q92 89 92 233q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86
q-91 -88 -91 -228zM326 1399h151q0 -55 31 -88t84 -33q49 0 81 36t32 85h155q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="1630" 
d="M100 702q0 302 207 515q204 210 506 210q304 0 508 -210q209 -212 209 -515q0 -300 -209 -512q-204 -210 -508 -210q-302 0 -506 210q-207 213 -207 512zM813 1243q-219 0 -371 -158q-151 -157 -151 -383q0 -228 149 -383t373 -155t373 155q151 157 151 383t-151 383
q-152 158 -373 158zM739 1905h189l-183 -316h-170zM1030 1905h193l-209 -316h-166z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="1185" 
d="M92 481q0 221 144 359q143 137 356 137t356 -137q146 -140 146 -359q0 -220 -148 -362q-145 -139 -354 -139t-354 139q-146 140 -146 362zM276 481q0 -142 91 -233q91 -88 225 -88q135 0 223 88q92 89 92 233q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86
q-91 -88 -91 -228zM485 1450h189l-182 -315h-170zM776 1450h193l-209 -315h-166z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2293" 
d="M1331 1184v227h832v-188h-641v-437h561v-188h-561v-410h663v-188h-854v221q-95 -114 -230.5 -177.5t-289.5 -63.5q-300 0 -504 210q-205 211 -205 512q0 304 205 515q204 210 504 210q153 0 289 -64.5t231 -178.5zM811 170q213 0 360 147q151 151 160 363v45
q-9 214 -160 362q-147 150 -360 150q-219 0 -369 -156q-149 -155 -149 -379q0 -223 149 -378q151 -154 369 -154z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1955" 
d="M1874 481q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-124 0 -229 52t-171 146q-69 -94 -176 -146t-231 -52q-209 0 -354 139q-146 140 -146 362q0 221 144 359
q143 137 356 137q263 0 407 -197q138 197 400 197q209 0 340 -137q135 -135 135 -359zM907 481q0 142 -92 228q-89 86 -223 86q-133 0 -225 -86q-91 -88 -91 -228q0 -142 91 -233q91 -88 225 -88q135 0 223 88q92 89 92 233zM1096 553h594q-17 118 -95.5 187t-195.5 69
q-122 0 -204 -69t-99 -187z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1267" 
d="M1161 0h-231l-365 528h-180v-528h-192v1411h542q209 0 314 -129q104 -128 104 -313q0 -171 -92 -295q-89 -124 -277 -139zM385 1233v-526h336q116 0 175.5 72.5t59.5 189.5q0 115 -58.5 189.5t-168.5 74.5h-344zM618 1905h224l-244 -316h-178z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="808" 
d="M342 348v-348h-186v956h186v-215q70 236 266 236q114 0 166 -37l-29 -174q-65 33 -141 33q-127 0 -194.5 -121.5t-67.5 -329.5zM356 1450h224l-244 -315h-178z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="1267" 
d="M784 535l377 -535h-231l-365 528h-180v-528h-192v1411h542q209 0 314 -129q104 -128 104 -313q0 -171 -92 -295q-89 -124 -277 -139zM729 1233h-344v-526h336q116 0 175.5 72.5t59.5 189.5q0 115 -58.5 189.5t-168.5 74.5zM586 -512l-119 39q62 82 84 160q22 76 10 196
h184q3 -264 -159 -395z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="808" 
d="M342 348v-348h-186v956h186v-215q70 236 266 236q114 0 166 -37l-29 -174q-65 33 -141 33q-127 0 -194.5 -121.5t-67.5 -329.5zM182 -512l-119 39q62 82 84 160q22 79 11 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1267" 
d="M1161 0h-231l-365 528h-180v-528h-192v1411h542q209 0 314 -129q104 -128 104 -313q0 -171 -92 -295q-89 -124 -277 -139zM385 1233v-526h336q116 0 175.5 72.5t59.5 189.5q0 115 -58.5 189.5t-168.5 74.5h-344zM479 1905l152 -179l153 179h168l-225 -316h-190l-226 316
h168z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="808" 
d="M342 348v-348h-186v956h186v-215q70 236 266 236q114 0 166 -37l-29 -174q-65 33 -141 33q-127 0 -194.5 -121.5t-67.5 -329.5zM268 1450l152 -178l153 178h168l-225 -315h-190l-226 315h168z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1173" 
d="M297 408q3 -99 88 -175q84 -75 215 -75q123 0 197 57.5t88 149.5q7 100 -58 162.5t-182 88.5l-182 46q-344 89 -344 368q0 174 135 285q133 112 321 112q197 0 326 -114t129 -299h-190q0 104 -74.5 167.5t-192.5 63.5q-106 0 -186 -59q-78 -60 -78 -154q0 -146 201 -196
l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-18 -158 -145 -264q-125 -104 -326 -104q-211 0 -352 126q-142 130 -142 302h191zM629 1905h223l-244 -316h-178z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="899" 
d="M504 391l-131 31q-140 38 -204 112t-52 199q9 101 106 174q94 70 226 70q139 0 229 -69t102 -201h-178q-7 50 -49 79t-100 29q-54 0 -93.5 -24t-50.5 -62q-12 -47 2 -76q20 -45 113 -73l141 -35q248 -61 248 -279q0 -130 -108 -209q-109 -77 -263 -77q-136 0 -245 83
q-108 82 -111 224h170q12 -73 78 -109q62 -39 151 -33q65 4 104.5 38.5t41.5 86.5q3 92 -127 121zM494 1450h223l-244 -315h-178z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="1173" 
d="M106 408h191q3 -99 88 -175q84 -75 215 -75q123 0 197 57.5t88 149.5q7 100 -58 162.5t-182 88.5l-182 46q-344 89 -344 368q0 174 135 285q133 112 321 112q197 0 326 -114t129 -299h-190q0 104 -74.5 167.5t-192.5 63.5q-106 0 -186 -59q-78 -60 -78 -154
q0 -146 201 -196l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-18 -158 -145 -264q-125 -104 -326 -104q-211 0 -352 126q-142 130 -142 302zM889 1589h-168l-154 178l-151 -178h-168l225 316h191z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="899" 
d="M504 391l-131 31q-140 38 -204 112t-52 199q9 101 106 174q94 70 226 70q139 0 229 -69t102 -201h-178q-7 50 -49 79t-100 29q-54 0 -93.5 -24t-50.5 -62q-12 -47 2 -76q20 -45 113 -73l141 -35q248 -61 248 -279q0 -130 -108 -209q-109 -77 -263 -77q-136 0 -245 83
q-108 82 -111 224h170q12 -73 78 -109q62 -39 151 -33q65 4 104.5 38.5t41.5 86.5q3 92 -127 121zM770 1135h-168l-153 178l-152 -178h-168l225 315h191z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1173" 
d="M520 -240q30 -69 103 -47q30 13 41.5 45t-0.5 64l-58 158h-6q-211 0 -352 126q-142 130 -142 302h191q3 -99 88 -175q84 -75 215 -75q123 0 197 57.5t88 149.5q7 100 -59 163t-181 88l-182 46q-344 89 -344 368q0 174 135 285q133 112 321 112q197 0 326 -114t129 -299
h-190q0 104 -74.5 167.5t-192.5 63.5q-106 0 -186 -59q-78 -60 -78 -154q0 -146 201 -196l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-18 -136 -111 -231.5t-243 -124.5l63 -119q17 -46 17 -86q0 -62 -34 -112t-95 -76q-42 -17 -80 -17q-60 0 -109.5 35t-75.5 96
q-20 44 -20 101l131 -9q-3 -30 6 -45z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="901" 
d="M573 -2l68 -125q16 -43 16 -86q0 -62 -34 -112t-95 -76q-42 -17 -79 -17q-60 0 -109.5 35t-75.5 96q-20 44 -20 101l131 -9q-3 -30 6 -45q30 -68 102 -47q30 13 41.5 45t-0.5 64l-57 160q-3 0 -12.5 -1t-12.5 -1q-136 0 -245 83q-108 82 -111 224h170q12 -73 78 -109
q62 -39 151 -33q65 4 104.5 38.5t41.5 86.5q3 92 -127 121l-131 31q-140 38 -204 112t-52 199q9 101 106 174q94 70 226 70q139 0 229 -69t102 -201h-178q-7 50 -49 79t-100 29q-54 0 -93.5 -24t-50.5 -62q-12 -47 2 -76q20 -45 113 -73l141 -35q248 -61 248 -279
q0 -98 -66 -169.5t-174 -98.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1173" 
d="M297 408q3 -99 88 -175q84 -75 215 -75q123 0 197 57.5t88 149.5q7 100 -58 162.5t-182 88.5l-182 46q-344 89 -344 368q0 174 135 285q133 112 321 112q197 0 326 -114t129 -299h-190q0 104 -74.5 167.5t-192.5 63.5q-106 0 -186 -59q-78 -60 -78 -154q0 -146 201 -196
l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-18 -158 -145 -264q-125 -104 -326 -104q-211 0 -352 126q-142 130 -142 302h191zM438 1905l152 -179l153 179h168l-225 -316h-190l-226 316h168z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="899" 
d="M504 391l-131 31q-140 38 -204 112t-52 199q9 101 106 174q94 70 226 70q139 0 229 -69t102 -201h-178q-7 50 -49 79t-100 29q-54 0 -93.5 -24t-50.5 -62q-12 -47 2 -76q20 -45 113 -73l141 -35q248 -61 248 -279q0 -130 -108 -209q-109 -77 -263 -77q-136 0 -245 83
q-108 82 -111 224h170q12 -73 78 -109q62 -39 151 -33q65 4 104.5 38.5t41.5 86.5q3 92 -127 121zM297 1450l152 -178l153 178h168l-225 -315h-191l-225 315h168z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="1163" 
d="M57 1231v180h1049v-180h-428v-1231h-193v1231h-428zM510 -512l-119 39q62 82 84 160q22 76 10 196h185q3 -263 -160 -395z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="671" 
d="M414 1317v-361h202v-157h-202v-799h-187v799h-172v157h172v361h187zM252 -512l-119 39q62 82 84 160q22 76 10 196h185q3 -263 -160 -395z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="1163" 
d="M1106 1411v-180h-428v-1231h-193v1231h-428v180h1049zM428 1905l152 -179l153 179h168l-225 -316h-191l-225 316h168z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="671" 
d="M616 956v-157h-202v-799h-187v799h-172v157h172v361h187v-361h202zM612 1081l-118 39q62 82 84 160q22 76 10 197h184q3 -264 -160 -396z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="1161" 
d="M676 1223v-383h272v-135h-272v-705h-191v705h-270v135h270v383h-432v188h1055v-188h-432z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="675" 
d="M416 0h-187v463h-139v125h139v211h-172v157h172v361h187v-361h202v-157h-202v-211h155v-125h-155v-463z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1388" 
d="M602 1704q-20 0 -37 -24t-18 -66h-125q6 118 56 178.5t124 60.5q52 0 119 -49q59 -46 85 -32t36 81h125q-6 -116 -57 -174.5t-126 -58.5q-53 0 -116 51q-47 33 -66 33zM1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366
v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM498 1249q-21 0 -38 -24t-18 -66h-125q6 118 56.5 179t124.5 61q51 0 118 -49q59 -46 85 -32
t36 81h125q-6 -117 -57 -175.5t-125 -58.5q-53 0 -117 52q-46 32 -65 32z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231zM436 1642v164h516v-164h-516z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM332 1188v164h516v-164h-516z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231zM426 1853h152q0 -55 30.5 -87.5t83.5 -32.5q50 0 81.5 35.5t31.5 84.5h156q0 -121 -73 -191.5
t-196 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="1150" 
d="M811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5zM324 1399h151q0 -55 31 -88t84 -33q49 0 80.5 36t31.5 85h156q0 -121 -72.5 -191.5
t-195.5 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1388" 
d="M1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231zM477 1776q0 92 61 154.5t152 62.5q92 0 155.5 -62.5t63.5 -154.5q0 -91 -63.5 -152
t-155.5 -61q-88 0 -150.5 62.5t-62.5 150.5zM586 1776q0 -43 30.5 -74t73.5 -31q44 0 77.5 31t33.5 74q0 45 -32 76.5t-79 31.5q-43 0 -73.5 -32t-30.5 -76z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1150" 
d="M375 1321q0 92 61 154.5t152 62.5q92 0 155.5 -62.5t63.5 -154.5q0 -91 -63.5 -152t-155.5 -61q-90 0 -151.5 62.5t-61.5 150.5zM483 1321q0 -43 31 -73.5t74 -30.5q44 0 77 30.5t33 73.5q0 45 -31.5 77t-78.5 32q-43 0 -74 -32.5t-31 -76.5zM811 440v516h186v-956h-186
v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1388" 
d="M592 1905h188l-182 -316h-170zM883 1905h192l-209 -316h-166zM1018 485v926h192v-926q0 -224 -151 -366q-151 -139 -365 -139q-215 0 -366 139q-150 141 -150 366v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1150" 
d="M471 1450h188l-182 -315h-170zM762 1450h192l-209 -315h-165zM811 440v516h186v-956h-186v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1388" 
d="M848 -240l6 45l131 9q0 -57 -20 -101q-26 -61 -75.5 -96t-109.5 -35q-38 0 -80 17q-61 26 -95 76t-34 112q0 40 17 86l57 109q-199 15 -334 153q-133 139 -133 350v926h193v-926q0 -143 94 -231q96 -90 229 -90q134 0 230 90q94 88 94 231v926h192v-926q0 -206 -129 -344
q-127 -136 -319 -157l-57 -162q-13 -32 -1.5 -64t41.5 -45q73 -22 103 47z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1150" 
d="M1004 -240l6 45l131 9q0 -54 -21 -101q-26 -61 -75 -96t-109 -35q-38 0 -80 17q-61 26 -95 76t-34 112q0 43 16 86l68 127v143q-40 -77 -122.5 -120t-174.5 -43q-178 0 -274.5 110t-94.5 313v553h187v-540q0 -119 55.5 -186.5t153.5 -67.5q121 0 195.5 70.5t74.5 207.5
v516h186v-956h-73l-64 -178q-12 -32 -0.5 -64t41.5 -45q73 -22 103 47z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="1845" 
d="M1255 307l355 1104h205l-478 -1411h-166l-249 881l-248 -881h-166l-477 1411h205l354 -1104l260 914h145zM1243 1589h-168l-153 178l-152 -178h-168l225 316h191z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="1617" 
d="M1575 956l-354 -956h-154l-260 702l-252 -702h-152l-364 956h199l239 -669l240 669h180l240 -669l239 669h199zM1126 1135h-168l-153 178l-152 -178h-168l226 315h190z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="1286" 
d="M741 649v-649h-196v649l-535 762h228l405 -571l410 571h229zM967 1589h-168l-154 178l-151 -178h-168l225 316h190z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="1042" 
d="M1008 956l-594 -1421h-195l201 494l-383 927h199l286 -692l287 692h199zM848 1135h-168l-154 178l-151 -178h-168l225 315h191z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1286" 
d="M741 649v-649h-196v649l-535 762h228l405 -571l410 571h229zM473 1866q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM729 1659q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1173" 
d="M86 0v166l745 1065h-716v180h958v-166l-749 -1065h749v-180h-987zM647 1905h223l-243 -316h-178z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="1028" 
d="M928 803l-580 -625h580v-178h-842v154l569 624h-536v178h809v-153zM559 1450h223l-243 -315h-179z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1173" 
d="M86 0v166l745 1065h-716v180h958v-166l-749 -1065h749v-180h-987zM487 1677q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="1028" 
d="M928 803l-580 -625h580v-178h-842v154l569 624h-536v178h809v-153zM416 1223q-37 35 -37 86t37 86q38 35 91.5 35t90.5 -35q38 -35 38 -86t-38 -86q-37 -35 -90.5 -35t-91.5 35z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1173" 
d="M86 0v166l745 1065h-716v180h958v-166l-749 -1065h749v-180h-987zM426 1905l152 -179l153 179h168l-225 -316h-191l-225 316h168z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="1028" 
d="M928 803l-580 -625h580v-178h-842v154l569 624h-536v178h809v-153zM354 1450l152 -178l153 178h168l-225 -315h-190l-226 315h168z" />
    <glyph glyph-name="longs" unicode="&#x17f;" horiz-adv-x="624" 
d="M606 1477h105v-179h-105q-181 0 -178 -245v-1053h-188v799h-185v157h185v97q0 202 92 313t274 111z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="1263" 
d="M430 33l164 751h-176l37 172h176l18 82q79 357 355 398q35 7 120 4l76 -4l-35 -170h-118q-65 0 -105 -29t-70 -82q-20 -45 -34 -100q-1 -13 -10.5 -54t-10.5 -45h221l-34 -172h-224l-151 -698q-43 -209 -129.5 -324t-239.5 -139q-50 -8 -199 2l35 168h121q106 0 152 82
q36 56 61 158z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="1320" 
d="M362 358l-137 -358h-202l536 1411h205l534 -1411h-200l-137 358h-599zM752 2234h223l-209 -270q111 -58 111 -188q0 -90 -63.5 -151.5t-156.5 -61.5q-88 0 -150.5 62.5t-62.5 150.5q0 73 41 129.5t107 76.5zM895 530l-233 627l-234 -627h467zM553 1776q0 -43 30.5 -74
t73.5 -31q44 0 77.5 31t33.5 74q0 45 -32 76.5t-79 31.5q-43 0 -73.5 -32t-30.5 -76z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="1253" 
d="M1098 0h-185v147q-46 -78 -137.5 -122.5t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-956zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90
q133 0 225 86t92 233zM664 1763h223l-209 -270q110 -58 110 -188q0 -91 -63.5 -152t-155.5 -61q-90 0 -151.5 62.5t-61.5 150.5q0 73 41 129.5t107 76.5zM465 1305q0 -43 30.5 -74t73.5 -31q44 0 77.5 31t33.5 74q0 45 -32 76.5t-79 31.5q-43 0 -73.5 -32t-30.5 -76z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="1912" 
d="M223 0h-223l891 1411h891v-188h-641v-437h561v-188h-561v-410h663v-188h-852v354h-501zM952 537v610l-389 -610h389zM1141 1905h223l-244 -316h-178z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="1959" 
d="M1098 879q125 98 305 98q209 0 340 -137q135 -135 135 -359q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-181 0 -305 100v-80h-185v147q-46 -78 -137.5 -122.5
t-197.5 -44.5q-203 0 -345 139q-141 138 -141 360q0 223 141 361q140 137 345 137q107 0 199.5 -44t135.5 -120v143h185v-77zM911 481q0 146 -96 230t-221 84q-135 0 -223 -88q-88 -85 -88 -226q0 -139 90 -229t221 -90q133 0 225 86t92 233zM1100 553h594
q-17 118 -95.5 187t-195.5 69q-122 0 -204 -69t-99 -187zM1059 1450h223l-244 -315h-178z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="1628" 
d="M909 1905h224l-244 -316h-178zM201 -33l-97 96l164 168q-170 203 -170 471q0 302 207 515q204 210 506 210q261 0 459 -165l162 165l96 -94l-164 -168q164 -203 164 -463q0 -300 -209 -512q-204 -210 -508 -210q-257 0 -446 155zM289 702q0 -192 110 -335l740 759
q-144 117 -328 117q-219 0 -371 -158q-151 -157 -151 -383zM811 164q224 0 373 155q151 157 151 383q0 183 -104 326l-735 -758q132 -106 315 -106z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="1181" 
d="M668 1450h223l-244 -315h-178zM178 -51l-96 96l115 117q-107 131 -107 319q0 222 143 359t357 137q170 0 307 -96l102 104l97 -94l-103 -105q99 -132 99 -305q0 -220 -148 -362q-145 -139 -354 -139q-170 0 -295 88zM590 795q-133 0 -225 -86q-91 -88 -91 -228
q0 -108 52 -184l440 448q-77 50 -176 50zM590 160q135 0 223 88q92 89 92 233q0 91 -45 168l-434 -446q70 -43 164 -43z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="1173" 
d="M106 408h191q3 -99 88 -175q84 -75 215 -75q123 0 197 57.5t88 149.5q15 113 -70 176q-68 55 -170 75l-182 46q-344 89 -344 368q0 174 135 285q133 112 321 112q197 0 326 -114t129 -299h-190q0 104 -74.5 167.5t-192.5 63.5q-106 0 -186 -59q-78 -60 -78 -154
q0 -146 201 -196l184 -45q202 -51 299.5 -164.5t77.5 -278.5q-18 -158 -145 -264q-125 -104 -326 -104q-211 0 -352 126q-142 130 -142 302zM543 -512l-119 39q62 82 84 160q22 76 10 196h184q3 -264 -159 -395z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="899" 
d="M504 391l-131 31q-140 38 -204 112t-52 199q9 101 106 174q94 70 226 70q139 0 229 -69t102 -201h-178q-7 50 -49 79t-100 29q-54 0 -93.5 -24t-50.5 -62q-12 -47 2 -76q20 -45 113 -73l141 -35q248 -61 248 -279q0 -130 -108 -209q-109 -77 -263 -77q-136 0 -245 83
q-108 82 -111 224h170q12 -73 78 -109q62 -39 151 -33q65 4 104.5 38.5t41.5 86.5q3 92 -127 121zM399 -512l-118 39q62 82 84 160q22 76 10 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="1071" 
d="M856 1135h-168l-153 178l-152 -178h-168l225 315h191z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="1071" 
d="M383 1450l152 -178l153 178h168l-225 -315h-191l-225 315h168z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="985" 
d="M225 1399h152q0 -55 31 -88t84 -33q49 0 80.5 36t31.5 85h156q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -198 73.5t-69 188.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="1077" 
d="M446 1223q-37 35 -37 86t37 86q38 35 92 35t91 -35q38 -35 38 -86t-38 -86q-37 -35 -91 -35t-92 35z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="1087" 
d="M328 1321q0 92 61 154.5t152 62.5q92 0 155.5 -62.5t63.5 -154.5q0 -91 -63.5 -152t-155.5 -61q-90 0 -151.5 62.5t-61.5 150.5zM436 1321q0 -43 31 -73.5t74 -30.5q44 0 77 30.5t33 73.5q0 45 -31.5 77t-78.5 32q-43 0 -74 -32.5t-31 -76.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="1069" 
d="M604 -240l6 45l131 9q0 -57 -20 -101q-26 -61 -75 -96t-109 -35q-37 0 -80 17q-61 26 -95 76t-34 112q0 43 16 86l105 195h100l-88 -246q-12 -32 -0.5 -64t41.5 -45q72 -21 102 47z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="872" 
d="M344 1249q-20 0 -37 -24t-18 -66h-125q6 118 56 179t124 61q52 0 119 -49q59 -46 85 -32t36 81h125q-6 -116 -57.5 -175t-125.5 -59q-52 0 -116 52q-46 32 -66 32z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="974" 
d="M328 1450h188l-182 -315h-170zM618 1450h193l-209 -315h-166z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="1845" 
d="M1255 307l355 1104h205l-478 -1411h-166l-249 881l-248 -881h-166l-477 1411h205l354 -1104l260 914h145zM1016 1589h-178l-244 316h223z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="1617" 
d="M1575 956l-354 -956h-154l-260 702l-252 -702h-152l-364 956h199l239 -669l240 669h180l240 -669l239 669h199zM901 1135h-178l-244 315h223z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="1845" 
d="M1815 1411l-478 -1411h-166l-249 881l-248 -881h-166l-477 1411h205l354 -1104l260 914h145l260 -914l355 1104h205zM1026 1905h223l-243 -316h-179z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="1617" 
d="M1067 0l-260 702l-252 -702h-152l-364 956h199l239 -669l240 669h180l240 -669l239 669h199l-354 -956h-154zM913 1450h224l-244 -315h-178z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="1845" 
d="M1815 1411l-478 -1411h-166l-249 881l-248 -881h-166l-477 1411h205l354 -1104l260 914h145l260 -914l355 1104h205zM750 1866q51 0 87.5 -35t36.5 -86t-36.5 -86t-87.5 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM1006 1659q-37 35 -37 86t37 86t90 35t90 -35
t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="1617" 
d="M1067 0l-260 702l-252 -702h-152l-364 956h199l239 -669l240 669h180l240 -669l239 669h199l-354 -956h-154zM633 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM889 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86
t-90 -35t-90 35z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1286" 
d="M741 649v-649h-196v649l-535 762h228l405 -571l410 571h229zM745 1589h-178l-243 316h223z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1042" 
d="M1008 956l-594 -1421h-195l201 494l-383 927h199l286 -692l287 692h199zM676 1135h-178l-244 315h223z" />
    <glyph glyph-name="thinspace" unicode="&#x2009;" horiz-adv-x="28" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1218" 
d="M145 399v160h928v-160h-928z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1640" 
d="M145 399v160h1350v-160h-1350z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="507" 
d="M313 1409l109 -39q-132 -187 -129 -432h-170q12 158 65.5 285.5t124.5 185.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="501" 
d="M211 930l-109 39q132 187 129 432h170q-12 -158 -65.5 -285.5t-124.5 -185.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="495" 
d="M166 -133l-109 39q132 187 129 432h170q-12 -158 -65.5 -285.5t-124.5 -185.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="813" 
d="M313 1411l109 -39q-132 -187 -129 -432h-170q12 158 65.5 285.5t124.5 185.5zM618 1411l109 -39q-132 -187 -129 -432h-170q12 158 65.5 285.5t124.5 185.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="808" 
d="M518 940l-108 39q132 187 129 432h170q-12 -158 -66 -285.5t-125 -185.5zM213 940l-109 39q132 187 129 432h170q-12 -158 -65.5 -285.5t-124.5 -185.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="800" 
d="M471 -133l-109 39q133 189 130 432h170q-12 -158 -66 -285.5t-125 -185.5zM166 -133l-109 39q132 187 129 432h170q-12 -158 -65.5 -285.5t-124.5 -185.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="847" 
d="M514 1384v-366h248v-184h-248v-1082h-182v1082h-246v184h246v366h182z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="917" 
d="M549 1384v-366h248v-184h-248v-482h248v-182h-248v-418h-182v418h-246v182h246v482h-246v184h246v366h182z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="944" 
d="M256 281q-90 84 -90 208q0 123 90 207t217 84q125 0 215 -84t90 -207q0 -124 -90 -208t-215 -84q-127 0 -217 84z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1470" 
d="M176 14q-38 37 -38 92.5t38 92.5t94.5 37t94.5 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94.5 -35t-94.5 35zM639 14q-38 37 -38 92.5t38 92.5t94 37t94 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94 -35t-94 35zM1102 14q-38 37 -38 92.5t38 92.5t94 37t94 -37
q41 -37 41 -92.5t-41 -92.5q-38 -35 -94 -35t-94 35z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2029" 
d="M956 1378h136l-674 -1378h-135zM627 1104q0 -115 -80 -195q-81 -78 -195 -78q-113 0 -194 78q-78 81 -78 195q0 113 78 194q80 80 194 80q116 0 195 -82q80 -80 80 -192zM483 1104q0 54 -38.5 92.5t-92.5 38.5q-41 3 -73.5 -21t-46 -59.5t-7.5 -75.5t35 -67
q27 -29 67 -35t75.5 7.5t59.5 46t21 73.5zM1323 252q0 -115 -80 -195q-80 -77 -194 -77q-115 0 -195 77q-78 81 -78 195q0 113 78 194q80 80 195 80t194 -82q80 -80 80 -192zM1180 252q0 54 -38.5 92.5t-92.5 38.5q-41 3 -73.5 -21t-46.5 -59.5t-8 -75.5t35 -67
q27 -29 67.5 -35t75.5 7.5t59.5 46t21.5 73.5zM1681 526q114 0 193 -82q82 -79 82 -192q0 -115 -80 -195q-80 -77 -195 -77q-114 0 -194 77q-78 81 -78 195q0 113 78 194q80 80 194 80zM1812 252q0 54 -38.5 92.5t-92.5 38.5q-41 3 -73.5 -21t-46 -59.5t-7.5 -75.5t35 -67
q27 -29 67 -35t75.5 7.5t59.5 46t21 73.5z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="641" 
d="M377 53l-283 424l283 424h162l-283 -424l283 -424h-162z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="641" 
d="M264 901l283 -424l-283 -424h-162l283 424l-283 424h162z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="368" 
d="M571 1372h138l-922 -1392h-139z" />
    <glyph glyph-name="zerosuperior" unicode="&#x2070;" horiz-adv-x="745" 
d="M80 1280q0 92 13.5 162.5t45 130t91 91.5t143.5 32t143.5 -32t91 -91.5t45 -130t13.5 -162.5q0 -410 -293 -410t-293 410zM201 1280q0 -291 172 -291t172 291q0 295 -172 295t-172 -295z" />
    <glyph glyph-name="foursuperior" unicode="&#x2074;" horiz-adv-x="735" 
d="M680 1042h-115v-161h-121v161h-415l446 644h90v-529h115v-115zM444 1464l-206 -307h206v307z" />
    <glyph glyph-name="fivesuperior" unicode="&#x2075;" horiz-adv-x="696" 
d="M53 1038l101 72q23 -56 72.5 -89.5t107.5 -33.5q79 0 127.5 43.5t48.5 110.5q0 64 -45 101.5t-109 37.5q-94 0 -227 -68l-37 48l72 423h411v-114h-311l-18 -99l-19 -98q61 27 129 27q119 0 196 -69t77 -189q0 -123 -81 -198t-214 -75q-88 0 -165.5 46t-115.5 124z" />
    <glyph glyph-name="sixsuperior" unicode="&#x2076;" horiz-adv-x="692" 
d="M616 1528l-108 -45q-46 94 -131 94q-78 0 -126 -75.5t-46 -194.5q50 96 164 96q110 0 182 -84q73 -82 70 -197q-6 -108 -84 -186q-76 -73 -189 -70q-109 5 -180.5 79t-81.5 198q-1 41 -3 90.5t5 103t16.5 105t32.5 97.5t53.5 81t78.5 54.5t108 17.5q169 -9 239 -164z
M211 1141q0 -63 41.5 -106.5t101.5 -45.5q60 -1 100.5 38t45.5 101q2 64 -35.5 107.5t-97.5 44.5q-64 2 -109.5 -38t-46.5 -101z" />
    <glyph glyph-name="sevensuperior" unicode="&#x2077;" horiz-adv-x="669" 
d="M80 1686h563l-440 -805h-142l375 684h-356v121z" />
    <glyph glyph-name="eightsuperior" unicode="&#x2078;" horiz-adv-x="700" 
d="M133 1485q0 88 62.5 149.5t154.5 61.5q93 0 157 -61.5t64 -149.5q0 -72 -59 -137q117 -76 117 -215q0 -110 -80 -187q-79 -76 -199 -76q-119 0 -198 76q-80 77 -80 187q0 141 114 215q-53 59 -53 137zM350 989q66 0 112 42t46 102q0 62 -46.5 107.5t-111.5 45.5h-8
q-62 0 -105.5 -46t-43.5 -107q0 -60 45 -102t112 -42zM344 1389h10q40 1 67.5 29.5t27.5 66.5q0 37 -29 63.5t-70 26.5t-68.5 -26.5t-27.5 -63.5t26 -66t64 -30z" />
    <glyph glyph-name="ninesuperior" unicode="&#x2079;" horiz-adv-x="692" 
d="M82 1032l108 45q46 -94 132 -94q78 0 126 75.5t46 194.5q-50 -96 -164 -96q-110 0 -185 84q-70 82 -67 197q6 109 84 184q75 75 188 72q109 -5 180.5 -79t81.5 -198q6 -48 3 -107.5t-8.5 -121t-26.5 -120t-51.5 -104t-84 -72t-122.5 -24.5q-170 9 -240 164zM487 1419
q0 63 -41.5 106.5t-101.5 45.5q-60 1 -100 -38t-45 -101q-2 -64 35.5 -107.5t97.5 -44.5q64 -2 109 38t46 101z" />
    <glyph glyph-name="zeroinferior" unicode="&#x2080;" horiz-adv-x="745" 
d="M80 100q0 92 13.5 162.5t45 130t91 91.5t143.5 32t143.5 -32t91 -91.5t45 -130t13.5 -162.5q0 -409 -293 -409t-293 409zM201 100q0 -290 172 -290t172 290q0 295 -172 295t-172 -295z" />
    <glyph glyph-name="oneinferior" unicode="&#x2081;" horiz-adv-x="423" 
d="M313 -299h-123v625l-139 -72v117l262 143v-813z" />
    <glyph glyph-name="twoinferior" unicode="&#x2082;" horiz-adv-x="665" 
d="M80 -299v57l323 392q39 49 49.5 96t-4 80t-47.5 53t-77 20q-55 0 -97 -37t-49 -96l-108 29q13 99 84.5 160t169.5 61q109 0 181.5 -68.5t72.5 -173.5q0 -108 -89 -208l-206 -244h297v-121h-500z" />
    <glyph glyph-name="threeinferior" unicode="&#x2083;" horiz-adv-x="655" 
d="M178 332l-82 65q36 58 97.5 89.5t134.5 27.5q94 -3 161 -70q63 -63 60 -151q0 -25 -6 -49q-21 -72 -88 -111q60 -23 96.5 -86t30.5 -129q-9 -104 -97 -168q-86 -68 -196 -59q-77 6 -142 52t-98 116l98 53q19 -44 61.5 -77t88.5 -34h-2q69 -5 121 33.5t53 97.5
q2 57 -44.5 100.5t-109.5 43.5h-92v106h92q41 0 72 26.5t39 65.5q9 50 -14 82q-27 44 -93 47q-88 9 -141 -71z" />
    <glyph glyph-name="fourinferior" unicode="&#x2084;" horiz-adv-x="737" 
d="M682 -137h-115v-162h-121v162h-415l446 643h90v-529h115v-114zM446 285l-206 -308h206v308z" />
    <glyph glyph-name="fiveinferior" unicode="&#x2085;" horiz-adv-x="692" 
d="M53 -141l101 71q23 -56 72.5 -89.5t107.5 -33.5q79 0 127.5 43.5t48.5 110.5q0 64 -45 101.5t-109 37.5q-96 0 -227 -67l-37 47l72 424h411v-115h-311l-18 -98l-19 -98q59 26 129 26q119 0 196 -69t77 -189q0 -123 -81 -197.5t-214 -74.5q-88 0 -165.5 46t-115.5 124z
" />
    <glyph glyph-name="sixinferior" unicode="&#x2086;" horiz-adv-x="690" 
d="M616 348l-108 -45q-46 94 -131 94q-78 0 -126 -75.5t-46 -194.5q50 96 164 96q110 0 182 -84q73 -82 70 -196q-6 -110 -84 -185q-74 -74 -189 -71q-110 5 -181 78.5t-81 197.5q-1 41 -3 90.5t5 103t16.5 105t32.5 97.5t53.5 81t78.5 54.5t108 17.5q169 -9 239 -164z
M211 -39q0 -63 41.5 -106t101.5 -45q60 -1 100.5 38t45.5 101q2 63 -35.5 106.5t-97.5 44.5q-64 2 -109.5 -38t-46.5 -101z" />
    <glyph glyph-name="seveninferior" unicode="&#x2087;" horiz-adv-x="655" 
d="M82 506h563l-440 -805h-142l375 684h-356v121z" />
    <glyph glyph-name="eightinferior" unicode="&#x2088;" horiz-adv-x="700" 
d="M133 305q0 88 62.5 149.5t154.5 61.5q93 0 157 -61.5t64 -149.5q0 -72 -59 -137q117 -76 117 -215q0 -109 -80 -186q-79 -76 -199 -76q-119 0 -198 76q-80 77 -80 186q0 141 114 215q-53 59 -53 137zM350 -190q66 0 112 41.5t46 101.5q0 62 -46.5 107.5t-111.5 45.5h-8
q-62 0 -105.5 -46t-43.5 -107q0 -60 45 -101.5t112 -41.5zM344 209h10q40 1 67.5 29.5t27.5 66.5q0 37 -29 63.5t-70 26.5t-68.5 -26.5t-27.5 -63.5t26 -66t64 -30z" />
    <glyph glyph-name="nineinferior" unicode="&#x2089;" horiz-adv-x="692" 
d="M84 -147l109 45q47 -95 131 -95q78 0 126 76t46 195q-51 -97 -164 -97q-110 0 -185 84q-70 82 -67 197q6 109 84 184q75 75 188 72q110 -5 181 -78.5t81 -197.5q6 -48 3 -107.5t-8.5 -121t-26.5 -120t-51.5 -104t-84 -72t-122.5 -24.5q-170 9 -240 164zM489 240
q0 63 -41.5 106t-101.5 45q-60 1 -100 -38t-45 -101q-2 -64 35.5 -107.5t97.5 -44.5q63 -2 108.5 38.5t46.5 101.5z" />
    <glyph glyph-name="colonmonetary" unicode="&#x20a1;" horiz-adv-x="1437" 
d="M637 0l-80 -223l-96 35l78 221q-85 34 -154 88l-96 -268l-96 32l108 308q-205 211 -205 509q0 282 185 488q188 206 460 233l80 226l97 -35l-68 -189q93 -3 176 -30l64 178l96 -33l-66 -182q120 -57 221 -170l-143 -119q-68 71 -141 111l-359 -1004q56 -12 113 -12
q239 0 391 180l144 -121q-97 -116 -236 -179.5t-299 -63.5q-87 0 -174 20zM811 1243h-27l-333 -936q63 -60 151 -100l361 1012q-70 24 -152 24zM287 702q0 -166 88 -303l295 824q-167 -46 -277 -193q-106 -141 -106 -328z" />
    <glyph glyph-name="lira" unicode="&#x20a4;" horiz-adv-x="1300" 
d="M139 815h156v213q0 176 123 287q124 112 297 112q171 0 286 -118q117 -117 117 -310h-184q0 87 -40.5 150t-107.5 82q-47 14 -92 8q-85 -3 -151 -63t-66 -148v-213h314v-108h-314v-136h314v-108h-314v-279h500q35 0 35 45v117h184v-100q0 -112 -51.5 -179t-145.5 -67
h-893v184h189v279h-156v108h156v136h-156v108z" />
    <glyph glyph-name="uni20A6" unicode="&#x20a6;" horiz-adv-x="1550" 
d="M1274 512v-512h-182l-310 512h-333v-512h-191v512h-141v113h141v151h-141v113h141v522h188l316 -522h321v522h191v-522h168v-113h-168v-151h168v-113h-168zM623 776h-174v-151h264zM1083 776h-254l93 -151h161v151zM553 889l-104 174v-174h104zM1083 512h-94l94 -156v156
z" />
    <glyph glyph-name="peseta" unicode="&#x20a7;" horiz-adv-x="1525" 
d="M272 0v817h-163v113h163v100h-163v113h163v268h564q144 0 242.5 -71.5t142.5 -196.5h213v-113h-185q6 -44 6 -84v-16h179v-113h-195q-33 -135 -135.5 -212t-267.5 -77h-371v-528h-193zM465 930h600v16q0 32 -10 84h-590v-100zM465 817v-110h358q155 0 213 110h-571z
M834 1233h-369v-90h541q-63 90 -172 90z" />
    <glyph glyph-name="rupee" unicode="&#x20a8;" horiz-adv-x="1380" 
d="M743 0l-364 528h-242v179h398q112 0 171 68.5t64 182.5h-633v113h621q-46 162 -215 162h-406l-2 178h1147v-113h-442q94 -88 118 -227h324v-113h-315q-3 -165 -97 -288q-88 -120 -272 -135l377 -535h-232z" />
    <glyph glyph-name="uni20A9" unicode="&#x20a9;" horiz-adv-x="2011" 
d="M590 0l-191 561h-276v113h239l-69 205h-170v112h131l-141 420h204l136 -420h413l66 230h145l66 -230h413l136 420h204l-143 -420h135v-112h-172l-69 -205h241v-113h-278l-191 -561h-166l-159 561h-181l-157 -561h-166zM1520 879h-346l59 -205h221zM834 879h-347l66 -205
h223zM1419 561h-155l73 -254zM743 561h-153l82 -254zM1004 879h-3l-57 -205h119z" />
    <glyph glyph-name="afii57636" unicode="&#x20aa;" horiz-adv-x="1538" 
d="M1032 680v-342h-182v342q0 103 -70.5 171.5t-179.5 68.5h-219v-920h-184v1102h403q190 0 311 -121t121 -301zM506 422v342h182v-342q0 -103 70.5 -171.5t179.5 -68.5h219v920h184v-1102h-403q-192 0 -313 121q-119 119 -119 301z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1603" 
d="M1063 862v-114h-957v114h209q55 233 232 385q181 152 422 152q291 0 512 -225l-129 -130q-176 173 -383 173q-163 0 -287 -98.5t-172 -256.5h553zM106 510v113h957v-113h-551q47 -151 170 -250q125 -96 287 -96q211 0 385 174l129 -131q-221 -225 -514 -225
q-243 0 -418 147t-234 381h-211z" />
    <glyph glyph-name="uni2113" unicode="&#x2113;" horiz-adv-x="804" 
d="M453 182h194v-182h-219q-281 0 -281 326v776q0 325 281 325q138 0 206 -86t68 -239v-389q0 -182 -81 -254q-80 -74 -291 -74v-59q0 -98 41 -125q36 -19 82 -19zM330 1090v-521h98q90 0 90 154v367q0 155 -90 155q-98 0 -98 -155z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="1984" 
d="M1612 1411q111 0 188 -80q80 -77 80 -188t-80 -191q-78 -78 -188 -78q-112 0 -193 78q-78 81 -78 191t78 188q80 80 193 80zM1520 1235q-38 -38 -38 -92t38 -92t92 -38t92 38t38 92t-38 92t-92 38t-92 -38zM1870 692h-518v96h518v-96zM1030 0l-643 1063v-1063h-190v1411
h188l637 -1055v1055h190v-1411h-182z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1728" 
d="M135 1274v121h731l252 -553l252 553h119l94 -785h-133l-66 549l-208 -465h-113l-213 465l-64 -549h-135l78 664h-217v-664h-139v664h-238z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="1349" 
d="M1251 571h-878v-395q135 -129 311 -129q148 0 245.5 63.5t184.5 196.5l64 -39q-63 -98 -129.5 -158.5t-157.5 -95t-207 -34.5q-243 0 -399 172q-156 176 -156 421q0 251 154 422q153 172 409 172q257 0 406 -174q153 -172 153 -422zM373 639h633v334q-139 127 -314 127
q-195 0 -319 -127v-334z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="1179" 
d="M152 457v168h876v-168h-876z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="1216" 
d="M741 621l-104 -218h442v-178h-530l-111 -225h-143l111 225h-269v178h355l106 218h-461v178h547l111 225h145l-111 -225h250v-178h-338z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="1339" 
d="M1171 485v-196l-1036 397v178l1036 398v-199l-778 -287zM154 0v164h1017v-164h-1017z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="1339" 
d="M1204 864v-178l-1036 -397v196l778 291l-778 287v199zM1186 164v-164h-1018v164h1018z" />
    <glyph glyph-name="commaaccent" unicode="&#xf6c3;" horiz-adv-x="567" 
d="M262 -512l-119 39q62 82 84 160q22 79 11 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="rupiah" unicode="&#xf6dd;" horiz-adv-x="2408" 
d="M743 535l377 -535h-231l-365 528h-180v-528h-192v1411h542q209 0 314 -129q104 -128 104 -313q0 -171 -92 -295q-89 -124 -277 -139zM688 1233h-344v-526h336q116 0 175.5 72.5t59.5 189.5q0 115 -58.5 189.5t-168.5 74.5zM1337 956h187v-149q47 78 138.5 124t195.5 46
q201 0 340 -139t139 -363q0 -219 -139 -358q-137 -137 -340 -137q-108 0 -199.5 43t-134.5 120v-608h-187v1421zM1526 473q0 -147 94 -229t219 -82q135 0 222 86.5t87 224.5q0 142 -90 232t-219 90q-132 0 -223 -88q-90 -87 -90 -234z" />
    <glyph glyph-name="apple" unicode="&#xf8ff;" horiz-adv-x="1734" 
d="M164 1407h1407v-1407h-1407v1407zM872 680l-536 539v-773h166l-152 154v365l522 -519l525 519v-365l-152 -154h172v773zM395 197l-28 73h-31v-108h18v76l27 -76h20l23 76v-76h18v108h-28zM508 270q-51 0 -51 -55q0 -24 13.5 -38.5t37.5 -14.5q22 0 34.5 15.5t12.5 37.5
q0 24 -12.5 39.5t-34.5 15.5zM508 182q-13 0 -24 11t-11 22q0 12 11 23.5t24 11.5q24 0 24 -35q0 -33 -24 -33zM582 203h-27q0 -41 45 -41t45 35q0 22 -31 28q-32 6 -32 13q0 12 18 12q21 0 21 -12h18q0 32 -39 32t-39 -32q0 -17 33 -31q27 0 27 -10q0 -15 -21 -15
q-18 0 -18 21zM645 270v-20h35v-88h16v88h31v20h-82zM815 162l-43 108h-18l-45 -108h24l8 26h45l7 -26h22zM760 244h6l14 -37h-32zM872 270h-57v-108h19v45h26q19 0 19 -19v-26h26q-6 6 -6 26q0 19 -14 27q14 6 14 23q0 32 -27 32zM860 221h-26v29h26q19 0 19 -12
q0 -17 -19 -17zM954 270h-41v-108h41q52 0 52 59q0 49 -52 49zM954 182h-16v68h8q35 0 35 -35q0 -12 -8.5 -22.5t-18.5 -10.5zM1087 250v20h-75v-108h82v20h-58v25h51v18h-51v25h51zM1112 203h-18q0 -41 45 -41t45 35q0 22 -31 28q-35 6 -35 13q0 12 15 12q24 0 24 -12h21
q0 32 -39 32q-11 0 -23 -5.5t-19.5 -13.5t1 -20t35.5 -24q24 0 24 -10q0 -15 -18 -15q-27 0 -27 21zM1206 162v108h-14v-108h14zM1296 176l9 -14h12v59h-45v-18h24q-9 -21 -24 -21q-13 0 -23 11t-10 22q0 13 10 24t23 11q18 0 18 -21h27q-9 41 -45 41q-23 0 -37 -15.5
t-14 -39.5q0 -21 14.5 -37t36.5 -16q18 0 24 14zM1391 203l-45 67h-17v-108h17v67l45 -67h26v108h-26v-67z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="CR" horiz-adv-x="681" 
 />
    <glyph glyph-name="published" horiz-adv-x="1656" 
d="M756 573v-249h-142v723h279q109 0 171 -68t62 -170q0 -91 -61 -164q-60 -72 -172 -72h-137zM756 907v-194h133q58 0 81.5 48.5t0.5 97t-78 48.5h-137zM319 190q-206 212 -206 512t206 512q210 213 508 213t508 -213q209 -212 209 -512t-209 -512q-207 -210 -508 -210
t-508 210zM410 1124q-172 -175 -172 -422q0 -246 172 -421t417 -175q246 0 418 175t172 421q0 247 -172 422q-173 176 -418 176q-244 0 -417 -176z" />
    <glyph glyph-name="frenchfranc" horiz-adv-x="1486" 
d="M979 238v-238h-186v676h-410v-676h-184v1384h802v-184h-618v-340h596v-229q70 235 266 235q114 0 166 -37l-29 -174q-65 33 -141 33q-127 0 -194.5 -121t-67.5 -329z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="743" 
d="M662 1665v-182q-175 0 -283 -209q-105 -208 -105 -561t105 -561q108 -209 283 -209v-183q-256 0 -410 256q-152 258 -152 697q0 438 152 696q154 256 410 256z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="743" 
d="M82 -240v183q175 0 280 209q107 207 107 561t-107 561q-105 209 -280 209v182q256 0 407 -256q154 -257 154 -696q0 -440 -154 -697q-151 -256 -407 -256z" />
    <glyph glyph-name="hyphen.case" horiz-adv-x="995" 
d="M141 629v178h713v-178h-713z" />
    <glyph glyph-name="at.case" horiz-adv-x="1988" 
d="M1231 461v264q0 108 -78 186t-186 78q-111 0 -189 -78q-76 -76 -76 -186q0 -108 78 -186q76 -76 187 -76q104 0 202 98v-174q-98 -70 -202 -70q-172 0 -291 119t-119 289t119 289q121 121 291 121q163 0 282 -113q121 -115 127 -279v-297q0 -135 148 -135q67 0 117.5 43
t76.5 115q45 131 45 281q0 308 -229 512q-229 206 -547 206q-311 0 -526 -221q-213 -219 -213 -532q0 -312 213 -531q211 -217 526 -217h47v-145h-47q-374 0 -629 258q-256 262 -256 635t256 635q258 264 629 264q144 4 284 -37.5t255.5 -121.5t203 -186.5t135.5 -241
t46 -277.5q0 -201 -84 -379q-44 -93 -123.5 -148t-179.5 -55q-135 0 -214 72t-79 221z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="669" 
d="M584 -219h-412v1876h412v-170h-228v-1536h228v-170z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="669" 
d="M86 1653h412v-1876h-412v170h227v1536h-227v170z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="819" 
d="M381 522v-356q0 -99 65 -170q64 -70 156 -70h137v-162h-131q-169 0 -286 111t-117 291v356q0 41 -38 73t-87 32v166q50 0 87.5 31.5t37.5 72.5v360q-3 181 114 289q117 111 289 111h131v-162h-137q-92 0 -156.5 -69.5t-64.5 -168.5v-360q0 -59 -27 -111.5t-73 -76.5
q43 -25 71.5 -79t28.5 -108z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="819" 
d="M438 166v356q0 54 29 108t72 79q-46 24 -73.5 76.5t-27.5 111.5v360q0 99 -64.5 168.5t-156.5 69.5h-137v162h131q172 0 289 -111q117 -108 114 -289v-360q0 -41 37.5 -72.5t87.5 -31.5v-166q-49 0 -87 -32t-38 -73v-356q0 -181 -116 -291q-117 -111 -287 -111h-131v162
h137q92 0 156 70q65 71 65 170z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="538" 
d="M199 1028h145l18 -1030h-184zM174 1208q-38 37 -38 92.5t38 90.5q38 37 94 37t94 -37q41 -35 41 -90.5t-41 -92.5q-38 -37 -94 -37t-94 37z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="1054" 
d="M399 293l-282 424l282 424h162l-282 -424l282 -424h-162zM760 293l-283 424l283 424h162l-283 -424l283 -424h-162z" />
    <glyph glyph-name="periodcentered.case" horiz-adv-x="548" 
d="M180 631q-38 37 -38 92t38 92t94.5 37t94.5 -37q41 -37 41 -92t-41 -92q-38 -35 -94.5 -35t-94.5 35z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="1054" 
d="M655 1141l283 -424l-283 -424h-161l282 424l-282 424h161zM295 1141l283 -424l-283 -424h-162l283 424l-283 424h162z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="987" 
d="M717 408h184q0 -192 -117 -312q-116 -116 -286 -116q-172 0 -297 110q-123 111 -123 289q0 139 82 243.5t213 143.5q79 25 112 55.5t33 93.5l4 121h180v-121q0 -134 -67 -208t-207 -117q-54 -14 -98 -62q-70 -61 -70 -149q0 -95 72 -158q71 -59 166 -59q94 0 156.5 68.5
t62.5 177.5zM705 1391q41 -35 41 -90.5t-41 -92.5q-38 -37 -94.5 -37t-94.5 37t-38 92.5t38 90.5q38 37 94.5 37t94.5 -37z" />
    <glyph glyph-name="endash.case" horiz-adv-x="1222" 
d="M147 637v160h928v-160h-928z" />
    <glyph glyph-name="emdash.case" horiz-adv-x="1644" 
d="M147 637v160h1350v-160h-1350z" />
    <glyph glyph-name="bullet.case" horiz-adv-x="948" 
d="M258 508q-90 84 -90 209q0 123 90 207t217 84q125 0 215 -84t90 -207q0 -125 -90 -209t-215 -84q-127 0 -217 84z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="694" 
d="M399 293l-282 424l282 424h162l-282 -424l282 -424h-162z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="694" 
d="M295 1141l283 -424l-283 -424h-162l283 424l-283 424h162z" />
    <glyph glyph-name="cent.case" horiz-adv-x="1091" 
d="M643 0h-127v219q-176 30 -295 164q-117 132 -117 330q0 193 117 325t295 162v211h127v-205q203 -9 336 -151l-141 -119q-90 90 -228 90q-133 0 -227 -88q-94 -85 -94 -225q0 -144 94 -232q96 -90 227 -90q140 0 228 94l143 -118q-130 -142 -338 -154v-213z" />
    <glyph glyph-name="zero.tnum" 
d="M109 666q0 300 96 485q112 203 389 203q282 0 387 -203q52 -95 74 -210.5t22 -274.5q0 -158 -22 -272.5t-74 -209.5q-105 -202 -387 -202q-112 0 -197.5 36t-139 96.5t-87.5 149.5t-47.5 186t-13.5 216zM293 666q0 -245 70 -373.5t231 -128.5t231 128.5t70 373.5
q0 162 -28 270.5t-96 170.5t-177 62t-177 -62t-96 -170.5t-28 -270.5z" />
    <glyph glyph-name="one.tnum" 
d="M739 0h-184v1104l-217 -76v176l401 146v-1350z" />
    <glyph glyph-name="two.tnum" 
d="M176 0v82l543 657q98 113 98 219q0 93 -68 152t-169 59q-94 0 -161.5 -57t-80.5 -154l-178 43q28 163 143 256q120 97 277 97q179 0 299 -111t120 -285q0 -178 -145 -346l-362 -428h509v-184h-825z" />
    <glyph glyph-name="three.tnum" 
d="M348 1051l-137 108q56 97 160.5 150.5t226.5 46.5q162 -9 266 -119q106 -106 103 -252q0 -35 -13 -82q-38 -121 -145 -182q103 -38 160 -143q60 -106 51 -218q-15 -168 -160 -280q-144 -111 -330 -96q-129 7 -235.5 83t-161.5 195l162 88q33 -73 103 -125.5t147 -58.5h-2
q114 -6 198 57q90 66 93 162q3 95 -76 168q-78 72 -183 72h-151v178h151q69 -1 121 42t64 111q15 84 -23 138q-47 71 -155 77q-145 12 -234 -120z" />
    <glyph glyph-name="four.tnum" 
d="M1112 274h-192v-274h-183v274h-696l741 1063h138v-888h192v-175zM737 1006l-383 -557h383v557z" />
    <glyph glyph-name="five.tnum" 
d="M115 285l157 86q38 -98 115.5 -155.5t171.5 -57.5q156 0 239 73.5t83 198.5q0 120 -77.5 188t-199.5 68q-84 0 -155 -24q-62 -19 -177 -87l-114 95l116 665h713v-176h-561l-74 -379q45 32 118 55t134 23q193 0 326 -115q131 -113 131 -313q0 -204 -139 -328
q-137 -122 -363 -122q-141 0 -262 83t-182 222z" />
    <glyph glyph-name="six.tnum" 
d="M1051 1096l-164 -70q-77 154 -228 154q-140 0 -223 -133q-81 -130 -63 -334q59 151 274 151q178 0 299 -139t115 -319q-9 -181 -139 -308q-128 -125 -310 -116q-179 8 -294 130t-132 328q-18 249 23 506q30 185 153 299q127 115 306 109q135 -6 229.5 -65.5t153.5 -192.5
zM371 434q0 -109 71 -186q74 -77 179 -80q107 -3 178 65q77 71 80 181q4 109 -64 185t-174 81q-106 3 -186 -68q-81 -72 -84 -178z" />
    <glyph glyph-name="seven.tnum" 
d="M178 1337h924l-733 -1337h-215l637 1155h-613v182z" />
    <glyph glyph-name="eight.tnum" 
d="M238 1012q0 142 102 241q101 101 254 101q152 0 256 -101q104 -98 104 -241q0 -142 -104 -236q97 -60 151 -154t54 -204q0 -182 -133 -309t-328 -127q-200 0 -330 124q-131 125 -131 312q0 110 54.5 205.5t148.5 152.5q-98 94 -98 236zM317 418q0 -106 80 -180t197 -74
t197 74q79 73 79 180q0 106 -79 182t-197 76h-14q-110 -3 -187 -80q-76 -76 -76 -178zM584 848h16q73 1 121.5 49t48.5 115q0 66 -50.5 111.5t-125.5 45.5q-73 0 -123.5 -45.5t-50.5 -111.5t47.5 -114.5t116.5 -49.5z" />
    <glyph glyph-name="nine.tnum" 
d="M160 238l164 69q76 -153 227 -153q139 0 221 131q84 134 66 336q-59 -152 -275 -152q-180 0 -299 137q-120 138 -114 322q9 180 139 307q129 126 309 117q179 -8 294 -130.5t132 -328.5q18 -248 -23 -508v2q-30 -186 -155 -299q-123 -114 -303 -108q-135 6 -229.5 65.5
t-153.5 192.5zM840 899q0 109 -74 186q-71 77 -176 80q-104 3 -180 -67q-75 -72 -78 -178q-5 -109 63 -186t174 -81q109 -3 187 66q81 72 84 180z" />
    <glyph glyph-name="zero.taboldstyle" 
d="M592 -20q-206 0 -350 147q-146 149 -146 381q0 235 146 381q145 145 350 145q209 0 354 -145q146 -146 146 -381q0 -233 -148 -381q-144 -147 -352 -147zM592 162q130 0 223 96q92 95 92 250q0 151 -92 246q-93 96 -223 96t-223 -96q-90 -96 -90 -246q0 -155 92 -250
q93 -96 221 -96z" />
    <glyph glyph-name="one.taboldstyle" 
d="M305 795l488 262v-1057h-185v758l-303 -158v195z" />
    <glyph glyph-name="two.taboldstyle" 
d="M205 0v84l481 520q74 74 74 135q0 55 -48 91t-122 36q-70 0 -117 -41.5t-53 -109.5l-180 30q15 141 116 222q104 84 242 84q154 0 252 -88q100 -87 100 -224q0 -48 -14 -92q-2 -8 -37 -78q-5 -8 -55 -71l-35 -37l-33 -37l-231 -240h407v-184h-747z" />
    <glyph glyph-name="six.taboldstyle" 
d="M1051 1096l-164 -70q-77 154 -228 154q-140 0 -223 -133q-81 -130 -63 -334q59 151 274 151q178 0 299 -139t115 -319q-9 -181 -139 -308q-128 -125 -310 -116q-179 8 -294 130t-132 328q-18 249 23 506q30 185 153 299q127 115 306 109q135 -6 229.5 -65.5t153.5 -192.5
zM371 434q0 -109 71 -186q74 -77 179 -80q107 -3 178 65q77 71 80 181q4 109 -64 185t-174 81q-106 3 -186 -68q-81 -72 -84 -178z" />
    <glyph glyph-name="eight.taboldstyle" 
d="M238 1012q0 142 102 241q101 101 254 101q152 0 256 -101q104 -98 104 -241q0 -142 -104 -236q97 -60 151 -154t54 -204q0 -182 -133 -309t-328 -127q-200 0 -330 124q-131 125 -131 312q0 110 54.5 205.5t148.5 152.5q-98 94 -98 236zM317 418q0 -106 80 -180t197 -74
t197 74q79 73 79 180q0 106 -79 182t-197 76h-14q-110 -3 -187 -80q-76 -76 -76 -178zM584 848h16q73 1 121.5 49t48.5 115q0 66 -50.5 111.5t-125.5 45.5q-73 0 -123.5 -45.5t-50.5 -111.5t47.5 -114.5t116.5 -49.5z" />
    <glyph glyph-name="three.taboldstyle" 
d="M354 666l-137 108q56 97 160.5 150.5t226.5 46.5q162 -9 266 -119q106 -106 103 -252q0 -38 -12 -82q-38 -120 -146 -182q103 -38 160 -143q60 -106 51 -218q-15 -168 -160 -280q-144 -111 -329 -96q-129 7 -236 83t-162 195l162 88q33 -73 103 -125.5t147 -58.5h-2
q113 -6 199 59q89 65 92 160t-76 168q-78 72 -182 72h-152v178h152q69 -1 120.5 42t63.5 111q15 84 -23 138q-47 71 -155 77q-145 12 -234 -120z" />
    <glyph glyph-name="four.taboldstyle" 
d="M1112 -109h-192v-274h-183v274h-696l741 1063h138v-888h192v-175zM737 623l-383 -557h383v557z" />
    <glyph glyph-name="five.taboldstyle" 
d="M117 -98l157 86q38 -98 115.5 -155.5t171.5 -57.5q156 0 239 73.5t83 198.5q0 120 -77.5 188t-199.5 68q-84 0 -155 -24q-66 -20 -177 -86l-114 94l116 665h713v-176h-561l-74 -379q45 32 118 55t134 23q193 0 326 -115q131 -113 131 -313q0 -204 -139 -328
q-137 -122 -363 -122q-141 0 -262 83t-182 222z" />
    <glyph glyph-name="seven.taboldstyle" 
d="M180 952h924l-733 -1337h-215l637 1155h-613v182z" />
    <glyph glyph-name="nine.taboldstyle" 
d="M170 -150l164 70q76 -153 227 -153q138 0 221 133q84 134 66 333q-59 -151 -275 -151q-180 0 -299 137q-120 138 -114 322q9 180 139 307q129 126 309 117q179 -8 294 -130.5t132 -328.5q18 -252 -22 -508v2q-30 -185 -156 -299q-124 -115 -303 -109q-135 6 -229.5 65.5
t-153.5 192.5zM850 512q0 109 -74 186q-71 77 -176 80q-104 3 -180 -67q-75 -72 -78 -179q-4 -109 64 -185t174 -81q108 -3 186 66q81 72 84 180z" />
    <glyph glyph-name="zero.oldstyle" horiz-adv-x="1212" 
d="M604 -20q-206 0 -350 147q-145 148 -145 381q0 236 145 381t350 145q209 0 354 -145q146 -146 146 -381q0 -233 -148 -381q-144 -147 -352 -147zM604 162q130 0 223 96t93 250q0 150 -93 246t-223 96t-223 -96q-90 -96 -90 -246q0 -155 92 -250q93 -96 221 -96z" />
    <glyph glyph-name="one.oldstyle" horiz-adv-x="747" 
d="M86 795l487 262v-1057h-184v758l-303 -158v195z" />
    <glyph glyph-name="two.oldstyle" horiz-adv-x="972" 
d="M98 0v84l482 520q73 73 73 135q0 55 -48 91t-122 36q-70 0 -117 -41.5t-53 -109.5l-180 30q15 140 117 222q104 84 242 84q153 0 251 -88q101 -88 101 -224q0 -45 -15 -92q-3 -13 -36 -78q-2 -3 -56 -71l-35 -37l-32 -37l-232 -240h408v-184h-748z" />
    <glyph glyph-name="three.oldstyle" horiz-adv-x="1054" 
d="M287 666l-137 108q56 97 160.5 150.5t226.5 46.5q162 -9 266 -119q105 -105 102 -252q0 -38 -12 -82q-38 -121 -145 -182q102 -37 159 -143q60 -106 51 -218q-15 -169 -159 -280t-330 -96q-129 7 -235.5 83t-161.5 195l161 88q33 -73 103 -125.5t147 -58.5h-2
q113 -6 199 59q89 65 92 160t-76 168q-78 72 -182 72h-152v178h152q69 -1 120.5 42t63.5 111q16 85 -22 138q-47 71 -156 77q-144 12 -233 -120z" />
    <glyph glyph-name="four.oldstyle" 
d="M1112 -109h-192v-274h-183v274h-696l741 1063h138v-888h192v-175zM737 623l-383 -557h383v557z" />
    <glyph glyph-name="five.oldstyle" horiz-adv-x="1122" 
d="M84 -98l158 86q38 -98 115 -155.5t171 -57.5q156 0 239 73.5t83 198.5q0 120 -77.5 188t-199.5 68q-85 0 -155 -24q-64 -19 -176 -86l-115 94l117 665h712v-176h-561l-73 -379q45 32 117.5 55t133.5 23q193 0 326 -115q131 -113 131 -313q0 -204 -139 -328
q-137 -122 -363 -122q-141 0 -262 83t-182 222z" />
    <glyph glyph-name="six.oldstyle" horiz-adv-x="1138" 
d="M1026 1096l-164 -70q-77 154 -227 154q-140 0 -223 -133q-82 -131 -64 -334q59 151 275 151q178 0 299 -139q120 -138 114 -319q-9 -181 -139 -308q-128 -125 -309 -116q-179 8 -294 130t-132 328q-18 253 22 506q30 184 154 299q127 115 305 109q135 -6 229.5 -65.5
t153.5 -192.5zM346 434q0 -108 72 -186q74 -77 178 -80q107 -3 178 65q77 71 80 181q4 109 -64 185t-174 81q-106 3 -186 -68q-81 -72 -84 -178z" />
    <glyph glyph-name="seven.oldstyle" horiz-adv-x="1036" 
d="M104 952h924l-733 -1337h-215l637 1155h-613v182z" />
    <glyph glyph-name="eight.oldstyle" horiz-adv-x="1150" 
d="M219 1012q0 141 103 241q101 101 253 101t256 -101q105 -99 105 -241t-105 -236q97 -60 151 -154t54 -204q0 -182 -133 -309t-328 -127q-199 0 -329 124q-131 125 -131 312q0 110 54 205.5t148 152.5q-98 94 -98 236zM299 418q0 -106 80 -180t196 -74q117 0 197 74
t80 180q0 105 -80 182q-79 76 -197 76h-14q-109 -3 -186 -80q-76 -76 -76 -178zM565 848h17q73 1 121.5 49t48.5 115q0 66 -51 111.5t-126 45.5q-73 0 -123.5 -45.5t-50.5 -111.5t47.5 -114.5t116.5 -49.5z" />
    <glyph glyph-name="nine.oldstyle" horiz-adv-x="1093" 
d="M123 -150l164 70q77 -153 227 -153q138 0 221 133q84 134 66 333q-59 -151 -275 -151q-180 0 -299 137q-120 138 -114 322q9 180 139 307q129 126 309 117q179 -8 294 -130.5t132 -328.5q18 -252 -22 -508v2q-30 -185 -156 -299q-124 -115 -303 -109q-135 6 -229.5 65.5
t-153.5 192.5zM803 512q0 109 -74 186q-71 77 -176 80q-104 3 -180 -67q-75 -72 -78 -179q-5 -109 63 -185.5t174 -80.5q109 -3 187 66q81 72 84 180z" />
    <glyph glyph-name="Lslash.sc" horiz-adv-x="888" 
d="M362 1196v-401l179 110v-129l-179 -112v-494h482v-170h-654v557l-110 -72v131l110 72v508h172z" />
    <glyph glyph-name="Zcaron.sc" horiz-adv-x="1038" 
d="M92 0v147l619 887h-594v162h817v-147l-625 -885h625v-164h-842zM385 1610l125 -146l129 146h154l-199 -275h-164l-201 275h156z" />
    <glyph glyph-name="exclam.sc" horiz-adv-x="536" 
d="M186 1196h166l-16 -879h-133zM162 94q0 44 30.5 74.5t73.5 30.5q44 0 75.5 -30.5t31.5 -74.5q0 -41 -31.5 -70.5t-75.5 -29.5q-43 0 -73.5 29.5t-30.5 70.5z" />
    <glyph glyph-name="ampersand.sc" horiz-adv-x="1181" 
d="M516 737h225v152h160v-154h150v-137h-150v-229q0 -116 39.5 -172t140.5 -56v-157q-91 0 -166 32.5t-114 87.5q-105 -120 -289 -120q-172 0 -285 110q-110 110 -110 275q0 201 172 315q-78 78 -78 209q0 125 90 215q88 88 223 88q90 0 168.5 -47.5t126.5 -128.5l-139 -88
q-64 104 -156 104q-66 0 -109.5 -40.5t-43.5 -102.5q0 -63 42 -109.5t103 -46.5zM741 358v240h-227q-98 0 -168 -70q-67 -70 -67 -159q0 -95 65.5 -159.5t167.5 -64.5q96 0 160 60.5t69 152.5z" />
    <glyph glyph-name="question.sc" horiz-adv-x="872" 
d="M248 840h-160q0 166 100 266q102 102 246 102q148 0 252 -94q107 -91 107 -244q0 -119 -71 -208.5t-181 -122.5q-63 -20 -91 -46t-28 -75l-2 -101h-164v101q0 114 57.5 178.5t175.5 101.5q57 14 98.5 63.5t41.5 108.5q0 73 -59 123.5t-136 50.5t-131.5 -57t-54.5 -147z
M338 213q51 0 85 -33t34 -84q0 -49 -34 -80.5t-85 -31.5q-50 0 -84.5 31.5t-34.5 80.5t34.5 83t84.5 34z" />
    <glyph glyph-name="A.sc" horiz-adv-x="1167" 
d="M584 956l-189 -499h377zM946 0l-115 301h-493l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182z" />
    <glyph glyph-name="B.sc" horiz-adv-x="1132" 
d="M963 883q0 -176 -136 -244q92 -28 144 -113q55 -85 55 -178q0 -144 -96 -248q-93 -100 -256 -100h-492v1196h453q152 0 240 -83.5t88 -229.5zM356 1036v-329h279q70 0 110.5 48t40.5 124q0 73 -45 115t-125 42h-260zM662 553h-306v-391h310q80 0 132 55.5t52 132.5
q0 72 -53 137q-54 66 -135 66z" />
    <glyph glyph-name="C.sc" horiz-adv-x="1228" 
d="M1040 303l129 -108q-86 -100 -204.5 -156.5t-251.5 -56.5q-254 0 -432 178q-177 183 -177 436q0 254 177 434q178 178 432 178q275 0 452 -204l-127 -109q-138 147 -325 147t-312 -131q-125 -128 -125 -315q0 -186 125 -317q126 -129 312 -129q195 0 327 153z" />
    <glyph glyph-name="D.sc" horiz-adv-x="1296" 
d="M647 0h-465v1196h465q249 0 393 -170q146 -172 146 -430t-146 -430q-144 -166 -393 -166zM354 1036v-874h293q174 0 270.5 119t96.5 315q0 190 -96 315q-94 125 -271 125h-293z" />
    <glyph glyph-name="E.sc" horiz-adv-x="1021" 
d="M182 0v1196h713v-160h-541v-366h473v-162h-473v-346h557v-162h-729z" />
    <glyph glyph-name="F.sc" horiz-adv-x="950" 
d="M182 0v1196h703v-160h-531v-366h420v-162h-420v-508h-172z" />
    <glyph glyph-name="G.sc" horiz-adv-x="1349" 
d="M1116 1047l-123 -115q-118 112 -284 112q-183 0 -308 -131t-125 -317t125 -317q126 -132 308 -132q159 0 261 84t109 224h-336v151h531q0 -305 -148.5 -464.5t-416.5 -159.5q-255 0 -430 178q-175 181 -175 436q0 256 175 434t430 178q240 0 407 -161z" />
    <glyph glyph-name="H.sc" horiz-adv-x="1269" 
d="M913 1196h174v-1196h-174v516h-559v-516h-172v1196h172v-522h559v522z" />
    <glyph glyph-name="I.sc" horiz-adv-x="536" 
d="M182 0v1196h172v-1196h-172z" />
    <glyph glyph-name="J.sc" horiz-adv-x="536" 
d="M-152 -412l17 166q50 -16 121 -16q99 0 147.5 59t48.5 162v1237h172v-1237q0 -179 -92 -284t-276 -105q-46 0 -138 18z" />
    <glyph glyph-name="K.sc" horiz-adv-x="1054" 
d="M946 1196l-459 -598l566 -598h-236l-463 496v-496h-172v1196h172v-500l383 500h209z" />
    <glyph glyph-name="L.sc" horiz-adv-x="880" 
d="M182 0v1196h172v-1034h484v-162h-656z" />
    <glyph glyph-name="M.sc" horiz-adv-x="1658" 
d="M416 1196l411 -858l416 858h156l145 -1196h-172l-104 874l-363 -743h-149l-365 743l-106 -874h-170l145 1196h156z" />
    <glyph glyph-name="N.sc" horiz-adv-x="1230" 
d="M1049 1196v-1196h-164l-531 877v-877h-172v1196h168l527 -866v866h172z" />
    <glyph glyph-name="O.sc" horiz-adv-x="1421" 
d="M279 160q-175 181 -175 436q0 256 175 434t430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178zM401 911q-125 -128 -125 -315q0 -186 125 -317q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315
q-125 131 -307 131q-183 0 -308 -131z" />
    <glyph glyph-name="P.sc" horiz-adv-x="1093" 
d="M182 0v1196h480q180 0 268 -111q90 -113 90 -266q0 -158 -90 -268q-89 -109 -268 -109h-306v-442h-174zM659 1036h-303v-432h295q111 -6 165 89q46 81 25 183q-20 101 -94 141q-39 21 -88 19z" />
    <glyph glyph-name="Q.sc" horiz-adv-x="1421" 
d="M1038 80l123 -197l-135 -86l-133 213q-106 -28 -184 -28q-255 0 -430 178q-175 181 -175 436q0 256 175 434t430 178q257 0 432 -178q176 -179 176 -434q0 -157 -75 -293.5t-204 -222.5zM678 356l133 84l133 -215q93 61 146 158t53 213q0 185 -127 315q-125 131 -307 131
q-183 0 -308 -131q-125 -128 -125 -315q0 -171 117 -297q119 -128 285 -143q82 0 119 6l-60 96q-47 74 -59 98z" />
    <glyph glyph-name="R.sc" horiz-adv-x="1118" 
d="M696 451l318 -451h-209l-305 442h-144v-442h-174v1196h461q177 0 266 -111q92 -112 92 -266t-78 -254.5t-227 -113.5zM639 1036h-283v-432h275q122 -8 171.5 103.5t0.5 223.5t-164 105z" />
    <glyph glyph-name="S.sc" horiz-adv-x="1034" 
d="M109 354h174q0 -84 69 -147q70 -64 176 -64q100 0 161.5 47.5t72.5 120.5q11 80 -45 132.5t-152 74.5l-153 39q-291 70 -291 315q0 147 115 242q116 94 274 94q169 0 276 -98q111 -94 111 -258h-172q0 85 -61 138.5t-156 53.5q-89 0 -152 -49t-63 -123q0 -116 164 -157
l153 -39q168 -43 251.5 -141.5t70.5 -237.5q-21 -134 -129 -225q-110 -90 -275 -90q-182 0 -301 110q-118 112 -118 262z" />
    <glyph glyph-name="T.sc" horiz-adv-x="987" 
d="M47 1034v162h893v-162h-360v-1034h-174v1034h-359z" />
    <glyph glyph-name="U.sc" horiz-adv-x="1218" 
d="M608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73z" />
    <glyph glyph-name="V.sc" horiz-adv-x="1019" 
d="M68 1196h178l266 -897l262 897h178l-360 -1196h-162z" />
    <glyph glyph-name="W.sc" horiz-adv-x="1607" 
d="M1083 283l293 913h185l-406 -1196h-147l-205 721l-203 -721h-147l-406 1196h184l293 -913l215 753h129z" />
    <glyph glyph-name="X.sc" horiz-adv-x="1118" 
d="M1034 1196l-370 -555l417 -641h-209l-313 496l-313 -496h-207l416 643l-371 553h207l268 -410l270 410h205z" />
    <glyph glyph-name="Y.sc" horiz-adv-x="1153" 
d="M664 549v-549h-177v549l-454 647h203l337 -477l342 477h209z" />
    <glyph glyph-name="Z.sc" horiz-adv-x="1038" 
d="M92 0v147l619 887h-594v162h817v-147l-625 -885h625v-164h-842z" />
    <glyph glyph-name="Scaron.sc" horiz-adv-x="1034" 
d="M395 1610l125 -146l129 146h156l-201 -275h-164l-200 275h155zM283 354q0 -84 69 -147q70 -64 176 -64q100 0 161.5 47.5t72.5 120.5q11 80 -45 132.5t-152 74.5l-153 39q-291 70 -291 315q0 147 115 242q116 94 274 94q169 0 276 -98q111 -94 111 -258h-172
q0 85 -61 138.5t-156 53.5q-89 0 -152 -49t-63 -123q0 -116 164 -157l153 -39q168 -43 251.5 -141.5t70.5 -237.5q-21 -134 -129 -225q-110 -90 -275 -90q-182 0 -301 110q-118 112 -118 262h174z" />
    <glyph glyph-name="OE.sc" horiz-adv-x="1974" 
d="M1135 1016v180h710v-170h-538v-354h471v-170h-471v-332h557v-170h-729v176q-176 -194 -430 -194t-429 178q-172 181 -172 436q0 256 172 434q175 178 429 178q259 0 430 -192zM705 156q168 0 290 125q125 125 140 294v39q-15 172 -140 297q-122 125 -290 125
q-182 0 -308 -129q-123 -126 -123 -311q0 -184 123 -313q127 -127 308 -127z" />
    <glyph glyph-name="Ydieresis.sc" horiz-adv-x="1153" 
d="M664 549v-549h-177v549l-454 647h203l337 -477l342 477h209zM319 1470q0 44 33 75.5t80 31.5t79 -31t32 -76q0 -47 -31.5 -77.5t-79.5 -30.5t-80.5 30.5t-32.5 77.5zM610 1470q0 44 33 75.5t80 31.5t80 -31.5t33 -75.5q0 -47 -32.5 -77.5t-80.5 -30.5t-80.5 30.5
t-32.5 77.5z" />
    <glyph glyph-name="exclamdown.sc" horiz-adv-x="540" 
d="M270 1210q51 0 85 -31.5t34 -80.5t-34 -82t-85 -33q-50 0 -83 33t-33 82t33 80.5t83 31.5zM352 0h-166l17 879h133z" />
    <glyph glyph-name="questiondown.sc" horiz-adv-x="872" 
d="M639 -29h166q0 -168 -100 -268q-102 -102 -244 -102q-147 0 -254 94q-105 95 -105 246q0 114 71 203t179 126q64 22 92.5 48t28.5 73l2 109h164v-109q0 -114 -57.5 -177.5t-178.5 -102.5q-45 -12 -77 -48q-58 -52 -58 -122q0 -77 56.5 -127t136.5 -50q75 0 126.5 58
t51.5 149zM639 799q35 -31 35 -80.5t-35 -81.5q-32 -32 -84 -32t-84 32t-32 81.5t32 80.5q32 32 84 32t84 -32z" />
    <glyph glyph-name="Agrave.sc" horiz-adv-x="1167" 
d="M584 956l-189 -499h377zM946 0l-115 301h-493l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182zM682 1335h-164l-215 275h203z" />
    <glyph glyph-name="Aacute.sc" horiz-adv-x="1167" 
d="M666 1597h202l-213 -274h-163zM772 457l-188 499l-189 -499h377zM338 301l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182l-115 301h-493z" />
    <glyph glyph-name="Acircumflex.sc" horiz-adv-x="1167" 
d="M584 956l-189 -499h377zM946 0l-115 301h-493l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182zM866 1335h-157l-127 148l-125 -148h-154l199 275h164z" />
    <glyph glyph-name="Atilde.sc" horiz-adv-x="1167" 
d="M584 956l-189 -499h377zM946 0l-115 301h-493l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182zM506 1430q-38 0 -41 -74h-117q6 105 50 158t108 53q43 0 92 -37l-2 2q2 0 12 -10q39 -25 51 -25q38 0 41 70h117q-6 -105 -50 -157t-108 -52q-51 0 -102 45l-20 12
q-21 15 -31 15z" />
    <glyph glyph-name="Adieresis.sc" horiz-adv-x="1167" 
d="M326 1470q0 44 32.5 75.5t79.5 31.5t79 -31t32 -76q0 -47 -31.5 -77.5t-79.5 -30.5t-80 30.5t-32 77.5zM616 1470q0 45 32 76t79 31t81 -31.5t34 -75.5q0 -47 -32.5 -77.5t-82.5 -30.5q-48 0 -79.5 30.5t-31.5 77.5zM772 457l-188 499l-189 -499h377zM338 301l-66 -172
l-49 -129h-184l455 1196h180l454 -1196h-182l-115 301h-493z" />
    <glyph glyph-name="Aring.sc" horiz-adv-x="1167" 
d="M395 1497q0 79 53.5 132.5t131.5 53.5q82 0 136 -53t54 -133q0 -78 -54.5 -131t-135.5 -53q-78 0 -131.5 53t-53.5 131zM500 1497q0 -32 24 -57t56 -25q37 0 62.5 23.5t25.5 58.5t-25.5 59.5t-62.5 24.5q-33 0 -56.5 -24.5t-23.5 -59.5zM772 457l-188 499l-189 -499h377z
M338 301l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182l-115 301h-493z" />
    <glyph glyph-name="AE.sc" horiz-adv-x="1667" 
d="M827 297h-409l-191 -297h-204l757 1196h758v-170h-539v-354h469v-170h-469v-332h557v-170h-729v297zM827 944l-307 -481h307v481z" />
    <glyph glyph-name="Ccedilla.sc" horiz-adv-x="1230" 
d="M578 -193q10 -23 33 -33.5t46 -2.5q49 21 31 84l-49 131q-231 27 -383 198q-152 178 -152 412q0 254 177 434q178 178 432 178q275 0 452 -204l-127 -109q-138 147 -325 147t-312 -131q-125 -128 -125 -315q0 -186 125 -317q126 -129 312 -129q195 0 327 153l129 -108
q-166 -193 -417 -211l45 -82q14 -32 14 -74q0 -111 -113 -166q-27 -12 -69 -12q-50 0 -93.5 30.5t-66.5 81.5q-18 46 -18 95l122 -9q0 -21 5 -41z" />
    <glyph glyph-name="Egrave.sc" horiz-adv-x="1021" 
d="M182 0v1196h713v-160h-541v-366h473v-162h-473v-346h557v-162h-729zM647 1335h-164l-215 275h205z" />
    <glyph glyph-name="Eacute.sc" horiz-adv-x="1021" 
d="M557 1610h205l-215 -275h-164zM895 1196v-160h-541v-366h473v-162h-473v-346h557v-162h-729v1196h713z" />
    <glyph glyph-name="Ecircumflex.sc" horiz-adv-x="1021" 
d="M182 0v1196h713v-160h-541v-366h473v-162h-473v-346h557v-162h-729zM793 1335h-154l-127 148l-127 -148h-156l201 275h164z" />
    <glyph glyph-name="Edieresis.sc" horiz-adv-x="1021" 
d="M260 1470q0 44 34 75.5t81 31.5q45 0 77.5 -31.5t32.5 -75.5q0 -46 -32.5 -77t-77.5 -31q-50 0 -82.5 30.5t-32.5 77.5zM551 1470q0 44 33 75.5t80 31.5t79.5 -31.5t32.5 -75.5q0 -47 -32 -77.5t-80 -30.5t-80.5 30.5t-32.5 77.5zM895 1196v-160h-541v-366h473v-162h-473
v-346h557v-162h-729v1196h713z" />
    <glyph glyph-name="Igrave.sc" horiz-adv-x="534" 
d="M182 0v1196h170v-1196h-170zM358 1335h-163l-213 275h204z" />
    <glyph glyph-name="Iacute.sc" horiz-adv-x="534" 
d="M350 1610h203l-215 -275h-164zM352 1196v-1196h-170v1196h170z" />
    <glyph glyph-name="Icircumflex.sc" horiz-adv-x="536" 
d="M182 0v1196h172v-1196h-172zM551 1335h-156l-129 148l-125 -148h-155l200 275h164z" />
    <glyph glyph-name="Idieresis.sc" horiz-adv-x="534" 
d="M10 1470q0 44 33 75.5t80 31.5t80 -31.5t33 -75.5q0 -47 -32.5 -77.5t-80.5 -30.5t-80.5 30.5t-32.5 77.5zM301 1470q0 45 32 76t79 31t80.5 -31.5t33.5 -75.5q0 -47 -32 -77.5t-82 -30.5q-48 0 -79.5 30.5t-31.5 77.5zM352 1196v-1196h-170v1196h170z" />
    <glyph glyph-name="Eth.sc" horiz-adv-x="1290" 
d="M647 0h-457v537h-159v122h159v537h457q251 0 391 -168q142 -171 142 -432q0 -259 -142 -430q-141 -166 -391 -166zM362 1026v-367h283v-122h-283v-367h285q171 0 267 116t96 310t-96.5 312t-266.5 118h-285z" />
    <glyph glyph-name="Ntilde.sc" horiz-adv-x="1230" 
d="M1049 1196v-1196h-164l-531 877v-877h-172v1196h168l527 -866v866h172zM539 1430q-15 0 -28 -20t-13 -54h-117q6 105 49.5 158t108.5 53q36 0 94 -37l8 -6q35 -27 49 -27q41 0 41 70h119q-6 -104 -50.5 -156.5t-109.5 -52.5q-49 0 -100 45l-21 12q-21 15 -30 15z" />
    <glyph glyph-name="Ograve.sc" horiz-adv-x="1421" 
d="M279 160q-175 181 -175 436q0 256 175 434t430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178zM401 911q-125 -128 -125 -315q0 -186 125 -317q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315
q-125 131 -307 131q-183 0 -308 -131zM813 1335h-164l-213 275h201z" />
    <glyph glyph-name="Oacute.sc" horiz-adv-x="1421" 
d="M762 1610h203l-213 -275h-164zM279 1030q175 178 430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178q-175 181 -175 436q0 256 175 434zM401 279q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315
q-125 131 -307 131q-183 0 -308 -131q-125 -128 -125 -315q0 -186 125 -317z" />
    <glyph glyph-name="Ocircumflex.sc" horiz-adv-x="1421" 
d="M279 160q-175 181 -175 436q0 256 175 434t430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178zM401 911q-125 -128 -125 -315q0 -186 125 -317q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315
q-125 131 -307 131q-183 0 -308 -131zM989 1335h-153l-127 148l-125 -148h-156l199 275h164z" />
    <glyph glyph-name="Otilde.sc" horiz-adv-x="1421" 
d="M279 160q-175 181 -175 436q0 256 175 434t430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178zM401 911q-125 -128 -125 -315q0 -186 125 -317q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315
q-125 131 -307 131q-183 0 -308 -131zM633 1430q-36 0 -39 -74h-119q6 105 50 158t108 53q38 0 96 -37l8 -6q34 -27 47 -27q40 0 43 70h117q-6 -105 -50 -157t-110 -52q-47 0 -98 45l-22 12q-21 15 -31 15z" />
    <glyph glyph-name="Odieresis.sc" horiz-adv-x="1421" 
d="M451 1470q0 44 33.5 75.5t80.5 31.5q44 0 76.5 -31.5t32.5 -75.5q0 -46 -32 -77t-77 -31q-50 0 -82 30.5t-32 77.5zM743 1470q0 45 32 76t79 31t80 -31.5t33 -75.5q0 -47 -32.5 -77.5t-80.5 -30.5t-79.5 30.5t-31.5 77.5zM279 1030q175 178 430 178q257 0 432 -178
q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178q-175 181 -175 436q0 256 175 434zM401 279q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315q-125 131 -307 131q-183 0 -308 -131q-125 -128 -125 -315q0 -186 125 -317z" />
    <glyph glyph-name="Oslash.sc" horiz-adv-x="1417" 
d="M332 111l-137 -142l-89 90l138 142q-142 177 -142 395q0 257 174 434q175 178 431 178q213 0 383 -135l139 137l88 -86l-137 -139q135 -176 135 -389q0 -254 -176 -436q-175 -178 -432 -178q-211 0 -375 129zM274 596q0 -153 86 -274l613 628q-115 92 -266 92
q-183 0 -308 -131q-125 -128 -125 -315zM707 150q181 0 307 129q127 133 127 317q0 144 -82 266l-610 -629q115 -83 258 -83z" />
    <glyph glyph-name="Ugrave.sc" horiz-adv-x="1218" 
d="M729 1335h-164l-215 275h205zM608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73z" />
    <glyph glyph-name="Uacute.sc" horiz-adv-x="1218" 
d="M608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73zM649 1610h203l-215 -275h-164z" />
    <glyph glyph-name="Ucircumflex.sc" horiz-adv-x="1218" 
d="M889 1335h-156l-125 148l-125 -148h-155l198 275h164zM608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73z" />
    <glyph glyph-name="Udieresis.sc" horiz-adv-x="1218" 
d="M608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73zM350 1470q0 44 33 75.5t80 31.5t78.5 -31t31.5 -76q0 -47 -31.5 -77.5t-78.5 -30.5
q-48 0 -80.5 30.5t-32.5 77.5zM641 1470q0 44 33 75.5t80 31.5t79.5 -31.5t32.5 -75.5q0 -47 -32 -77.5t-80 -30.5t-80.5 30.5t-32.5 77.5z" />
    <glyph glyph-name="Yacute.sc" horiz-adv-x="1153" 
d="M664 549v-549h-177v549l-454 647h203l337 -477l342 477h209zM633 1610h205l-215 -275h-164z" />
    <glyph glyph-name="Thorn.sc" horiz-adv-x="1034" 
d="M588 238h-234v-238h-172v1196h172v-211h234q159 0 264 -102q106 -100 106 -269t-106 -272q-107 -104 -264 -104zM354 823v-420h234q85 0 141.5 59.5t56.5 151.5q0 91 -59.5 146.5t-153.5 60.5h-110q-47 2 -109 2z" />
    <glyph glyph-name="Amacron.sc" horiz-adv-x="1167" 
d="M584 956l-189 -499h377zM946 0l-115 301h-493l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182zM360 1378v150h445v-150h-445z" />
    <glyph glyph-name="Abreve.sc" horiz-adv-x="1167" 
d="M354 1567h140q0 -45 23.5 -73t64.5 -28q40 0 65 30.5t25 70.5h141q0 -105 -63.5 -168.5t-167.5 -63.5q-111 0 -169.5 64.5t-58.5 167.5zM772 457l-188 499l-189 -499h377zM338 301l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182l-115 301h-493z" />
    <glyph glyph-name="Cacute.sc" horiz-adv-x="1228" 
d="M756 1610h202l-215 -275h-163zM1169 195q-86 -100 -204.5 -156.5t-251.5 -56.5q-254 0 -432 178q-177 183 -177 436q0 254 177 434q178 178 432 178q275 0 452 -204l-127 -109q-138 147 -325 147t-312 -131q-125 -128 -125 -315q0 -186 125 -317q126 -129 312 -129
q195 0 327 153z" />
    <glyph glyph-name="Ccircumflex.sc" horiz-adv-x="1228" 
d="M1040 303l129 -108q-86 -100 -204.5 -156.5t-251.5 -56.5q-254 0 -432 178q-177 183 -177 436q0 254 177 434q178 178 432 178q275 0 452 -204l-127 -109q-138 147 -325 147t-312 -131q-125 -128 -125 -315q0 -186 125 -317q126 -129 312 -129q195 0 327 153zM991 1335
h-155l-127 148l-127 -148h-154l199 275h164z" />
    <glyph glyph-name="Cdotaccent.sc" horiz-adv-x="1228" 
d="M569 1487q0 44 33 75t80 31t81 -31t34 -75q0 -47 -32.5 -78t-82.5 -31q-48 0 -80.5 31t-32.5 78zM1169 195q-86 -100 -204.5 -156.5t-251.5 -56.5q-254 0 -432 178q-177 183 -177 436q0 254 177 434q178 178 432 178q275 0 452 -204l-127 -109q-138 147 -325 147
t-312 -131q-125 -128 -125 -315q0 -186 125 -317q126 -129 312 -129q195 0 327 153z" />
    <glyph glyph-name="Ccaron.sc" horiz-adv-x="1228" 
d="M559 1610l125 -146l127 146h156l-201 -275h-164l-196 275h153zM1169 195q-86 -100 -204.5 -156.5t-251.5 -56.5q-254 0 -432 178q-177 183 -177 436q0 254 177 434q178 178 432 178q275 0 452 -204l-127 -109q-138 147 -325 147t-312 -131q-125 -128 -125 -315
q0 -186 125 -317q126 -129 312 -129q195 0 327 153z" />
    <glyph glyph-name="Dcaron.sc" horiz-adv-x="1282" 
d="M539 1610l125 -146l127 146h153l-196 -275h-164l-201 275h156zM182 0v1196h457q251 0 391 -168q141 -170 141 -432q0 -260 -141 -430q-141 -166 -391 -166h-457zM354 170h285q171 0 266.5 116t95.5 310t-96 312t-266 118h-285v-856z" />
    <glyph glyph-name="Aogonek.sc" horiz-adv-x="1167" 
d="M584 956l-187 -499h375zM887 -98l55 106l-113 293h-491l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-79l-52 -145q-22 -60 31 -84q53 -18 76 36l6 41l121 9q0 -42 -17 -95q-23 -51 -66.5 -81.5t-94.5 -30.5q-44 0 -68 12q-113 52 -113 166q0 40 15 74z" />
    <glyph glyph-name="Emacron.sc" horiz-adv-x="1021" 
d="M182 0v1196h713v-160h-541v-366h473v-162h-473v-346h557v-162h-729zM322 1378v150h444v-150h-444z" />
    <glyph glyph-name="Ebreve.sc" horiz-adv-x="1021" 
d="M281 1567h139q0 -44 25 -72.5t67 -28.5q37 0 62.5 30.5t25.5 70.5h141q0 -106 -62.5 -169t-166.5 -63q-111 0 -171 64.5t-60 167.5zM895 1196v-160h-541v-366h473v-162h-473v-346h557v-162h-729v1196h713z" />
    <glyph glyph-name="Edotaccent.sc" horiz-adv-x="1021" 
d="M436 1487q0 44 33 75t80 31q49 0 83 -31t34 -75q0 -47 -33.5 -78t-83.5 -31q-48 0 -80.5 31t-32.5 78zM895 1196v-160h-541v-366h473v-162h-473v-346h557v-162h-729v1196h713z" />
    <glyph glyph-name="Ecaron.sc" horiz-adv-x="1021" 
d="M385 1610l127 -146l127 146h154l-199 -275h-164l-201 275h156zM895 1196v-160h-541v-366h473v-162h-473v-346h557v-162h-729v1196h713z" />
    <glyph glyph-name="Gcircumflex.sc" horiz-adv-x="1349" 
d="M1116 1047l-123 -115q-118 112 -284 112q-183 0 -308 -131t-125 -317t125 -317q126 -132 308 -132q159 0 261 84t109 224h-336v151h531q0 -305 -148.5 -464.5t-416.5 -159.5q-255 0 -430 178q-175 181 -175 436q0 256 175 434t430 178q240 0 407 -161zM963 1335h-156
l-127 148l-123 -148h-156l199 275h162z" />
    <glyph glyph-name="Gbreve.sc" horiz-adv-x="1349" 
d="M451 1567h141q0 -45 23.5 -73t64.5 -28q39 0 63.5 30.5t24.5 70.5h145q0 -106 -63.5 -169t-169.5 -63q-111 0 -170 64.5t-59 167.5zM993 932q-118 112 -284 112q-183 0 -308 -131t-125 -317t125 -317q126 -132 308 -132q159 0 261 84t109 224h-336v151h531
q0 -305 -148.5 -464.5t-416.5 -159.5q-255 0 -430 178q-175 181 -175 436q0 256 175 434t430 178q240 0 407 -161z" />
    <glyph glyph-name="Eogonek.sc" horiz-adv-x="1021" 
d="M907 2l-6 -2l-43 -127q-11 -23 -1.5 -48.5t32.5 -35.5q23 -7 45 3.5t33 33.5l4 43l123 6q0 -45 -19 -94q-23 -52 -66 -82.5t-94 -30.5q-35 0 -69 15q-111 49 -111 163q0 37 13 74l43 80h-609v1196h715v-160h-543v-366h473v-162h-473v-346h557z" />
    <glyph glyph-name="Dcroat.sc" horiz-adv-x="1292" 
d="M649 0h-456v532h-160v136h160v528h456q251 0 391 -168q142 -171 142 -432q0 -259 -142 -430q-141 -166 -391 -166zM365 1026v-358h282v-136h-282v-362h284q171 0 267 116t96 310t-96.5 312t-266.5 118h-284z" />
    <glyph glyph-name="Gdotaccent.sc" horiz-adv-x="1349" 
d="M545 1487q0 44 32.5 75t79.5 31t81 -31t34 -75q0 -47 -32.5 -78t-82.5 -31q-48 0 -80 31t-32 78zM993 932q-118 112 -284 112q-183 0 -308 -131t-125 -317t125 -317q126 -132 308 -132q159 0 261 84t109 224h-336v151h531q0 -305 -148.5 -464.5t-416.5 -159.5
q-255 0 -430 178q-175 181 -175 436q0 256 175 434t430 178q240 0 407 -161z" />
    <glyph glyph-name="Hcircumflex.sc" horiz-adv-x="1269" 
d="M913 1196h174v-1196h-174v516h-559v-516h-172v1196h172v-522h559v522zM915 1335h-155l-127 148l-125 -148h-154l199 275h164z" />
    <glyph glyph-name="Hbar.sc" horiz-adv-x="1286" 
d="M1094 1196v-215h120v-107h-120v-874h-172v512h-560v-512h-172v874h-116v107h116v215h172v-215h560v215h172zM362 874v-198h560v198h-560z" />
    <glyph glyph-name="Itilde.sc" horiz-adv-x="536" 
d="M182 0v1196h172v-1196h-172zM193 1430q-38 0 -41 -74h-117q6 105 50 158t108 53q36 0 94 -37l8 -6q34 -27 49 -27q40 0 43 70h117q-6 -105 -50 -157t-110 -52q-49 0 -100 45l-21 12q-21 15 -30 15z" />
    <glyph glyph-name="Imacron.sc" horiz-adv-x="536" 
d="M182 0v1196h172v-1196h-172zM47 1378v150h445v-150h-445z" />
    <glyph glyph-name="Iogonek.sc" horiz-adv-x="536" 
d="M354 0h-69l-49 -145q-22 -60 28 -84q54 -19 78 36l6 41l121 9q0 -49 -18 -95q-23 -51 -66 -81.5t-94 -30.5q-44 0 -68 12q-53 24 -83.5 69t-30.5 97q0 42 14 74l59 108v1186h172v-1196z" />
    <glyph glyph-name="IJ.sc" horiz-adv-x="1073" 
d="M385 -412l16 166q50 -16 121 -16q99 0 148 59t49 162v1237h172v-1237q0 -179 -92.5 -284t-276.5 -105q-46 0 -137 18zM182 0v1196h172v-1196h-172z" />
    <glyph glyph-name="Jcircumflex.sc" horiz-adv-x="536" 
d="M535 1335h-154l-127 148l-125 -148h-156l201 275h164zM-152 -412l17 166q50 -16 121 -16q99 0 147.5 59t48.5 162v1237h172v-1237q0 -179 -92 -284t-276 -105q-46 0 -138 18z" />
    <glyph glyph-name="Lacute.sc" horiz-adv-x="880" 
d="M360 1591h203l-213 -276h-164zM354 1196v-1034h484v-162h-656v1196h172z" />
    <glyph glyph-name="Lcaron.sc" horiz-adv-x="882" 
d="M182 0v1196h172v-1034h484v-162h-656zM584 866l-113 29q78 116 78 229q0 4 -0.5 12.5t-1.5 23.5t-2 26h168q0 -218 -129 -320z" />
    <glyph glyph-name="Ldot.sc" horiz-adv-x="880" 
d="M182 0v1196h172v-1034h484v-162h-656zM594 557q44 0 76 -29.5t32 -72.5q0 -42 -31.5 -71.5t-76.5 -29.5q-43 0 -74 29.5t-31 71.5t31 72t74 30z" />
    <glyph glyph-name="Nacute.sc" horiz-adv-x="1230" 
d="M651 1610h205l-215 -275h-164zM885 0l-531 877v-877h-172v1196h168l527 -866v866h172v-1196h-164z" />
    <glyph glyph-name="Ncaron.sc" horiz-adv-x="1230" 
d="M487 1610l125 -146l127 146h154l-197 -275h-164l-200 275h155zM885 0l-531 877v-877h-172v1196h168l527 -866v866h172v-1196h-164z" />
    <glyph glyph-name="Omacron.sc" horiz-adv-x="1421" 
d="M279 160q-175 181 -175 436q0 256 175 434t430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178zM401 911q-125 -128 -125 -315q0 -186 125 -317q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315
q-125 131 -307 131q-183 0 -308 -131zM487 1378v150h445v-150h-445z" />
    <glyph glyph-name="Obreve.sc" horiz-adv-x="1421" 
d="M479 1567h137q0 -44 25.5 -72.5t67.5 -28.5q39 0 63.5 30.5t24.5 70.5h141q0 -106 -62.5 -169t-166.5 -63q-111 0 -170.5 64.5t-59.5 167.5zM279 1030q175 178 430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178
q-175 181 -175 436q0 256 175 434zM401 279q126 -129 308 -129q181 0 307 129q127 133 127 317q0 185 -127 315q-125 131 -307 131q-183 0 -308 -131q-125 -128 -125 -315q0 -186 125 -317z" />
    <glyph glyph-name="Ohungarumlaut.sc" horiz-adv-x="1421" 
d="M641 1610h176l-162 -275h-157zM887 1610h176l-184 -275h-154zM279 1030q175 178 430 178q257 0 432 -178q176 -179 176 -434q0 -254 -176 -436q-175 -178 -432 -178q-255 0 -430 178q-175 181 -175 436q0 256 175 434zM401 279q126 -129 308 -129q181 0 307 129
q127 133 127 317q0 185 -127 315q-125 131 -307 131q-183 0 -308 -131q-125 -128 -125 -315q0 -186 125 -317z" />
    <glyph glyph-name="Racute.sc" horiz-adv-x="1118" 
d="M543 1610h200l-213 -275h-163zM1014 0h-209l-305 442h-144v-442h-174v1196h461q177 0 266 -111q92 -112 92 -266t-78 -254.5t-227 -113.5zM356 1036v-432h275q122 -8 171.5 103.5t0.5 223.5t-164 105h-283z" />
    <glyph glyph-name="Rcaron.sc" horiz-adv-x="1118" 
d="M430 1610l127 -146l127 146h154l-199 -275h-164l-201 275h156zM1014 0h-209l-305 442h-144v-442h-174v1196h461q177 0 266 -111q92 -112 92 -266t-78 -254.5t-227 -113.5zM356 1036v-432h275q122 -8 171.5 103.5t0.5 223.5t-164 105h-283z" />
    <glyph glyph-name="Sacute.sc" horiz-adv-x="1034" 
d="M549 1610h205l-215 -275h-164zM283 354q0 -84 69 -147q70 -64 176 -64q100 0 161.5 47.5t72.5 120.5q11 80 -45 132.5t-152 74.5l-153 39q-291 70 -291 315q0 147 115 242q116 94 274 94q169 0 276 -98q111 -94 111 -258h-172q0 85 -61 138.5t-156 53.5q-89 0 -152 -49
t-63 -123q0 -116 164 -157l153 -39q168 -43 251.5 -141.5t70.5 -237.5q-21 -134 -129 -225q-110 -90 -275 -90q-182 0 -301 110q-118 112 -118 262h174z" />
    <glyph glyph-name="Scircumflex.sc" horiz-adv-x="1034" 
d="M784 1335h-155l-125 148l-127 -148h-156l201 275h164zM109 354h174q0 -84 69 -147q70 -64 176 -64q100 0 161.5 47.5t72.5 120.5q11 80 -45 132.5t-152 74.5l-153 39q-291 70 -291 315q0 147 115 242q116 94 274 94q169 0 276 -98q111 -94 111 -258h-172q0 85 -61 138.5
t-156 53.5q-89 0 -152 -49t-63 -123q0 -116 164 -157l153 -39q168 -43 251.5 -141.5t70.5 -237.5q-21 -134 -129 -225q-110 -90 -275 -90q-182 0 -301 110q-118 112 -118 262z" />
    <glyph glyph-name="Scedilla.sc" horiz-adv-x="1036" 
d="M469 -193q10 -23 32.5 -33.5t45.5 -2.5q23 11 32 35t-1 49l-54 127q-179 3 -297 112t-118 260h174q0 -84 69 -147q70 -64 176 -64q100 0 161.5 47.5t72.5 120.5q11 80 -45 132.5t-152 74.5l-153 39q-291 70 -291 315q0 147 115 242q116 94 274 94q169 0 276 -98
q111 -94 111 -258h-172q0 85 -61 138.5t-156 53.5q-89 0 -152 -49t-63 -123q0 -116 164 -157l153 -39q168 -43 251.5 -141.5t70.5 -237.5q-19 -112 -97 -194.5t-196 -106.5l47 -94q14 -32 14 -74q0 -114 -112 -166q-27 -12 -70 -12q-50 0 -93.5 30.5t-66.5 81.5
q-16 56 -16 95l123 -9q-3 -27 4 -41z" />
    <glyph glyph-name="Utilde.sc" horiz-adv-x="1218" 
d="M532 1430q-15 0 -27 -19.5t-13 -54.5h-117q6 105 49.5 158t107.5 53q48 0 99 -39l8 -8q41 -23 47 -23q40 0 43 70h115q-6 -105 -50 -157t-108 -52q-51 0 -102 45q-7 0 -13 6q-27 21 -39 21zM608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312
q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73z" />
    <glyph glyph-name="Umacron.sc" horiz-adv-x="1218" 
d="M387 1378v150h444v-150h-444zM608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73z" />
    <glyph glyph-name="Ubreve.sc" horiz-adv-x="1218" 
d="M608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73zM379 1567h139q0 -44 24.5 -72.5t65.5 -28.5q37 0 62.5 30.5t25.5 70.5h144
q0 -105 -63.5 -168.5t-168.5 -63.5q-111 0 -170 64.5t-59 167.5z" />
    <glyph glyph-name="Uring.sc" horiz-adv-x="1218" 
d="M608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73zM420 1497q0 79 54 132.5t132 53.5q82 0 136.5 -53t54.5 -133q0 -78 -54.5 -131t-136.5 -53
q-78 0 -132 53t-54 131zM606 1415q35 0 61.5 25t26.5 57q0 33 -26.5 58.5t-61.5 25.5q-32 0 -57 -26t-25 -58t25 -57t57 -25z" />
    <glyph glyph-name="Uhungarumlaut.sc" horiz-adv-x="1218" 
d="M608 150q113 0 189 73q80 74 80 189v784h172v-784q0 -192 -129 -312q-124 -118 -312 -118q-184 0 -311 118q-127 121 -127 312v784h174v-784q0 -114 78 -189q76 -73 186 -73zM520 1610h174l-162 -275h-155zM764 1610h176l-182 -275h-156z" />
    <glyph glyph-name="Uogonek.sc" horiz-adv-x="1222" 
d="M733 -193l2 3l4 38l123 9q0 -52 -16 -95q-25 -51 -69 -81.5t-93 -30.5q-46 0 -70 12q-112 55 -112 166q0 42 14 74l45 84q-163 15 -278 133q-111 117 -111 293v784h174v-784q0 -114 78 -189q76 -73 186 -73q113 0 189 73q80 74 80 189v784h172v-784q0 -175 -107 -291
q-108 -114 -270 -135q-5 -18 -24 -69t-23 -62q-22 -60 30 -84q24 -8 46.5 2t29.5 34z" />
    <glyph glyph-name="Wcircumflex.sc" horiz-adv-x="1607" 
d="M1083 283l293 913h185l-406 -1196h-147l-205 721l-203 -721h-147l-406 1196h184l293 -913l215 753h129zM1085 1335h-155l-127 148l-125 -148h-156l199 275h164z" />
    <glyph glyph-name="Ycircumflex.sc" horiz-adv-x="1153" 
d="M860 1335h-158l-127 148l-124 -148h-154l199 275h163zM664 549v-549h-177v549l-454 647h203l337 -477l342 477h209z" />
    <glyph glyph-name="Tcaron.sc" horiz-adv-x="987" 
d="M367 1610l127 -146l124 146h156l-201 -275h-163l-199 275h156zM940 1196v-162h-360v-1034h-174v1034h-359v162h893z" />
    <glyph glyph-name="Tbar.sc" horiz-adv-x="989" 
d="M580 1026v-309h229v-117h-229v-600h-172v600h-226v117h226v309h-363v170h899v-170h-364z" />
    <glyph glyph-name="Zacute.sc" horiz-adv-x="1038" 
d="M92 0v147l619 887h-594v162h817v-147l-625 -885h625v-164h-842zM565 1610h205l-217 -275h-164z" />
    <glyph glyph-name="Zdotaccent.sc" horiz-adv-x="1038" 
d="M92 0v147l619 887h-594v162h817v-147l-625 -885h625v-164h-842zM397 1487q0 44 34 75t81 31q49 0 82 -30.5t33 -75.5q0 -47 -32.5 -78t-82.5 -31q-48 0 -81.5 31t-33.5 78z" />
    <glyph glyph-name="Wgrave.sc" horiz-adv-x="1607" 
d="M1083 283l293 913h185l-406 -1196h-147l-205 721l-203 -721h-147l-406 1196h184l293 -913l215 753h129zM895 1335h-164l-217 275h205z" />
    <glyph glyph-name="Wacute.sc" horiz-adv-x="1607" 
d="M887 1610h203l-216 -275h-163zM1561 1196l-406 -1196h-147l-205 721l-203 -721h-147l-406 1196h184l293 -913l215 753h129l215 -753l293 913h185z" />
    <glyph glyph-name="Wdieresis.sc" horiz-adv-x="1607" 
d="M545 1470q0 44 32.5 75.5t79.5 31.5t80 -31.5t33 -75.5q0 -47 -32.5 -77.5t-80.5 -30.5t-80 30.5t-32 77.5zM836 1470q0 44 33.5 75.5t80.5 31.5t79 -31t32 -76q0 -47 -31.5 -77.5t-79.5 -30.5q-50 0 -82 30.5t-32 77.5zM1561 1196l-406 -1196h-147l-205 721l-203 -721
h-147l-406 1196h184l293 -913l215 753h129l215 -753l293 913h185z" />
    <glyph glyph-name="Aringacute.sc" horiz-adv-x="1167" 
d="M655 1886h205l-178 -229q88 -52 88 -160q0 -78 -54.5 -131t-135.5 -53q-78 0 -131.5 53t-53.5 131q0 63 34.5 111t90.5 65zM500 1497q0 -32 24 -57t56 -25q37 0 62.5 23.5t25.5 58.5t-25.5 59.5t-62.5 24.5q-33 0 -56.5 -24.5t-23.5 -59.5zM772 457l-188 499l-189 -499
h377zM338 301l-66 -172l-49 -129h-184l455 1196h180l454 -1196h-182l-115 301h-493z" />
    <glyph glyph-name="AEacute.sc" horiz-adv-x="1667" 
d="M989 1610h203l-215 -275h-164zM227 0h-204l757 1196h758v-170h-539v-354h469v-170h-469v-332h557v-170h-729v297h-409zM827 463v481l-307 -481h307z" />
    <glyph glyph-name="Oslashacute.sc" horiz-adv-x="1417" 
d="M784 1610h203l-215 -275h-164zM195 -31l-89 90l138 142q-142 177 -142 395q0 257 174 434q175 178 431 178q213 0 383 -135l139 137l88 -86l-137 -139q135 -176 135 -389q0 -254 -176 -436q-175 -178 -432 -178q-211 0 -375 129zM274 596q0 -153 86 -274l613 628
q-115 92 -266 92q-183 0 -308 -131q-125 -128 -125 -315zM707 150q181 0 307 129q127 133 127 317q0 144 -82 266l-610 -629q115 -83 258 -83z" />
    <glyph glyph-name="Ibreve.sc" horiz-adv-x="536" 
d="M39 1567h137q0 -44 24.5 -72.5t65.5 -28.5q40 0 65 30.5t25 70.5h142q0 -105 -63.5 -168.5t-168.5 -63.5q-111 0 -169 64.5t-58 167.5zM354 1196v-1196h-172v1196h172z" />
    <glyph glyph-name="Eng.sc" horiz-adv-x="1226" 
d="M870 -35v53l-518 850v-868h-170v1196h166l522 -866v866h174v-1184q0 -436 -370 -436q-68 0 -135 21l14 170l100 -15q67 -3 109 17q108 47 108 196z" />
    <glyph glyph-name="Ygrave.sc" horiz-adv-x="1153" 
d="M674 1335h-164l-215 275h203zM664 549v-549h-177v549l-454 647h203l337 -477l342 477h209z" />
    <glyph glyph-name="Gcommaaccent.sc" horiz-adv-x="1349" 
d="M1116 1047l-123 -115q-118 112 -284 112q-183 0 -308 -131t-125 -317t125 -317q126 -132 308 -132q159 0 261 84t109 224h-336v151h531q0 -305 -148.5 -464.5t-416.5 -159.5q-255 0 -430 178q-175 181 -175 436q0 256 175 434t430 178q240 0 407 -161zM657 -430l-110 35
q88 122 88 237q0 8 -4 74h166q3 -227 -140 -346z" />
    <glyph glyph-name="Kcommaaccent.sc" horiz-adv-x="1054" 
d="M545 -430l-111 35q88 122 88 237q0 8 -4 74h166q3 -228 -139 -346zM946 1196l-459 -598l566 -598h-236l-463 496v-496h-172v1196h172v-500l383 500h209z" />
    <glyph glyph-name="Lcommaaccent.sc" horiz-adv-x="880" 
d="M182 0v1196h172v-1034h484v-162h-656zM449 -430l-111 35q88 122 88 237q0 8 -4 74h166q3 -228 -139 -346z" />
    <glyph glyph-name="Ncommaaccent.sc" horiz-adv-x="1230" 
d="M1049 1196v-1196h-164l-531 877v-877h-172v1196h168l527 -866v866h172zM582 -430l-111 35q88 122 88 237q0 8 -4 74h166q3 -228 -139 -346z" />
    <glyph glyph-name="Rcommaaccent.sc" horiz-adv-x="1118" 
d="M696 451l318 -451h-209l-305 442h-144v-442h-174v1196h461q177 0 266 -111q92 -112 92 -266t-78 -254.5t-227 -113.5zM639 1036h-283v-432h275q122 -8 171.5 103.5t0.5 223.5t-164 105zM537 -430l-111 35q88 122 88 237q0 8 -4 74h166q3 -228 -139 -346z" />
    <glyph glyph-name="Tcommaaccent.sc" horiz-adv-x="987" 
d="M47 1034v162h893v-162h-360v-1034h-174v1034h-359zM430 -430l-111 35q89 124 89 237q0 7 -1 18t-2 27t-2 29h166q3 -228 -139 -346z" />
    <glyph glyph-name="Scommaaccent.sc" horiz-adv-x="1034" 
d="M109 354h174q0 -84 69 -147q70 -64 176 -64q100 0 161.5 47.5t72.5 120.5q11 80 -45 132.5t-152 74.5l-153 39q-291 70 -291 315q0 147 115 242q116 94 274 94q169 0 276 -98q111 -94 111 -258h-172q0 85 -61 138.5t-156 53.5q-89 0 -152 -49t-63 -123q0 -116 164 -157
l153 -39q168 -43 251.5 -141.5t70.5 -237.5q-21 -134 -129 -225q-110 -90 -275 -90q-182 0 -301 110q-118 112 -118 262zM481 -430l-110 35q88 122 88 237q0 8 -4 74h166q3 -227 -140 -346z" />
    <glyph glyph-name="Idotaccent.sc" horiz-adv-x="536" 
d="M154 1487q0 44 33.5 75t80.5 31q49 0 82 -30.5t33 -75.5q0 -47 -32.5 -78t-82.5 -31q-48 0 -81 31t-33 78zM354 1196v-1196h-172v1196h172z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="745" 
d="M80 932q0 92 13.5 162.5t45 130t91 91.5t143.5 32t143.5 -32t91 -91.5t45 -130t13.5 -162.5q0 -410 -293 -410t-293 410zM201 932q0 -291 172 -291t172 291q0 295 -172 295t-172 -295z" />
    <glyph glyph-name="one.numr" horiz-adv-x="428" 
d="M315 532h-122v625l-140 -72v117l262 144v-814z" />
    <glyph glyph-name="two.numr" horiz-adv-x="653" 
d="M74 532v58l323 391q39 49 49.5 96.5t-4.5 80.5t-48 53t-77 20q-55 0 -96.5 -37t-48.5 -96l-109 28q13 99 84.5 160.5t169.5 61.5q109 0 181.5 -68.5t72.5 -173.5q0 -111 -88 -209l-207 -244h297v-121h-499z" />
    <glyph glyph-name="three.numr" horiz-adv-x="657" 
d="M180 1153l-92 74q35 60 99.5 93t138.5 28q97 -3 163 -72q65 -65 62 -154q0 -89 -84 -155q115 -59 115 -197q0 -113 -79.5 -179.5t-199.5 -66.5q-75 0 -149.5 46t-104.5 114l103 59q18 -40 66 -69t93 -29q64 0 105.5 34t42.5 89q5 56 -31.5 95.5t-95.5 39.5h-115v117h96
q38 0 66.5 24t36.5 60q6 55 -24.5 89t-90.5 34q-71 0 -121 -74z" />
    <glyph glyph-name="four.numr" horiz-adv-x="739" 
d="M684 694h-115v-162h-120v162h-416l446 643h90v-528h115v-115zM449 1116l-207 -307h207v307z" />
    <glyph glyph-name="five.numr" horiz-adv-x="696" 
d="M55 690l101 72q23 -56 72.5 -89.5t107.5 -33.5q79 0 127.5 43.5t48.5 110.5q0 64 -45 101.5t-109 37.5q-94 0 -227 -68l-37 47l72 424h412v-114h-312l-18 -99l-19 -98q61 27 129 27q119 0 196 -69t77 -189q0 -123 -81 -198t-214 -75q-88 0 -165.5 46t-115.5 124z" />
    <glyph glyph-name="six.numr" horiz-adv-x="692" 
d="M616 1180l-108 -45q-46 94 -131 94q-78 0 -126 -76t-46 -195q51 97 164 97q110 0 182 -84q73 -82 70 -197q-6 -108 -84 -186q-76 -73 -189 -70q-109 5 -180.5 79t-81.5 198q-1 41 -3 90.5t5 103t16.5 104.5t32.5 97.5t53.5 81t78.5 54t108 17.5q169 -9 239 -163zM211 793
q0 -63 41.5 -106.5t101.5 -45.5q60 -1 100.5 38t45.5 101q2 64 -35.5 107.5t-97.5 44.5q-64 2 -109.5 -38t-46.5 -101z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="659" 
d="M76 1337h563l-440 -805h-142l375 685h-356v120z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="700" 
d="M133 1137q0 88 62.5 149.5t154.5 61.5q93 0 157 -61.5t64 -149.5q0 -73 -59 -138q117 -77 117 -215q0 -109 -80 -186q-79 -76 -199 -76q-119 0 -198 76q-80 77 -80 186q0 142 114 215q-53 59 -53 138zM350 641q66 0 112 41.5t46 101.5q0 62 -46.5 108t-111.5 46h-8
q-62 0 -105.5 -46.5t-43.5 -107.5q0 -60 45 -101.5t112 -41.5zM344 1040h10q40 1 67.5 30t27.5 67q0 37 -29 63.5t-70 26.5t-68.5 -26.5t-27.5 -63.5t26 -66.5t64 -30.5z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="690" 
d="M82 684l108 45q46 -94 132 -94q78 0 126 75.5t46 194.5q-50 -96 -164 -96q-110 0 -185 84q-70 82 -67 197q6 109 84 184q75 75 188 72q109 -5 180.5 -79t81.5 -198q6 -48 3 -107.5t-8.5 -121t-26.5 -120t-51.5 -104t-84 -72t-122.5 -24.5q-170 9 -240 164zM487 1071
q0 63 -41.5 106.5t-101.5 45.5q-59 1 -99.5 -38.5t-45.5 -101.5q-2 -63 35.5 -106.5t97.5 -44.5q64 -2 109 38t46 101z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="745" 
d="M80 399q0 92 13.5 162.5t45 130t91 91.5t143.5 32t143.5 -32t91 -91.5t45 -130t13.5 -162.5q0 -409 -293 -409t-293 409zM201 399q0 -290 172 -290t172 290q0 295 -172 295t-172 -295z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="428" 
d="M315 0h-122v625l-140 -72v117l262 143v-813z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="653" 
d="M74 0v57l323 392q52 65 53.5 124t-36.5 92t-97 33q-55 0 -96.5 -37t-48.5 -96l-109 29q13 99 84.5 160t169.5 61q109 0 181.5 -68.5t72.5 -173.5q0 -110 -88 -208l-207 -244h297v-121h-499z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="657" 
d="M180 621l-92 73q35 60 99.5 93t138.5 28q97 -3 163 -72q65 -65 62 -153q0 -90 -84 -156q115 -59 115 -196q0 -113 -79.5 -179.5t-199.5 -66.5q-75 0 -149.5 46t-104.5 114l103 59q18 -40 66 -69t93 -29q64 0 105.5 34t42.5 89q5 56 -31.5 95.5t-95.5 39.5h-115v116h96
q38 0 66.5 24t36.5 60q6 55 -24.5 89t-90.5 34q-72 0 -121 -73z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="739" 
d="M684 162h-115v-162h-120v162h-416l446 643h90v-529h115v-114zM449 584l-207 -308h207v308z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="696" 
d="M55 158l101 71q23 -56 72.5 -89.5t107.5 -33.5q79 0 127.5 43.5t48.5 110.5q0 64 -45 101.5t-109 37.5q-96 0 -227 -67l-37 47l72 424h412v-115h-312l-18 -98l-19 -98q59 26 129 26q119 0 196 -69t77 -189q0 -123 -81 -197.5t-214 -74.5q-88 0 -165.5 46t-115.5 124z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="692" 
d="M616 647l-108 -45q-46 94 -131 94q-78 0 -126 -75.5t-46 -194.5q50 96 164 96q110 0 182 -84q73 -82 70 -196q-6 -109 -84 -187q-75 -72 -189 -69q-110 5 -181 78.5t-81 197.5q-1 41 -3 90.5t5 103t16.5 105t32.5 97.5t53.5 81t78.5 54.5t108 17.5q169 -9 239 -164z
M211 260q0 -63 41.5 -106t101.5 -45q60 -1 100.5 38t45.5 101q2 63 -35.5 106.5t-97.5 44.5q-64 2 -109.5 -38t-46.5 -101z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="657" 
d="M76 805h563l-440 -805h-142l375 684h-356v121z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="700" 
d="M133 604q0 88 62.5 149.5t154.5 61.5q93 0 157 -61.5t64 -149.5q0 -72 -59 -137q117 -77 117 -215q0 -109 -80 -186q-79 -76 -199 -76q-119 0 -198 76q-80 77 -80 186q0 141 114 215q-53 59 -53 137zM350 109q66 0 112 41.5t46 101.5q0 62 -46.5 108t-111.5 46h-8
q-62 0 -105.5 -46.5t-43.5 -107.5q0 -60 45 -101.5t112 -41.5zM344 508h10q40 1 67.5 29.5t27.5 66.5q0 37 -29 63.5t-70 26.5t-68.5 -26.5t-27.5 -63.5t26 -66t64 -30z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="690" 
d="M82 152l108 45q47 -95 132 -95q78 0 126 76t46 195q-51 -97 -164 -97q-110 0 -185 84q-70 82 -67 197q6 109 84 184q75 75 188 72q110 -5 181 -78.5t81 -197.5q6 -48 3 -107.5t-8.5 -121t-26.5 -120t-51.5 -104t-84 -72t-122.5 -24.5q-170 9 -240 164zM487 539
q0 63 -41.5 106t-101.5 45q-60 1 -100 -38t-45 -101q-2 -64 35.5 -107.5t97.5 -44.5q63 -2 108.5 38.5t46.5 101.5z" />
    <glyph glyph-name="l.alt1" horiz-adv-x="571" 
d="M504 174h63v-174h-80q-344 0 -344 410v1067h189v-1065q-3 -238 172 -238z" />
    <glyph glyph-name="a.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90z" />
    <glyph glyph-name="y.alt1" horiz-adv-x="1040" 
d="M367 -98l53 127l-383 927h199l284 -688l287 688h199l-469 -1126q-74 -165 -160 -238q-81 -69 -227 -69q-64 0 -113 14v164q37 -6 108 -6q83 0 126.5 43t95.5 164z" />
    <glyph glyph-name="lslash.alt1" horiz-adv-x="593" 
d="M522 174h64v-174h-80q-344 0 -344 410v241l-135 -86v131l135 86v695h188v-574l156 101v-132l-156 -100v-360q-3 -238 172 -238z" />
    <glyph glyph-name="lacute.alt1" horiz-adv-x="571" 
d="M344 1907h223l-243 -316h-179zM567 0h-80q-344 0 -344 410v1067h189v-1065q-3 -238 172 -238h63v-174z" />
    <glyph glyph-name="lcommaaccent.alt1" horiz-adv-x="571" 
d="M504 174h63v-174h-80q-344 0 -344 410v1067h189v-1065q-3 -238 172 -238zM344 -512l-119 39q62 82 84 160q22 76 10 196h185q3 -263 -160 -395z" />
    <glyph glyph-name="lcaron.alt1" horiz-adv-x="571" 
d="M537 1081l-119 39q62 82 84 160q22 76 10 197h184q3 -265 -159 -396zM567 0h-80q-344 0 -344 410v1067h189v-1065q-3 -238 172 -238h63v-174z" />
    <glyph glyph-name="aacute.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM625 1450h223l-244 -315h-178z" />
    <glyph glyph-name="agrave.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM649 1135h-178l-244 315h224z" />
    <glyph glyph-name="acircumflex.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM842 1135h-168l-154 178l-151 -178h-168l225 315h190z" />
    <glyph glyph-name="adieresis.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM375 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM631 1204q-37 35 -37 86t37 86t90 35t90 -35t37 -86t-37 -86t-90 -35t-90 35z" />
    <glyph glyph-name="atilde.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM428 1249q-20 0 -37 -24t-18 -66h-125q6 118 56 179t124 61q52 0 119 -49q59 -46 85 -32t36 81h125q-6 -116 -57.5 -175t-125.5 -59q-52 0 -116 52q-46 32 -66 32z" />
    <glyph glyph-name="aring.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM315 1321q0 92 61 154.5t152 62.5q93 0 156.5 -62.5t63.5 -154.5q0 -90 -63.5 -151.5t-156.5 -61.5q-90 0 -151.5 62.5t-61.5 150.5zM424 1321q0 -43 30.5 -73.5t73.5 -30.5q45 0 78 31t33 73
q0 45 -32 77t-79 32q-43 0 -73.5 -32.5t-30.5 -76.5z" />
    <glyph glyph-name="amacron.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM281 1188v164h516v-164h-516z" />
    <glyph glyph-name="abreve.alt1" horiz-adv-x="1105" 
d="M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5
q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM283 1399h151q0 -55 31 -88t84 -33q49 0 81 36t32 85h155q0 -121 -72.5 -191.5t-195.5 -70.5q-129 0 -197.5 73t-68.5 189z" />
    <glyph glyph-name="aogonek.alt1" horiz-adv-x="1105" 
d="M244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-575h-72l-64 -178q-12 -32 -0.5 -64t41.5 -45q73 -22 103 47l6 45l131 9q0 -54 -21 -101q-26 -61 -75 -96t-109 -35q-37 0 -80 17q-61 26 -95 76t-34 112q0 40 17 86l67 127v123q-48 -67 -141.5 -105
t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90zM446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36z" />
    <glyph glyph-name="yacute.alt1" horiz-adv-x="1040" 
d="M604 1450h223l-243 -315h-178zM420 29l-383 927h199l284 -688l287 688h199l-469 -1126q-74 -165 -160 -238q-81 -69 -227 -69q-64 0 -113 14v164q37 -6 108 -6q83 0 126.5 43t95.5 164z" />
    <glyph glyph-name="ydieresis.alt1" horiz-adv-x="1040" 
d="M356 1411q51 0 88 -35t37 -86t-37 -86t-88 -35q-54 0 -90.5 35t-36.5 86t36.5 86t90.5 35zM612 1204q-37 35 -37 86t37 86t90.5 35t90.5 -35t37 -86t-37 -86t-90.5 -35t-90.5 35zM420 29l-383 927h199l284 -688l287 688h199l-469 -1126q-74 -165 -160 -238
q-81 -69 -227 -69q-64 0 -113 14v164q37 -6 108 -6q83 0 126.5 43t95.5 164z" />
    <glyph glyph-name="ae.alt1" horiz-adv-x="1822" 
d="M963 78v-78h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90l-115 111q125 180 397 180q260 0 369 -158q143 158 371 158q209 0 340 -137
q135 -135 135 -359q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-178 0 -303 98zM963 553h593q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187zM446 145q87 0 185 43
q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36z" />
    <glyph glyph-name="aeacute.alt1" horiz-adv-x="1822" 
d="M1030 1450h223l-243 -315h-179zM963 0h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5q124 0 286 -52v49q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90l-115 111q125 180 397 180q260 0 369 -158q143 158 371 158
q209 0 340 -137q135 -135 135 -359q0 -59 -2 -73h-776q17 -118 101.5 -189.5t207.5 -71.5q88 0 162 39.5t112 108.5q9 -3 36 -14.5t46 -18.5l78 -29q-59 -121 -177 -187t-263 -66q-178 0 -303 98v-78zM1556 553q-17 119 -95 187.5t-195 68.5q-122 0 -204 -69t-99 -187h593z
M446 145q87 0 185 43q99 44 145 119v70q-140 49 -284 49q-234 0 -234 -150q0 -59 49 -95t139 -36z" />
    <glyph glyph-name="ycircumflex.alt1" horiz-adv-x="1040" 
d="M367 -98l53 127l-383 927h199l284 -688l287 688h199l-469 -1126q-74 -165 -160 -238q-81 -69 -227 -69q-64 0 -113 14v164q37 -6 108 -6q83 0 126.5 43t95.5 164zM838 1135h-168l-154 178l-151 -178h-168l225 315h190z" />
    <glyph glyph-name="ldot.alt1" horiz-adv-x="694" 
d="M504 174h63v-174h-80q-344 0 -344 410v1067h189v-1065q-3 -238 172 -238zM483 442q-38 37 -38 92.5t38 92.5t94.5 37t94.5 -37q41 -37 41 -92.5t-41 -92.5q-38 -35 -94.5 -35t-94.5 35z" />
    <glyph glyph-name="t.alt1" horiz-adv-x="718" 
d="M590 174h55v-174h-72q-344 0 -344 410v389h-172v157h172v256l189 58v-314h200v-157h-200v-387q-3 -238 172 -238z" />
    <glyph glyph-name="tcaron.alt1" horiz-adv-x="718" 
d="M614 1081l-118 39q62 82 84 160q22 76 10 197h184q3 -264 -160 -396zM645 0h-72q-344 0 -344 410v389h-172v157h172v256l189 58v-314h200v-157h-200v-387q-3 -238 172 -238h55v-174z" />
    <glyph glyph-name="tbar.alt1" horiz-adv-x="722" 
d="M594 174h55v-174h-71q-345 0 -345 410v53h-139v125h139v211h-172v157h172v256l189 58v-314h201v-157h-201v-211h153v-125h-153v-51q-3 -238 172 -238z" />
    <glyph glyph-name="tcommaaccent.alt1" horiz-adv-x="718" 
d="M590 174h55v-174h-72q-344 0 -344 410v389h-172v157h172v256l189 58v-314h200v-157h-200v-387q-3 -238 172 -238zM395 -512l-119 39q62 82 84 160q22 79 11 196h184q3 -263 -160 -395z" />
    <glyph glyph-name="T.sc.alt1" horiz-adv-x="987" 
d="M47 1034v162h893v-162h-360v-1034h-174v1034h-359z" />
    <glyph glyph-name="Tcaron.sc.alt1" horiz-adv-x="987" 
d="M367 1610l127 -146l124 146h156l-201 -275h-163l-199 275h156zM940 1196v-162h-360v-1034h-174v1034h-359v162h893z" />
    <glyph glyph-name="Tbar.sc.alt1" horiz-adv-x="989" 
d="M580 1026v-309h229v-117h-229v-600h-172v600h-226v117h226v309h-363v170h899v-170h-364z" />
    <glyph glyph-name="Tcommaaccent.sc.alt1" horiz-adv-x="987" 
d="M47 1034v162h893v-162h-360v-1034h-174v1034h-359zM430 -430l-111 35q89 124 89 237q0 7 -1 18t-2 27t-2 29h166q3 -228 -139 -346z" />
    <glyph glyph-name="aringacute.alt1" horiz-adv-x="1105" 
d="M631 1763h223l-209 -270q111 -59 111 -188q0 -91 -63.5 -152t-155.5 -61q-90 0 -151.5 62.5t-61.5 150.5q0 73 40.5 129.5t106.5 76.5zM432 1305q0 -43 31 -74t74 -31q44 0 77 31t33 74q0 45 -31.5 76.5t-78.5 31.5q-43 0 -74 -32t-31 -76zM446 145q87 0 185 43
q99 44 145 119v70q-125 43 -284 43q-234 0 -234 -144q0 -59 49 -95t139 -36zM244 686l-115 111q125 180 397 180q209 0 323 -103.5t114 -298.5v-122v-453h-185v123q-48 -67 -141.5 -105t-194.5 -38q-166 0 -263 79t-97 217q0 139 103 220.5t307 81.5q124 0 286 -52v49
q0 107 -71.5 170.5t-194.5 63.5q-73 0 -146 -33t-122 -90z" />
    <hkern u1="&#x20;" g2="V.sc" k="55" />
    <hkern u1="&#x20;" u2="&#x2019;" k="33" />
    <hkern u1="&#x20;" u2="v" k="51" />
    <hkern u1="&#x20;" u2="f" k="29" />
    <hkern u1="&#x20;" u2="V" k="63" />
    <hkern u1="&#x23;" g2="five.oldstyle" k="25" />
    <hkern u1="&#x23;" g2="four.oldstyle" k="121" />
    <hkern u1="&#x23;" g2="three.oldstyle" k="27" />
    <hkern u1="&#x23;" u2="&#x34;" k="35" />
    <hkern u1="&#x24;" g2="four.oldstyle" k="47" />
    <hkern u1="&#x24;" g2="three.oldstyle" k="31" />
    <hkern u1="&#x26;" u2="V" k="37" />
    <hkern u1="&#x28;" g2="Jcircumflex.sc" k="-137" />
    <hkern u1="&#x28;" g2="Icircumflex.sc" k="-2" />
    <hkern u1="&#x28;" g2="M.sc" k="49" />
    <hkern u1="&#x28;" g2="J.sc" k="-137" />
    <hkern u1="&#x28;" g2="nine.oldstyle" k="23" />
    <hkern u1="&#x28;" g2="eight.oldstyle" k="49" />
    <hkern u1="&#x28;" g2="six.oldstyle" k="47" />
    <hkern u1="&#x28;" g2="five.oldstyle" k="23" />
    <hkern u1="&#x28;" g2="four.oldstyle" k="74" />
    <hkern u1="&#x28;" g2="two.oldstyle" k="68" />
    <hkern u1="&#x28;" g2="one.oldstyle" k="63" />
    <hkern u1="&#x28;" g2="zero.oldstyle" k="86" />
    <hkern u1="&#x28;" u2="&#x135;" k="-109" />
    <hkern u1="&#x28;" u2="&#x134;" k="-197" />
    <hkern u1="&#x28;" u2="&#xef;" k="-25" />
    <hkern u1="&#x28;" u2="&#xec;" k="-49" />
    <hkern u1="&#x28;" u2="&#x7b;" k="29" />
    <hkern u1="&#x28;" u2="x" k="49" />
    <hkern u1="&#x28;" u2="v" k="43" />
    <hkern u1="&#x28;" u2="j" k="-109" />
    <hkern u1="&#x28;" u2="f" k="27" />
    <hkern u1="&#x28;" u2="M" k="33" />
    <hkern u1="&#x28;" u2="J" k="-197" />
    <hkern u1="&#x28;" u2="&#x39;" k="25" />
    <hkern u1="&#x28;" u2="&#x38;" k="49" />
    <hkern u1="&#x28;" u2="&#x36;" k="47" />
    <hkern u1="&#x28;" u2="&#x35;" k="27" />
    <hkern u1="&#x28;" u2="&#x34;" k="111" />
    <hkern u1="&#x28;" u2="&#x30;" k="45" />
    <hkern u1="&#x28;" u2="&#x28;" k="41" />
    <hkern u1="&#x29;" u2="&#x7d;" k="43" />
    <hkern u1="&#x29;" u2="]" k="25" />
    <hkern u1="&#x29;" u2="&#x29;" k="41" />
    <hkern u1="&#x2a;" u2="&#x166;" k="-20" />
    <hkern u1="&#x2a;" u2="&#x135;" k="-72" />
    <hkern u1="&#x2a;" u2="&#x129;" k="-23" />
    <hkern u1="&#x2a;" u2="&#x110;" k="-25" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-45" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-74" />
    <hkern u1="&#x2a;" u2="&#xd0;" k="-25" />
    <hkern u1="&#x2b;" g2="seven.oldstyle" k="31" />
    <hkern u1="&#x2b;" g2="four.oldstyle" k="109" />
    <hkern u1="&#x2b;" g2="three.oldstyle" k="49" />
    <hkern u1="&#x2b;" u2="&#x37;" k="27" />
    <hkern u1="&#x2b;" u2="&#x31;" k="23" />
    <hkern u1="&#x2d;" g2="seven.oldstyle" k="51" />
    <hkern u1="&#x2d;" g2="four.oldstyle" k="82" />
    <hkern u1="&#x2d;" g2="three.oldstyle" k="66" />
    <hkern u1="&#x2d;" g2="two.oldstyle" k="35" />
    <hkern u1="&#x2d;" u2="&#x37;" k="43" />
    <hkern u1="&#x2d;" u2="&#x32;" k="29" />
    <hkern u1="&#x2d;" u2="&#x31;" k="41" />
    <hkern u1="&#x2f;" g2="M.sc" k="39" />
    <hkern u1="&#x2f;" g2="nine.oldstyle" k="94" />
    <hkern u1="&#x2f;" g2="eight.oldstyle" k="35" />
    <hkern u1="&#x2f;" g2="seven.oldstyle" k="33" />
    <hkern u1="&#x2f;" g2="six.oldstyle" k="25" />
    <hkern u1="&#x2f;" g2="five.oldstyle" k="88" />
    <hkern u1="&#x2f;" g2="four.oldstyle" k="199" />
    <hkern u1="&#x2f;" g2="three.oldstyle" k="88" />
    <hkern u1="&#x2f;" g2="two.oldstyle" k="66" />
    <hkern u1="&#x2f;" g2="one.oldstyle" k="55" />
    <hkern u1="&#x2f;" g2="zero.oldstyle" k="82" />
    <hkern u1="&#x2f;" u2="&#xef;" k="-14" />
    <hkern u1="&#x2f;" u2="&#xec;" k="-59" />
    <hkern u1="&#x2f;" u2="x" k="37" />
    <hkern u1="&#x2f;" u2="v" k="20" />
    <hkern u1="&#x2f;" u2="&#x38;" k="35" />
    <hkern u1="&#x2f;" u2="&#x36;" k="25" />
    <hkern u1="&#x2f;" u2="&#x34;" k="125" />
    <hkern u1="&#x2f;" u2="&#x30;" k="23" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="381" />
    <hkern u1="&#x30;" u2="&#x2044;" k="-94" />
    <hkern u1="&#x30;" u2="&#xc6;" k="37" />
    <hkern u1="&#x30;" u2="&#x7d;" k="49" />
    <hkern u1="&#x30;" u2="]" k="31" />
    <hkern u1="&#x30;" u2="\" k="23" />
    <hkern u1="&#x30;" u2="Y" k="59" />
    <hkern u1="&#x30;" u2="V" k="25" />
    <hkern u1="&#x30;" u2="T" k="25" />
    <hkern u1="&#x30;" u2="&#x29;" k="43" />
    <hkern u1="&#x31;" u2="&#x2044;" k="-217" />
    <hkern u1="&#x32;" u2="&#x2044;" k="-285" />
    <hkern u1="&#x32;" u2="&#xb7;" k="27" />
    <hkern u1="&#x32;" u2="&#x7d;" k="27" />
    <hkern u1="&#x32;" u2="Y" k="41" />
    <hkern u1="&#x32;" u2="V" k="20" />
    <hkern u1="&#x33;" u2="&#x2044;" k="-127" />
    <hkern u1="&#x33;" u2="&#x7d;" k="45" />
    <hkern u1="&#x33;" u2="]" k="25" />
    <hkern u1="&#x33;" u2="\" k="20" />
    <hkern u1="&#x33;" u2="Y" k="53" />
    <hkern u1="&#x33;" u2="V" k="27" />
    <hkern u1="&#x33;" u2="&#x29;" k="35" />
    <hkern u1="&#x34;" u2="&#x2044;" k="-147" />
    <hkern u1="&#x34;" u2="&#xb0;" k="23" />
    <hkern u1="&#x34;" u2="&#x7d;" k="45" />
    <hkern u1="&#x34;" u2="]" k="27" />
    <hkern u1="&#x34;" u2="\" k="27" />
    <hkern u1="&#x34;" u2="Y" k="51" />
    <hkern u1="&#x34;" u2="W" k="27" />
    <hkern u1="&#x34;" u2="V" k="33" />
    <hkern u1="&#x34;" u2="T" k="25" />
    <hkern u1="&#x34;" u2="&#x29;" k="43" />
    <hkern u1="&#x35;" u2="&#x2044;" k="-127" />
    <hkern u1="&#x36;" u2="&#x2044;" k="-121" />
    <hkern u1="&#x36;" u2="&#x7d;" k="20" />
    <hkern u1="&#x36;" u2="Y" k="37" />
    <hkern u1="&#x37;" u2="&#x2212;" k="80" />
    <hkern u1="&#x37;" u2="&#x2044;" k="47" />
    <hkern u1="&#x37;" u2="&#xc6;" k="176" />
    <hkern u1="&#x37;" u2="&#xb7;" k="102" />
    <hkern u1="&#x37;" u2="&#xa2;" k="88" />
    <hkern u1="&#x37;" u2="Y" k="-18" />
    <hkern u1="&#x37;" u2="A" k="100" />
    <hkern u1="&#x37;" u2="&#x3d;" k="53" />
    <hkern u1="&#x37;" u2="&#x38;" k="31" />
    <hkern u1="&#x37;" u2="&#x36;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="131" />
    <hkern u1="&#x37;" u2="&#x2f;" k="137" />
    <hkern u1="&#x37;" u2="&#x2d;" k="100" />
    <hkern u1="&#x37;" u2="&#x2b;" k="74" />
    <hkern u1="&#x37;" u2="&#x23;" k="57" />
    <hkern u1="&#x38;" u2="&#x2044;" k="-125" />
    <hkern u1="&#x38;" u2="&#x7d;" k="53" />
    <hkern u1="&#x38;" u2="]" k="35" />
    <hkern u1="&#x38;" u2="\" k="33" />
    <hkern u1="&#x38;" u2="Y" k="66" />
    <hkern u1="&#x38;" u2="W" k="27" />
    <hkern u1="&#x38;" u2="V" k="33" />
    <hkern u1="&#x38;" u2="T" k="31" />
    <hkern u1="&#x38;" u2="&#x29;" k="49" />
    <hkern u1="&#x39;" u2="&#x2044;" k="-84" />
    <hkern u1="&#x39;" u2="&#xc6;" k="41" />
    <hkern u1="&#x39;" u2="&#x7d;" k="47" />
    <hkern u1="&#x39;" u2="]" k="29" />
    <hkern u1="&#x39;" u2="\" k="20" />
    <hkern u1="&#x39;" u2="Y" k="55" />
    <hkern u1="&#x39;" u2="V" k="25" />
    <hkern u1="&#x39;" u2="T" k="20" />
    <hkern u1="&#x39;" u2="&#x29;" k="41" />
    <hkern u1="&#x3d;" g2="four.oldstyle" k="45" />
    <hkern u1="&#x40;" g2="Y.sc" k="31" />
    <hkern u1="&#x40;" u2="Y" k="86" />
    <hkern u1="&#x40;" u2="W" k="27" />
    <hkern u1="&#x40;" u2="V" k="31" />
    <hkern u1="&#x40;" u2="T" k="66" />
    <hkern u1="A" g2="at.case" k="20" />
    <hkern u1="A" u2="&#x31;" k="29" />
    <hkern u1="B" g2="X.sc" k="10" />
    <hkern u1="B" g2="V.sc" k="12" />
    <hkern u1="B" g2="braceright.case" k="86" />
    <hkern u1="B" g2="bracketright.case" k="47" />
    <hkern u1="B" g2="parenright.case" k="72" />
    <hkern u1="B" u2="&#x7d;" k="53" />
    <hkern u1="B" u2="x" k="29" />
    <hkern u1="B" u2="v" k="23" />
    <hkern u1="B" u2="]" k="31" />
    <hkern u1="B" u2="X" k="16" />
    <hkern u1="B" u2="V" k="27" />
    <hkern u1="B" u2="&#x29;" k="45" />
    <hkern u1="C" u2="&#x135;" k="-51" />
    <hkern u1="C" u2="&#x129;" k="-23" />
    <hkern u1="C" u2="&#xef;" k="-43" />
    <hkern u1="C" u2="&#xee;" k="-53" />
    <hkern u1="D" u2="&#x141;" k="12" />
    <hkern u1="D" u2="&#x126;" k="12" />
    <hkern u1="E" u2="&#x131;" k="18" />
    <hkern u1="F" g2="Jcircumflex.sc" k="-35" />
    <hkern u1="F" g2="Icircumflex.sc" k="-23" />
    <hkern u1="F" g2="M.sc" k="33" />
    <hkern u1="F" u2="&#x135;" k="-18" />
    <hkern u1="F" u2="&#x131;" k="74" />
    <hkern u1="F" u2="&#xef;" k="-43" />
    <hkern u1="F" u2="&#xee;" k="-20" />
    <hkern u1="F" u2="&#xed;" k="27" />
    <hkern u1="F" u2="&#xec;" k="-55" />
    <hkern u1="F" u2="&#xdf;" k="27" />
    <hkern u1="F" u2="x" k="72" />
    <hkern u1="F" u2="v" k="31" />
    <hkern u1="F" u2="M" k="14" />
    <hkern u1="F" u2="&#x34;" k="70" />
    <hkern u1="F" u2="&#x2f;" k="84" />
    <hkern u1="F" u2="&#x20;" k="49" />
    <hkern u1="K" u2="&#xec;" k="-14" />
    <hkern u1="L" g2="periodcentered.case" k="498" />
    <hkern u1="L" g2="at.case" k="25" />
    <hkern u1="L" u2="&#xb7;" k="383" />
    <hkern u1="L" u2="&#x31;" k="29" />
    <hkern u1="M" g2="V.sc" k="14" />
    <hkern u1="M" u2="&#x2122;" k="16" />
    <hkern u1="M" u2="&#x7d;" k="33" />
    <hkern u1="M" u2="v" k="23" />
    <hkern u1="M" u2="V" k="29" />
    <hkern u1="M" u2="&#x29;" k="29" />
    <hkern u1="O" u2="&#x141;" k="12" />
    <hkern u1="O" u2="&#x126;" k="12" />
    <hkern u1="P" g2="M.sc" k="12" />
    <hkern u1="P" g2="braceright.case" k="86" />
    <hkern u1="P" g2="bracketright.case" k="43" />
    <hkern u1="P" g2="parenright.case" k="74" />
    <hkern u1="P" u2="&#x135;" k="-27" />
    <hkern u1="P" u2="&#xee;" k="-29" />
    <hkern u1="P" u2="X" k="31" />
    <hkern u1="P" u2="&#x34;" k="70" />
    <hkern u1="P" u2="&#x2f;" k="80" />
    <hkern u1="P" u2="&#x20;" k="55" />
    <hkern u1="Q" u2="&#x141;" k="12" />
    <hkern u1="Q" u2="&#x126;" k="12" />
    <hkern u1="R" u2="&#x34;" k="27" />
    <hkern u1="T" g2="Jcircumflex.sc" k="-39" />
    <hkern u1="T" g2="Icircumflex.sc" k="-27" />
    <hkern u1="T" u2="&#x161;" k="154" />
    <hkern u1="T" u2="&#x15d;" k="150" />
    <hkern u1="T" u2="&#x159;" k="127" />
    <hkern u1="T" u2="&#x135;" k="-20" />
    <hkern u1="T" u2="&#x131;" k="188" />
    <hkern u1="T" u2="&#x12d;" k="-14" />
    <hkern u1="T" u2="&#xef;" k="-47" />
    <hkern u1="T" u2="&#xee;" k="-23" />
    <hkern u1="T" u2="&#xed;" k="41" />
    <hkern u1="T" u2="&#xec;" k="-59" />
    <hkern u1="T" u2="&#xdf;" k="35" />
    <hkern u1="T" u2="&#x40;" k="72" />
    <hkern u1="T" u2="&#x38;" k="27" />
    <hkern u1="T" u2="&#x36;" k="25" />
    <hkern u1="T" u2="&#x34;" k="109" />
    <hkern u1="T" u2="&#x30;" k="20" />
    <hkern u1="V" g2="Jcircumflex.sc" k="-14" />
    <hkern u1="V" g2="Icircumflex.sc" k="-2" />
    <hkern u1="V" g2="M.sc" k="45" />
    <hkern u1="V" g2="at.case" k="23" />
    <hkern u1="V" u2="&#x159;" k="59" />
    <hkern u1="V" u2="&#x131;" k="74" />
    <hkern u1="V" u2="&#xef;" k="-16" />
    <hkern u1="V" u2="&#xed;" k="29" />
    <hkern u1="V" u2="&#xec;" k="-55" />
    <hkern u1="V" u2="&#xdf;" k="33" />
    <hkern u1="V" u2="&#xae;" k="27" />
    <hkern u1="V" u2="x" k="53" />
    <hkern u1="V" u2="v" k="23" />
    <hkern u1="V" u2="M" k="27" />
    <hkern u1="V" u2="&#x40;" k="37" />
    <hkern u1="V" u2="&#x39;" k="20" />
    <hkern u1="V" u2="&#x38;" k="33" />
    <hkern u1="V" u2="&#x36;" k="25" />
    <hkern u1="V" u2="&#x35;" k="23" />
    <hkern u1="V" u2="&#x34;" k="74" />
    <hkern u1="V" u2="&#x33;" k="20" />
    <hkern u1="V" u2="&#x30;" k="25" />
    <hkern u1="V" u2="&#x2f;" k="88" />
    <hkern u1="V" u2="&#x26;" k="29" />
    <hkern u1="V" u2="&#x20;" k="63" />
    <hkern u1="W" g2="Jcircumflex.sc" k="-39" />
    <hkern u1="W" g2="Idieresis.sc" k="-10" />
    <hkern u1="W" g2="Icircumflex.sc" k="-27" />
    <hkern u1="W" u2="&#x131;" k="76" />
    <hkern u1="W" u2="&#x12d;" k="-33" />
    <hkern u1="W" u2="&#x12b;" k="-12" />
    <hkern u1="W" u2="&#xef;" k="-39" />
    <hkern u1="W" u2="&#xed;" k="27" />
    <hkern u1="W" u2="&#xec;" k="-86" />
    <hkern u1="W" u2="&#xdf;" k="29" />
    <hkern u1="W" u2="&#x40;" k="33" />
    <hkern u1="W" u2="&#x38;" k="25" />
    <hkern u1="W" u2="&#x34;" k="76" />
    <hkern u1="X" u2="&#xec;" k="-39" />
    <hkern u1="X" u2="&#xae;" k="25" />
    <hkern u1="X" u2="v" k="84" />
    <hkern u1="Y" g2="adieresis.alt1" k="195" />
    <hkern u1="Y" g2="Ibreve.sc" k="25" />
    <hkern u1="Y" g2="Jcircumflex.sc" k="-37" />
    <hkern u1="Y" g2="Imacron.sc" k="-14" />
    <hkern u1="Y" g2="Itilde.sc" k="-20" />
    <hkern u1="Y" g2="Hbar.sc" k="70" />
    <hkern u1="Y" g2="Idieresis.sc" k="-33" />
    <hkern u1="Y" g2="Icircumflex.sc" k="-25" />
    <hkern u1="Y" g2="Lslash.sc" k="70" />
    <hkern u1="Y" g2="at.case" k="45" />
    <hkern u1="Y" u2="&#x161;" k="106" />
    <hkern u1="Y" u2="&#x159;" k="76" />
    <hkern u1="Y" u2="&#x141;" k="12" />
    <hkern u1="Y" u2="&#x131;" k="158" />
    <hkern u1="Y" u2="&#x12d;" k="-53" />
    <hkern u1="Y" u2="&#x12b;" k="-14" />
    <hkern u1="Y" u2="&#xef;" k="-53" />
    <hkern u1="Y" u2="&#xed;" k="63" />
    <hkern u1="Y" u2="&#xec;" k="-109" />
    <hkern u1="Y" u2="&#xdf;" k="68" />
    <hkern u1="Y" u2="&#x40;" k="96" />
    <hkern u1="Y" u2="&#x39;" k="41" />
    <hkern u1="Y" u2="&#x38;" k="61" />
    <hkern u1="Y" u2="&#x36;" k="55" />
    <hkern u1="Y" u2="&#x35;" k="37" />
    <hkern u1="Y" u2="&#x34;" k="145" />
    <hkern u1="Y" u2="&#x33;" k="35" />
    <hkern u1="Y" u2="&#x32;" k="31" />
    <hkern u1="Y" u2="&#x30;" k="53" />
    <hkern u1="Z" u2="&#x131;" k="20" />
    <hkern u1="Z" u2="&#xec;" k="-16" />
    <hkern u1="[" g2="Jcircumflex.sc" k="-133" />
    <hkern u1="[" g2="M.sc" k="35" />
    <hkern u1="[" g2="J.sc" k="-133" />
    <hkern u1="[" g2="eight.oldstyle" k="35" />
    <hkern u1="[" g2="six.oldstyle" k="33" />
    <hkern u1="[" g2="four.oldstyle" k="61" />
    <hkern u1="[" g2="two.oldstyle" k="49" />
    <hkern u1="[" g2="one.oldstyle" k="53" />
    <hkern u1="[" g2="zero.oldstyle" k="61" />
    <hkern u1="[" u2="&#x135;" k="-104" />
    <hkern u1="[" u2="&#x134;" k="-190" />
    <hkern u1="[" u2="&#xef;" k="-20" />
    <hkern u1="[" u2="&#xec;" k="-33" />
    <hkern u1="[" u2="&#x7b;" k="20" />
    <hkern u1="[" u2="x" k="35" />
    <hkern u1="[" u2="v" k="41" />
    <hkern u1="[" u2="j" k="-104" />
    <hkern u1="[" u2="f" k="25" />
    <hkern u1="[" u2="M" k="23" />
    <hkern u1="[" u2="J" k="-190" />
    <hkern u1="[" u2="&#x38;" k="35" />
    <hkern u1="[" u2="&#x36;" k="33" />
    <hkern u1="[" u2="&#x34;" k="51" />
    <hkern u1="[" u2="&#x30;" k="31" />
    <hkern u1="[" u2="&#x28;" k="25" />
    <hkern u1="\" g2="V.sc" k="61" />
    <hkern u1="\" g2="one.oldstyle" k="27" />
    <hkern u1="\" u2="&#x2019;" k="104" />
    <hkern u1="\" u2="v" k="53" />
    <hkern u1="\" u2="V" k="88" />
    <hkern u1="\" u2="&#x31;" k="27" />
    <hkern u1="c" u2="Y" k="190" />
    <hkern u1="c" u2="W" k="74" />
    <hkern u1="c" u2="V" k="74" />
    <hkern u1="c" u2="T" k="213" />
    <hkern u1="c" u2="S" k="20" />
    <hkern u1="d" u2="Z" k="12" />
    <hkern u1="f" u2="&#x135;" k="-18" />
    <hkern u1="f" u2="&#x12d;" k="-76" />
    <hkern u1="f" u2="&#x12b;" k="-68" />
    <hkern u1="f" u2="&#x129;" k="-49" />
    <hkern u1="f" u2="&#xef;" k="-109" />
    <hkern u1="f" u2="&#xec;" k="-123" />
    <hkern u1="f" u2="&#xc6;" k="57" />
    <hkern u1="f" u2="\" k="-12" />
    <hkern u1="f" u2="Y" k="-55" />
    <hkern u1="f" u2="W" k="-37" />
    <hkern u1="f" u2="V" k="-12" />
    <hkern u1="f" u2="T" k="-10" />
    <hkern u1="f" u2="&#x2f;" k="23" />
    <hkern u1="f" u2="&#x2a;" k="-20" />
    <hkern u1="f" u2="&#x20;" k="43" />
    <hkern u1="k" u2="Y" k="158" />
    <hkern u1="k" u2="W" k="63" />
    <hkern u1="k" u2="V" k="57" />
    <hkern u1="k" u2="T" k="170" />
    <hkern u1="k" u2="S" k="20" />
    <hkern u1="l" u2="&#xb7;" k="137" />
    <hkern u1="l" u2="Z" k="12" />
    <hkern u1="r" u2="&#xc6;" k="123" />
    <hkern u1="r" u2="Z" k="70" />
    <hkern u1="r" u2="Y" k="133" />
    <hkern u1="r" u2="X" k="98" />
    <hkern u1="r" u2="W" k="27" />
    <hkern u1="r" u2="V" k="27" />
    <hkern u1="r" u2="T" k="168" />
    <hkern u1="r" u2="S" k="10" />
    <hkern u1="r" u2="M" k="31" />
    <hkern u1="s" u2="Y" k="201" />
    <hkern u1="s" u2="W" k="90" />
    <hkern u1="s" u2="V" k="96" />
    <hkern u1="s" u2="T" k="172" />
    <hkern u1="t" u2="&#xc6;" k="47" />
    <hkern u1="t" u2="Z" k="43" />
    <hkern u1="t" u2="Y" k="88" />
    <hkern u1="t" u2="X" k="53" />
    <hkern u1="t" u2="W" k="23" />
    <hkern u1="t" u2="V" k="23" />
    <hkern u1="t" u2="T" k="70" />
    <hkern u1="t" u2="M" k="18" />
    <hkern u1="v" u2="&#x2122;" k="18" />
    <hkern u1="v" u2="&#xc6;" k="90" />
    <hkern u1="v" u2="&#x7d;" k="47" />
    <hkern u1="v" u2="]" k="41" />
    <hkern u1="v" u2="\" k="20" />
    <hkern u1="v" u2="Z" k="49" />
    <hkern u1="v" u2="Y" k="117" />
    <hkern u1="v" u2="X" k="84" />
    <hkern u1="v" u2="W" k="23" />
    <hkern u1="v" u2="V" k="20" />
    <hkern u1="v" u2="T" k="166" />
    <hkern u1="v" u2="M" k="23" />
    <hkern u1="v" u2="&#x2f;" k="53" />
    <hkern u1="v" u2="&#x29;" k="43" />
    <hkern u1="v" u2="&#x20;" k="51" />
    <hkern u1="w" u2="&#xc6;" k="82" />
    <hkern u1="w" u2="Z" k="47" />
    <hkern u1="w" u2="Y" k="117" />
    <hkern u1="w" u2="X" k="84" />
    <hkern u1="w" u2="W" k="25" />
    <hkern u1="w" u2="V" k="25" />
    <hkern u1="w" u2="T" k="160" />
    <hkern u1="w" u2="M" k="23" />
    <hkern u1="x" u2="&#x2122;" k="37" />
    <hkern u1="x" u2="&#x7d;" k="55" />
    <hkern u1="x" u2="]" k="35" />
    <hkern u1="x" u2="\" k="35" />
    <hkern u1="x" u2="Y" k="147" />
    <hkern u1="x" u2="W" k="53" />
    <hkern u1="x" u2="V" k="51" />
    <hkern u1="x" u2="T" k="158" />
    <hkern u1="x" u2="S" k="10" />
    <hkern u1="x" u2="&#x29;" k="49" />
    <hkern u1="y" u2="&#x142;" k="12" />
    <hkern u1="y" u2="&#xc6;" k="88" />
    <hkern u1="y" u2="Z" k="47" />
    <hkern u1="y" u2="Y" k="115" />
    <hkern u1="y" u2="X" k="84" />
    <hkern u1="y" u2="W" k="23" />
    <hkern u1="y" u2="V" k="20" />
    <hkern u1="y" u2="T" k="166" />
    <hkern u1="y" u2="M" k="23" />
    <hkern u1="z" u2="Y" k="154" />
    <hkern u1="z" u2="W" k="53" />
    <hkern u1="z" u2="V" k="51" />
    <hkern u1="z" u2="T" k="178" />
    <hkern u1="&#x7b;" g2="Jcircumflex.sc" k="-141" />
    <hkern u1="&#x7b;" g2="Hbar.sc" k="45" />
    <hkern u1="&#x7b;" g2="Icircumflex.sc" k="-4" />
    <hkern u1="&#x7b;" g2="M.sc" k="59" />
    <hkern u1="&#x7b;" g2="J.sc" k="-141" />
    <hkern u1="&#x7b;" g2="Lslash.sc" k="45" />
    <hkern u1="&#x7b;" g2="nine.oldstyle" k="23" />
    <hkern u1="&#x7b;" g2="eight.oldstyle" k="55" />
    <hkern u1="&#x7b;" g2="six.oldstyle" k="53" />
    <hkern u1="&#x7b;" g2="five.oldstyle" k="27" />
    <hkern u1="&#x7b;" g2="four.oldstyle" k="102" />
    <hkern u1="&#x7b;" g2="two.oldstyle" k="74" />
    <hkern u1="&#x7b;" g2="one.oldstyle" k="68" />
    <hkern u1="&#x7b;" g2="zero.oldstyle" k="90" />
    <hkern u1="&#x7b;" u2="&#x135;" k="-111" />
    <hkern u1="&#x7b;" u2="&#x134;" k="-195" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-27" />
    <hkern u1="&#x7b;" u2="&#xec;" k="-51" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="31" />
    <hkern u1="&#x7b;" u2="x" k="57" />
    <hkern u1="&#x7b;" u2="v" k="47" />
    <hkern u1="&#x7b;" u2="j" k="-111" />
    <hkern u1="&#x7b;" u2="f" k="33" />
    <hkern u1="&#x7b;" u2="M" k="35" />
    <hkern u1="&#x7b;" u2="J" k="-195" />
    <hkern u1="&#x7b;" u2="&#x39;" k="33" />
    <hkern u1="&#x7b;" u2="&#x38;" k="55" />
    <hkern u1="&#x7b;" u2="&#x36;" k="51" />
    <hkern u1="&#x7b;" u2="&#x35;" k="29" />
    <hkern u1="&#x7b;" u2="&#x34;" k="106" />
    <hkern u1="&#x7b;" u2="&#x33;" k="25" />
    <hkern u1="&#x7b;" u2="&#x30;" k="51" />
    <hkern u1="&#x7b;" u2="&#x28;" k="43" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="33" />
    <hkern u1="&#x7d;" u2="]" k="20" />
    <hkern u1="&#x7d;" u2="&#x29;" k="29" />
    <hkern u1="&#xa1;" g2="Jcircumflex.sc" k="-61" />
    <hkern u1="&#xa1;" g2="J.sc" k="-61" />
    <hkern u1="&#xa1;" u2="&#x135;" k="-31" />
    <hkern u1="&#xa1;" u2="&#x134;" k="-109" />
    <hkern u1="&#xa1;" u2="j" k="-31" />
    <hkern u1="&#xa1;" u2="V" k="33" />
    <hkern u1="&#xa1;" u2="J" k="-109" />
    <hkern u1="&#xa3;" g2="three.oldstyle" k="25" />
    <hkern u1="&#xae;" u2="X" k="31" />
    <hkern u1="&#xae;" u2="V" k="27" />
    <hkern u1="&#xb0;" g2="nine.oldstyle" k="61" />
    <hkern u1="&#xb0;" g2="five.oldstyle" k="43" />
    <hkern u1="&#xb0;" g2="four.oldstyle" k="203" />
    <hkern u1="&#xb0;" g2="three.oldstyle" k="55" />
    <hkern u1="&#xb0;" g2="two.oldstyle" k="20" />
    <hkern u1="&#xb0;" g2="zero.oldstyle" k="41" />
    <hkern u1="&#xb0;" u2="&#x34;" k="127" />
    <hkern u1="&#xb7;" g2="l.alt1" k="137" />
    <hkern u1="&#xb7;" g2="seven.oldstyle" k="49" />
    <hkern u1="&#xb7;" g2="four.oldstyle" k="100" />
    <hkern u1="&#xb7;" g2="three.oldstyle" k="59" />
    <hkern u1="&#xb7;" g2="two.oldstyle" k="37" />
    <hkern u1="&#xb7;" u2="l" k="137" />
    <hkern u1="&#xb7;" u2="&#x37;" k="55" />
    <hkern u1="&#xb7;" u2="&#x32;" k="43" />
    <hkern u1="&#xb7;" u2="&#x31;" k="47" />
    <hkern u1="&#xbf;" g2="Jcircumflex.sc" k="-90" />
    <hkern u1="&#xbf;" g2="V.sc" k="51" />
    <hkern u1="&#xbf;" g2="J.sc" k="-90" />
    <hkern u1="&#xbf;" u2="&#x135;" k="-49" />
    <hkern u1="&#xbf;" u2="&#x134;" k="-100" />
    <hkern u1="&#xbf;" u2="v" k="31" />
    <hkern u1="&#xbf;" u2="j" k="-49" />
    <hkern u1="&#xbf;" u2="V" k="76" />
    <hkern u1="&#xbf;" u2="J" k="-100" />
    <hkern u1="&#xce;" g2="braceright.case" k="-29" />
    <hkern u1="&#xce;" g2="bracketright.case" k="-23" />
    <hkern u1="&#xce;" g2="parenright.case" k="-27" />
    <hkern u1="&#xd0;" u2="&#x141;" k="12" />
    <hkern u1="&#xd0;" u2="&#x126;" k="12" />
    <hkern u1="&#xd2;" u2="&#x141;" k="12" />
    <hkern u1="&#xd2;" u2="&#x126;" k="12" />
    <hkern u1="&#xd3;" u2="&#x141;" k="12" />
    <hkern u1="&#xd3;" u2="&#x126;" k="12" />
    <hkern u1="&#xd4;" u2="&#x141;" k="12" />
    <hkern u1="&#xd4;" u2="&#x126;" k="12" />
    <hkern u1="&#xd5;" u2="&#x141;" k="12" />
    <hkern u1="&#xd5;" u2="&#x126;" k="12" />
    <hkern u1="&#xd6;" u2="&#x141;" k="12" />
    <hkern u1="&#xd6;" u2="&#x126;" k="12" />
    <hkern u1="&#xdd;" u2="&#x141;" k="12" />
    <hkern u1="&#xdd;" u2="&#xdf;" k="68" />
    <hkern u1="&#xde;" g2="AE.sc" k="78" />
    <hkern u1="&#xde;" g2="Y.sc" k="20" />
    <hkern u1="&#xde;" g2="X.sc" k="18" />
    <hkern u1="&#xde;" g2="V.sc" k="10" />
    <hkern u1="&#xde;" g2="A.sc" k="33" />
    <hkern u1="&#xde;" g2="braceright.case" k="94" />
    <hkern u1="&#xde;" g2="bracketright.case" k="49" />
    <hkern u1="&#xde;" g2="parenright.case" k="96" />
    <hkern u1="&#xde;" u2="&#x2122;" k="27" />
    <hkern u1="&#xde;" u2="&#x7d;" k="66" />
    <hkern u1="&#xde;" u2="]" k="39" />
    <hkern u1="&#xde;" u2="\" k="25" />
    <hkern u1="&#xde;" u2="X" k="55" />
    <hkern u1="&#xde;" u2="V" k="29" />
    <hkern u1="&#xde;" u2="&#x37;" k="23" />
    <hkern u1="&#xde;" u2="&#x2f;" k="33" />
    <hkern u1="&#xde;" u2="&#x29;" k="61" />
    <hkern u1="&#xdf;" u2="&#x2122;" k="23" />
    <hkern u1="&#xdf;" u2="&#xc6;" k="18" />
    <hkern u1="&#xdf;" u2="&#x7d;" k="47" />
    <hkern u1="&#xdf;" u2="x" k="27" />
    <hkern u1="&#xdf;" u2="v" k="27" />
    <hkern u1="&#xdf;" u2="]" k="33" />
    <hkern u1="&#xdf;" u2="\" k="29" />
    <hkern u1="&#xdf;" u2="Z" k="23" />
    <hkern u1="&#xdf;" u2="Y" k="106" />
    <hkern u1="&#xdf;" u2="X" k="35" />
    <hkern u1="&#xdf;" u2="W" k="57" />
    <hkern u1="&#xdf;" u2="V" k="61" />
    <hkern u1="&#xdf;" u2="U" k="25" />
    <hkern u1="&#xdf;" u2="T" k="63" />
    <hkern u1="&#xdf;" u2="S" k="10" />
    <hkern u1="&#xdf;" u2="M" k="12" />
    <hkern u1="&#xdf;" u2="A" k="12" />
    <hkern u1="&#xdf;" u2="&#x29;" k="43" />
    <hkern u1="&#xed;" u2="&#x2122;" k="-63" />
    <hkern u1="&#xed;" u2="&#x159;" k="-39" />
    <hkern u1="&#xed;" u2="&#x7d;" k="-47" />
    <hkern u1="&#xed;" u2="]" k="-29" />
    <hkern u1="&#xed;" u2="\" k="-55" />
    <hkern u1="&#xed;" u2="&#x29;" k="-45" />
    <hkern u1="&#xee;" u2="&#x2122;" k="-33" />
    <hkern u1="&#xee;" u2="&#x3f;" k="-41" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-96" />
    <hkern u1="&#xef;" u2="&#x2122;" k="-61" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-47" />
    <hkern u1="&#xef;" u2="]" k="-41" />
    <hkern u1="&#xef;" u2="\" k="-23" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-57" />
    <hkern u1="&#xef;" u2="&#x29;" k="-45" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="53" />
    <hkern u1="&#xf0;" u2="&#xc6;" k="27" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="84" />
    <hkern u1="&#xf0;" u2="x" k="31" />
    <hkern u1="&#xf0;" u2="v" k="23" />
    <hkern u1="&#xf0;" u2="]" k="59" />
    <hkern u1="&#xf0;" u2="\" k="78" />
    <hkern u1="&#xf0;" u2="Z" k="29" />
    <hkern u1="&#xf0;" u2="Y" k="182" />
    <hkern u1="&#xf0;" u2="X" k="41" />
    <hkern u1="&#xf0;" u2="W" k="96" />
    <hkern u1="&#xf0;" u2="V" k="94" />
    <hkern u1="&#xf0;" u2="U" k="23" />
    <hkern u1="&#xf0;" u2="T" k="139" />
    <hkern u1="&#xf0;" u2="S" k="10" />
    <hkern u1="&#xf0;" u2="M" k="14" />
    <hkern u1="&#xf0;" u2="A" k="14" />
    <hkern u1="&#xf0;" u2="&#x3f;" k="20" />
    <hkern u1="&#xf0;" u2="&#x2a;" k="41" />
    <hkern u1="&#xf0;" u2="&#x29;" k="82" />
    <hkern u1="&#x104;" g2="y.alt1" k="-98" />
    <hkern u1="&#x104;" g2="J.sc" k="-303" />
    <hkern u1="&#x104;" g2="braceright.case" k="-100" />
    <hkern u1="&#x104;" g2="bracketright.case" k="-94" />
    <hkern u1="&#x104;" g2="parenright.case" k="-98" />
    <hkern u1="&#x104;" u2="&#x134;" k="-336" />
    <hkern u1="&#x104;" u2="&#x12e;" k="-76" />
    <hkern u1="&#x104;" u2="&#x7d;" k="-72" />
    <hkern u1="&#x104;" u2="j" k="-266" />
    <hkern u1="&#x104;" u2="g" k="-16" />
    <hkern u1="&#x104;" u2="]" k="-68" />
    <hkern u1="&#x104;" u2="J" k="-336" />
    <hkern u1="&#x104;" u2="&#x3b;" k="-104" />
    <hkern u1="&#x104;" u2="&#x2c;" k="-129" />
    <hkern u1="&#x104;" u2="&#x29;" k="-72" />
    <hkern u1="&#x105;" u2="&#x7d;" k="57" />
    <hkern u1="&#x105;" u2="j" k="-147" />
    <hkern u1="&#x10e;" u2="&#x141;" k="12" />
    <hkern u1="&#x10e;" u2="&#x126;" k="12" />
    <hkern u1="&#x10f;" g2="l.alt1" k="-121" />
    <hkern u1="&#x10f;" u2="&#x2122;" k="-207" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-39" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-63" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-41" />
    <hkern u1="&#x10f;" u2="&#x2018;" k="-63" />
    <hkern u1="&#x10f;" u2="&#x17e;" k="-51" />
    <hkern u1="&#x10f;" u2="&#x133;" k="-143" />
    <hkern u1="&#x10f;" u2="&#xfe;" k="-111" />
    <hkern u1="&#x10f;" u2="&#xdf;" k="-43" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-193" />
    <hkern u1="&#x10f;" u2="&#x7c;" k="-100" />
    <hkern u1="&#x10f;" u2="t" k="-33" />
    <hkern u1="&#x10f;" u2="l" k="-111" />
    <hkern u1="&#x10f;" u2="k" k="-111" />
    <hkern u1="&#x10f;" u2="j" k="-141" />
    <hkern u1="&#x10f;" u2="i" k="-143" />
    <hkern u1="&#x10f;" u2="h" k="-111" />
    <hkern u1="&#x10f;" u2="b" k="-111" />
    <hkern u1="&#x10f;" u2="]" k="-186" />
    <hkern u1="&#x10f;" u2="\" k="-213" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-117" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-174" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-190" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-119" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-119" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-92" />
    <hkern u1="&#x110;" u2="&#x141;" k="12" />
    <hkern u1="&#x110;" u2="&#x126;" k="12" />
    <hkern u1="&#x111;" u2="&#x2122;" k="-51" />
    <hkern u1="&#x111;" u2="&#x2a;" k="-41" />
    <hkern u1="&#x118;" g2="y.alt1" k="-88" />
    <hkern u1="&#x118;" g2="J.sc" k="-297" />
    <hkern u1="&#x118;" g2="braceright.case" k="-102" />
    <hkern u1="&#x118;" g2="bracketright.case" k="-96" />
    <hkern u1="&#x118;" g2="parenright.case" k="-98" />
    <hkern u1="&#x118;" u2="&#x134;" k="-326" />
    <hkern u1="&#x118;" u2="&#x12e;" k="-78" />
    <hkern u1="&#x118;" u2="&#xfe;" k="-12" />
    <hkern u1="&#x118;" u2="&#x7d;" k="-59" />
    <hkern u1="&#x118;" u2="p" k="-10" />
    <hkern u1="&#x118;" u2="j" k="-258" />
    <hkern u1="&#x118;" u2="g" k="-14" />
    <hkern u1="&#x118;" u2="]" k="-57" />
    <hkern u1="&#x118;" u2="J" k="-326" />
    <hkern u1="&#x118;" u2="&#x3b;" k="-104" />
    <hkern u1="&#x118;" u2="&#x2c;" k="-129" />
    <hkern u1="&#x118;" u2="&#x29;" k="-61" />
    <hkern u1="&#x126;" g2="t.alt1" k="23" />
    <hkern u1="&#x126;" g2="ae.alt1" k="27" />
    <hkern u1="&#x126;" g2="a.alt1" k="27" />
    <hkern u1="&#x126;" g2="l.alt1" k="20" />
    <hkern u1="&#x126;" g2="Gdotaccent.sc" k="14" />
    <hkern u1="&#x126;" g2="OE.sc" k="14" />
    <hkern u1="&#x126;" g2="Q.sc" k="14" />
    <hkern u1="&#x126;" g2="O.sc" k="14" />
    <hkern u1="&#x126;" g2="G.sc" k="14" />
    <hkern u1="&#x126;" g2="C.sc" k="14" />
    <hkern u1="&#x126;" u2="&#x17c;" k="25" />
    <hkern u1="&#x126;" u2="&#x153;" k="35" />
    <hkern u1="&#x126;" u2="&#x152;" k="12" />
    <hkern u1="&#x126;" u2="&#x150;" k="12" />
    <hkern u1="&#x126;" u2="&#x14e;" k="12" />
    <hkern u1="&#x126;" u2="&#x14c;" k="12" />
    <hkern u1="&#x126;" u2="&#x141;" k="14" />
    <hkern u1="&#x126;" u2="&#x126;" k="14" />
    <hkern u1="&#x126;" u2="&#x122;" k="12" />
    <hkern u1="&#x126;" u2="&#x121;" k="35" />
    <hkern u1="&#x126;" u2="&#x120;" k="12" />
    <hkern u1="&#x126;" u2="&#x11e;" k="12" />
    <hkern u1="&#x126;" u2="&#x11c;" k="12" />
    <hkern u1="&#x126;" u2="&#x10c;" k="12" />
    <hkern u1="&#x126;" u2="&#x10a;" k="12" />
    <hkern u1="&#x126;" u2="&#x108;" k="12" />
    <hkern u1="&#x126;" u2="&#x106;" k="12" />
    <hkern u1="&#x126;" u2="&#xf0;" k="35" />
    <hkern u1="&#x126;" u2="&#xe6;" k="35" />
    <hkern u1="&#x126;" u2="&#xd6;" k="12" />
    <hkern u1="&#x126;" u2="&#xd5;" k="12" />
    <hkern u1="&#x126;" u2="&#xd4;" k="12" />
    <hkern u1="&#x126;" u2="&#xd3;" k="12" />
    <hkern u1="&#x126;" u2="&#xd2;" k="12" />
    <hkern u1="&#x126;" u2="&#xc7;" k="12" />
    <hkern u1="&#x126;" u2="z" k="25" />
    <hkern u1="&#x126;" u2="x" k="20" />
    <hkern u1="&#x126;" u2="u" k="18" />
    <hkern u1="&#x126;" u2="s" k="27" />
    <hkern u1="&#x126;" u2="q" k="35" />
    <hkern u1="&#x126;" u2="o" k="35" />
    <hkern u1="&#x126;" u2="g" k="35" />
    <hkern u1="&#x126;" u2="e" k="35" />
    <hkern u1="&#x126;" u2="d" k="35" />
    <hkern u1="&#x126;" u2="c" k="35" />
    <hkern u1="&#x126;" u2="a" k="35" />
    <hkern u1="&#x126;" u2="Q" k="12" />
    <hkern u1="&#x126;" u2="O" k="12" />
    <hkern u1="&#x126;" u2="G" k="12" />
    <hkern u1="&#x126;" u2="C" k="12" />
    <hkern u1="&#x129;" u2="&#x2122;" k="-33" />
    <hkern u1="&#x129;" u2="&#x7d;" k="-18" />
    <hkern u1="&#x129;" u2="]" k="-12" />
    <hkern u1="&#x129;" u2="\" k="-31" />
    <hkern u1="&#x129;" u2="&#x2a;" k="-14" />
    <hkern u1="&#x129;" u2="&#x29;" k="-16" />
    <hkern u1="&#x12b;" u2="&#x2122;" k="-18" />
    <hkern u1="&#x12b;" u2="&#x2a;" k="-18" />
    <hkern u1="&#x12d;" u2="&#x2122;" k="-29" />
    <hkern u1="&#x12d;" u2="&#x7d;" k="-14" />
    <hkern u1="&#x12d;" u2="\" k="-27" />
    <hkern u1="&#x12d;" u2="&#x2a;" k="-23" />
    <hkern u1="&#x12d;" u2="&#x29;" k="-12" />
    <hkern u1="&#x12e;" g2="J.sc" k="-143" />
    <hkern u1="&#x12e;" u2="&#x134;" k="-178" />
    <hkern u1="&#x12e;" u2="j" k="-109" />
    <hkern u1="&#x12e;" u2="J" k="-178" />
    <hkern u1="&#x12f;" u2="j" k="-150" />
    <hkern u1="&#x12f;" u2="&#x2c;" k="-12" />
    <hkern u1="&#x134;" g2="braceright.case" k="-14" />
    <hkern u1="&#x134;" g2="parenright.case" k="-12" />
    <hkern u1="&#x135;" u2="&#x2122;" k="-31" />
    <hkern u1="&#x135;" u2="&#x3f;" k="-37" />
    <hkern u1="&#x135;" u2="&#x2a;" k="-92" />
    <hkern u1="&#x13d;" u2="&#x1ef2;" k="152" />
    <hkern u1="&#x13d;" u2="&#x178;" k="152" />
    <hkern u1="&#x13d;" u2="&#x176;" k="152" />
    <hkern u1="&#x13d;" u2="&#xdd;" k="152" />
    <hkern u1="&#x13d;" u2="Y" k="152" />
    <hkern u1="&#x13e;" g2="l.alt1" k="-121" />
    <hkern u1="&#x13e;" u2="&#x2122;" k="-203" />
    <hkern u1="&#x13e;" u2="&#x201d;" k="-37" />
    <hkern u1="&#x13e;" u2="&#x201c;" k="-37" />
    <hkern u1="&#x13e;" u2="&#x2019;" k="-39" />
    <hkern u1="&#x13e;" u2="&#x2018;" k="-35" />
    <hkern u1="&#x13e;" u2="&#x17e;" k="-80" />
    <hkern u1="&#x13e;" u2="&#x161;" k="-137" />
    <hkern u1="&#x13e;" u2="&#x133;" k="-133" />
    <hkern u1="&#x13e;" u2="&#x10d;" k="-14" />
    <hkern u1="&#x13e;" u2="&#xfe;" k="-111" />
    <hkern u1="&#x13e;" u2="&#xdf;" k="-14" />
    <hkern u1="&#x13e;" u2="&#x7d;" k="-190" />
    <hkern u1="&#x13e;" u2="&#x7c;" k="-100" />
    <hkern u1="&#x13e;" u2="t" k="-20" />
    <hkern u1="&#x13e;" u2="l" k="-111" />
    <hkern u1="&#x13e;" u2="k" k="-111" />
    <hkern u1="&#x13e;" u2="j" k="-131" />
    <hkern u1="&#x13e;" u2="i" k="-133" />
    <hkern u1="&#x13e;" u2="h" k="-111" />
    <hkern u1="&#x13e;" u2="b" k="-111" />
    <hkern u1="&#x13e;" u2="]" k="-184" />
    <hkern u1="&#x13e;" u2="\" k="-211" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="-86" />
    <hkern u1="&#x13e;" u2="&#x2a;" k="-147" />
    <hkern u1="&#x13e;" u2="&#x29;" k="-188" />
    <hkern u1="&#x13e;" u2="&#x27;" k="-117" />
    <hkern u1="&#x13e;" u2="&#x22;" k="-117" />
    <hkern u1="&#x13e;" u2="&#x21;" k="-90" />
    <hkern u1="&#x14c;" u2="&#x141;" k="12" />
    <hkern u1="&#x14c;" u2="&#x126;" k="12" />
    <hkern u1="&#x14e;" u2="&#x141;" k="12" />
    <hkern u1="&#x14e;" u2="&#x126;" k="12" />
    <hkern u1="&#x150;" u2="&#x141;" k="12" />
    <hkern u1="&#x150;" u2="&#x126;" k="12" />
    <hkern u1="&#x162;" g2="Icircumflex.sc" k="-27" />
    <hkern u1="&#x162;" u2="&#xee;" k="-23" />
    <hkern u1="&#x162;" u2="&#xdf;" k="35" />
    <hkern u1="&#x164;" u2="&#xdf;" k="35" />
    <hkern u1="&#x165;" g2="l.alt1" k="-14" />
    <hkern u1="&#x165;" u2="&#x2122;" k="-94" />
    <hkern u1="&#x165;" u2="&#x161;" k="-27" />
    <hkern u1="&#x165;" u2="&#x133;" k="-25" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-82" />
    <hkern u1="&#x165;" u2="j" k="-23" />
    <hkern u1="&#x165;" u2="i" k="-25" />
    <hkern u1="&#x165;" u2="]" k="-76" />
    <hkern u1="&#x165;" u2="\" k="-102" />
    <hkern u1="&#x165;" u2="&#x2a;" k="-63" />
    <hkern u1="&#x165;" u2="&#x29;" k="-80" />
    <hkern u1="&#x166;" u2="&#xdf;" k="35" />
    <hkern u1="&#x173;" u2="&#x7d;" k="57" />
    <hkern u1="&#x173;" u2="j" k="-147" />
    <hkern u1="&#x174;" u2="&#xdf;" k="29" />
    <hkern u1="&#x176;" u2="&#x141;" k="12" />
    <hkern u1="&#x176;" u2="&#xdf;" k="68" />
    <hkern u1="&#x178;" u2="&#x141;" k="12" />
    <hkern u1="&#x178;" u2="&#xdf;" k="68" />
    <hkern u1="&#x17f;" u2="&#x159;" k="-43" />
    <hkern u1="&#x17f;" u2="&#x149;" k="-160" />
    <hkern u1="&#x17f;" u2="&#x135;" k="-84" />
    <hkern u1="&#x17f;" u2="&#x131;" k="-2" />
    <hkern u1="&#x17f;" u2="&#x12f;" k="-2" />
    <hkern u1="&#x17f;" u2="&#x12d;" k="-154" />
    <hkern u1="&#x17f;" u2="&#x12b;" k="-145" />
    <hkern u1="&#x17f;" u2="&#x129;" k="-127" />
    <hkern u1="&#x17f;" u2="&#xef;" k="-186" />
    <hkern u1="&#x17f;" u2="&#xee;" k="-57" />
    <hkern u1="&#x17f;" u2="&#xed;" k="-2" />
    <hkern u1="&#x17f;" u2="&#xec;" k="-201" />
    <hkern u1="&#x1e80;" u2="&#xdf;" k="29" />
    <hkern u1="&#x1e82;" u2="&#xdf;" k="29" />
    <hkern u1="&#x1e84;" u2="&#xdf;" k="29" />
    <hkern u1="&#x1ef2;" u2="&#x141;" k="12" />
    <hkern u1="&#x1ef2;" u2="&#xdf;" k="68" />
    <hkern u1="&#x2018;" u2="&#xef;" k="-14" />
    <hkern u1="&#x2018;" u2="&#xec;" k="-18" />
    <hkern u1="&#x2019;" u2="&#xec;" k="-33" />
    <hkern u1="&#x2019;" u2="&#x40;" k="47" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="133" />
    <hkern u1="&#x2019;" u2="&#x20;" k="41" />
    <hkern u1="&#x201c;" u2="&#xef;" k="-12" />
    <hkern u1="&#x201c;" u2="&#xec;" k="-18" />
    <hkern u1="&#x201d;" u2="&#xec;" k="-18" />
    <hkern u1="&#x2044;" g2="four.dnom" k="51" />
    <hkern u1="&#x2044;" g2="eight.oldstyle" k="-61" />
    <hkern u1="&#x2044;" g2="seven.oldstyle" k="-31" />
    <hkern u1="&#x2044;" g2="six.oldstyle" k="-72" />
    <hkern u1="&#x2044;" g2="four.oldstyle" k="117" />
    <hkern u1="&#x2044;" g2="one.oldstyle" k="-12" />
    <hkern u1="&#x2044;" u2="&#x39;" k="-106" />
    <hkern u1="&#x2044;" u2="&#x38;" k="-61" />
    <hkern u1="&#x2044;" u2="&#x37;" k="-266" />
    <hkern u1="&#x2044;" u2="&#x36;" k="-72" />
    <hkern u1="&#x2044;" u2="&#x35;" k="-137" />
    <hkern u1="&#x2044;" u2="&#x34;" k="33" />
    <hkern u1="&#x2044;" u2="&#x33;" k="-125" />
    <hkern u1="&#x2044;" u2="&#x32;" k="-127" />
    <hkern u1="&#x2044;" u2="&#x31;" k="-201" />
    <hkern u1="&#x2044;" u2="&#x30;" k="-76" />
    <hkern u1="&#x20ac;" g2="four.oldstyle" k="41" />
    <hkern u1="&#x20ac;" g2="three.oldstyle" k="41" />
    <hkern u1="&#x2122;" u2="&#xec;" k="-14" />
    <hkern u1="&#x2212;" g2="seven.oldstyle" k="25" />
    <hkern u1="&#x2212;" g2="four.oldstyle" k="94" />
    <hkern u1="&#x2212;" g2="three.oldstyle" k="41" />
    <hkern u1="&#x2212;" u2="&#x37;" k="41" />
    <hkern u1="&#x2212;" u2="&#x32;" k="23" />
    <hkern u1="&#x2212;" u2="&#x31;" k="29" />
    <hkern g1="f_f" u2="&#x149;" k="-82" />
    <hkern g1="f_f" u2="&#x135;" k="-18" />
    <hkern g1="f_f" u2="&#x12d;" k="-76" />
    <hkern g1="f_f" u2="&#x12b;" k="-68" />
    <hkern g1="f_f" u2="&#x129;" k="-49" />
    <hkern g1="f_f" u2="&#xef;" k="-109" />
    <hkern g1="f_f" u2="&#xec;" k="-123" />
    <hkern g1="fl" u2="&#x149;" k="-16" />
    <hkern g1="f_f_l" u2="&#x149;" k="-16" />
    <hkern g1="parenleft.case" u2="&#x134;" k="-41" />
    <hkern g1="parenleft.case" u2="&#xce;" k="-27" />
    <hkern g1="at.case" u2="&#xc6;" k="63" />
    <hkern g1="at.case" u2="Y" k="41" />
    <hkern g1="at.case" u2="A" k="20" />
    <hkern g1="bracketleft.case" u2="&#x134;" k="-37" />
    <hkern g1="bracketleft.case" u2="&#xce;" k="-23" />
    <hkern g1="braceleft.case" u2="&#x141;" k="29" />
    <hkern g1="braceleft.case" u2="&#x134;" k="-43" />
    <hkern g1="braceleft.case" u2="&#x126;" k="29" />
    <hkern g1="braceleft.case" u2="&#xce;" k="-29" />
    <hkern g1="guillemotright.case" u2="&#x141;" k="25" />
    <hkern g1="guillemotright.case" u2="&#x126;" k="25" />
    <hkern g1="questiondown.case" u2="V" k="27" />
    <hkern g1="guilsinglright.case" u2="&#x141;" k="25" />
    <hkern g1="guilsinglright.case" u2="&#x126;" k="25" />
    <hkern g1="zero.oldstyle" g2="four.oldstyle" k="63" />
    <hkern g1="zero.oldstyle" g2="three.oldstyle" k="20" />
    <hkern g1="zero.oldstyle" u2="&#x2044;" k="-104" />
    <hkern g1="zero.oldstyle" u2="&#xb0;" k="41" />
    <hkern g1="zero.oldstyle" u2="&#x7d;" k="90" />
    <hkern g1="zero.oldstyle" u2="]" k="61" />
    <hkern g1="zero.oldstyle" u2="\" k="82" />
    <hkern g1="zero.oldstyle" u2="&#x29;" k="86" />
    <hkern g1="one.oldstyle" u2="&#x2044;" k="-236" />
    <hkern g1="one.oldstyle" u2="&#x7d;" k="49" />
    <hkern g1="one.oldstyle" u2="]" k="35" />
    <hkern g1="one.oldstyle" u2="\" k="33" />
    <hkern g1="one.oldstyle" u2="&#x29;" k="45" />
    <hkern g1="two.oldstyle" u2="&#x2044;" k="-285" />
    <hkern g1="two.oldstyle" u2="&#x7d;" k="72" />
    <hkern g1="two.oldstyle" u2="]" k="49" />
    <hkern g1="two.oldstyle" u2="\" k="61" />
    <hkern g1="two.oldstyle" u2="&#x29;" k="66" />
    <hkern g1="three.oldstyle" u2="&#x2044;" k="-328" />
    <hkern g1="three.oldstyle" u2="&#xb0;" k="66" />
    <hkern g1="three.oldstyle" u2="&#x7d;" k="33" />
    <hkern g1="three.oldstyle" u2="]" k="23" />
    <hkern g1="three.oldstyle" u2="\" k="100" />
    <hkern g1="three.oldstyle" u2="&#x29;" k="29" />
    <hkern g1="four.oldstyle" g2="one.oldstyle" k="29" />
    <hkern g1="four.oldstyle" g2="zero.oldstyle" k="20" />
    <hkern g1="four.oldstyle" u2="&#x2212;" k="35" />
    <hkern g1="four.oldstyle" u2="&#x2044;" k="-348" />
    <hkern g1="four.oldstyle" u2="&#xb7;" k="47" />
    <hkern g1="four.oldstyle" u2="&#xb0;" k="49" />
    <hkern g1="four.oldstyle" u2="&#x7d;" k="51" />
    <hkern g1="four.oldstyle" u2="]" k="41" />
    <hkern g1="four.oldstyle" u2="\" k="102" />
    <hkern g1="four.oldstyle" u2="&#x2d;" k="37" />
    <hkern g1="four.oldstyle" u2="&#x2b;" k="39" />
    <hkern g1="four.oldstyle" u2="&#x29;" k="47" />
    <hkern g1="five.oldstyle" u2="&#x2212;" k="29" />
    <hkern g1="five.oldstyle" u2="&#x2044;" k="-328" />
    <hkern g1="five.oldstyle" u2="&#xb7;" k="39" />
    <hkern g1="five.oldstyle" u2="&#x7d;" k="31" />
    <hkern g1="five.oldstyle" u2="]" k="20" />
    <hkern g1="five.oldstyle" u2="\" k="57" />
    <hkern g1="five.oldstyle" u2="&#x2d;" k="25" />
    <hkern g1="five.oldstyle" u2="&#x2b;" k="27" />
    <hkern g1="five.oldstyle" u2="&#x29;" k="27" />
    <hkern g1="six.oldstyle" g2="four.oldstyle" k="55" />
    <hkern g1="six.oldstyle" g2="three.oldstyle" k="27" />
    <hkern g1="six.oldstyle" u2="&#x2044;" k="-121" />
    <hkern g1="six.oldstyle" u2="&#x7d;" k="20" />
    <hkern g1="seven.oldstyle" g2="four.oldstyle" k="137" />
    <hkern g1="seven.oldstyle" u2="&#xb7;" k="27" />
    <hkern g1="seven.oldstyle" u2="&#x7d;" k="39" />
    <hkern g1="seven.oldstyle" u2="]" k="31" />
    <hkern g1="seven.oldstyle" u2="&#x2f;" k="57" />
    <hkern g1="seven.oldstyle" u2="&#x2d;" k="27" />
    <hkern g1="seven.oldstyle" u2="&#x29;" k="35" />
    <hkern g1="eight.oldstyle" g2="four.oldstyle" k="51" />
    <hkern g1="eight.oldstyle" g2="three.oldstyle" k="29" />
    <hkern g1="eight.oldstyle" u2="&#x2044;" k="-125" />
    <hkern g1="eight.oldstyle" u2="&#x7d;" k="53" />
    <hkern g1="eight.oldstyle" u2="]" k="35" />
    <hkern g1="eight.oldstyle" u2="\" k="33" />
    <hkern g1="eight.oldstyle" u2="&#x29;" k="49" />
    <hkern g1="nine.oldstyle" u2="&#x2044;" k="-287" />
    <hkern g1="nine.oldstyle" u2="&#xb0;" k="66" />
    <hkern g1="nine.oldstyle" u2="&#x7d;" k="51" />
    <hkern g1="nine.oldstyle" u2="]" k="35" />
    <hkern g1="nine.oldstyle" u2="\" k="96" />
    <hkern g1="nine.oldstyle" u2="&#x29;" k="49" />
    <hkern g1="ampersand.sc" g2="V.sc" k="27" />
    <hkern g1="B.sc" g2="X.sc" k="12" />
    <hkern g1="B.sc" g2="V.sc" k="23" />
    <hkern g1="B.sc" u2="&#x2122;" k="35" />
    <hkern g1="B.sc" u2="&#x7d;" k="76" />
    <hkern g1="B.sc" u2="]" k="49" />
    <hkern g1="B.sc" u2="\" k="47" />
    <hkern g1="B.sc" u2="&#x29;" k="72" />
    <hkern g1="F.sc" g2="M.sc" k="10" />
    <hkern g1="F.sc" u2="&#x2f;" k="61" />
    <hkern g1="F.sc" u2="&#x20;" k="43" />
    <hkern g1="L.sc" u2="&#xb7;" k="389" />
    <hkern g1="M.sc" g2="V.sc" k="23" />
    <hkern g1="M.sc" u2="&#x2122;" k="27" />
    <hkern g1="M.sc" u2="&#x7d;" k="59" />
    <hkern g1="M.sc" u2="]" k="35" />
    <hkern g1="M.sc" u2="\" k="39" />
    <hkern g1="M.sc" u2="&#x29;" k="49" />
    <hkern g1="O.sc" g2="Hbar.sc" k="12" />
    <hkern g1="O.sc" g2="Lslash.sc" k="12" />
    <hkern g1="P.sc" g2="X.sc" k="23" />
    <hkern g1="P.sc" u2="&#x2122;" k="16" />
    <hkern g1="P.sc" u2="&#x7d;" k="49" />
    <hkern g1="P.sc" u2="]" k="35" />
    <hkern g1="P.sc" u2="&#x2f;" k="59" />
    <hkern g1="P.sc" u2="&#x29;" k="45" />
    <hkern g1="P.sc" u2="&#x20;" k="47" />
    <hkern g1="Q.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Q.sc" g2="Lslash.sc" k="12" />
    <hkern g1="V.sc" g2="M.sc" k="23" />
    <hkern g1="V.sc" g2="ampersand.sc" k="25" />
    <hkern g1="V.sc" u2="&#x2f;" k="61" />
    <hkern g1="V.sc" u2="&#x20;" k="55" />
    <hkern g1="Y.sc" u2="&#x40;" k="41" />
    <hkern g1="questiondown.sc" g2="Jcircumflex.sc" k="-78" />
    <hkern g1="questiondown.sc" g2="V.sc" k="23" />
    <hkern g1="questiondown.sc" g2="J.sc" k="-78" />
    <hkern g1="Icircumflex.sc" u2="&#x2122;" k="-23" />
    <hkern g1="Icircumflex.sc" u2="&#x7d;" k="-4" />
    <hkern g1="Icircumflex.sc" u2="&#x29;" k="-2" />
    <hkern g1="Idieresis.sc" u2="&#x2122;" k="-25" />
    <hkern g1="Oacute.sc" g2="Lslash.sc" k="12" />
    <hkern g1="Thorn.sc" g2="X.sc" k="43" />
    <hkern g1="Thorn.sc" g2="V.sc" k="25" />
    <hkern g1="Thorn.sc" u2="&#x2122;" k="37" />
    <hkern g1="Thorn.sc" u2="&#x7d;" k="78" />
    <hkern g1="Thorn.sc" u2="]" k="49" />
    <hkern g1="Thorn.sc" u2="\" k="57" />
    <hkern g1="Thorn.sc" u2="&#x29;" k="74" />
    <hkern g1="Aogonek.sc" g2="J.sc" k="-246" />
    <hkern g1="Aogonek.sc" u2="&#x7d;" k="10" />
    <hkern g1="Aogonek.sc" u2="]" k="8" />
    <hkern g1="Aogonek.sc" u2="&#x3b;" k="-57" />
    <hkern g1="Aogonek.sc" u2="&#x2c;" k="-84" />
    <hkern g1="Aogonek.sc" u2="&#x29;" k="4" />
    <hkern g1="Eogonek.sc" g2="J.sc" k="-242" />
    <hkern g1="Eogonek.sc" u2="&#x201e;" k="-37" />
    <hkern g1="Eogonek.sc" u2="&#x201a;" k="-37" />
    <hkern g1="Eogonek.sc" u2="]" k="-12" />
    <hkern g1="Eogonek.sc" u2="&#x3b;" k="-59" />
    <hkern g1="Eogonek.sc" u2="&#x2c;" k="-84" />
    <hkern g1="Eogonek.sc" u2="&#x29;" k="-12" />
    <hkern g1="Hbar.sc" g2="Hbar.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Gdotaccent.sc" k="12" />
    <hkern g1="Hbar.sc" g2="OE.sc" k="12" />
    <hkern g1="Hbar.sc" g2="Q.sc" k="12" />
    <hkern g1="Hbar.sc" g2="O.sc" k="12" />
    <hkern g1="Hbar.sc" g2="G.sc" k="12" />
    <hkern g1="Hbar.sc" g2="C.sc" k="12" />
    <hkern g1="Hbar.sc" u2="&#x7d;" k="45" />
    <hkern g1="Iogonek.sc" g2="J.sc" k="-113" />
    <hkern g1="Jcircumflex.sc" u2="&#x7d;" k="12" />
    <hkern g1="seven.numr" u2="&#x2044;" k="78" />
    <hkern g1="l.alt1" u2="&#xb7;" k="207" />
    <hkern g1="lslash.alt1" u2="x" k="-25" />
    <hkern g1="lcaron.alt1" g2="l.alt1" k="-37" />
    <hkern g1="lcaron.alt1" u2="&#x2122;" k="-119" />
    <hkern g1="lcaron.alt1" u2="&#x161;" k="-53" />
    <hkern g1="lcaron.alt1" u2="&#x133;" k="-47" />
    <hkern g1="lcaron.alt1" u2="&#xfe;" k="-25" />
    <hkern g1="lcaron.alt1" u2="&#x7d;" k="-106" />
    <hkern g1="lcaron.alt1" u2="&#x7c;" k="-14" />
    <hkern g1="lcaron.alt1" u2="l" k="-25" />
    <hkern g1="lcaron.alt1" u2="k" k="-25" />
    <hkern g1="lcaron.alt1" u2="j" k="-45" />
    <hkern g1="lcaron.alt1" u2="i" k="-47" />
    <hkern g1="lcaron.alt1" u2="h" k="-25" />
    <hkern g1="lcaron.alt1" u2="b" k="-25" />
    <hkern g1="lcaron.alt1" u2="]" k="-100" />
    <hkern g1="lcaron.alt1" u2="\" k="-125" />
    <hkern g1="lcaron.alt1" u2="&#x3f;" k="-16" />
    <hkern g1="lcaron.alt1" u2="&#x2a;" k="-76" />
    <hkern g1="lcaron.alt1" u2="&#x29;" k="-104" />
    <hkern g1="lcaron.alt1" u2="&#x27;" k="-31" />
    <hkern g1="lcaron.alt1" u2="&#x22;" k="-31" />
    <hkern g1="aogonek.alt1" g2="y.alt1" k="6" />
    <hkern g1="aogonek.alt1" u2="&#x7d;" k="43" />
    <hkern g1="aogonek.alt1" u2="j" k="-160" />
    <hkern g1="aogonek.alt1" u2="]" k="45" />
    <hkern g1="aogonek.alt1" u2="&#x2c;" k="-23" />
    <hkern g1="aogonek.alt1" u2="&#x29;" k="41" />
    <hkern g1="tcaron.alt1" u2="&#x2122;" k="-49" />
    <hkern g1="tcaron.alt1" u2="&#x7d;" k="-37" />
    <hkern g1="tcaron.alt1" u2="]" k="-31" />
    <hkern g1="tcaron.alt1" u2="\" k="-57" />
    <hkern g1="tcaron.alt1" u2="&#x2a;" k="-31" />
    <hkern g1="tcaron.alt1" u2="&#x29;" k="-35" />
    <hkern g1="space"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="space"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="51" />
    <hkern g1="space"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="49" />
    <hkern g1="space"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="63" />
    <hkern g1="space"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="59" />
    <hkern g1="space"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="82" />
    <hkern g1="space"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="66" />
    <hkern g1="space"
  g2="AE,AEacute"
  k="72" />
    <hkern g1="space"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="57" />
    <hkern g1="space"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="53" />
    <hkern g1="space"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="57" />
    <hkern g1="space"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="70" />
    <hkern g1="space"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="space"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="51" />
    <hkern g1="space"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="41" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="78" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="68" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="119" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="154" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="111" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="86" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="84" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="129" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="76" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="45" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="v"
  k="78" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="45" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="29" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V"
  k="104" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quotedbl,quotesingle"
  k="246" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one"
  k="45" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteright,quotedblright"
  k="266" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="quoteleft,quotedblleft"
  k="266" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="one.oldstyle"
  k="74" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="seven.oldstyle"
  k="59" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="nine.oldstyle"
  k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="25" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="16" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
  g2="V.sc"
  k="80" />
    <hkern g1="colon,semicolon"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="98" />
    <hkern g1="colon,semicolon"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="80" />
    <hkern g1="colon,semicolon"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="colon,semicolon"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="39" />
    <hkern g1="colon,semicolon"
  g2="V"
  k="27" />
    <hkern g1="quotedbl,quotesingle"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE,AEacute"
  k="152" />
    <hkern g1="quotedbl,quotesingle"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="70" />
    <hkern g1="quotedbl,quotesingle"
  g2="AE.sc,AEacute.sc"
  k="143" />
    <hkern g1="quotedbl,quotesingle"
  g2="nine.oldstyle"
  k="29" />
    <hkern g1="quotedbl,quotesingle"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="quotedbl,quotesingle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="246" />
    <hkern g1="quotedbl,quotesingle"
  g2="four"
  k="94" />
    <hkern g1="quotedbl,quotesingle"
  g2="slash"
  k="96" />
    <hkern g1="quotedbl,quotesingle"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="quotedbl,quotesingle"
  g2="three.oldstyle"
  k="23" />
    <hkern g1="quotedbl,quotesingle"
  g2="four.oldstyle"
  k="178" />
    <hkern g1="quotedbl,quotesingle"
  g2="five.oldstyle"
  k="27" />
    <hkern g1="exclamdown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="121" />
    <hkern g1="exclamdown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="96" />
    <hkern g1="exclamdown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="29" />
    <hkern g1="exclamdown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="43" />
    <hkern g1="bracketleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="bracketleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="41" />
    <hkern g1="bracketleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="43" />
    <hkern g1="bracketleft"
  g2="AE,AEacute"
  k="51" />
    <hkern g1="bracketleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="49" />
    <hkern g1="bracketleft"
  g2="AE.sc,AEacute.sc"
  k="51" />
    <hkern g1="bracketleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="29" />
    <hkern g1="bracketleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="41" />
    <hkern g1="bracketleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="55" />
    <hkern g1="bracketleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="66" />
    <hkern g1="bracketleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="55" />
    <hkern g1="bracketleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="49" />
    <hkern g1="bracketleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="49" />
    <hkern g1="bracketleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="43" />
    <hkern g1="bracketleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="37" />
    <hkern g1="bracketleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="55" />
    <hkern g1="slash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="20" />
    <hkern g1="slash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="20" />
    <hkern g1="slash"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="104" />
    <hkern g1="slash"
  g2="AE,AEacute"
  k="172" />
    <hkern g1="slash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="121" />
    <hkern g1="slash"
  g2="AE.sc,AEacute.sc"
  k="180" />
    <hkern g1="slash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="slash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="slash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="59" />
    <hkern g1="slash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="92" />
    <hkern g1="slash"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="78" />
    <hkern g1="slash"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="51" />
    <hkern g1="slash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="49" />
    <hkern g1="slash"
  g2="z,zacute,zdotaccent,zcaron"
  k="43" />
    <hkern g1="slash"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="31" />
    <hkern g1="slash"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="80" />
    <hkern g1="backslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="53" />
    <hkern g1="backslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="47" />
    <hkern g1="backslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="104" />
    <hkern g1="backslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="139" />
    <hkern g1="backslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="90" />
    <hkern g1="backslash"
  g2="AE,AEacute"
  k="-16" />
    <hkern g1="backslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="68" />
    <hkern g1="backslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="66" />
    <hkern g1="backslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="104" />
    <hkern g1="backslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="53" />
    <hkern g1="backslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="backslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="backslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="backslash"
  g2="quotedbl,quotesingle"
  k="96" />
    <hkern g1="hyphen,endash,emdash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="hyphen,endash,emdash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="29" />
    <hkern g1="hyphen,endash,emdash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="25" />
    <hkern g1="hyphen,endash,emdash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="113" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="145" />
    <hkern g1="hyphen,endash,emdash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="59" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="hyphen,endash,emdash"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="hyphen,endash,emdash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="84" />
    <hkern g1="hyphen,endash,emdash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="41" />
    <hkern g1="hyphen,endash,emdash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="115" />
    <hkern g1="hyphen,endash,emdash"
  g2="AE.sc,AEacute.sc"
  k="37" />
    <hkern g1="hyphen,endash,emdash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="29" />
    <hkern g1="hyphen,endash,emdash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="33" />
    <hkern g1="hyphen,endash,emdash"
  g2="v"
  k="31" />
    <hkern g1="hyphen,endash,emdash"
  g2="V"
  k="59" />
    <hkern g1="hyphen,endash,emdash"
  g2="quoteright,quotedblright"
  k="51" />
    <hkern g1="hyphen,endash,emdash"
  g2="V.sc"
  k="41" />
    <hkern g1="hyphen,endash,emdash"
  g2="z,zacute,zdotaccent,zcaron"
  k="37" />
    <hkern g1="hyphen,endash,emdash"
  g2="x"
  k="45" />
    <hkern g1="hyphen,endash,emdash"
  g2="X"
  k="27" />
    <hkern g1="hyphen,endash,emdash"
  g2="X.sc"
  k="39" />
    <hkern g1="hyphen,endash,emdash"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="25" />
    <hkern g1="registered"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="25" />
    <hkern g1="registered"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="41" />
    <hkern g1="registered"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="70" />
    <hkern g1="registered"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="registered"
  g2="AE,AEacute"
  k="63" />
    <hkern g1="registered"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="25" />
    <hkern g1="registered"
  g2="AE.sc,AEacute.sc"
  k="61" />
    <hkern g1="registered"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="20" />
    <hkern g1="ampersand"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="80" />
    <hkern g1="ampersand"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="90" />
    <hkern g1="ampersand"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="33" />
    <hkern g1="ampersand"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="27" />
    <hkern g1="ampersand"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="45" />
    <hkern g1="quoteright,quotedblright"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="100" />
    <hkern g1="quoteright,quotedblright"
  g2="AE,AEacute"
  k="184" />
    <hkern g1="quoteright,quotedblright"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="96" />
    <hkern g1="quoteright,quotedblright"
  g2="AE.sc,AEacute.sc"
  k="174" />
    <hkern g1="quoteright,quotedblright"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="16" />
    <hkern g1="quoteright,quotedblright"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="quoteright,quotedblright"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="76" />
    <hkern g1="quoteright,quotedblright"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="301" />
    <hkern g1="quoteright,quotedblright"
  g2="guillemotleft,guilsinglleft"
  k="72" />
    <hkern g1="quoteright,quotedblright"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="49" />
    <hkern g1="quoteright,quotedblright"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="16" />
    <hkern g1="quoteright,quotedblright"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="49" />
    <hkern g1="quoteright,quotedblright"
  g2="M"
  k="20" />
    <hkern g1="quoteright,quotedblright"
  g2="hyphen,endash,emdash"
  k="82" />
    <hkern g1="quoteright,quotedblright"
  g2="M.sc"
  k="16" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="96" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE,AEacute"
  k="182" />
    <hkern g1="quoteleft,quotedblleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="96" />
    <hkern g1="quoteleft,quotedblleft"
  g2="AE.sc,AEacute.sc"
  k="172" />
    <hkern g1="quoteleft,quotedblleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="33" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="68" />
    <hkern g1="quoteleft,quotedblleft"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="289" />
    <hkern g1="quoteleft,quotedblleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="43" />
    <hkern g1="quoteleft,quotedblleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="16" />
    <hkern g1="quoteleft,quotedblleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="43" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M"
  k="20" />
    <hkern g1="quoteleft,quotedblleft"
  g2="M.sc"
  k="18" />
    <hkern g1="trademark"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="45" />
    <hkern g1="trademark"
  g2="AE,AEacute"
  k="102" />
    <hkern g1="trademark"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="41" />
    <hkern g1="trademark"
  g2="AE.sc,AEacute.sc"
  k="96" />
    <hkern g1="braceleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="27" />
    <hkern g1="braceleft"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="braceleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="47" />
    <hkern g1="braceleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="84" />
    <hkern g1="braceleft"
  g2="AE,AEacute"
  k="100" />
    <hkern g1="braceleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="96" />
    <hkern g1="braceleft"
  g2="AE.sc,AEacute.sc"
  k="104" />
    <hkern g1="braceleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="37" />
    <hkern g1="braceleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="63" />
    <hkern g1="braceleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="84" />
    <hkern g1="braceleft"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="31" />
    <hkern g1="braceleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="98" />
    <hkern g1="braceleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="82" />
    <hkern g1="braceleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="70" />
    <hkern g1="braceleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="68" />
    <hkern g1="braceleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="63" />
    <hkern g1="braceleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="55" />
    <hkern g1="braceleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="84" />
    <hkern g1="braceleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="33" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="125" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="96" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="59" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="39" />
    <hkern g1="guillemotleft,guilsinglleft"
  g2="V"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="25" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="139" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="154" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="70" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE,AEacute"
  k="29" />
    <hkern g1="guillemotright,guilsinglright"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="92" />
    <hkern g1="guillemotright,guilsinglright"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="49" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="117" />
    <hkern g1="guillemotright,guilsinglright"
  g2="AE.sc,AEacute.sc"
  k="43" />
    <hkern g1="guillemotright,guilsinglright"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="23" />
    <hkern g1="guillemotright,guilsinglright"
  g2="v"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V"
  k="72" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quotedbl,quotesingle"
  k="31" />
    <hkern g1="guillemotright,guilsinglright"
  g2="quoteright,quotedblright"
  k="39" />
    <hkern g1="guillemotright,guilsinglright"
  g2="V.sc"
  k="49" />
    <hkern g1="guillemotright,guilsinglright"
  g2="z,zacute,zdotaccent,zcaron"
  k="27" />
    <hkern g1="guillemotright,guilsinglright"
  g2="x"
  k="39" />
    <hkern g1="guillemotright,guilsinglright"
  g2="X.sc"
  k="39" />
    <hkern g1="guillemotright,guilsinglright"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="23" />
    <hkern g1="questiondown"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="25" />
    <hkern g1="questiondown"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="29" />
    <hkern g1="questiondown"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="137" />
    <hkern g1="questiondown"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="135" />
    <hkern g1="questiondown"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="72" />
    <hkern g1="questiondown"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="55" />
    <hkern g1="questiondown"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="49" />
    <hkern g1="questiondown"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="86" />
    <hkern g1="questiondown"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="questiondown"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="29" />
    <hkern g1="questiondown"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="35" />
    <hkern g1="questiondown"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="questiondown"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="31" />
    <hkern g1="questiondown"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="41" />
    <hkern g1="questiondown"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="25" />
    <hkern g1="questiondown"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="31" />
    <hkern g1="questiondown"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="20" />
    <hkern g1="questiondown"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="31" />
    <hkern g1="asterisk"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="74" />
    <hkern g1="asterisk"
  g2="AE,AEacute"
  k="152" />
    <hkern g1="asterisk"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="74" />
    <hkern g1="asterisk"
  g2="AE.sc,AEacute.sc"
  k="145" />
    <hkern g1="asterisk"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="35" />
    <hkern g1="asterisk"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="asterisk"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="25" />
    <hkern g1="parenleft"
  g2="t,tcommaaccent,tcaron,tbar"
  k="23" />
    <hkern g1="parenleft"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="parenleft"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="parenleft"
  g2="AE,AEacute"
  k="84" />
    <hkern g1="parenleft"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="82" />
    <hkern g1="parenleft"
  g2="AE.sc,AEacute.sc"
  k="86" />
    <hkern g1="parenleft"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="33" />
    <hkern g1="parenleft"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="57" />
    <hkern g1="parenleft"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="80" />
    <hkern g1="parenleft"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="94" />
    <hkern g1="parenleft"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="78" />
    <hkern g1="parenleft"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="63" />
    <hkern g1="parenleft"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="66" />
    <hkern g1="parenleft"
  g2="z,zacute,zdotaccent,zcaron"
  k="57" />
    <hkern g1="parenleft"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="51" />
    <hkern g1="parenleft"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="80" />
    <hkern g1="parenleft"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="20" />
    <hkern g1="ampersand.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="55" />
    <hkern g1="ampersand.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="25" />
    <hkern g1="ampersand.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="78" />
    <hkern g1="questiondown.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="questiondown.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="72" />
    <hkern g1="questiondown.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="questiondown.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="72" />
    <hkern g1="questiondown.sc"
  g2="AE.sc,AEacute.sc"
  k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="41" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="AE.sc,AEacute.sc"
  k="14" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="v"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="V"
  k="23" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="x"
  k="16" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright"
  k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="parenright.case"
  k="55" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="bracketright.case"
  k="33" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  g2="braceright.case"
  k="66" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="104" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="129" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="86" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="100" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="47" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="v"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quotedbl,quotesingle"
  k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteright,quotedblright"
  k="76" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="quoteleft,quotedblleft"
  k="78" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="V.sc"
  k="49" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="10" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="braceright"
  k="86" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="space"
  k="63" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="bracketright"
  k="37" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="backslash"
  k="104" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="registered"
  k="23" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="trademark"
  k="86" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="asterisk"
  k="74" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="parenright"
  k="70" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="hyphen.case,endash.case,emdash.case"
  k="35" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="57" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="31" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="49" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="84" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE,AEacute"
  k="72" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="31" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="AE.sc,AEacute.sc"
  k="78" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V"
  k="31" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="V.sc"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="45" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="slash"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="x"
  k="27" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X"
  k="45" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="X.sc"
  k="23" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright"
  k="63" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright.case"
  k="94" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright.case"
  k="53" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="braceright.case"
  k="98" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="bracketright"
  k="39" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="backslash"
  k="25" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="trademark"
  k="20" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="parenright"
  k="57" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="39" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="35" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="39" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="29" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="v"
  k="37" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="18" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="27" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="23" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="guillemotleft.case,guilsinglleft.case"
  k="25" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="10" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="H,I,J,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Nacute,Ncommaaccent,Ncaron,Eng"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t,tcommaaccent,tcaron,tbar"
  k="35" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="123" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="98" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="147" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="168" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="117" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="109" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="104" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="174" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="121" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="82" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="43" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="v"
  k="123" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="53" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="29" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V"
  k="104" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quotedbl,quotesingle"
  k="154" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteright,quotedblright"
  k="156" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="quoteleft,quotedblleft"
  k="156" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="29" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="16" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="V.sc"
  k="90" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft,guilsinglleft"
  k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="16" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen,endash,emdash"
  k="100" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="16" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="braceright"
  k="88" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="space"
  k="59" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="bracketright"
  k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="backslash"
  k="147" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="registered"
  k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="trademark"
  k="156" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="asterisk"
  k="158" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="parenright"
  k="74" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="hyphen.case,endash.case,emdash.case"
  k="154" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
  g2="guillemotleft.case,guilsinglleft.case"
  k="117" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="braceright.case"
  k="20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="27" />
    <hkern g1="F"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="F"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="31" />
    <hkern g1="F"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="29" />
    <hkern g1="F"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="100" />
    <hkern g1="F"
  g2="AE,AEacute"
  k="172" />
    <hkern g1="F"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="102" />
    <hkern g1="F"
  g2="AE.sc,AEacute.sc"
  k="217" />
    <hkern g1="F"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="31" />
    <hkern g1="F"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="F"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="F"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="16" />
    <hkern g1="F"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="F"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="76" />
    <hkern g1="F"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="145" />
    <hkern g1="F"
  g2="guillemotleft,guilsinglleft"
  k="41" />
    <hkern g1="F"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="70" />
    <hkern g1="F"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="74" />
    <hkern g1="F"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="66" />
    <hkern g1="F"
  g2="z,zacute,zdotaccent,zcaron"
  k="61" />
    <hkern g1="F"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="23" />
    <hkern g1="F"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="90" />
    <hkern g1="F"
  g2="hyphen,endash,emdash"
  k="23" />
    <hkern g1="F"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="F"
  g2="colon,semicolon"
  k="20" />
    <hkern g1="F"
  g2="guillemotright,guilsinglright"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="39" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="76" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE,AEacute"
  k="63" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="AE.sc,AEacute.sc"
  k="70" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V"
  k="29" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="V.sc"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="slash"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="12" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="x"
  k="25" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X"
  k="39" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="X.sc"
  k="18" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="20" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright"
  k="55" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright.case"
  k="90" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright.case"
  k="51" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="braceright.case"
  k="96" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="bracketright"
  k="33" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="backslash"
  k="23" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="trademark"
  k="16" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="parenright"
  k="49" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="l,lacute,lcommaaccent,lcaron,ldot,lslash"
  k="10" />
    <hkern g1="D,Eth,Dcaron,Dcroat"
  g2="b,h,k,germandbls,thorn,hcircumflex,hbar,kcommaaccent"
  k="10" />
    <hkern g1="B"
  g2="t,tcommaaccent,tcaron,tbar"
  k="20" />
    <hkern g1="B"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="B"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="B"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="14" />
    <hkern g1="B"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="35" />
    <hkern g1="B"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="59" />
    <hkern g1="B"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="20" />
    <hkern g1="B"
  g2="AE,AEacute"
  k="18" />
    <hkern g1="B"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="B"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="10" />
    <hkern g1="B"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="10" />
    <hkern g1="B"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="B"
  g2="AE.sc,AEacute.sc"
  k="25" />
    <hkern g1="B"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="B"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="B"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="23" />
    <hkern g1="B"
  g2="z,zacute,zdotaccent,zcaron"
  k="12" />
    <hkern g1="P"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="78" />
    <hkern g1="P"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="20" />
    <hkern g1="P"
  g2="AE,AEacute"
  k="154" />
    <hkern g1="P"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="88" />
    <hkern g1="P"
  g2="AE.sc,AEacute.sc"
  k="199" />
    <hkern g1="P"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="37" />
    <hkern g1="P"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="154" />
    <hkern g1="P"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="P"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="P"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="P"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="27" />
    <hkern g1="P"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="12" />
    <hkern g1="P"
  g2="hyphen,endash,emdash"
  k="27" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="33" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="12" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="V"
  k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="43" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
  g2="braceright"
  k="23" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t,tcommaaccent,tcaron,tbar"
  k="47" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="166" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="160" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="106" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE,AEacute"
  k="164" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="131" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="AE.sc,AEacute.sc"
  k="182" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="166" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="78" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="v"
  k="166" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="49" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="117" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="47" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="V.sc"
  k="12" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="186" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="119" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="slash"
  k="104" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft,guilsinglleft"
  k="139" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="172" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="188" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="188" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="z,zacute,zdotaccent,zcaron"
  k="174" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="82" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="172" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="x"
  k="158" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="X.sc"
  k="14" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="23" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M"
  k="25" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen,endash,emdash"
  k="113" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="M.sc"
  k="53" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="space"
  k="59" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="registered"
  k="37" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="asterisk"
  k="-16" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="hyphen.case,endash.case,emdash.case"
  k="111" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotleft.case,guilsinglleft.case"
  k="94" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="colon,semicolon"
  k="98" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar"
  g2="guillemotright,guilsinglright"
  k="125" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t,tcommaaccent,tcaron,tbar"
  k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="47" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="43" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="47" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="v"
  k="45" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="29" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="31" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="hyphen.case,endash.case,emdash.case"
  k="41" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
  g2="guillemotleft.case,guilsinglleft.case"
  k="68" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="43" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="78" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE,AEacute"
  k="31" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="49" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="39" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="AE.sc,AEacute.sc"
  k="37" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V"
  k="31" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="quoteright,quotedblright"
  k="27" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="quoteleft,quotedblleft"
  k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="V.sc"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="18" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="x"
  k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X"
  k="23" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="X.sc"
  k="16" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="25" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright"
  k="51" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright.case"
  k="74" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright.case"
  k="39" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="braceright.case"
  k="80" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="bracketright"
  k="29" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="backslash"
  k="20" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent"
  g2="parenright"
  k="49" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE,AEacute"
  k="49" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="33" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="AE.sc,AEacute.sc"
  k="68" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="31" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="slash"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="12" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="20" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="z,zacute,zdotaccent,zcaron"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="x"
  k="27" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="12" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="parenright.case"
  k="23" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  g2="braceright.case"
  k="33" />
    <hkern g1="K,Kcommaaccent"
  g2="t,tcommaaccent,tcaron,tbar"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="76" />
    <hkern g1="K,Kcommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="76" />
    <hkern g1="K,Kcommaaccent"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="12" />
    <hkern g1="K,Kcommaaccent"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="16" />
    <hkern g1="K,Kcommaaccent"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="16" />
    <hkern g1="K,Kcommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="76" />
    <hkern g1="K,Kcommaaccent"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="70" />
    <hkern g1="K,Kcommaaccent"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="39" />
    <hkern g1="K,Kcommaaccent"
  g2="v"
  k="76" />
    <hkern g1="K,Kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="80" />
    <hkern g1="K,Kcommaaccent"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="78" />
    <hkern g1="K,Kcommaaccent"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="16" />
    <hkern g1="K,Kcommaaccent"
  g2="V.sc"
  k="16" />
    <hkern g1="K,Kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="39" />
    <hkern g1="K,Kcommaaccent"
  g2="slash"
  k="-31" />
    <hkern g1="K,Kcommaaccent"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="20" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="18" />
    <hkern g1="K,Kcommaaccent"
  g2="space"
  k="23" />
    <hkern g1="K,Kcommaaccent"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="33" />
    <hkern g1="K,Kcommaaccent"
  g2="registered"
  k="29" />
    <hkern g1="K,Kcommaaccent"
  g2="hyphen.case,endash.case,emdash.case"
  k="72" />
    <hkern g1="K,Kcommaaccent"
  g2="guillemotleft.case,guilsinglleft.case"
  k="96" />
    <hkern g1="M"
  g2="t,tcommaaccent,tcaron,tbar"
  k="16" />
    <hkern g1="M"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="M"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="M"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="25" />
    <hkern g1="M"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="45" />
    <hkern g1="M"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="25" />
    <hkern g1="M"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="10" />
    <hkern g1="M"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="M"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="23" />
    <hkern g1="M"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="M"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="20" />
    <hkern g1="M"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="16" />
    <hkern g1="M"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="12" />
    <hkern g1="V"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="V"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="V"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="V"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="72" />
    <hkern g1="V"
  g2="AE,AEacute"
  k="147" />
    <hkern g1="V"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="76" />
    <hkern g1="V"
  g2="AE.sc,AEacute.sc"
  k="143" />
    <hkern g1="V"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="20" />
    <hkern g1="V"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="25" />
    <hkern g1="V"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="27" />
    <hkern g1="V"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="31" />
    <hkern g1="V"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="53" />
    <hkern g1="V"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="V"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="104" />
    <hkern g1="V"
  g2="guillemotleft,guilsinglleft"
  k="72" />
    <hkern g1="V"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="96" />
    <hkern g1="V"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="74" />
    <hkern g1="V"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="76" />
    <hkern g1="V"
  g2="z,zacute,zdotaccent,zcaron"
  k="61" />
    <hkern g1="V"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="31" />
    <hkern g1="V"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="96" />
    <hkern g1="V"
  g2="hyphen,endash,emdash"
  k="59" />
    <hkern g1="V"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="10" />
    <hkern g1="V"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="18" />
    <hkern g1="V"
  g2="hyphen.case,endash.case,emdash.case"
  k="31" />
    <hkern g1="V"
  g2="guillemotleft.case,guilsinglleft.case"
  k="49" />
    <hkern g1="V"
  g2="colon,semicolon"
  k="27" />
    <hkern g1="V"
  g2="guillemotright,guilsinglright"
  k="27" />
    <hkern g1="X"
  g2="t,tcommaaccent,tcaron,tbar"
  k="35" />
    <hkern g1="X"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="84" />
    <hkern g1="X"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="82" />
    <hkern g1="X"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="84" />
    <hkern g1="X"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="66" />
    <hkern g1="X"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="49" />
    <hkern g1="X"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="45" />
    <hkern g1="X"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="51" />
    <hkern g1="X"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="18" />
    <hkern g1="X"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="43" />
    <hkern g1="X"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="10" />
    <hkern g1="X"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="37" />
    <hkern g1="X"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="X"
  g2="hyphen,endash,emdash"
  k="27" />
    <hkern g1="X"
  g2="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  k="16" />
    <hkern g1="X"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="X"
  g2="hyphen.case,endash.case,emdash.case"
  k="63" />
    <hkern g1="X"
  g2="guillemotleft.case,guilsinglleft.case"
  k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t,tcommaaccent,tcaron,tbar"
  k="68" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="117" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="129" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE,AEacute"
  k="211" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="152" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="AE.sc,AEacute.sc"
  k="225" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="100" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="v"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="84" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="137" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="43" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="219" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="154" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="slash"
  k="139" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft,guilsinglleft"
  k="154" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="201" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="158" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="164" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="z,zacute,zdotaccent,zcaron"
  k="166" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="104" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="207" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="x"
  k="150" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="X.sc"
  k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M"
  k="45" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen,endash,emdash"
  k="145" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="M.sc"
  k="98" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="53" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="space"
  k="82" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="registered"
  k="63" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="trademark"
  k="-49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="hyphen.case,endash.case,emdash.case"
  k="102" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotleft.case,guilsinglleft.case"
  k="115" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="colon,semicolon"
  k="80" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright,guilsinglright"
  k="96" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="ampersand"
  k="49" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  g2="guillemotright.case,guilsinglright.case"
  k="39" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t,tcommaaccent,tcaron,tbar"
  k="18" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="70" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE,AEacute"
  k="145" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="78" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="AE.sc,AEacute.sc"
  k="145" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="v"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="51" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="96" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="111" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="slash"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="70" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,napostrophe,eng,racute,rcommaaccent,rcaron"
  k="76" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek"
  k="78" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="z,zacute,zdotaccent,zcaron"
  k="63" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="90" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="x"
  k="55" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M"
  k="25" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen,endash,emdash"
  k="59" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="M.sc"
  k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="Lslash.sc,B.sc,D.sc,E.sc,F.sc,H.sc,I.sc,J.sc,K.sc,L.sc,N.sc,P.sc,R.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Eth.sc,Ntilde.sc,Thorn.sc,Dcaron.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,Dcroat.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Nacute.sc,Ncaron.sc,Racute.sc,Rcaron.sc,Ibreve.sc,Eng.sc,Kcommaaccent.sc,Lcommaaccent.sc,Ncommaaccent.sc,Rcommaaccent.sc,Idotaccent.sc"
  k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="space"
  k="66" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="12" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="registered"
  k="20" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="hyphen.case,endash.case,emdash.case"
  k="27" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotleft.case,guilsinglleft.case"
  k="43" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="colon,semicolon"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="guillemotright,guilsinglright"
  k="23" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  g2="ampersand"
  k="23" />
    <hkern g1="Thorn"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="35" />
    <hkern g1="Thorn"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="80" />
    <hkern g1="Thorn"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="90" />
    <hkern g1="Thorn"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="23" />
    <hkern g1="Thorn"
  g2="AE,AEacute"
  k="88" />
    <hkern g1="Thorn"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="74" />
    <hkern g1="Thorn"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="31" />
    <hkern g1="d,dcaron,dslash"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="12" />
    <hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex,fi,f_f_i,f_f_j,f_j"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="10" />
    <hkern g1="f,f_f"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="47" />
    <hkern g1="f,f_f"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="f,f_f"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="45" />
    <hkern g1="f,f_f"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="f,f_f"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="8" />
    <hkern g1="f,f_f"
  g2="hyphen,endash,emdash"
  k="45" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="186" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="219" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="AE,AEacute"
  k="25" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="v"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="V"
  k="96" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quotedbl,quotesingle"
  k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteright,quotedblright"
  k="39" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="quoteleft,quotedblleft"
  k="39" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="x"
  k="31" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="X"
  k="43" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="29" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="M"
  k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="braceright"
  k="98" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="bracketright"
  k="66" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="backslash"
  k="92" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="trademark"
  k="53" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="asterisk"
  k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="parenright"
  k="92" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,oslashacute"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="20" />
    <hkern g1="l,lacute,lcommaaccent,lcaron,lslash,fl,f_f_l"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="v"
  k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="18" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="23" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="guillemotleft,guilsinglleft"
  k="33" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="10" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="hyphen,endash,emdash"
  k="43" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="braceright"
  k="68" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="bracketright"
  k="47" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="backslash"
  k="57" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="trademark"
  k="43" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="parenright"
  k="61" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="172" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="215" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="90" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="AE,AEacute"
  k="12" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="V"
  k="96" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteright,quotedblright"
  k="29" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="quoteleft,quotedblleft"
  k="29" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="x"
  k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="X"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="18" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="braceright"
  k="90" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="bracketright"
  k="59" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="backslash"
  k="84" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="trademark"
  k="49" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="asterisk"
  k="31" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="parenright"
  k="86" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,ae.alt1,aeacute.alt1"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="k,kcommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="47" />
    <hkern g1="k,kcommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="31" />
    <hkern g1="k,kcommaaccent"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="59" />
    <hkern g1="k,kcommaaccent"
  g2="guillemotleft,guilsinglleft"
  k="41" />
    <hkern g1="k,kcommaaccent"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="23" />
    <hkern g1="k,kcommaaccent"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="18" />
    <hkern g1="k,kcommaaccent"
  g2="hyphen,endash,emdash"
  k="63" />
    <hkern g1="k,kcommaaccent"
  g2="braceright"
  k="53" />
    <hkern g1="k,kcommaaccent"
  g2="bracketright"
  k="27" />
    <hkern g1="k,kcommaaccent"
  g2="backslash"
  k="37" />
    <hkern g1="k,kcommaaccent"
  g2="trademark"
  k="47" />
    <hkern g1="k,kcommaaccent"
  g2="parenright"
  k="43" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="v"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="quoteleft,quotedblleft"
  k="16" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="braceright"
  k="84" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="bracketright"
  k="55" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="backslash"
  k="78" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="trademark"
  k="53" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="asterisk"
  k="23" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="parenright"
  k="80" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="12" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="195" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="217" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="78" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="v"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="16" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="V"
  k="76" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quotedbl,quotesingle"
  k="18" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteright,quotedblright"
  k="29" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="quoteleft,quotedblleft"
  k="29" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="braceright"
  k="90" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="bracketright"
  k="59" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="backslash"
  k="88" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="trademark"
  k="53" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="asterisk"
  k="31" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng"
  g2="parenright"
  k="84" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="14" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="guillemotleft,guilsinglleft"
  k="29" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="8" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="hyphen,endash,emdash"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="braceright"
  k="35" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="space"
  k="39" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="bracketright"
  k="27" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="parenright"
  k="29" />
    <hkern g1="t,tcommaaccent,tcaron,tbar"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="10" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="88" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="119" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="slash"
  k="74" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="guillemotleft,guilsinglleft"
  k="41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="8" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="hyphen,endash,emdash"
  k="100" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="braceright"
  k="51" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="space"
  k="49" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="bracketright"
  k="45" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="trademark"
  k="23" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="parenright"
  k="47" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="16" />
    <hkern g1="v"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="49" />
    <hkern g1="v"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="v"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="78" />
    <hkern g1="v"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="v"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="v"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="v"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="18" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="hyphen,endash,emdash"
  k="37" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="braceright"
  k="59" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="bracketright"
  k="41" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="backslash"
  k="39" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="trademark"
  k="39" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="parenright"
  k="53" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="49" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="78" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="slash"
  k="53" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="guillemotleft,guilsinglleft"
  k="31" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="braceright"
  k="43" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="space"
  k="51" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="bracketright"
  k="37" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="backslash"
  k="20" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="trademark"
  k="18" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,ygrave"
  g2="parenright"
  k="41" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="43" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="68" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="slash"
  k="45" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="12" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="hyphen,endash,emdash"
  k="25" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="braceright"
  k="49" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="space"
  k="49" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="bracketright"
  k="41" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="backslash"
  k="20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="trademark"
  k="18" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
  g2="parenright"
  k="45" />
    <hkern g1="x"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="25" />
    <hkern g1="x"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="x"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="31" />
    <hkern g1="x"
  g2="guillemotleft,guilsinglleft"
  k="39" />
    <hkern g1="x"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="8" />
    <hkern g1="x"
  g2="hyphen,endash,emdash"
  k="45" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="188" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="158" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="76" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="12" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="V"
  k="74" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="X"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="14" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="braceright"
  k="70" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="10" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="bracketright"
  k="49" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="backslash"
  k="51" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="trademark"
  k="41" />
    <hkern g1="a,g,q,u,agrave,aacute,acircumflex,atilde,adieresis,aring,ugrave,uacute,ucircumflex,udieresis,amacron,abreve,aogonek,gcircumflex,gbreve,gdotaccent,gcommaaccent,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,aringacute"
  g2="parenright"
  k="63" />
    <hkern g1="germandbls"
  g2="t,tcommaaccent,tcaron,tbar"
  k="14" />
    <hkern g1="germandbls"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="27" />
    <hkern g1="germandbls"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="23" />
    <hkern g1="germandbls"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="25" />
    <hkern g1="germandbls"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="18" />
    <hkern g1="germandbls"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="16" />
    <hkern g1="germandbls"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="10" />
    <hkern g1="germandbls"
  g2="quotedbl,quotesingle"
  k="16" />
    <hkern g1="germandbls"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="germandbls"
  g2="quoteleft,quotedblleft"
  k="16" />
    <hkern g1="germandbls"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="germandbls"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="18" />
    <hkern g1="eth"
  g2="t,tcommaaccent,tcaron,tbar"
  k="10" />
    <hkern g1="eth"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="23" />
    <hkern g1="eth"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="18" />
    <hkern g1="eth"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="23" />
    <hkern g1="eth"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="14" />
    <hkern g1="eth"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="12" />
    <hkern g1="eth"
  g2="quotedbl,quotesingle"
  k="35" />
    <hkern g1="eth"
  g2="quoteright,quotedblright"
  k="49" />
    <hkern g1="eth"
  g2="quoteleft,quotedblleft"
  k="51" />
    <hkern g1="eth"
  g2="z,zacute,zdotaccent,zcaron"
  k="10" />
    <hkern g1="eth"
  g2="B,D,E,F,H,I,J,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idot,IJ,Jcircumflex,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron"
  k="18" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="90" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="115" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quotedbl,quotesingle"
  k="70" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteright,quotedblright"
  k="72" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="quoteleft,quotedblleft"
  k="72" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="16" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="V.sc"
  k="61" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="14" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="hyphen,endash,emdash"
  k="16" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="braceright"
  k="96" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="space"
  k="57" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="bracketright"
  k="49" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="backslash"
  k="121" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="registered"
  k="25" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="trademark"
  k="92" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="asterisk"
  k="76" />
    <hkern g1="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  g2="parenright"
  k="80" />
    <hkern g1="B.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="14" />
    <hkern g1="B.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="25" />
    <hkern g1="B.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="18" />
    <hkern g1="B.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="53" />
    <hkern g1="B.sc"
  g2="AE.sc,AEacute.sc"
  k="18" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="29" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="68" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="AE.sc,AEacute.sc"
  k="55" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="V.sc"
  k="25" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="20" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="X.sc"
  k="29" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="14" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="braceright"
  k="80" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="bracketright"
  k="53" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="backslash"
  k="55" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="trademark"
  k="39" />
    <hkern g1="D.sc,Eth.sc,Dcaron.sc,Dcroat.sc"
  g2="parenright"
  k="78" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="E.sc,OE.sc,AE.sc,Egrave.sc,Eacute.sc,Ecircumflex.sc,Edieresis.sc,Emacron.sc,Ebreve.sc,Edotaccent.sc,Ecaron.sc,Eogonek.sc,AEacute.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="20" />
    <hkern g1="F.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="78" />
    <hkern g1="F.sc"
  g2="AE.sc,AEacute.sc"
  k="135" />
    <hkern g1="F.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="12" />
    <hkern g1="F.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="106" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="33" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="68" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="29" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="16" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="16" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="V.sc"
  k="25" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="X.sc"
  k="16" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="12" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="braceright"
  k="76" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="bracketright"
  k="47" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="backslash"
  k="53" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="trademark"
  k="35" />
    <hkern g1="G.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Gcommaaccent.sc"
  g2="parenright"
  k="74" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="braceright"
  k="33" />
    <hkern g1="H.sc,I.sc,J.sc,N.sc,Igrave.sc,Iacute.sc,Icircumflex.sc,Idieresis.sc,Ntilde.sc,Hcircumflex.sc,Hbar.sc,Itilde.sc,Imacron.sc,Iogonek.sc,IJ.sc,Jcircumflex.sc,Nacute.sc,Ncaron.sc,Ibreve.sc,Eng.sc,Ncommaaccent.sc,Idotaccent.sc"
  g2="parenright"
  k="20" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="70" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="35" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="31" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="39" />
    <hkern g1="K.sc,Kcommaaccent.sc"
  g2="space"
  k="20" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="94" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="96" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="147" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quotedbl,quotesingle"
  k="117" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteright,quotedblright"
  k="115" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="quoteleft,quotedblleft"
  k="115" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="39" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  k="20" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="V.sc"
  k="90" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="27" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="hyphen,endash,emdash"
  k="106" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="braceright"
  k="92" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="space"
  k="55" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="bracketright"
  k="41" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="backslash"
  k="141" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="registered"
  k="43" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="trademark"
  k="135" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="asterisk"
  k="121" />
    <hkern g1="Lslash.sc,L.sc,Lacute.sc,Lcaron.sc,Ldot.sc,Lcommaaccent.sc"
  g2="parenright"
  k="76" />
    <hkern g1="M.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="16" />
    <hkern g1="M.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="M.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="39" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="39" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="23" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="76" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="AE.sc,AEacute.sc"
  k="61" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="V.sc"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="27" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="X.sc"
  k="33" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="20" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="braceright"
  k="86" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="bracketright"
  k="55" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="backslash"
  k="59" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="trademark"
  k="41" />
    <hkern g1="O.sc,Q.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc"
  g2="parenright"
  k="82" />
    <hkern g1="P.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="66" />
    <hkern g1="P.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="18" />
    <hkern g1="P.sc"
  g2="AE.sc,AEacute.sc"
  k="127" />
    <hkern g1="P.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="119" />
    <hkern g1="P.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="10" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="8" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="12" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="33" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="V.sc"
  k="16" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="braceright"
  k="61" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="bracketright"
  k="37" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="backslash"
  k="33" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="trademark"
  k="27" />
    <hkern g1="R.sc,Racute.sc,Rcaron.sc,Rcommaaccent.sc"
  g2="parenright"
  k="53" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="12" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="14" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="37" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="AE.sc,AEacute.sc"
  k="8" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="V.sc"
  k="18" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="braceright"
  k="61" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="bracketright"
  k="41" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="backslash"
  k="37" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="trademark"
  k="35" />
    <hkern g1="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  g2="parenright"
  k="55" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="90" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="AE.sc,AEacute.sc"
  k="139" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="37" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="88" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="slash"
  k="68" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotleft,guilsinglleft"
  k="92" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="hyphen,endash,emdash"
  k="84" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="M.sc"
  k="16" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="space"
  k="53" />
    <hkern g1="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  g2="guillemotright,guilsinglright"
  k="59" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="16" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="AE.sc,AEacute.sc"
  k="41" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="18" />
    <hkern g1="U.sc,Ugrave.sc,Uacute.sc,Ucircumflex.sc,Udieresis.sc,Utilde.sc,Umacron.sc,Ubreve.sc,Uring.sc,Uhungarumlaut.sc,Uogonek.sc"
  g2="braceright"
  k="31" />
    <hkern g1="V.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="V.sc"
  g2="AE.sc,AEacute.sc"
  k="129" />
    <hkern g1="V.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="27" />
    <hkern g1="V.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="78" />
    <hkern g1="V.sc"
  g2="guillemotleft,guilsinglleft"
  k="49" />
    <hkern g1="V.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="16" />
    <hkern g1="V.sc"
  g2="hyphen,endash,emdash"
  k="39" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="61" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="AE.sc,AEacute.sc"
  k="129" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="23" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="84" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="slash"
  k="66" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="guillemotleft,guilsinglleft"
  k="49" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="12" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="hyphen,endash,emdash"
  k="41" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="M.sc"
  k="20" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="space"
  k="57" />
    <hkern g1="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  g2="ampersand.sc"
  k="20" />
    <hkern g1="X.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="31" />
    <hkern g1="X.sc"
  g2="guillemotleft,guilsinglleft"
  k="39" />
    <hkern g1="X.sc"
  g2="hyphen,endash,emdash"
  k="39" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="115" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="AE.sc,AEacute.sc"
  k="186" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="74" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="131" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="slash"
  k="104" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotleft,guilsinglleft"
  k="117" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="S.sc,Scaron.sc,Sacute.sc,Scircumflex.sc,Scedilla.sc,Scommaaccent.sc"
  k="27" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="hyphen,endash,emdash"
  k="115" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="M.sc"
  k="39" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="space"
  k="70" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="colon,semicolon"
  k="39" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="guillemotright,guilsinglright"
  k="39" />
    <hkern g1="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  g2="ampersand.sc"
  k="49" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="C.sc,G.sc,O.sc,Q.sc,OE.sc,Ccedilla.sc,Ograve.sc,Oacute.sc,Ocircumflex.sc,Otilde.sc,Odieresis.sc,Oslash.sc,Cacute.sc,Ccircumflex.sc,Cdotaccent.sc,Ccaron.sc,Gcircumflex.sc,Gbreve.sc,Gdotaccent.sc,Omacron.sc,Obreve.sc,Ohungarumlaut.sc,Oslashacute.sc,Gcommaaccent.sc"
  k="18" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="guillemotleft,guilsinglleft"
  k="23" />
    <hkern g1="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  g2="hyphen,endash,emdash"
  k="25" />
    <hkern g1="Thorn.sc"
  g2="A.sc,Agrave.sc,Aacute.sc,Acircumflex.sc,Atilde.sc,Adieresis.sc,Aring.sc,Amacron.sc,Abreve.sc,Aogonek.sc,Aringacute.sc"
  k="31" />
    <hkern g1="Thorn.sc"
  g2="T.sc,Tcaron.sc,Tbar.sc,Tcommaaccent.sc,T.sc.alt1,Tcaron.sc.alt1,Tbar.sc.alt1,Tcommaaccent.sc.alt1"
  k="49" />
    <hkern g1="Thorn.sc"
  g2="W.sc,Wcircumflex.sc,Wgrave.sc,Wacute.sc,Wdieresis.sc"
  k="20" />
    <hkern g1="Thorn.sc"
  g2="Y.sc,Ydieresis.sc,Yacute.sc,Ycircumflex.sc,Ygrave.sc"
  k="80" />
    <hkern g1="Thorn.sc"
  g2="AE.sc,AEacute.sc"
  k="78" />
    <hkern g1="Thorn.sc"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="57" />
    <hkern g1="Thorn.sc"
  g2="Zcaron.sc,Z.sc,Zacute.sc,Zdotaccent.sc"
  k="25" />
    <hkern g1="seven"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="27" />
    <hkern g1="seven"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="164" />
    <hkern g1="four"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="nine"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="20" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="35" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="111" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="102" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="27" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="AE,AEacute"
  k="70" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="V"
  k="31" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="X"
  k="63" />
    <hkern g1="hyphen.case,endash.case,emdash.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="41" />
    <hkern g1="questiondown.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="35" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="41" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
  g2="AE,AEacute"
  k="45" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute"
  k="57" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="T,Tcommaaccent,Tcaron,Tbar"
  k="94" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Y,Yacute,Ycircumflex,Ydieresis,Ygrave"
  k="115" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
  k="47" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="AE,AEacute"
  k="106" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="V"
  k="53" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="X"
  k="80" />
    <hkern g1="guillemotright.case,guilsinglright.case"
  g2="Z,Zacute,Zdotaccent,Zcaron"
  k="70" />
    <hkern g1="three.oldstyle"
  g2="quotedbl,quotesingle"
  k="33" />
    <hkern g1="four.oldstyle"
  g2="quotedbl,quotesingle"
  k="41" />
    <hkern g1="seven.oldstyle"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="102" />
    <hkern g1="nine.oldstyle"
  g2="quotedbl,quotesingle"
  k="35" />
    <hkern g1="longs"
  g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,jcircumflex"
  k="-25" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="10" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="16" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="v"
  k="16" />
    <hkern g1="l.alt1,lslash.alt1,lacute.alt1,lcommaaccent.alt1,lcaron.alt1"
  g2="space"
  k="23" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y,yacute,ydieresis,ycircumflex,ygrave"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="w,wcircumflex,wgrave,wacute,wdieresis"
  k="14" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  k="8" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="f,longs,f_f,fi,fl,f_f_i,f_f_l,f_f_j,f_j"
  k="8" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="v"
  k="18" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quotedbl,quotesingle"
  k="20" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteright,quotedblright"
  k="31" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="quoteleft,quotedblleft"
  k="31" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="braceright"
  k="90" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="bracketright"
  k="59" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="backslash"
  k="90" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="trademark"
  k="53" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="asterisk"
  k="31" />
    <hkern g1="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,aringacute.alt1"
  g2="parenright"
  k="84" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,amacron,abreve,aogonek,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dslash,emacron,ebreve,edotaccent,eogonek,ecaron,gcircumflex,gbreve,gdotaccent,gcommaaccent,omacron,obreve,ohungarumlaut,oe,aringacute,aeacute,oslashacute"
  k="23" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
  k="78" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="slash"
  k="53" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="guillemotleft,guilsinglleft"
  k="29" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
  k="14" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="a.alt1,aacute.alt1,agrave.alt1,acircumflex.alt1,adieresis.alt1,atilde.alt1,aring.alt1,amacron.alt1,abreve.alt1,aogonek.alt1,ae.alt1,aeacute.alt1,aringacute.alt1"
  k="16" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="hyphen,endash,emdash"
  k="29" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="braceright"
  k="45" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="space"
  k="51" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="bracketright"
  k="37" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="backslash"
  k="20" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="trademark"
  k="18" />
    <hkern g1="y.alt1,yacute.alt1,ydieresis.alt1,ycircumflex.alt1"
  g2="parenright"
  k="41" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="braceright"
  k="49" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="bracketright"
  k="31" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="backslash"
  k="31" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="trademark"
  k="27" />
    <hkern g1="t.alt1,tcaron.alt1,tbar.alt1,tcommaaccent.alt1"
  g2="parenright"
  k="45" />
    <hkern g1="parenleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="94" />
    <hkern g1="parenleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="25" />
    <hkern g1="parenleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="57" />
    <hkern g1="bracketleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="53" />
    <hkern g1="bracketleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="20" />
    <hkern g1="bracketleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="39" />
    <hkern g1="braceleft.case"
  g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute"
  k="98" />
    <hkern g1="braceleft.case"
  g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek"
  k="33" />
    <hkern g1="braceleft.case"
  g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
  k="68" />
  </font>
</defs></svg>
