// transparent background and border properties included for button version.
// iOS requires the button element instead of an anchor tag.
// If you want the anchor version, it requires `href="#"`.
// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile

.btn-close,
.btn-close-lg,
.btn-close-xs {
  padding: 0;
  border: 0; // for button elements
  box-sizing: content-box;
  color: $btn-close-color;
  @include border-radius();
  opacity: $btn-close-opacity;

  // Override <a>'s hover style
  &:hover {
    color: $btn-close-color;
    text-decoration: none;
    opacity: $btn-close-hover-opacity;
  }

  &:focus {
    outline: none;
    box-shadow: $btn-close-focus-shadow;
    opacity: $btn-close-focus-opacity;
  }

  &:disabled,
  &.disabled {
    pointer-events: none;
    user-select: none;
    opacity: $btn-close-disabled-opacity;
  }
}

.btn-close {
  width: $btn-close-width;
  height: $btn-close-height;
  padding: $btn-close-padding-y $btn-close-padding-x;
  background: transparent escape-svg($btn-close-bg) center / $btn-close-width auto no-repeat; // include transparent for button elements
}

.btn-close-lg {
  width: $btn-close-lg-width;
  height: $btn-close-lg-height;
  background: transparent escape-svg($btn-close-lg-bg) center / $btn-close-lg-width auto no-repeat; // include transparent for button elements
}

.btn-close-xs {
  width: $btn-close-xs-width;
  height: $btn-close-xs-height;
  padding: 0;
  background: transparent escape-svg($btn-close-xs-bg) center / $btn-close-xs-width auto no-repeat; // include transparent for button elements
}

.btn-close-white {
  filter: $btn-close-white-filter;
}
