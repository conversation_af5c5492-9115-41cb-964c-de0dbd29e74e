// Base class
//
// Kickstart any navigation component with a set of style resets. Works with
// `<nav>`s, `<ul>`s or `<ol>`s.

.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: $nav-link-padding-top $nav-link-padding-x $nav-link-padding-bottom;
  color: $nav-link-color;
  font-weight: $nav-link-font-weight;
  line-height: $nav-link-line-height;
  text-decoration: if($link-decoration == none, null, none);
  outline: none;
  @include transition($nav-link-transition);

  &:hover,
  &:focus {
    color: $nav-link-hover-color;
    text-decoration: if($link-hover-decoration == underline, none, null);
  }

  // Disabled state lightens text
  &.disabled {
    color: $nav-link-disabled-color;
    pointer-events: none;
    cursor: default;
  }

  @include media-breakpoint-up(lg) {
    @include font-size($nav-link-font-size);
  }
}

.nav-link_underscore {
  position: relative;
  padding: $nav-link-padding-top $nav-tabs-link-padding-x
    $nav-link-padding-bottom;
  color: $nav-tabs-link-color;

  &:after {
    content: "";
    display: block;
    position: absolute;
    bottom: 0;
    left: $nav-tabs-link-padding-x;
    width: 0;
    height: $nav-tabs-border-width;
    background-color: $nav-tabs-link-active-border;
    @include transition($nav-link-underline-transition-l);
  }

  &.theme-color {
    &:after {
      background-color: var(--theme-color);
    }
  }

  &:hover,
  &:focus,
  &.active,
  .nav-item.show & {
    color: $nav-tabs-link-active-color;

    &:after {
      width: subtract(100%, $nav-tabs-link-padding-x * 2);
    }
  }

  &.underscore-sm {
    &:hover,
    &:focus,
    &.active,
    .nav-item.show & {
      &:after {
        width: 2rem;
      }
    }
  }

  &.underscore-md {
    &:hover,
    &:focus,
    &.active,
    .nav-item.show & {
      &:after {
        width: calc((100% - #{$nav-tabs-link-padding-x} * 2) * 0.7);
      }
    }
  }

  &.disabled {
    color: $nav-link-disabled-color;
    background-color: transparent;
    border-color: transparent;
  }
}

// Right lined links
.nav-link_rline {
  &:before {
    content: "";
    position: absolute;
    top: 49%;
    left: 0;
    width: 0px;
    height: 2px;
    background-color: $body-color;
    @include transition($nav-link-underline-transition-l);
  }

  &.active {
    &:before {
      width: 100%;
    }
  }
}

.rline-content {
  display: inline-block;
  position: relative;
  padding-right: 1.5em;
  background-color: $menu-bg;

  @include media-breakpoint-up(md) {
    padding-right: 2.5em;
  }

  @include media-breakpoint-up(xl) {
    padding-right: 3.5em;
  }
}

//
// Pills
//

.nav-pills {
  .nav-link {
    @include border-radius($nav-pills-border-radius);
  }

  .nav-link.active,
  .show > .nav-link {
    color: $nav-pills-link-active-color;
    @include gradient-bg($nav-pills-link-active-bg);
  }
}

//
// Justified variants
//

.nav-fill {
  > .nav-link,
  .nav-item {
    flex: 1 1 auto;
    text-align: center;
  }
}

.nav-justified {
  > .nav-link,
  .nav-item {
    flex-basis: 0;
    flex-grow: 1;
    text-align: center;
  }
}

// Tabbable tabs
//
// Hide tabbable panes to start, show them when `.active`

.tab-content {
  > .tab-pane {
    display: none;
  }
  > .active {
    display: block;
  }
}
