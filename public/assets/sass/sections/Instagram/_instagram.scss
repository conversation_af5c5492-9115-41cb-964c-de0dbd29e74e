$items-space-mobile: 7px;
$items-space-desktop: 3px;

.instagram__img {
  width: 100%;
  max-width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-wrapper {
  width: 100%; /* fixed width */
  height: 320px; /* force same height */
  overflow: hidden;

  @include media-breakpoint-down(md) {
    height: 270px;
  }

  @include media-breakpoint-down(sm) {
    height: 200px;
  }
}

.instagram {
  .row {
    margin: 0 -$items-space-mobile;

    @include media-breakpoint-up(md) {
      margin: 0 -$items-space-desktop;
    }
  }
}

.instagram__tile {
  padding: $items-space-mobile;

  @include media-breakpoint-up(md) {
    padding: $items-space-desktop;
  }
}

.instagram__tile {
  position: relative;

  .instagram__overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    z-index: 1;
  }

  &:hover .instagram__overlay {
    opacity: 1;
  }

  .icon_instagram {
    width: 50px;
    height: 50px;
    fill: #fff; // Adjust the icon color as needed
  }
}
