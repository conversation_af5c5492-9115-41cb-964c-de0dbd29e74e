import Footer1 from '@/components/footers/Footer1'
import Header1 from '@/components/headers/Header1'
import VariantProduct from '@/components/product/VariantProduct'
import RelatedSlider from '@/components/singleProduct/RelatedSlider'
import React from 'react'
import NotFound from '../../../not-found'
import initTranslations from '@/app/i18n'
import { generateMedataProduct } from '@/utils/metadata.util'

const i18nNamespaces = ['default']

const fetchProductById = async id => {
  try {
    const result = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/products/${id}`, {
      next: {
        revalidate: 300, // Cache for 5 minutes
        tags: [`product-${id}`] // Tag for cache invalidation
      }
    })
    if (!result.ok) {
      return null
    }

    return await result.json()
  } catch (error) {
    console.error('Error fetching product:', error)
    return null
  }
}

export async function generateMetadata({ params }) {
  const { id, locale } = params
  const { t } = await initTranslations(locale, i18nNamespaces)
  const product = await fetchProductById(id)

  return generateMedataProduct({ product, t, locale })
}

export default async function ProductDetailPage({ params }) {
  const { id: productId, locale } = params
  const { t } = await initTranslations(locale, i18nNamespaces)
  const product = await fetchProductById(productId)

  // Handle product not found
  if (!product) {
    return <NotFound />
  }

  return (
    <>
      <Header1 />
      <main className='page-wrapper'>
        <div className='mb-md-1 pb-md-3'></div>
        <VariantProduct product={product} />
        <RelatedSlider />
      </main>
      <Footer1 />
    </>
  )
}
