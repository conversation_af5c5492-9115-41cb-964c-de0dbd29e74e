import Footer1 from '@/components/footers/Footer1'
import Header1 from '@/components/headers/Header1'
import { generateBlogMetadata } from '@/utils/metadata.util'
import { notFound } from 'next/navigation'

// Helper function to fetch blog by slug
const getBlogBySlug = async (slug) => {
  try {
    const result = await fetch(
      `${process.env.NEXT_PUBLIC_APP_API_URL}/blogs/${slug}`,
      { cache: 'no-store' }
    )
    
    if (!result.ok) {
      return null
    }
    
    const blog = await result.json()
    return blog
  } catch (error) {
    console.log(error)
    return null
  }
}

// Dynamic metadata function
export async function generateMetadata({ params }) {
  const blog = await getBlogBySlug(params.slug)
  
  if (!blog) {
    return {
      title: 'Bài viết không tồn tại || Shapewear by W.Store',
      description: 'Bài viết bạn tìm kiếm không tồn tại hoặc đã bị xóa.'
    }
  }

  const defaultTitle = `${blog.title} || Shapewear by W.Store`
  const defaultDescription = blog.shortDescription || 'Đọc bài viết mới nhất từ Shapewear by W.Store'

  return generateBlogMetadata(blog, params, defaultTitle, defaultDescription)
}

export default async function BlogDetailPage({ params }) {
  const blog = await getBlogBySlug(params.slug)

  if (!blog) {
    notFound()
  }

  return (
    <>
      <Header1 />
      <main className='page-wrapper'>
        <div className='mb-4 pb-4'></div>
        
        <div className="container">
          <article className="blog-detail">
            <header className="blog-header mb-5">
              <h1 className="blog-title">{blog.title}</h1>
              {blog.publishedAt && (
                <time className="blog-date text-muted">
                  {new Date(blog.publishedAt).toLocaleDateString('vi-VN')}
                </time>
              )}
              {blog.image && (
                <div className="blog-image mt-4">
                  <img 
                    src={blog.image} 
                    alt={blog.title}
                    className="img-fluid rounded"
                  />
                </div>
              )}
            </header>
            
            <div 
              className="blog-content"
              dangerouslySetInnerHTML={{ __html: blog.description }}
            />
            
            {blog.tags && blog.tags.length > 0 && (
              <footer className="blog-footer mt-5">
                <div className="blog-tags">
                  <strong>Tags: </strong>
                  {blog.tags.map((tag, index) => (
                    <span key={index} className="badge bg-secondary me-2">
                      {tag}
                    </span>
                  ))}
                </div>
              </footer>
            )}
          </article>
        </div>
      </main>
      
      <div className='mb-5 pb-xl-5'></div>
      <Footer1 />
    </>
  )
}
