import Footer1 from '@/components/footers/Footer1'

import Header1 from '@/components/headers/Header1'
import About from '@/components/otherPages/about/About'
import Clients from '@/components/otherPages/about/Clients'
import Services from '@/components/otherPages/about/Services'
import React from 'react'

export const metadata = {
  title: 'Về chúng tôi || Shapewear by W.Store',
  description:
    'Với sứ mệnh mang lại sự tự tin cho phái nữ, Shapewear by W.Store tự hào là một trong những brand shapewear đầu tiên tại thị trường Việt Nam cho ra đời các sản phẩm vừa là nội y, nhưng vừa được xem như đồ thời trang, giúp tôn đường cong sẵn có và khéo léo che khuyết điểm tối đa.'
}

const getBlog = async type => {
  try {
    const result = await fetch(
      `${process.env.NEXT_PUBLIC_APP_API_URL}/blogs?page=1&limit=10&order=desc&type=${type}`
    )
    const response = await result.json()
    return response[0]
  } catch (error) {
    console.log(error)
  }
}

export default async function AboutPage() {
  const blog = await getBlog('aboutMe')

  return (
    <>
      <Header1 />
      <main className='page-wrapper'>
        <div className='mb-4 pb-4'></div>
        <About blog={blog} />
        <Services />
        {/* <Clients /> */}
      </main>
      <div className='mb-5 pb-xl-5'></div>
      <Footer1 />
    </>
  )
}
