import Footer1 from '@/components/footers/Footer1'
import Header1 from '@/components/headers/Header1'
import About from '@/components/otherPages/about/About'
import Clients from '@/components/otherPages/about/Clients'
import Services from '@/components/otherPages/about/Services'
import { generateBlogMetadata } from '@/utils/metadata.util'
import { BlogStructuredData, OrganizationStructuredData } from '@/components/seo/StructuredData'
import React from 'react'

// Helper function to fetch blog data
const getBlog = async type => {
  try {
    const result = await fetch(
      `/api/blogs?page=1&limit=1&order=desc&type=${type}`,
      { cache: 'no-store' } // Ensure fresh data for metadata
    )
    const response = await result.json()
    return response[0]
  } catch (error) {
    console.log(error)
    return null
  }
}

// Dynamic metadata function
export async function generateMetadata({ params }) {
  const blog = await getBlog('aboutMe')

  const defaultTitle = 'Về chúng tôi || Shapewear by W.Store'
  const defaultDescription =
    '<PERSON><PERSON><PERSON> sứ mệnh mang lại sự tự tin cho phái nữ, Shapewear by W.Store tự hào là một trong những brand shapewear đầu tiên tại thị trường Việt Nam cho ra đời các sản phẩm vừa là nội y, nhưng vừa được xem như đồ thời trang, giúp tôn đường cong sẵn có và khéo léo che khuyết điểm tối đa.'

  return generateBlogMetadata(blog, params, defaultTitle, defaultDescription)
}

export default async function AboutPage({ params }) {
  const blog = await getBlog('aboutMe')

  return (
    <>
      {/* SEO Structured Data */}
      <BlogStructuredData blog={blog} locale={params.locale} />
      <OrganizationStructuredData locale={params.locale} />

      <Header1 />
      <main className='page-wrapper'>
        <div className='mb-4 pb-4'></div>
        <About blog={blog} />
        <Services />
        {/* <Clients /> */}
      </main>
      <div className='mb-5 pb-xl-5'></div>
      <Footer1 />
    </>
  )
}
