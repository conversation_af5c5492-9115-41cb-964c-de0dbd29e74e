'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  const getErrorMessage = (error) => {
    switch (error) {
      case 'Configuration':
        return 'Có lỗi trong cấu hình server.'
      case 'AccessDenied':
        return 'Truy cập bị từ chối.'
      case 'Verification':
        return 'Token đã hết hạn hoặc đã được sử dụng.'
      default:
        return 'Có lỗi xảy ra trong quá trình đăng nhập.'
    }
  }

  return (
    <div className="container d-flex justify-content-center align-items-center" style={{ minHeight: '100vh' }}>
      <div className="text-center">
        <h2 className="text-danger mb-4">Lỗi đăng nhập</h2>
        <p className="mb-4">{getErrorMessage(error)}</p>
        <Link href="/" className="btn btn-primary">
          Về trang chủ
        </Link>
      </div>
    </div>
  )
}
