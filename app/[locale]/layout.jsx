import ClientLayout from '@/components/layouts/ClientLayout'
import '../../public/assets/css/plugins/swiper.min.css'
import '../../public/assets/sass/style.scss'
import 'rc-slider/assets/index.css'
import 'tippy.js/dist/tippy.css'
import 'react-tooltip/dist/react-tooltip.css'
import '../i18n'
import TranslationsProvider from '@/components/i18n/TranslationsProvider'
import initTranslations from '../i18n'
import { Suspense } from 'react'
import Loading from './loading'
import { GoogleAnalytics } from '@next/third-parties/google'
import { AuthProvider } from '@/context/AuthContext'

const i18nNamespaces = ['default']

import { cookies } from 'next/headers'

export const ALL_LANGUAGES = ['en', 'vi']
const LANGUAGE_COOKIE = 'NEXT_LOCALE'

export async function getLanguage() {
  const cookieStore = await cookies()
  const locale = cookieStore.get(LANGUAGE_COOKIE)?.value
  if (locale && ALL_LANGUAGES.includes(locale)) {
    return locale
  }
}

export default async function RootLayout({ children }) {
  const locale = await getLanguage()
  const { t, resources } = await initTranslations(locale, i18nNamespaces)

  const activeGGA4Production = () => {
    return process.env.NODE_ENV === 'production' ? (
      <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID} />
    ) : (
      <></>
    )
  }

  return (
    <html lang={locale}>
      <head>
        <link
          rel='stylesheet'
          href='https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap'
        />
      </head>
      <body style={{ fontFamily: "'Roboto', sans-serif" }}>
        <Suspense fallback={<Loading />}>
          <AuthProvider>
            <TranslationsProvider namespaces={i18nNamespaces} locale={locale} resources={resources}>
              <ClientLayout>{children}</ClientLayout>
            </TranslationsProvider>
          </AuthProvider>
        </Suspense>
      </body>
      {activeGGA4Production()}
    </html>
  )
}
