'use client'

import { createContext, useContext } from 'react'
import { SessionProvider, useSession, signIn, signOut } from 'next-auth/react'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

function AuthContextProvider({ children }) {
  const { data: session, status } = useSession()

  const loginWithGoogle = async () => {
    try {
      const result = await signIn('google', {
        callbackUrl: '/',
        redirect: false
      })
      
      if (result?.error) {
        console.error('Google login error:', result.error)
        throw new Error(result.error)
      }
      
      return result
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await signOut({
        callbackUrl: '/',
        redirect: false
      })
    } catch (error) {
      console.error('Logout failed:', error)
      throw error
    }
  }

  const value = {
    user: session?.user || null,
    session,
    status,
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    loginWithGoogle,
    logout
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function AuthProvider({ children }) {
  return (
    <SessionProvider>
      <AuthContextProvider>
        {children}
      </AuthContextProvider>
    </SessionProvider>
  )
}
