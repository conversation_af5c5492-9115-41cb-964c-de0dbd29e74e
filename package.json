{"name": "wstore-nextjs", "version": "1.2.0", "private": true, "scripts": {"dev": "next dev --port 4000", "start:dev": "next dev --port 4000", "build": "next build", "start": "next start", "lint": "next lint", "i18n": "i18nexus pull"}, "dependencies": {"@next/third-parties": "^15.1.8", "@popperjs/core": "2.11.8", "@react-google-maps/api": "^2.19.3", "@sentry/nextjs": "^9.22.0", "bootstrap": "^5.0.2", "classnames": "^2.5.1", "i18next": "^23.15.1", "i18next-resources-to-backend": "^1.2.1", "next": "14.2.3", "next-i18n-router": "^5.5.1", "next-i18next": "11.0.0", "photoswipe": "^5.4.4", "rc-slider": "^10.6.2", "react": "^18", "react-countdown": "^2.3.5", "react-css-spinners": "^4.0.2", "react-dom": "^18", "react-i18next": "^15.0.2", "react-photoswipe-gallery": "^3.0.1", "react-tooltip": "^5.26.4", "sass": "^1.75.0", "swiper": "^11.1.1", "tippy.js": "^6.3.7"}}