export const defaultKeywords = ['shapewear', 'W.Store', 'shapewear by W.Store']
export const defaultSiteName = 'Shapewear by W.Store'
export const defaultMainBrand = 'W.Store'
export const defaultCurrency = 'VND'
export const defaultCategories = 'Shapewear'

export const generateMedataProduct = ({ product, t, locale }) => {
  if (!product) {
    return {
      title: t('product.notFound', 'Không tìm thấy') + ' || ' + defaultSiteName,
      description: t('product.notFoundDesc', 'Không tìm thấy trang bạn yêu cầu.'),
      keywords: defaultKeywords.join(', ')
    }
  }

  // Basic metadata
  const basicMetadata = {
    title: `${product.productName} || ${defaultSiteName}`,
    description:
      product.shortDescription ||
      t(
        'keyword.metadata.shortDescription',
        'Mua ngay {{productName}} - <PERSON><PERSON> định hình cao cấp từ W.Store.',
        {
          productName: product.productName
        }
      ),
    keywords: [
      product.productName,
      ...defaultKeywords,
      ...(product.tags || []),
      ...(product.categories || [])
    ].join(', ')
    // Open Graph metadata for social sharing
  }

  // Facebook, LinkedIn, Pinterest
  // Open Graph metadata for social sharing
  const openGraph = {
    title: `${product.productName} - ${defaultSiteName}`,
    description:
      product.shortDescription ||
      t('product.highQuality', 'High quality {{productName}} available at W.Store', {
        productName: product.productName
      }),
    images:
      product.images?.map(img => ({
        url: img.url || img,
        width: 800,
        height: 600,
        alt: product.productName
      })) || [],
    type: 'website',
    defaultSiteName: defaultSiteName,
    locale: locale === 'vi' ? 'vi_VN' : 'en_US'
  }

  // Twitter Card metadata
  const twitter = {
    card: 'summary_large_image',
    title: `${product.productName} - ${defaultSiteName}`,
    description: product.shortDescription,
    images: product.images?.[0]?.url || ''
  }

  // Additional metadata for e-commerce
  const other = {
    'product:price:amount': product.sellingPrice || 0,
    'product:price:currency': locale === 'vi' ? defaultCurrency : 'USD',
    'product:availability': t('product.inStock', 'in stock'),
    'product:brand': defaultMainBrand,
    'product:category': product.categories?.[0] || defaultCategories
  }

  return {
    title: basicMetadata.title,
    description: basicMetadata.description,
    keywords: basicMetadata.keywords,

    openGraph: {
      title: openGraph.title,
      description: openGraph.description,
      images: openGraph.images,
      type: openGraph.type,
      defaultSiteName: openGraph.defaultSiteName,
      locale: openGraph.locale
    },

    twitter: {
      card: twitter.card,
      title: twitter.title,
      description: twitter.shortDescription,
      images: twitter.images
    },

    // Additional metadata for e-commerce
    other: {
      'product:price:amount': other.price,
      'product:price:currency': other.currency,
      'product:availability': other.availability,
      'product:brand': other.brand,
      'product:category': other.category
    }
  }
}
