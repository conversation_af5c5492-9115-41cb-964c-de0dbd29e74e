export const defaultKeywords = ['shapewear', 'W.Store', 'shapewear by W.Store']
export const defaultSiteName = 'Shapewear by W.Store'
export const defaultMainBrand = 'W.Store'
export const defaultCurrency = 'VND'
export const defaultCategories = 'Shapewear'

export const generateMedataProduct = ({ product, t, locale }) => {
  if (!product) {
    return {
      title: t('product.notFound', 'Không tìm thấy') + ' || ' + defaultSiteName,
      description: t('product.notFoundDesc', 'Không tìm thấy trang bạn yêu cầu.'),
      keywords: defaultKeywords.join(', ')
    }
  }

  // Basic metadata
  const basicMetadata = {
    title: `${product.productName} || ${defaultSiteName}`,
    description:
      product.shortDescription ||
      t(
        'keyword.metadata.shortDescription',
        'Mua ngay {{productName}} - <PERSON><PERSON> định hình cao cấp từ W.Store.',
        {
          productName: product.productName
        }
      ),
    keywords: [
      product.productName,
      ...defaultKeywords,
      ...(product.tags || []),
      ...(product.categories || [])
    ].join(', ')
    // Open Graph metadata for social sharing
  }

  // Facebook, LinkedIn, Pinterest
  // Open Graph metadata for social sharing
  const openGraph = {
    title: `${product.productName} - ${defaultSiteName}`,
    description:
      product.shortDescription ||
      t('product.highQuality', 'High quality {{productName}} available at W.Store', {
        productName: product.productName
      }),
    images:
      product.images?.map(img => ({
        url: img.url || img,
        width: 800,
        height: 600,
        alt: product.productName
      })) || [],
    type: 'website',
    defaultSiteName: defaultSiteName,
    locale: locale === 'vi' ? 'vi_VN' : 'en_US'
  }

  // Twitter Card metadata
  const twitter = {
    card: 'summary_large_image',
    title: `${product.productName} - ${defaultSiteName}`,
    description: product.shortDescription,
    images: product.images?.[0]?.url || ''
  }

  // Additional metadata for e-commerce
  const other = {
    'product:price:amount': product.sellingPrice || 0,
    'product:price:currency': locale === 'vi' ? defaultCurrency : 'USD',
    'product:availability': t('product.inStock', 'in stock'),
    'product:brand': defaultMainBrand,
    'product:category': product.categories?.[0] || defaultCategories
  }

  return {
    title: basicMetadata.title,
    description: basicMetadata.description,
    keywords: basicMetadata.keywords,

    openGraph: {
      title: openGraph.title,
      description: openGraph.description,
      images: openGraph.images,
      type: openGraph.type,
      defaultSiteName: openGraph.defaultSiteName,
      locale: openGraph.locale
    },

    twitter: {
      card: twitter.card,
      title: twitter.title,
      description: twitter.shortDescription,
      images: twitter.images
    },

    // Additional metadata for e-commerce
    other: {
      'product:price:amount': other.price,
      'product:price:currency': other.currency,
      'product:availability': other.availability,
      'product:brand': other.brand,
      'product:category': other.category
    }
  }
}

/**
 * Strip HTML tags from string and limit length
 * @param {string} html - HTML string
 * @param {number} maxLength - Maximum length (default: 160)
 * @returns {string} - Clean text
 */
export const stripHtml = (html, maxLength = 160) => {
  if (!html) return ''
  return html
    .replace(/<[^>]*>/g, '')
    .substring(0, maxLength)
    .trim()
}

/**
 * Generate blog metadata
 * @param {Object} blog - Blog data
 * @param {Object} params - Route params
 * @param {string} defaultTitle - Default title
 * @param {string} defaultDescription - Default description
 * @returns {Object} - Metadata object
 */
export const generateBlogMetadata = (blog, params, defaultTitle, defaultDescription) => {
  // Default metadata if blog is not found
  const defaultMetadata = {
    title: defaultTitle || `${defaultSiteName}`,
    description: defaultDescription || 'Shapewear by W.Store - Thời trang nội y định hình cao cấp'
  }

  if (!blog) {
    return defaultMetadata
  }

  const title = blog.title ? `${blog.title} || ${defaultSiteName}` : defaultMetadata.title
  const description =
    blog.shortDescription || stripHtml(blog.description) || defaultMetadata.description
  const locale = params?.locale === 'vi' ? 'vi_VN' : 'en_US'
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://shapewearbywstore.com'

  return {
    title,
    description,
    keywords: blog.keywords || 'shapewear, đồ lót định hình, nội y, thời trang nữ, W.Store',

    // Open Graph metadata for Facebook, LinkedIn
    openGraph: {
      title,
      description,
      images: blog.image
        ? [
            {
              url: blog.image,
              width: 1200,
              height: 630,
              alt: blog.title || title
            }
          ]
        : [],
      type: 'article',
      locale,
      siteName: defaultSiteName,
      ...(blog.publishedAt && { publishedTime: blog.publishedAt }),
      ...(blog.updatedAt && { modifiedTime: blog.updatedAt })
    },

    // Twitter Card metadata
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: blog.image ? [blog.image] : []
    },

    // Canonical and alternate language URLs
    alternates: {
      canonical: `${baseUrl}/${params?.locale || 'vi'}/about`,
      languages: {
        vi: `${baseUrl}/vi/about`,
        en: `${baseUrl}/en/about`
      }
    },

    // Additional metadata
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1
      }
    },

    // Structured data for SEO
    other: {
      'article:author': blog.author || defaultSiteName,
      'article:section': blog.category || 'About',
      ...(blog.tags && { 'article:tag': blog.tags.join(', ') })
    }
  }
}
