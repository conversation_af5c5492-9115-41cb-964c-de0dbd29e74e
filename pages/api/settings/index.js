import { apiService } from "@/services/ws";

export default async function handler(req, res) {
    switch (req.method) {
        case "GET": {
            const result = await apiService.get("settings", req.query);
            return res.status(200).json(result);
        }
        case "POST": {
            const newItem = { id: Date.now(), ...req.body };
            return apiService.post("", newItem);
        }
        default: {
            return res.status(405).json({ message: "Method not allowed" });
        }
    }
}
