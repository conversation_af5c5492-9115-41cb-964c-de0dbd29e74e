import { apiService } from "@/services/ws";

export default async function handler(req, res) {
    const { id } = req.query; // Get the item ID from the URL
    switch (req.method) {
        case "GET": {
            const result = await apiService.get(`products/detail/${id}`);
            return res.status(200).json(result);
        }
        case "PUT": {
            const result = await apiService.put(`/${id}`, req.body);
            return res.status(200).json(result);
        }

        case "DELETE": {
            const result = await apiService.get(`/${id}`);
            return res.status(200).json(result);
        }

        default: {
            return res.status(405).json({ message: "Method not allowed" });
        }
    }
}
