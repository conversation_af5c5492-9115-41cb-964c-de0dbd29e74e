import { apiService } from '@/services/ws'

export default async function handler(req, res) {
  const { id } = req.query // Get the item ID from the URL
  console.log(req.query)
  switch (req.method) {
    case 'GET': {
      const result = await apiService.get(`categories/${id}`)
      return res.status(200).json(result)
    }

    default: {
      return res.status(405).json({ message: 'Method not allowed' })
    }
  }
}
